# 图片去水印功能集成文档

## 概述

本文档描述了如何将 `video-subtitle-remover` 工具的图片处理功能集成到现有项目中，实现图片去水印功能。

## 功能特性

- **智能检测**: 自动检测图片中的文字水印
- **批量处理**: 支持一次处理多张图片
- **多种算法**: 支持 LAMA 和 STTN 算法
- **手动指定**: 支持手动指定水印区域
- **实时进度**: 提供处理进度查询
- **多格式支持**: 支持 JPG、PNG、GIF、WEBP 等格式

## 系统架构

```
前端 (Vue.js)
    ↓
后端 API (Spring Boot)
    ↓
Python 处理脚本
    ↓
video-subtitle-remover 工具
```

## 环境要求

### 后端环境
- Java 17+
- Spring Boot 3.x
- Maven 3.6+

### Python 环境
- Python 3.8+
- OpenCV
- PyTorch
- PaddleOCR
- 其他依赖见 `video-subtitle-remover/requirements.txt`

## 安装配置

### 1. 安装 Python 依赖

```bash
cd video-subtitle-remover
pip install -r requirements.txt
```

### 2. 配置应用程序

在 `application.yml` 中添加配置：

```yaml
app:
  image-processor:
    work-dir: ./temp/image-processor
    python-script: ./scripts/image_watermark_processor.py
    python-command: python3
    output-url-prefix: http://localhost:8080/file/processed/
    max-concurrent-tasks: 4
    task-timeout: 300
```

### 3. 创建工作目录

```bash
mkdir -p temp/image-processor
mkdir -p scripts
```

## API 接口

### 1. 批量图片去水印

**接口**: `POST /api/tools/media/image-watermark-removal/batch`

**参数**:
- `files`: 图片文件列表 (MultipartFile[])
- `algorithm`: 处理算法 (lama/sttn, 默认: lama)
- `strength`: 处理强度 (0-100, 默认: 50)
- `outputFormat`: 输出格式 (png/jpg/jpeg/webp, 默认: png)
- `quality`: 输出质量 (low/medium/high/ultra, 默认: high)
- `autoDetect`: 是否自动检测水印 (boolean, 默认: true)
- `preserveDetails`: 是否保留细节 (boolean, 默认: true)
- `watermarkAreas`: 水印区域坐标 (JSON字符串, 可选)

**响应**:
```json
{
  "success": true,
  "data": {
    "taskId": "uuid",
    "totalCount": 3,
    "status": "PENDING",
    "message": "图片去水印任务已启动"
  }
}
```

### 2. 单张图片去水印

**接口**: `POST /api/tools/media/image-watermark-removal/single`

**参数**:
- `file`: 图片文件 (MultipartFile)
- `algorithm`: 处理算法 (lama/sttn, 默认: lama)
- `autoDetect`: 是否自动检测水印 (boolean, 默认: true)
- `outputFormat`: 输出格式 (png/jpg/jpeg/webp, 默认: png)
- `quality`: 输出质量 (low/medium/high/ultra, 默认: high)

### 3. 查询处理进度

**接口**: `GET /api/tools/media/image-watermark-removal/progress/{taskId}`

**响应**:
```json
{
  "success": true,
  "data": {
    "taskId": "uuid",
    "progress": 75,
    "status": "PROCESSING"
  }
}
```

### 4. 获取处理结果

**接口**: `GET /api/tools/media/image-watermark-removal/result/{taskId}`

**响应**:
```json
{
  "success": true,
  "data": {
    "taskId": "uuid",
    "status": "COMPLETED",
    "progress": 100,
    "totalCount": 3,
    "successCount": 3,
    "failedCount": 0,
    "results": [
      {
        "originalFileName": "image1.jpg",
        "status": "SUCCESS",
        "processedUrl": "http://localhost:8080/file/processed/image/uuid/output/image1_processed.png",
        "previewUrl": "http://localhost:8080/file/processed/image/uuid/output/image1_processed.png",
        "downloadUrl": "http://localhost:8080/file/processed/image/uuid/output/image1_processed.png",
        "fileSize": 1024000,
        "dimensions": "1920x1080",
        "processingTimeMs": 3200,
        "qualityScore": 92.5
      }
    ]
  }
}
```

### 5. 清理任务

**接口**: `DELETE /api/tools/media/image-watermark-removal/cleanup/{taskId}`

### 6. 获取支持的算法

**接口**: `GET /api/tools/media/image-watermark-removal/algorithms`

## 前端集成

### 1. 安装依赖

```bash
npm install
```

### 2. 配置 API 地址

在 `config/api.js` 中配置：

```javascript
export const API_CONFIG = {
  baseURL: 'http://localhost:8080',
  timeout: 30000
}
```

### 3. 使用示例

```javascript
// 批量处理图片
const formData = new FormData()
files.forEach(file => formData.append('files', file))
formData.append('algorithm', 'lama')
formData.append('autoDetect', 'true')

const response = await httpRequest.postFormData(
  API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.BATCH,
  formData
)
```

## 测试

### 1. 运行 Python 脚本测试

```bash
cd tools_backend/scripts
python test_image_processor.py
```

### 2. 运行后端测试

```bash
cd tools_backend
mvn test
```

### 3. 手动测试

1. 启动后端服务
2. 打开前端页面
3. 选择图片文件
4. 点击"开始批量去水印"
5. 查看处理进度和结果

## 故障排除

### 1. Python 环境问题

**问题**: `ModuleNotFoundError: No module named 'cv2'`

**解决**: 安装 OpenCV
```bash
pip install opencv-python
```

### 2. 模型文件缺失

**问题**: 找不到模型文件

**解决**: 确保 `video-subtitle-remover` 目录下有完整的模型文件

### 3. 内存不足

**问题**: 处理大图片时内存不足

**解决**: 
- 减少批量处理的图片数量
- 增加 JVM 内存: `-Xmx4g`
- 调整 Python 脚本的处理参数

### 4. 处理超时

**问题**: 图片处理超时

**解决**: 
- 增加超时时间配置
- 检查 Python 环境和依赖
- 优化图片大小

## 性能优化

### 1. 并发处理

- 调整 `max-concurrent-tasks` 参数
- 使用线程池处理多个任务

### 2. 缓存优化

- 缓存处理结果
- 使用 Redis 存储任务状态

### 3. 资源管理

- 定期清理临时文件
- 监控磁盘空间使用

## 扩展功能

### 1. 支持更多算法

- 集成其他图像修复算法
- 添加算法选择界面

### 2. 批处理优化

- 支持更大批量处理
- 添加队列管理

### 3. 结果优化

- 添加处理前后对比
- 提供质量评估指标

## 许可证

本功能基于 `video-subtitle-remover` 工具开发，请遵循相应的开源许可证。
