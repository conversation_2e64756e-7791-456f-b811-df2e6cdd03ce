package com.toolkit.dto.dev;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 网站快照请求DTO
 */
@Data
@Schema(description = "网站快照请求参数")
public class WebsiteSnapshotDTO {

    @Schema(description = "网站URL", example = "https://www.example.com")
    @NotBlank(message = "网站URL不能为空")
    private String url;

    @Schema(description = "设备类型", example = "desktop", allowableValues = {"desktop", "tablet", "mobile"})
    @Pattern(regexp = "^(desktop|tablet|mobile)$", message = "设备类型必须是 desktop、tablet 或 mobile")
    private String device = "desktop";

    @Schema(description = "输出格式", example = "png", allowableValues = {"png", "jpg", "webp"})
    @Pattern(regexp = "^(png|jpg|webp)$", message = "输出格式必须是 png、jpg 或 webp")
    private String format = "png";

    @Schema(description = "图片质量(50-100)", example = "90")
    @Min(value = 50, message = "质量不能小于50")
    @Max(value = 100, message = "质量不能大于100")
    private int quality = 90;

    @Schema(description = "延迟时间(秒)", example = "2")
    @Min(value = 0, message = "延迟时间不能小于0")
    @Max(value = 10, message = "延迟时间不能大于10")
    private int delay = 2;

    @Schema(description = "是否全页截图", example = "false")
    private boolean fullPage = false;

    @Schema(description = "是否移除广告", example = "false")
    private boolean removeAds = false;

    // 内部使用的字段，根据device自动计算
    @Schema(description = "截图宽度", hidden = true)
    private int width;

    @Schema(description = "截图高度", hidden = true)
    private int height;

    @Schema(description = "是否隐藏滚动条", hidden = true)
    private boolean hideScrollbar = true;

    /**
     * 根据设备类型设置对应的宽高
     */
    public void setDeviceDimensions() {
        switch (this.device) {
            case "desktop":
                this.width = 1366;  // 降低默认分辨率以减少文件大小
                this.height = 768;
                break;
            case "tablet":
                this.width = 768;
                this.height = 1024;
                break;
            case "mobile":
                this.width = 375;
                this.height = 667;
                break;
            default:
                this.width = 1366;
                this.height = 768;
        }
    }

    /**
     * 获取设备名称
     */
    public String getDeviceName() {
        switch (this.device) {
            case "desktop":
                return "桌面端";
            case "tablet":
                return "平板端";
            case "mobile":
                return "手机端";
            default:
                return "桌面端";
        }
    }
}