package com.toolkit.dto.media;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 图片处理结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "图片处理结果")
public class ImageProcessResult {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务状态", allowableValues = {"PENDING", "PROCESSING", "COMPLETED", "FAILED"})
    private String status;

    @Schema(description = "处理进度百分比")
    private Integer progress;

    @Schema(description = "处理成功的图片数量")
    private Integer successCount;

    @Schema(description = "处理失败的图片数量")
    private Integer failedCount;

    @Schema(description = "总图片数量")
    private Integer totalCount;

    @Schema(description = "处理结果列表")
    private List<SingleImageResult> results;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "处理开始时间")
    private LocalDateTime startTime;

    @Schema(description = "处理结束时间")
    private LocalDateTime endTime;

    @Schema(description = "处理耗时（秒）")
    private Long processingTimeSeconds;

    @Schema(description = "处理配置")
    private Map<String, Object> processConfig;

    @Schema(description = "处理统计信息")
    private Map<String, Object> statistics;

    /**
     * 单张图片处理结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "单张图片处理结果")
    public static class SingleImageResult {

        @Schema(description = "原始文件名")
        private String originalFileName;

        @Schema(description = "处理状态")
        private String status;

        @Schema(description = "原始图片URL")
        private String originalUrl;

        @Schema(description = "处理后图片URL")
        private String processedUrl;

        @Schema(description = "预览图URL")
        private String previewUrl;

        @Schema(description = "下载URL")
        private String downloadUrl;

        @Schema(description = "对比图URL")
        private String comparisonUrl;

        @Schema(description = "检测到的水印信息")
        private List<WatermarkInfo> detectedWatermarks;

        @Schema(description = "处理质量评分")
        private Double qualityScore;

        @Schema(description = "处理耗时（毫秒）")
        private Long processingTimeMs;

        @Schema(description = "错误信息")
        private String errorMessage;

        @Schema(description = "文件大小（字节）")
        private Long fileSize;

        @Schema(description = "图片尺寸")
        private String dimensions;

        @Schema(description = "处理方法")
        private String processingMethod;
    }

    /**
     * 水印信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "水印信息")
    public static class WatermarkInfo {

        @Schema(description = "水印ID")
        private Integer id;

        @Schema(description = "水印类型", allowableValues = {"text", "logo", "pattern"})
        private String type;

        @Schema(description = "水印内容")
        private String content;

        @Schema(description = "位置信息")
        private BoundingBox position;

        @Schema(description = "置信度")
        private Double confidence;

        @Schema(description = "透明度")
        private Double opacity;

        @Schema(description = "是否可移除")
        private Boolean removable;

        @Schema(description = "移除状态")
        private String removalStatus;

        @Schema(description = "移除方法")
        private String removalMethod;
    }

    /**
     * 边界框信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "边界框信息")
    public static class BoundingBox {

        @Schema(description = "X坐标")
        private Integer x;

        @Schema(description = "Y坐标")
        private Integer y;

        @Schema(description = "宽度")
        private Integer width;

        @Schema(description = "高度")
        private Integer height;
    }
}
