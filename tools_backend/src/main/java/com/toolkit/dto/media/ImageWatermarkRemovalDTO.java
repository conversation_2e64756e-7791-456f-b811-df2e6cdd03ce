package com.toolkit.dto.media;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.util.List;

/**
 * 图片去水印请求DTO
 */
@Data
@Schema(description = "图片去水印请求参数")
public class ImageWatermarkRemovalDTO {

    @Schema(description = "图片文件列表")
    @NotEmpty(message = "图片文件列表不能为空")
    @Size(max = 10, message = "一次最多处理10张图片")
    private List<MultipartFile> imageFiles;

    @Schema(description = "处理算法", example = "lama", allowableValues = {"lama", "sttn"})
    @Pattern(regexp = "^(lama|sttn)$", message = "处理算法必须是 lama 或 sttn")
    private String algorithm = "lama";

    @Schema(description = "处理强度", example = "50")
    @Min(value = 0, message = "处理强度不能小于0")
    @Max(value = 100, message = "处理强度不能大于100")
    private Integer strength = 50;

    @Schema(description = "输出格式", example = "png")
    @Pattern(regexp = "^(png|jpg|jpeg|webp)$", message = "输出格式必须是 png、jpg、jpeg 或 webp")
    private String outputFormat = "png";

    @Schema(description = "输出质量", example = "high")
    @Pattern(regexp = "^(low|medium|high|ultra)$", message = "输出质量必须是 low、medium、high 或 ultra")
    private String quality = "high";

    @Schema(description = "是否自动检测水印", example = "true")
    private Boolean autoDetect = true;

    @Schema(description = "是否保留细节", example = "true")
    private Boolean preserveDetails = true;

    @Schema(description = "水印区域坐标列表，格式：[{\"x\": 100, \"y\": 100, \"width\": 200, \"height\": 50}]")
    private String watermarkAreas;

    @Schema(description = "扩展参数")
    private String extra;

    // 为了兼容 Service 层的调用，添加 is 前缀的方法
    public Boolean isAutoDetect() {
        return this.autoDetect;
    }

    public Boolean isPreserveDetails() {
        return this.preserveDetails;
    }
}
