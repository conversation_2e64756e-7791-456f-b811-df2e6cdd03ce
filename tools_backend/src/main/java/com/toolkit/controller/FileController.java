package com.toolkit.controller;

import com.toolkit.common.ResultVO;
import com.toolkit.config.OSSConfig;
import com.toolkit.utils.OSSUtils;
import com.toolkit.utils.JwtUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件上传控制器
 * 支持图片、音频、视频等文件上传
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Tag(name = "文件管理")
public class FileController {

    @Autowired
    private OSSUtils ossUtils;

    @Resource
    private JwtUtils jwtUtils;

    @Value("${app.image-processor.work-dir:./temp/image-processor}")
    private String imageWorkDir;

    @Value("${app.video-processor.work-dir:./temp/video-processor}")
    private String videoWorkDir;

    @PostMapping("/upload/avatar")
    @Operation(summary = "上传头像")
    public ResultVO<String> uploadAvatar(HttpServletRequest request,
                                       @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file) {
        try {
            Long userId = getCurrentUserId(request);
            
            // 校验文件
            if (file.isEmpty()) {
                return ResultVO.error("文件不能为空");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                return ResultVO.error("只支持图片格式：jpg、jpeg、png、gif、webp");
            }
            
            if (file.getSize() > OSSConfig.FileSize.MAX_IMAGE_SIZE) {
                return ResultVO.error("图片大小不能超过10MB");
            }
            
            // 上传文件
            String url = ossUtils.uploadFile(file, OSSConfig.PathPrefix.AVATAR);
            if (url == null) {
                return ResultVO.error("文件上传失败");
            }
            
            log.info("用户{}上传头像成功: {}", userId, url);
            return ResultVO.success(url);
            
        } catch (Exception e) {
            log.error("上传头像失败", e);
            return ResultVO.error("上传头像失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/wallpaper")
    @Operation(summary = "上传壁纸")
    public ResultVO<Map<String, Object>> uploadWallpaper(HttpServletRequest request,
                                                        @Parameter(description = "壁纸文件") @RequestParam("file") MultipartFile file,
                                                        @Parameter(description = "壁纸标题") @RequestParam(required = false) String title,
                                                        @Parameter(description = "壁纸分类") @RequestParam(required = false) String category) {
        try {
            Long userId = getCurrentUserId(request);
            
            // 校验文件
            if (file.isEmpty()) {
                return ResultVO.error("文件不能为空");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                return ResultVO.error("只支持图片格式：jpg、jpeg、png、gif、webp");
            }
            
            if (file.getSize() > OSSConfig.FileSize.MAX_IMAGE_SIZE) {
                return ResultVO.error("图片大小不能超过10MB");
            }
            
            // 上传文件
            String url = ossUtils.uploadFile(file, OSSConfig.PathPrefix.WALLPAPER);
            if (url == null) {
                return ResultVO.error("文件上传失败");
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("filename", originalFilename);
            result.put("size", file.getSize());
            result.put("uploadTime", System.currentTimeMillis());
            
            log.info("用户{}上传壁纸成功: {}", userId, url);
            return ResultVO.success(result);
            
        } catch (Exception e) {
            log.error("上传壁纸失败", e);
            return ResultVO.error("上传壁纸失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/audio")
    @Operation(summary = "上传音频文件")
    public ResultVO<Map<String, Object>> uploadAudio(HttpServletRequest request,
                                                    @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file) {
        try {
            Long userId = getCurrentUserId(request);
            
            // 校验文件
            if (file.isEmpty()) {
                return ResultVO.error("文件不能为空");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isAudioFile(originalFilename)) {
                return ResultVO.error("只支持音频格式：mp3、wav、aac、m4a、flac");
            }
            
            if (file.getSize() > OSSConfig.FileSize.MAX_AUDIO_SIZE) {
                return ResultVO.error("音频文件大小不能超过50MB");
            }
            
            // 上传文件
            String url = ossUtils.uploadFile(file, OSSConfig.PathPrefix.AUDIO);
            if (url == null) {
                return ResultVO.error("文件上传失败");
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("filename", originalFilename);
            result.put("size", file.getSize());
            result.put("uploadTime", System.currentTimeMillis());
            
            log.info("用户{}上传音频成功: {}", userId, url);
            return ResultVO.success(result);
            
        } catch (Exception e) {
            log.error("上传音频失败", e);
            return ResultVO.error("上传音频失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/image")
    @Operation(summary = "上传图片（用于工具处理）")
    public ResultVO<Map<String, Object>> uploadImage(HttpServletRequest request,
                                                    @Parameter(description = "图片文件") @RequestParam("file") MultipartFile file,
                                                    @Parameter(description = "是否临时文件") @RequestParam(defaultValue = "false") Boolean temporary) {
        try {
            Long userId = getCurrentUserId(request);
            
            // 校验文件
            if (file.isEmpty()) {
                return ResultVO.error("文件不能为空");
            }
            
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || !isImageFile(originalFilename)) {
                return ResultVO.error("只支持图片格式：jpg、jpeg、png、gif、webp");
            }
            
            if (file.getSize() > OSSConfig.FileSize.MAX_IMAGE_SIZE) {
                return ResultVO.error("图片大小不能超过10MB");
            }
            
            // 选择上传路径
            String pathPrefix = temporary ? OSSConfig.PathPrefix.TEMP : OSSConfig.PathPrefix.USER_GENERATED;
            
            // 上传文件
            String url = ossUtils.uploadFile(file, pathPrefix);
            if (url == null) {
                return ResultVO.error("文件上传失败");
            }
            
            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("url", url);
            result.put("filename", originalFilename);
            result.put("size", file.getSize());
            result.put("uploadTime", System.currentTimeMillis());
            result.put("temporary", temporary);
            
            log.info("用户{}上传图片成功: {}", userId, url);
            return ResultVO.success(result);
            
        } catch (Exception e) {
            log.error("上传图片失败", e);
            return ResultVO.error("上传图片失败：" + e.getMessage());
        }
    }

    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传文件")
    public ResultVO<List<Map<String, Object>>> uploadBatch(HttpServletRequest request,
                                                          @Parameter(description = "文件列表") @RequestParam("files") MultipartFile[] files,
                                                          @Parameter(description = "文件类型") @RequestParam(defaultValue = "image") String type) {
        try {
            Long userId = getCurrentUserId(request);
            
            if (files.length == 0) {
                return ResultVO.error("请选择要上传的文件");
            }
            
            if (files.length > 10) {
                return ResultVO.error("一次最多只能上传10个文件");
            }
            
            List<Map<String, Object>> results = new java.util.ArrayList<>();
            
            for (MultipartFile file : files) {
                try {
                    if (file.isEmpty()) {
                        continue;
                    }
                    
                    String originalFilename = file.getOriginalFilename();
                    if (originalFilename == null) {
                        continue;
                    }
                    
                    // 根据类型选择校验和路径
                    String pathPrefix;
                    long maxSize;
                    
                    switch (type.toLowerCase()) {
                        case "image":
                            if (!isImageFile(originalFilename)) {
                                continue;
                            }
                            pathPrefix = OSSConfig.PathPrefix.USER_GENERATED;
                            maxSize = OSSConfig.FileSize.MAX_IMAGE_SIZE;
                            break;
                        case "audio":
                            if (!isAudioFile(originalFilename)) {
                                continue;
                            }
                            pathPrefix = OSSConfig.PathPrefix.AUDIO;
                            maxSize = OSSConfig.FileSize.MAX_AUDIO_SIZE;
                            break;
                        default:
                            continue;
                    }
                    
                    if (file.getSize() > maxSize) {
                        continue;
                    }
                    
                    // 上传文件
                    String url = ossUtils.uploadFile(file, pathPrefix);
                    if (url != null) {
                        Map<String, Object> result = new HashMap<>();
                        result.put("url", url);
                        result.put("filename", originalFilename);
                        result.put("size", file.getSize());
                        result.put("uploadTime", System.currentTimeMillis());
                        results.add(result);
                    }
                    
                } catch (Exception e) {
                    log.error("批量上传单个文件失败: {}", e.getMessage());
                }
            }
            
            log.info("用户{}批量上传{}个文件成功", userId, results.size());
            return ResultVO.success(results);
            
        } catch (Exception e) {
            log.error("批量上传失败", e);
            return ResultVO.error("批量上传失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除文件")
    public ResultVO<Boolean> deleteFile(HttpServletRequest request,
                                       @Parameter(description = "文件URL") @RequestParam String fileUrl) {
        try {
            Long userId = getCurrentUserId(request);
            
            boolean success = ossUtils.deleteFile(fileUrl);
            
            log.info("用户{}删除文件{}: {}", userId, fileUrl, success ? "成功" : "失败");
            return ResultVO.success(success);
            
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return ResultVO.error("删除文件失败：" + e.getMessage());
        }
    }

    @GetMapping("/info")
    @Operation(summary = "获取文件信息")
    public ResultVO<Map<String, Object>> getFileInfo(@Parameter(description = "文件URL") @RequestParam String fileUrl) {
        try {
            Map<String, Object> fileInfo = ossUtils.getFileInfo(fileUrl);
            return ResultVO.success(fileInfo);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return ResultVO.error("获取文件信息失败");
        }
    }

    /**
     * 从请求中获取当前用户ID
     */
    private Long getCurrentUserId(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
            return jwtUtils.getUserIdFromToken(token);
        }
        throw new RuntimeException("用户未登录");
    }

    /**
     * 判断是否为图片文件
     */
    private boolean isImageFile(String filename) {
        if (filename == null) {
            return false;
        }
        String extension = getFileExtension(filename).toLowerCase();
        return Arrays.asList(OSSConfig.FileType.IMAGE_TYPES).contains("." + extension);
    }

    /**
     * 判断是否为音频文件
     */
    private boolean isAudioFile(String filename) {
        if (filename == null) {
            return false;
        }
        String extension = getFileExtension(filename).toLowerCase();
        return Arrays.asList(OSSConfig.FileType.AUDIO_TYPES).contains("." + extension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }

    // ==================== 处理后文件访问接口 ====================

    @GetMapping("/processed/image/{taskId}/{type}/{fileName}")
    @Operation(summary = "获取图片处理文件", description = "根据任务ID和文件名获取处理后的图片文件")
    public ResponseEntity<org.springframework.core.io.Resource> getProcessedImageFile(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "文件类型：input/output") @PathVariable String type,
            @Parameter(description = "文件名") @PathVariable String fileName,
            @Parameter(description = "是否下载") @RequestParam(required = false, defaultValue = "false") boolean download
    ) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(imageWorkDir, taskId, type, fileName);
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                log.warn("文件不存在: {}", filePath);
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件在允许的目录内
            Path workDirPath = Paths.get(imageWorkDir).toAbsolutePath().normalize();
            Path requestedPath = filePath.toAbsolutePath().normalize();
            if (!requestedPath.startsWith(workDirPath)) {
                log.warn("非法文件访问尝试: {}", requestedPath);
                return ResponseEntity.badRequest().build();
            }

            org.springframework.core.io.Resource resource = new FileSystemResource(file);

            // 确定媒体类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));

            if (download) {
                headers.setContentDispositionFormData("attachment", fileName);
            } else {
                headers.setContentDispositionFormData("inline", fileName);
            }

            log.info("提供图片文件访问 - 任务ID: {}, 文件: {}, 下载: {}", taskId, fileName, download);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("获取图片文件失败 - 任务ID: {}, 文件: {}", taskId, fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/processed/video/{taskId}/{fileName}")
    @Operation(summary = "获取视频处理文件", description = "根据任务ID和文件名获取处理后的视频文件")
    public ResponseEntity<org.springframework.core.io.Resource> getProcessedVideoFile(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Parameter(description = "文件名") @PathVariable String fileName,
            @Parameter(description = "是否下载") @RequestParam(required = false, defaultValue = "false") boolean download
    ) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(videoWorkDir, taskId, fileName);
            File file = filePath.toFile();

            if (!file.exists() || !file.isFile()) {
                log.warn("文件不存在: {}", filePath);
                return ResponseEntity.notFound().build();
            }

            // 安全检查：确保文件在允许的目录内
            Path workDirPath = Paths.get(videoWorkDir).toAbsolutePath().normalize();
            Path requestedPath = filePath.toAbsolutePath().normalize();
            if (!requestedPath.startsWith(workDirPath)) {
                log.warn("非法文件访问尝试: {}", requestedPath);
                return ResponseEntity.badRequest().build();
            }

            org.springframework.core.io.Resource resource = new FileSystemResource(file);

            // 确定媒体类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 构建响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));

            if (download) {
                headers.setContentDispositionFormData("attachment", fileName);
            } else {
                headers.setContentDispositionFormData("inline", fileName);
            }

            log.info("提供视频文件访问 - 任务ID: {}, 文件: {}, 下载: {}", taskId, fileName, download);

            return ResponseEntity.ok()
                    .headers(headers)
                    .contentLength(file.length())
                    .body(resource);

        } catch (Exception e) {
            log.error("获取视频文件失败 - 任务ID: {}, 文件: {}", taskId, fileName, e);
            return ResponseEntity.internalServerError().build();
        }
    }
}