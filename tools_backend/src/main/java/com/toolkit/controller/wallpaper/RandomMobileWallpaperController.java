package com.toolkit.controller.wallpaper;

import com.toolkit.common.Result;
import com.toolkit.dto.wallpaper.RandomMobileWallpaperDTO;
import com.toolkit.service.wallpaper.RandomMobileWallpaperService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 随机手机壁纸控制器
 */
@RestController
@RequestMapping("/api/tools/wallpaper")
@RequiredArgsConstructor
@Tag(name = "随机手机壁纸", description = "随机手机壁纸")
public class RandomMobileWallpaperController {

    private final RandomMobileWallpaperService randomMobileWallpaperService;

    @GetMapping("/random-mobile-wallpaper")
    @Operation(summary = "随机手机壁纸", description = "获取随机手机壁纸数据")
    public Result<Object> handleRandomMobileWallpaper(
            @Parameter(description = "分类") @RequestParam(defaultValue = "all") String category,
            @Parameter(description = "分辨率宽度") @RequestParam(defaultValue = "750") Integer width,
            @Parameter(description = "分辨率高度") @RequestParam(defaultValue = "1334") Integer height,
            @Parameter(description = "数量") @RequestParam(defaultValue = "6") Integer count) {
        try {
            // 构建请求参数
            RandomMobileWallpaperDTO request = new RandomMobileWallpaperDTO();
            request.setCategory(category);
            request.setWidth(width);
            request.setHeight(height);
            request.setCount(count);

            Object result = randomMobileWallpaperService.process(request);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("随机手机壁纸查询失败：" + e.getMessage());
        }
    }
}
