package com.toolkit.controller.media;

import com.toolkit.common.Result;
import com.toolkit.dto.media.ImageWatermarkRemovalDTO;
import com.toolkit.service.media.ImageWatermarkRemovalService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 图片去水印控制器
 * 集成video-subtitle-remover工具的图片处理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/tools/media")
@RequiredArgsConstructor
@Tag(name = "图片去水印", description = "图片去水印处理接口")
public class ImageWatermarkRemovalController {

    private final ImageWatermarkRemovalService imageWatermarkRemovalService;

    @PostMapping("/image-watermark-removal/batch")
    @Operation(summary = "批量图片去水印", description = "上传多张图片进行批量去水印处理")
    public Result<Object> batchRemoveWatermark(
            @Parameter(description = "图片文件列表")
            @RequestParam("files") List<MultipartFile> files,
            
            @Parameter(description = "处理算法：lama(推荐)/sttn")
            @RequestParam(required = false, defaultValue = "lama") String algorithm,
            
            @Parameter(description = "处理强度：0-100")
            @RequestParam(required = false, defaultValue = "50") Integer strength,
            
            @Parameter(description = "输出格式：png/jpg/jpeg/webp")
            @RequestParam(required = false, defaultValue = "png") String outputFormat,
            
            @Parameter(description = "输出质量：low/medium/high/ultra")
            @RequestParam(required = false, defaultValue = "high") String quality,
            
            @Parameter(description = "是否自动检测水印")
            @RequestParam(required = false, defaultValue = "true") Boolean autoDetect,
            
            @Parameter(description = "是否保留细节")
            @RequestParam(required = false, defaultValue = "true") Boolean preserveDetails,
            
            @Parameter(description = "水印区域坐标（JSON格式）")
            @RequestParam(required = false) String watermarkAreas,
            
            @Parameter(description = "扩展参数")
            @RequestParam(required = false) String extra
    ) {
        try {
            log.info("收到批量图片去水印请求 - 文件数量: {}, 算法: {}, 质量: {}", 
                    files.size(), algorithm, quality);

            // 验证文件
            if (files.isEmpty()) {
                return Result.error("请选择要处理的图片文件");
            }

            if (files.size() > 10) {
                return Result.error("一次最多处理10张图片");
            }

            // 验证文件格式
            for (MultipartFile file : files) {
                if (!isValidImageFile(file)) {
                    return Result.error("不支持的图片格式，请上传JPG、PNG、GIF、WEBP等格式的图片文件");
                }
                
                // 文件大小检查 (限制10MB)
                if (file.getSize() > 10 * 1024 * 1024) {
                    return Result.error("图片文件过大，请上传小于10MB的图片文件");
                }
            }

            // 创建请求DTO
            ImageWatermarkRemovalDTO request = new ImageWatermarkRemovalDTO();
            request.setImageFiles(files);
            request.setAlgorithm(algorithm);
            request.setStrength(strength);
            request.setOutputFormat(outputFormat);
            request.setQuality(quality);
            request.setAutoDetect(autoDetect);
            request.setPreserveDetails(preserveDetails);
            request.setWatermarkAreas(watermarkAreas);
            request.setExtra(extra);

            // 处理图片
            Map<String, Object> result = imageWatermarkRemovalService.processImages(request);
            return Result.success(result);

        } catch (Exception e) {
            log.error("批量图片去水印处理失败", e);
            return Result.error("图片处理失败：" + e.getMessage());
        }
    }

    @PostMapping("/image-watermark-removal/single")
    @Operation(summary = "单张图片去水印", description = "上传单张图片进行去水印处理")
    public Result<Object> singleRemoveWatermark(
            @Parameter(description = "图片文件")
            @RequestParam("file") MultipartFile file,
            
            @Parameter(description = "处理算法：lama(推荐)/sttn")
            @RequestParam(required = false, defaultValue = "lama") String algorithm,
            
            @Parameter(description = "是否自动检测水印")
            @RequestParam(required = false, defaultValue = "true") Boolean autoDetect,
            
            @Parameter(description = "输出格式：png/jpg/jpeg/webp")
            @RequestParam(required = false, defaultValue = "png") String outputFormat,
            
            @Parameter(description = "输出质量：low/medium/high/ultra")
            @RequestParam(required = false, defaultValue = "high") String quality
    ) {
        try {
            log.info("收到单张图片去水印请求 - 文件: {}, 算法: {}", file.getOriginalFilename(), algorithm);

            // 验证文件
            if (file.isEmpty()) {
                return Result.error("请选择要处理的图片文件");
            }

            if (!isValidImageFile(file)) {
                return Result.error("不支持的图片格式，请上传JPG、PNG、GIF、WEBP等格式的图片文件");
            }

            // 文件大小检查 (限制10MB)
            if (file.getSize() > 10 * 1024 * 1024) {
                return Result.error("图片文件过大，请上传小于10MB的图片文件");
            }

            // 处理图片
            Map<String, Object> result = imageWatermarkRemovalService.processSingleImage(
                    file, algorithm, autoDetect, outputFormat, quality);
            return Result.success(result);

        } catch (Exception e) {
            log.error("单张图片去水印处理失败", e);
            return Result.error("图片处理失败：" + e.getMessage());
        }
    }

    @GetMapping("/image-watermark-removal/progress/{taskId}")
    @Operation(summary = "获取处理进度", description = "根据任务ID获取图片处理进度")
    public Result<Object> getProgress(@PathVariable String taskId) {
        try {
            Integer progress = imageWatermarkRemovalService.getProcessProgress(taskId);
            String status = imageWatermarkRemovalService.getTaskStatus(taskId);
            
            Map<String, Object> result = Map.of(
                    "taskId", taskId,
                    "progress", progress,
                    "status", status
            );
            
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取处理进度失败 - 任务ID: {}", taskId, e);
            return Result.error("获取进度失败：" + e.getMessage());
        }
    }

    @GetMapping("/image-watermark-removal/result/{taskId}")
    @Operation(summary = "获取处理结果", description = "根据任务ID获取图片处理结果")
    public Result<Object> getResult(@PathVariable String taskId) {
        try {
            Map<String, Object> result = imageWatermarkRemovalService.getProcessResult(taskId);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取处理结果失败 - 任务ID: {}", taskId, e);
            return Result.error("获取结果失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/image-watermark-removal/cleanup/{taskId}")
    @Operation(summary = "清理任务", description = "清理指定任务的临时文件")
    public Result<Object> cleanupTask(@PathVariable String taskId) {
        try {
            imageWatermarkRemovalService.cleanupTask(taskId);
            return Result.success("任务清理完成");
        } catch (Exception e) {
            log.error("清理任务失败 - 任务ID: {}", taskId, e);
            return Result.error("清理任务失败：" + e.getMessage());
        }
    }

    @GetMapping("/image-watermark-removal/algorithms")
    @Operation(summary = "获取支持的算法列表", description = "获取可用的图片去水印算法")
    public Result<Object> getSupportedAlgorithms() {
        try {
            Map<String, Object> algorithms = imageWatermarkRemovalService.getSupportedAlgorithms();
            return Result.success(algorithms);
        } catch (Exception e) {
            log.error("获取算法列表失败", e);
            return Result.error("获取算法列表失败：" + e.getMessage());
        }
    }

    /**
     * 验证是否为有效的图片文件
     */
    private boolean isValidImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }

        return contentType.startsWith("image/") && 
               (contentType.equals("image/jpeg") || 
                contentType.equals("image/jpg") || 
                contentType.equals("image/png") || 
                contentType.equals("image/gif") || 
                contentType.equals("image/webp") ||
                contentType.equals("image/bmp") ||
                contentType.equals("image/tiff"));
    }
}
