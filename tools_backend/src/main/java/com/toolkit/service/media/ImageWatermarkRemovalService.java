package com.toolkit.service.media;

import com.toolkit.dto.media.ImageWatermarkRemovalDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 图片去水印服务接口
 * 集成video-subtitle-remover工具的图片处理功能
 */
public interface ImageWatermarkRemovalService {

    /**
     * 批量处理图片去水印
     *
     * @param request 请求参数
     * @return 处理结果
     */
    Map<String, Object> processImages(ImageWatermarkRemovalDTO request);

    /**
     * 单张图片去水印处理
     *
     * @param imageFile 图片文件
     * @param algorithm 处理算法
     * @param autoDetect 是否自动检测
     * @param outputFormat 输出格式
     * @param quality 输出质量
     * @return 处理结果
     */
    Map<String, Object> processSingleImage(MultipartFile imageFile, String algorithm, 
                                         Boolean autoDetect, String outputFormat, String quality);

    /**
     * 获取处理进度
     *
     * @param taskId 任务ID
     * @return 进度百分比
     */
    Integer getProcessProgress(String taskId);

    /**
     * 获取处理结果
     *
     * @param taskId 任务ID
     * @return 处理结果
     */
    Map<String, Object> getProcessResult(String taskId);

    /**
     * 清理临时文件
     *
     * @param taskId 任务ID
     */
    void cleanupTask(String taskId);

    /**
     * 检查任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    String getTaskStatus(String taskId);

    /**
     * 获取支持的算法列表
     *
     * @return 算法列表
     */
    Map<String, Object> getSupportedAlgorithms();
}
