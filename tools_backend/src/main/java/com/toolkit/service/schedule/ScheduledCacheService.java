package com.toolkit.service.schedule;

import com.toolkit.service.cache.DataCacheService;

import com.toolkit.service.fun.GpuLadderService;
import com.toolkit.service.fun.CpuLadderService;
import com.toolkit.service.fun.HistoryTodayService;
import com.toolkit.service.utility.VehiclePriceQueryService;
import com.toolkit.service.utility.GasPriceQueryService;

import com.toolkit.dto.fun.GpuLadderDTO;
import com.toolkit.dto.fun.CpuLadderDTO;
import com.toolkit.dto.fun.HistoryTodayDTO;
import com.toolkit.dto.utility.VehiclePriceQueryDTO;
import com.toolkit.dto.utility.GasPriceQueryDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * 定时任务服务 - 定期更新缓存数据
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ScheduledCacheService {

    private final DataCacheService cacheService;

    private final GpuLadderService gpuLadderService;
    private final CpuLadderService cpuLadderService;
    private final HistoryTodayService historyTodayService;
    private final VehiclePriceQueryService vehiclePriceQueryService;
    private final GasPriceQueryService gasPriceQueryService;



    /**
     * 每天凌晨2点更新历史上的今天
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void updateHistoryToday() {
        log.info("开始定时更新历史上的今天缓存");
        try {
            HistoryTodayDTO request = new HistoryTodayDTO();
            Object result = historyTodayService.fetchFromApi(request);
            cacheService.setCache("history_today", result, "history", 25); // 25小时过期
            
            log.info("历史上的今天缓存更新成功");
        } catch (Exception e) {
            log.error("历史上的今天缓存更新失败", e);
        }
    }

    /**
     * 每周日凌晨3点更新GPU天梯图
     */
    @Scheduled(cron = "0 0 3 * * SUN")
    public void updateGpuLadder() {
        log.info("开始定时更新GPU天梯图缓存");
        try {
            GpuLadderDTO request = new GpuLadderDTO();
            Object result = gpuLadderService.fetchFromApi(request);
            cacheService.setCache("gpu_ladder", result, "ladder", 168); // 7天过期
            
            log.info("GPU天梯图缓存更新成功");
        } catch (Exception e) {
            log.error("GPU天梯图缓存更新失败", e);
        }
    }

    /**
     * 每周日凌晨4点更新CPU天梯图
     */
    @Scheduled(cron = "0 0 4 * * SUN")
    public void updateCpuLadder() {
        log.info("开始定时更新CPU天梯图缓存");
        try {
            CpuLadderDTO request = new CpuLadderDTO();
            Object result = cpuLadderService.fetchFromApi(request);
            cacheService.setCache("cpu_ladder", result, "ladder", 168); // 7天过期
            
            log.info("CPU天梯图缓存更新成功");
        } catch (Exception e) {
            log.error("CPU天梯图缓存更新失败", e);
        }
    }

    /**
     * 每天凌晨5点更新全国油价
     */
    @Scheduled(cron = "0 0 5 * * ?")
    public void updateGasPrice() {
        log.info("开始定时更新全国油价缓存");
        try {
            GasPriceQueryDTO request = new GasPriceQueryDTO();
            Object result = gasPriceQueryService.fetchFromApi(request);
            cacheService.setCache("gas_price", result, "utility", 25); // 25小时过期
            
            log.info("全国油价缓存更新成功");
        } catch (Exception e) {
            log.error("全国油价缓存更新失败", e);
        }
    }

    /**
     * 每周日凌晨6点更新车辆价格数据
     */
    @Scheduled(cron = "0 0 6 * * SUN")
    public void updateVehiclePrice() {
        log.info("开始定时更新车辆价格缓存");
        try {
            VehiclePriceQueryDTO request = new VehiclePriceQueryDTO();
            request.setType("search");
            request.setKeyword("");
            
            Object result = vehiclePriceQueryService.fetchFromApi(request);
            cacheService.setCache("vehicle_price", result, "vehicle", 169); // 7天+1小时过期
            
            log.info("车辆价格缓存更新成功");
        } catch (Exception e) {
            log.error("车辆价格缓存更新失败", e);
        }
    }

    /**
     * 每小时清理过期缓存
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanExpiredCache() {
        log.info("开始清理过期缓存");
        cacheService.cleanExpiredCache();
    }
}