package com.toolkit.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 静态资源配置
 */
@Configuration
public class StaticResourceConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置截图文件访问路径 - 使用绝对路径
        String screenshotsPath = System.getProperty("user.dir") + "/screenshots/";
        registry.addResourceHandler("/screenshots/**")
                .addResourceLocations("file:" + screenshotsPath)
                .setCachePeriod(3600); // 缓存1小时

        // 配置其他静态资源
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/")
                .setCachePeriod(3600);

        System.out.println("Screenshots path configured: " + screenshotsPath);
    }
}
