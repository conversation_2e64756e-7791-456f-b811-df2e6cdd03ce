# 图片处理器配置
app:
  image-processor:
    # 工作目录
    work-dir: ./temp/image-processor
    # Python脚本路径
    python-script: ./scripts/image_watermark_processor.py
    # Python命令
    python-command: python3
    # 输出URL前缀
    output-url-prefix: http://localhost:8080/file/processed/
    # 最大并发处理数
    max-concurrent-tasks: 4
    # 任务超时时间（秒）
    task-timeout: 300
    # 临时文件清理间隔（小时）
    cleanup-interval: 24
    # 支持的图片格式
    supported-formats:
      - jpg
      - jpeg
      - png
      - gif
      - webp
      - bmp
      - tiff
    # 文件大小限制（字节）
    max-file-size: 10485760  # 10MB
    # 批量处理最大文件数
    max-batch-size: 10

  video-processor:
    # 工作目录
    work-dir: ./temp/video-processor
    # Python脚本路径
    python-script: ./video-subtitle-remover/backend/main.py
    # Python命令
    python-command: python3
    # 输出URL前缀
    output-url-prefix: http://localhost:8080/file/processed/
    # 最大并发处理数
    max-concurrent-tasks: 2
    # 任务超时时间（秒）
    task-timeout: 1800  # 30分钟
    # 临时文件清理间隔（小时）
    cleanup-interval: 24
    # 支持的视频格式
    supported-formats:
      - mp4
      - avi
      - mov
      - mkv
      - flv
      - wmv
      - webm
    # 文件大小限制（字节）
    max-file-size: 524288000  # 500MB

# 日志配置
logging:
  level:
    com.toolkit.service.media: DEBUG
    com.toolkit.controller.media: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/image-processor.log
    max-size: 100MB
    max-history: 30

# 线程池配置
spring:
  task:
    execution:
      pool:
        core-size: 4
        max-size: 8
        queue-capacity: 100
        keep-alive: 60s
      thread-name-prefix: image-processor-
    scheduling:
      pool:
        size: 2
      thread-name-prefix: scheduler-

# 文件上传配置
servlet:
  multipart:
    max-file-size: 10MB
    max-request-size: 100MB
    enabled: true

# 跨域配置
cors:
  allowed-origins:
    - http://localhost:3000
    - http://localhost:8080
    - http://127.0.0.1:3000
    - http://127.0.0.1:8080
  allowed-methods:
    - GET
    - POST
    - PUT
    - DELETE
    - OPTIONS
  allowed-headers:
    - "*"
  allow-credentials: true
  max-age: 3600
