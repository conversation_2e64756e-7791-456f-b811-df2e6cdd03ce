# 万能工具箱后端服务配置
server:
  port: 8080
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  ssl:
    enabled: false  # 使用Nginx处理SSL
  # Spring Boot 3.0+ 新增配置
  shutdown: graceful
  tomcat:
    connection-timeout: 5s
    keep-alive-timeout: 60s
    threads:
      max: 200
      min-spare: 10

spring:
  profiles:
    active: dev
  application:
    name: toolkit-backend
  
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: 123456
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # 数据库初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:sql/init.sql
      continue-on-error: true

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update  # 自动创建和更新表结构
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
    database-platform: org.hibernate.dialect.MySQL8Dialect

  # Redis配置
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:123}
      database: 0
      timeout: 10000ms
      connect-timeout: 5000ms
      client-type: lettuce
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
        shutdown-timeout: 100ms

  # JSON配置
  jackson:
    serialization:
      INDENT_OUTPUT: true
      WRITE_DATES_AS_TIMESTAMPS: false
      FAIL_ON_EMPTY_BEANS: false
    deserialization:
      FAIL_ON_UNKNOWN_PROPERTIES: false
    default-property-inclusion: non_null
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    # 修复Java 8时间类型序列化问题
    modules:
      - com.fasterxml.jackson.datatype.jsr310.JavaTimeModule

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
      enabled: true
      location: ${java.io.tmpdir}

  # Bean覆盖配置
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:/mapper/*.xml
  type-aliases-package: com.toolkit.entity

# 日志配置
logging:
  level:
    root: INFO
    com.toolkit: DEBUG
    com.toolkit.mapper: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.springframework.boot.autoconfigure: WARN
  pattern:
    console: '%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}'
    file: '%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}'
  file:
    name: ${user.home}/toolkit/logs/toolkit.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30
      total-size-cap: 1GB

# Actuator配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: GET
  endpoint:
    health:
      show-details: when_authorized
      roles: ADMIN
    shutdown:
      enabled: false
  info:
    env:
      enabled: true
    git:
      mode: full
    java:
      enabled: true
    os:
      enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
    export:
      prometheus:
        enabled: true

# JWT配置 (直接配置，用于JwtUtils类)
jwt:
  secret: ${JWT_SECRET:toolkit-jwt-secret-2024-very-long-key-string-for-security-production-12345678901234567890}
  expire: ${JWT_EXPIRATION:604800}

# 七牛云配置 (直接配置，用于OSSConfig类)
qiniu:
  access-key: lYcN6wLvY06QnrDXaKOo8vgZqPtV50eGd27WFr7o
  secret-key: SxE_XeqMBBsF4XxoWbkusD11W9dQ1Yza-Ny_3m2C
  bucket: orderfood-space
  domain: https://tinyorder.top/
  zone: zone2
  max-file-size: 10485760

# 应用配置
app:
  # 工具使用限制
  tools:
    free-daily-limit: 10    # 免费用户每日工具使用次数限制
    vip-daily-limit: 1000   # VIP用户每日工具使用次数限制
  # 文件处理配置
  file:
    max-size: 50MB          # 单文件最大大小
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar
  # 图片处理配置
  image-processor:
    work-dir: ${user.home}/toolkit/temp/image-processor  # 图片处理工作目录
    python-script: ${user.dir}/scripts/image_watermark_processor.py  # Python脚本路径
    python-command: python3  # Python命令，可以是python3或具体路径
    output-url-prefix: http://localhost:8080/file/processed/  # 输出文件URL前缀
    max-file-size: 10MB     # 最大图片文件大小
    max-batch-size: 10      # 批量处理最大文件数
    supported-formats: jpg,jpeg,png,gif,webp,bmp,tiff  # 支持的图片格式
    cleanup-after: 3600     # 处理完成后文件保留时间（秒）
  # 视频处理配置
  video-processor:
    work-dir: ${user.home}/toolkit/temp/video-processor  # 视频处理工作目录
    python-script: ${user.dir}/../video-subtitle-remover/backend/main.py  # Python脚本路径（使用项目根目录）
    python-command: python  # Python命令，可以是python3或具体路径
    max-file-size: 500MB    # 最大视频文件大小
    supported-formats: mp4,avi,mov,wmv,flv,mkv,webm  # 支持的视频格式
    cleanup-after: 3600     # 处理完成后文件保留时间（秒）
  # 数据库配置
  db:
    auto-backup: true       # 启用自动备份
    backup-interval: 86400  # 自动备份间隔（秒）
    auto-import: false      # 禁用数据库自动导入

# 证件照处理配置
idphoto:
  python:
    script:
      path: ../HivisionIDPhotos/id_photo_api.py  # Python脚本路径
    executable: python      # Python可执行文件路径
  temp:
    dir: temp/idphoto      # 临时文件目录
  output:
    dir: static/idphoto    # 输出文件目录

# 文件上传路径
upload:
  dir: ${user.home}/toolkit/uploads/
  temp-dir: ${upload.dir}/temp/
  image-dir: ${upload.dir}/images/
  document-dir: ${upload.dir}/documents/
  backup-dir: ${upload.dir}/backup/

# 跨域配置
cors:
  allowed-origins: ${CORS_ORIGINS:http://localhost:3000,https://localhost:3000,https://www.tinyorder.top}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  max-age: 3600

# 微信小程序配置 (直接配置，用于WxAuthServiceImpl类)
wx:
  miniapp:
    appid: ${WECHAT_MP_APPID:wx4c9fe19f2a8d5b9e}
    secret: ${WECHAT_MP_SECRET:674407c944977e246e690b91968b0024}
    token: ${WECHAT_MP_TOKEN:a7f3e9d2b5c8416f0e8d3a2b7c6d5e4f}
    encoding-aes-key: ${WECHAT_MP_AES_KEY:Nc9XEFEkxxtQyz5ElWipfiZUDGshbH5vAJCbneyydZA}
  # 支付配置
  pay:
    mchid: ${WECHAT_PAY_MCHID:1718176535}
    api-key: ${WECHAT_PAY_API_KEY:8zX5p2rQ7wE9aS1dF3gH6jK8lZ0xV2nM4cB5vN7m}
    api-v3-key: ${WECHAT_PAY_API_V3_KEY:eR4tY6uI8oP0aS2dF4gH6jK8lZ0xV7cB}
    cert-serial-no: ${WECHAT_PAY_CERT_SERIAL:486641337A66EFD8CBE9C75BC308C86C621A87FC}
    cert-path: classpath:cert/wx_pay_cert.pem
    private-key-path: classpath:cert/wx_pay_key.pem
    notify-url: ${WECHAT_PAY_NOTIFY_URL:https://www.tinyorder.top/api/pay/callback}
    refund-notify-url: ${WECHAT_PAY_REFUND_NOTIFY_URL:https://www.tinyorder.top/api/pay/refund-callback}

# Toolkit专用配置
toolkit:
  # JWT配置 (用于JwtUtils类)
  jwt:
    secret: ${JWT_SECRET:toolkit-jwt-secret-2024-very-long-key-string-for-security-production-12345678901234567890}
    expire: ${JWT_EXPIRATION:604800}
  
  # 微信配置 (用于PaymentController类)
  wx:
    appid: ${WECHAT_MP_APPID:wx4c9fe19f2a8d5b9e}
    secret: ${WECHAT_MP_SECRET:674407c944977e246e690b91968b0024}
    pay:
      mchid: ${WECHAT_PAY_MCHID:1718176535}
      api-key: ${WECHAT_PAY_API_KEY:8zX5p2rQ7wE9aS1dF3gH6jK8lZ0xV2nM4cB5vN7m}
      notify-url: ${WECHAT_PAY_NOTIFY_URL:https://www.tinyorder.top/api/pay/callback}

# 第三方服务配置
service:
  # 微信支付配置 (用于PaymentService类)
  wechat:
    pay:
      app-id: ${WECHAT_MP_APPID:wx4c9fe19f2a8d5b9e}
      mch-id: ${WECHAT_PAY_MCHID:1718176535}
      api-key: ${WECHAT_PAY_API_KEY:8zX5p2rQ7wE9aS1dF3gH6jK8lZ0xV2nM4cB5vN7m}
      notify-url: ${WECHAT_PAY_NOTIFY_URL:https://www.tinyorder.top/api/pay/callback}
      
  # 阿里云短信配置 (用于SmsService类)
  aliyun:
    sms:
      access-key-id: ${ALIYUN_SMS_ACCESS_KEY_ID:LTAI5t86V9Kkn3vnJY3PUW9Q}
      access-key-secret: ${ALIYUN_SMS_ACCESS_KEY_SECRET:******************************}
      sign-name: ${ALIYUN_SMS_SIGN_NAME:点云科技工作室}
      template-code: ${ALIYUN_SMS_TEMPLATE_CODE:SMS_470055048}

# AI图片生成配置
ai:
  image:
    # 默认服务提供商
    default-provider: laozhang
    
    # OpenAI DALL-E配置
    openai:
      api-key: ${AI_OPENAI_API_KEY:your_openai_api_key}
      base-url: ${AI_OPENAI_BASE_URL:https://api.openai.com}
      model: dall-e-3
      quality: hd
      style: natural
      max-retries: 3
      timeout-seconds: 60
    
    # Stability AI配置
    stability:
      api-key: ${AI_STABILITY_API_KEY:your_stability_api_key}
      base-url: ${AI_STABILITY_BASE_URL:https://api.stability.ai}
      model: stable-diffusion-xl-1024-v1-0
      cfg-scale: 7
      steps: 30
      samples: 1
      max-retries: 3
      timeout-seconds: 60
    
    # laozhang.ai配置（推荐，成本低75%）
    laozhang:
      api-key: ${AI_LAOZHANG_API_KEY:sk-EKyMF2OwheDXsAQp44A191A9Ce6c41DbAa0093820bE4934d}
      base-url: ${AI_LAOZHANG_BASE_URL:https://api.laozhang.ai}
      model: gpt-4o-all
      stream: false
      max-retries: 3
      timeout-seconds: 60
    
    # 通用配置
    common:
      default-size: 1024x1024
      default-format: png
      default-style: realistic
      max-prompt-length: 1000
      enable-cache: true
      cache-expiration-hours: 24
      image-storage-path: ${upload.image-dir}
      image-url-prefix: ${qiniu.domain}images/
  
  # 第三方API配置
  api:
    # 天气API (高德)
    weather:
      url: ${WEATHER_API_URL:https://restapi.amap.com/v3/weather/weatherInfo}
      key: ${WEATHER_API_KEY:your_amap_key}
    # 视频解析API
    video-parse:
      url: ${VIDEO_PARSE_URL:https://api.video-parse.com/parse}
      key: ${VIDEO_PARSE_KEY:your_video_parse_key}
      timeout: 30000
    # OCR识别API (阿里云)
    ocr:
      url: ${OCR_API_URL:https://ecs.aliyuncs.com}
      access-key-id: ${OCR_ACCESS_KEY_ID:your_access_key_id}
      access-key-secret: ${OCR_ACCESS_KEY_SECRET:your_access_key_secret}
      timeout: 30000
    # 语音合成API (腾讯云)
    tts:
      url: ${TTS_API_URL:https://tts.tencentcloudapi.com}
      secret-id: ${TTS_SECRET_ID:your_secret_id}
      secret-key: ${TTS_SECRET_KEY:your_secret_key}
      timeout: 30000

# 短信配置
sms:
  # 阿里云短信
  aliyun:
    access-key-id: ${SMS_ACCESS_KEY_ID:LTAI5t86V9Kkn3vnJY3PUW9Q}
    access-key-secret: ${SMS_ACCESS_KEY_SECRET:******************************}
    sign-name: ${SMS_SIGN_NAME:万能工具箱}
    template-code: ${SMS_TEMPLATE_CODE:SMS_470055048}
  # 验证码配置
  code:
    expire-minutes: 5       # 验证码过期时间（分钟）
    retry-minutes: 1        # 重试等待时间（分钟）
    max-send-count: 5       # 每日最大发送次数

# 安全配置
security:
  # 访问频率限制
  rate-limit:
    enabled: true
    requests-per-minute: 60    # 每分钟请求次数限制
    requests-per-hour: 1000    # 每小时请求次数限制
  # IP白名单（管理后台访问）
  whitelist:
    admin-ips: ${ADMIN_IPS:127.0.0.1,localhost}
  # 防护配置
  protection:
    sql-injection: true        # SQL注入防护
    xss-filter: true          # XSS过滤
    csrf-protection: false    # CSRF防护（API模式关闭） 

# Knife4j配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.toolkit.controller
  default-flat-param-object: true

knife4j:
  enable: true
  setting:
    language: zh_cn
    swagger-model-name: 实体类列表
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: MIT License | Copyright © 2024 Toolkit Team
  documents:
    - name: 接口文档
      locations: classpath:markdown/*
  basic:
    enable: false
    username: admin
    password: 123456 

# 视频解析配置 - 优化为成本最低方案
video:
  parser:
    # 改为免费的douyin.wtf作为主要提供商
    default-provider: douyin-wtf
    api:
      # 主要API改为免费的douyin.wtf
      url: ${VIDEO_PARSER_API_URL:https://api.douyin.wtf}
      timeout: 30000
      retry-count: 3
      # 暂时不需要API key
      key: ${VIDEO_PARSER_API_KEY:}
      backup:
        # 备用API可以使用其他免费服务
        url: ${VIDEO_PARSER_BACKUP_URL:https://api.snapany.com}
        timeout: 20000
        enabled: true
    # 添加多个免费API端点
    free-apis:
      douyin-wtf:
        url: https://api.douyin.wtf
        platforms: ["douyin", "tiktok", "kuaishou"]
        cost: 0
        enabled: true
      # 可以添加更多免费API
      backup-1:
        url: https://video-api.example.com
        platforms: ["douyin", "xiaohongshu"]
        cost: 0
        enabled: false
    platforms:
      douyin:
        name: 抖音
        enabled: true
        base-url: https://www.douyin.com
        icon: 📱
        description: 支持抖音短视频解析去水印（免费）
        priority-api: douyin-wtf
      tiktok:
        name: TikTok
        enabled: true
        base-url: https://www.tiktok.com
        icon: 🎵
        description: 支持TikTok短视频解析去水印（免费）
        priority-api: douyin-wtf
      kuaishou:
        name: 快手
        enabled: true
        base-url: https://www.kuaishou.com
        icon: ⚡
        description: 支持快手短视频解析去水印（免费）
        priority-api: douyin-wtf
      xiaohongshu:
        name: 小红书
        enabled: true
        base-url: https://www.xiaohongshu.com
        icon: 📚
        description: 支持小红书视频解析去水印
        priority-api: backup-1
      bilibili:
        name: 哔哩哔哩
        enabled: true
        base-url: https://www.bilibili.com
        icon: 📺
        description: 支持B站视频解析去水印
        priority-api: douyin-wtf
      weibo:
        name: 微博
        enabled: false
        base-url: https://weibo.com
        icon: 📰
        description: 支持微博视频解析去水印
    process:
      default-quality: high
      default-format: mp4
      keep-original-resolution: true
      max-file-size-mb: 500
      max-duration-seconds: 300
      max-concurrent-tasks: 10
      cache:
        enabled: true
        ttl-minutes: 60
        max-entries: 1000 