-- UniApp工具箱后端数据库初始化脚本
-- 数据库版本: MySQL 8.0+
-- 字符集: utf8mb4

-- 创建数据库
CREATE DATABASE IF NOT EXISTS toolkit_db_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE toolkit_db_dev;

-- 用户表
DROP TABLE IF EXISTS t_user;
CREATE TABLE t_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    openid VARCHAR(64) NOT NULL UNIQUE COMMENT '微信OpenID',
    union_id VARCHAR(64) COMMENT '微信UnionID',
    nickname VARCHAR(100) COMMENT '用户昵称',
    avatar_url VARCHAR(255) COMMENT '用户头像',
    gender TINYINT DEFAULT 0 COMMENT '用户性别 0-未知 1-男 2-女',
    country VARCHAR(50) COMMENT '用户国家',
    province VARCHAR(50) COMMENT '用户省份',
    city VARCHAR(50) COMMENT '用户城市',
    language VARCHAR(20) COMMENT '用户语言',
    is_vip TINYINT DEFAULT 0 COMMENT '是否VIP 0-否 1-是',
    vip_expire_time DATETIME COMMENT 'VIP到期时间',
    status TINYINT DEFAULT 0 COMMENT '用户状态 0-正常 1-禁用',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    
    INDEX idx_openid (openid),
    INDEX idx_vip_expire (vip_expire_time),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) COMMENT '用户表';

-- VIP订单表
DROP TABLE IF EXISTS t_vip_order;
CREATE TABLE t_vip_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    openid VARCHAR(64) COMMENT '微信OpenID',
    vip_type TINYINT NOT NULL COMMENT 'VIP类型 1-月卡 2-季卡 3-年卡',
    vip_duration INT COMMENT 'VIP时长（天）',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    paid_amount DECIMAL(10,2) NOT NULL COMMENT '实付金额',
    pay_type TINYINT DEFAULT 1 COMMENT '支付方式 1-微信支付',
    status TINYINT DEFAULT 0 COMMENT '订单状态 0-待支付 1-已支付 2-已取消 3-已过期 4-已退款',
    transaction_id VARCHAR(64) COMMENT '微信交易号',
    prepay_id VARCHAR(64) COMMENT '微信预支付ID',
    pay_time DATETIME COMMENT '支付时间',
    cancel_time DATETIME COMMENT '取消时间',
    refund_time DATETIME COMMENT '退款时间',
    refund_amount DECIMAL(10,2) COMMENT '退款金额',
    refund_reason VARCHAR(255) COMMENT '退款原因',
    expire_time DATETIME COMMENT '订单过期时间',
    remark VARCHAR(500) COMMENT '订单备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    
    INDEX idx_order_no (order_no),
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_status (status),
    INDEX idx_expire_time (expire_time),
    INDEX idx_create_time (create_time),
    INDEX idx_pay_time (pay_time)
) COMMENT 'VIP订单表';

-- 工具表
DROP TABLE IF EXISTS t_tool;
CREATE TABLE t_tool (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '工具ID',
    name VARCHAR(100) NOT NULL COMMENT '工具名称',
    identifier VARCHAR(100) COMMENT '工具标识',
    description TEXT COMMENT '工具描述',
    icon VARCHAR(255) COMMENT '工具图标',
    category VARCHAR(50) NOT NULL COMMENT '工具分类',
    require_vip TINYINT DEFAULT 0 COMMENT '是否需要VIP 0-否 1-是',
    status TINYINT DEFAULT 0 COMMENT '工具状态 0-正常 1-禁用 2-维护中',
    sort_order INT DEFAULT 0 COMMENT '排序值',
    use_count BIGINT DEFAULT 0 COMMENT '使用次数',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门 0-否 1-是',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐 0-否 1-是',
    api_url VARCHAR(500) COMMENT '外部API地址',
    api_key VARCHAR(255) COMMENT 'API密钥',
    http_method VARCHAR(10) COMMENT '请求方式 GET POST',
    request_template TEXT COMMENT '请求参数模板',
    response_rule TEXT COMMENT '响应数据处理规则',
    config JSON COMMENT '工具配置信息JSON格式',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    
    INDEX idx_identifier (identifier),
    INDEX idx_category (category),
    INDEX idx_require_vip (require_vip),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_use_count (use_count)
) COMMENT '工具表';

-- 壁纸表
DROP TABLE IF EXISTS t_wallpaper;
CREATE TABLE t_wallpaper (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '壁纸ID',
    title VARCHAR(200) NOT NULL COMMENT '壁纸标题',
    description TEXT COMMENT '壁纸描述',
    thumbnail_url VARCHAR(255) NOT NULL COMMENT '缩略图URL',
    original_url VARCHAR(255) NOT NULL COMMENT '原图URL',
    hd_url VARCHAR(255) COMMENT '高清图URL',
    category VARCHAR(50) NOT NULL COMMENT '壁纸分类',
    tags VARCHAR(500) COMMENT '标签，逗号分隔',
    width INT NOT NULL COMMENT '图片宽度',
    height INT NOT NULL COMMENT '图片高度',
    file_size BIGINT COMMENT '文件大小(字节)',
    format VARCHAR(20) COMMENT '图片格式',
    download_count BIGINT DEFAULT 0 COMMENT '下载次数',
    favorite_count BIGINT DEFAULT 0 COMMENT '收藏次数',
    is_vip_only TINYINT DEFAULT 0 COMMENT '是否VIP专享 0-否 1-是',
    is_hot TINYINT DEFAULT 0 COMMENT '是否热门 0-否 1-是',
    is_recommend TINYINT DEFAULT 0 COMMENT '是否推荐 0-否 1-是',
    status TINYINT DEFAULT 0 COMMENT '状态 0-正常 1-禁用',
    sort_order INT DEFAULT 0 COMMENT '排序值',
    source VARCHAR(100) COMMENT '来源网站',
    source_url VARCHAR(500) COMMENT '原始链接',
    author VARCHAR(100) COMMENT '作者',
    copyright VARCHAR(200) COMMENT '版权信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    is_deleted TINYINT DEFAULT 0 COMMENT '是否删除 0-否 1-是',
    
    INDEX idx_category (category),
    INDEX idx_is_recommend (is_recommend),
    INDEX idx_download_count (download_count),
    INDEX idx_favorite_count (favorite_count),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time),
    FULLTEXT idx_search (title, description, tags)
) COMMENT '壁纸表';

-- 使用日志表
DROP TABLE IF EXISTS t_usage_log;
CREATE TABLE t_usage_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    user_id BIGINT COMMENT '用户ID',
    openid VARCHAR(64) COMMENT '微信OpenID',
    tool_id BIGINT COMMENT '工具ID',
    tool_identifier VARCHAR(100) COMMENT '工具标识',
    tool_name VARCHAR(100) COMMENT '工具名称',
    tool_category VARCHAR(50) COMMENT '工具分类',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    request_params TEXT COMMENT '请求参数JSON格式',
    result_status TINYINT COMMENT '响应结果状态 0-失败 1-成功',
    result_message VARCHAR(500) COMMENT '响应消息',
    process_time BIGINT COMMENT '处理耗时毫秒',
    error_code VARCHAR(50) COMMENT '错误代码',
    error_detail TEXT COMMENT '错误详情',
    is_vip_user TINYINT DEFAULT 0 COMMENT '是否VIP用户 0-否 1-是',
    device_type VARCHAR(20) COMMENT '设备类型',
    os VARCHAR(50) COMMENT '操作系统',
    browser VARCHAR(50) COMMENT '浏览器',
    location VARCHAR(100) COMMENT '地理位置',
    usage_date VARCHAR(10) COMMENT '使用日期YYYY-MM-DD',
    usage_hour TINYINT COMMENT '使用小时0-23',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_openid (openid),
    INDEX idx_tool_id (tool_id),
    INDEX idx_tool_identifier (tool_identifier),
    INDEX idx_result_status (result_status),
    INDEX idx_usage_date (usage_date),
    INDEX idx_create_time (create_time)
) COMMENT '使用日志表';

-- 用户收藏表
DROP TABLE IF EXISTS t_user_favorite;
CREATE TABLE t_user_favorite (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '收藏ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type VARCHAR(20) NOT NULL COMMENT '收藏类型 tool-工具 wallpaper-壁纸',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_target (user_id, target_type, target_id),
    INDEX idx_user_id (user_id),
    INDEX idx_target (target_type, target_id)
) COMMENT '用户收藏表';

-- 用户点赞表
DROP TABLE IF EXISTS t_user_like;
CREATE TABLE t_user_like (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '点赞ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    target_type VARCHAR(20) NOT NULL COMMENT '点赞类型 wallpaper-壁纸',
    target_id BIGINT NOT NULL COMMENT '目标ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_target (user_id, target_type, target_id),
    INDEX idx_user_id (user_id),
    INDEX idx_target (target_type, target_id)
) COMMENT '用户点赞表';

-- 系统配置表
DROP TABLE IF EXISTS t_system_config;
CREATE TABLE t_system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_config_key (config_key)
) COMMENT '系统配置表';

-- 插入工具数据 - 根据前端toolsData.js完全对应的117个工具
INSERT INTO t_tool (id, name, identifier, description, category, icon, require_vip, status, sort_order) VALUES
-- 好玩推荐 (3个)
(1, '趣味图片生成器', 'fun-image-generator', '生成有趣的图片', 'fun', '🎨', 0, 0, 1),
(2, '获取永久会员', 'get-vip-membership', '会员权益获取', 'fun', '👑', 0, 0, 2),
(3, '表白代码制作', 'love-code-generator', '制作浪漫表白代码', 'fun', '💝', 0, 1, 3),

-- 媒体工具 (25个)
(4, '视频解析去水印', 'video-watermark-remover', '去除视频水印', 'media', '🎬', 0, 0, 4),
(5, '图集解析去水印', 'image-watermark-remover', '批量去图片水印', 'media', '🖼️', 0, 0, 5),
(6, '视频H下载', 'video-downloader', '高清视频下载', 'media', '📥', 0, 0, 6),
(7, '音频剪辑', 'audio-editor', '在线音频编辑', 'media', '🎵', 0, 1, 7),
(8, '视频转音频', 'video-to-audio', '提取视频音轨', 'media', '🔄', 0, 1, 8),
(9, '音乐下载器', 'music-downloader', '音乐下载工具', 'media', '🎼', 0, 0, 9),
(10, '文案提取器', 'text-extractor', '提取图片文案', 'media', '📝', 0, 0, 10),
(11, '九宫格切图', 'nine-grid-cutter', '图片九宫格分割', 'media', '⚏', 0, 1, 11),
(12, '图片压缩', 'image-compressor', '压缩图片大小', 'media', '📷', 0, 0, 12),
(13, '修改MD5', 'md5-modifier', '修改文件MD5值', 'media', '🔧', 0, 1, 13),
(14, '截图加壳', 'screenshot-frame', '手机截图美化', 'media', '📱', 0, 1, 14),
(15, '游戏语音合成', 'game-voice-synthesizer', '游戏角色语音', 'media', '🎮', 0, 0, 15),
(16, '音效大全', 'sound-effects-library', '各种音效资源', 'media', '🔊', 0, 0, 16),
(17, '图片拼接', 'image-stitcher', '多图拼接合成', 'media', '🖼️', 0, 1, 17),
(18, '黑白图转换', 'grayscale-converter', '彩色转黑白', 'media', '⚫', 0, 0, 18),
(19, '图片加水印', 'image-watermark-adder', '添加图片水印', 'media', '💧', 0, 1, 19),
(20, '图片打码', 'image-mosaic', '隐私信息打码', 'media', '🔒', 0, 1, 20),
(21, '魔法抹除水印', 'magic-watermark-remover', 'AI智能去水印', 'media', '✨', 0, 0, 21),
(22, '证件照制作', 'id-photo-maker', '制作标准证件照', 'media', '📸', 0, 0, 22),
(23, '视频压缩', 'video-compressor', '压缩视频文件', 'media', '🗜️', 0, 1, 23),
(24, '图片镜像翻转', 'image-flip', '图片镜像处理', 'media', '🪞', 0, 0, 24),
(25, '图片像素化', 'image-pixelate', '像素风格转换', 'media', '🔲', 0, 1, 25),
(26, '隐藏图制作', 'hidden-image-maker', '制作隐藏图片', 'media', '🫥', 0, 1, 26),
(27, '网站快照', 'website-snapshot', '网站截图工具', 'media', '📸', 0, 0, 27),

-- 颜色工具 (7个)
(28, '色卡配色', 'color-palette', '专业配色方案', 'color', '🎨', 0, 0, 28),
(29, 'RGB与HEX', 'rgb-hex-converter', '颜色格式转换', 'color', '🌈', 0, 1, 29),
(30, '图片取色', 'image-color-picker', '从图片提取颜色', 'color', '🎯', 0, 1, 30),
(31, '渐变色卡', 'gradient-colors', '渐变色彩搭配', 'color', '🌅', 0, 0, 31),
(32, '渐变代码生成', 'gradient-generator', 'CSS渐变代码', 'color', '📋', 0, 1, 32),
(33, '对比度检测', 'contrast-checker', '检查颜色对比度', 'color', '👁️', 0, 1, 33),
(34, '色调生成器', 'tone-generator', '色调变化生成', 'color', '🎪', 0, 0, 34),

-- 实用工具 (9个)
(35, '车辆价格查询', 'vehicle-price-query', '汽车价格查询', 'utility', '🚗', 0, 0, 35),
(36, '全国油价', 'gas-price-query', '实时油价信息', 'utility', '⛽', 0, 0, 36),
(37, '常用号码', 'common-numbers', '常用电话号码', 'utility', '📞', 0, 1, 37),
(38, '手机清灰', 'phone-cleaner', '声波清理灰尘', 'utility', '🔊', 0, 0, 38),
(39, '手持弹幕', 'handheld-danmaku', '手持弹幕显示', 'utility', '💬', 0, 1, 39),
(40, 'BMI计算器', 'bmi-calculator', '身体质量指数', 'utility', '⚖️', 0, 1, 40),
(41, '全国天气查询', 'weather-query', '实时天气信息', 'utility', '🌤️', 0, 0, 41),
(42, '生成随机数', 'random-number', '随机数生成器', 'utility', '🎲', 0, 1, 42),
(43, '安全期计算', 'safe-period', '生理周期计算', 'utility', '📅', 0, 1, 43),

-- 趣味工具 (15个)
(44, '兽语加密解密', 'beast-language', '兽语转换工具', 'game', '🐾', 0, 0, 44),
(45, '偏心大转盘', 'biased-wheel', '随机选择转盘', 'game', '🎡', 0, 1, 45),
(46, '起始时间计算', 'time-calculator', '计算时间差', 'game', '⏰', 0, 1, 46),
(47, '历史上的今天', 'history-today', '今日历史事件', 'game', '📅', 0, 0, 47),
(48, '吃掉GIF头像', 'eat-gif-avatar', '趣味头像生成', 'game', '😋', 0, 1, 48),
(49, 'IP签名档', 'ip-signature', 'IP地址签名', 'game', '📝', 0, 1, 49),
(50, '全屏时钟', 'fullscreen-clock', '大屏数字时钟', 'game', '⏰', 0, 0, 50),
(51, '模拟来电', 'fake-call', '假装来电话', 'game', '📞', 0, 1, 51),
(52, '手机检测', 'phone-detector', '手机性能检测', 'game', '📱', 0, 1, 52),
(53, '一生时间', 'lifetime-calculator', '人生时间计算', 'game', '🕐', 0, 0, 53),
(54, '显卡天梯图', 'gpu-ladder', '显卡性能排行', 'game', '🎮', 0, 0, 54),
(55, 'CPU天梯图', 'cpu-ladder', 'CPU性能排行', 'game', '💻', 0, 0, 55),
(56, '功德木鱼', 'merit-wooden-fish', '电子木鱼敲击', 'game', '🪕', 0, 0, 56),
(57, '便携风扇', 'portable-fan', '手机变身小风扇', 'game', '🌀', 0, 1, 57),
(58, '摇骰子', 'dice-roller', '虚拟骰子游戏', 'game', '🎲', 0, 1, 58),

-- 程序员工具 (20个)
(59, 'PX与EM', 'px-em-converter', '单位转换工具', 'dev', '📐', 0, 0, 59),
(60, '时间戳工具', 'timestamp-converter', '时间戳转换', 'dev', '⏱️', 0, 1, 60),
(61, 'MD5加密', 'md5-encoder', 'MD5哈希加密', 'dev', '🔐', 0, 1, 61),
(62, 'ASCII转换', 'ascii-converter', 'ASCII码转换', 'dev', '🔤', 0, 0, 62),
(63, '动画代码生成', 'animation-generator', 'CSS动画生成', 'dev', '🎬', 0, 1, 63),
(64, 'Grid布局生成', 'grid-generator', 'CSS Grid生成', 'dev', '📏', 0, 1, 64),
(65, 'Flex布局生成', 'flex-generator', 'CSS Flex生成', 'dev', '📋', 0, 0, 65),
(66, '磨砂玻璃生成', 'glass-generator', '毛玻璃效果CSS', 'dev', '🪟', 0, 1, 66),
(67, 'CSS文字效果', 'text-effects', 'CSS特效生成', 'dev', '✨', 0, 1, 67),
(68, '生成网站快照', 'website-snapshot', '网站截图工具', 'dev', '📸', 0, 0, 68),
(69, '端口检测', 'port-scanner', '网络端口检测', 'dev', '🔌', 0, 1, 69),
(70, 'Unicode', 'unicode-converter', 'Unicode编码', 'dev', '🌐', 0, 1, 70),
(71, 'HTTP状态码', 'http-status-codes', 'HTTP状态码查询', 'dev', '🌐', 0, 0, 71),
(72, 'HTTP请求头', 'http-headers', 'HTTP请求头查看', 'dev', '📡', 0, 1, 72),
(73, 'keyCode按键码', 'keycode-detector', '键盘按键码', 'dev', '⌨️', 0, 1, 73),
(74, 'Linux常用命令', 'linux-commands', 'Linux命令查询', 'dev', '🐧', 0, 0, 74),
(75, '密码生成器', 'password-generator', '生成安全密码', 'dev', '🔑', 0, 1, 75),
(76, '常用端口大全', 'common-ports', '常用端口查询', 'dev', '🔌', 0, 1, 76),
(77, 'UA标识大全', 'user-agents', '用户代理标识', 'dev', '🏷️', 0, 0, 77),
(78, 'UUID生成器', 'uuid-generator', '生成唯一标识符', 'dev', '🆔', 0, 1, 78),

-- 文字工具 (30个)
(79, '伤感文案库', 'sad-copywriting', '伤感文案集合', 'text', '😢', 0, 0, 79),
(80, '疯狂星期四文案', 'crazy-thursday', 'KFC文案生成器', 'text', '🍗', 0, 1, 80),
(81, '答案之书', 'answer-book', '随机答案生成', 'text', '📚', 0, 1, 81),
(82, '名言警句', 'famous-quotes', '经典名言集合', 'text', '💭', 0, 0, 82),
(83, '文案大全', 'copywriting-collection', '各类文案素材', 'text', '📝', 0, 1, 83),
(84, 'WiFi昵称', 'wifi-names', '创意WiFi名称', 'text', '📶', 0, 1, 84),
(85, '特殊小尾巴', 'special-tails', '文字装饰符号', 'text', '🎀', 0, 0, 85),
(86, '小辫子昵称', 'braided-nicknames', '特殊字符昵称', 'text', '🎭', 0, 1, 86),
(87, '文本逆序', 'text-reverse', '文字顺序颠倒', 'text', '🔄', 0, 1, 87),
(88, '上标电话', 'superscript-phone', '上标电话号码', 'text', '☎️', 0, 0, 88),
(89, '下标电话', 'subscript-phone', '下标电话号码', 'text', '📞', 0, 1, 89),
(90, '尖叫文字', 'screaming-text', '夸张文字效果', 'text', '😱', 0, 1, 90),
(91, '小字母昵称', 'small-letters', '小字母装饰', 'text', 'ᵃᵇᶜ', 0, 0, 91),
(92, '大小写转换', 'case-converter', '英文大小写转换', 'text', 'Aa', 0, 1, 92),
(93, '爱心文字', 'heart-text', '爱心形状文字', 'text', '💖', 0, 1, 93),
(94, '文字九宫格', 'text-grid', '九宫格文字排列', 'text', '#️⃣', 0, 0, 94),
(95, '带框文字', 'bordered-text', '文字加边框', 'text', '🔲', 0, 1, 95),
(96, '颜文字合集', 'emoticons', '各种表情符号', 'text', '(ﾟ∀ﾟ)', 0, 1, 96),
(97, '删除线文字', 'strikethrough-text', '删除线效果', 'text', '̶T̶e̶x̶t̶', 0, 0, 97),
(98, '爱心颜文字', 'heart-emoticons', '爱心表情符号', 'text', '♥', 0, 1, 98),
(99, '520字', 'love-520', '520数字艺术', 'text', '5️⃣2️⃣0️⃣', 0, 1, 99),
(100, '箭头文字', 'arrow-text', '各种箭头符号', 'text', '➡️', 0, 0, 100),
(101, '人间凑数的日子', 'daily-quotes', '佛系文案生成', 'text', '📆', 0, 1, 101),
(102, '坦克文字', 'tank-ascii', '坦克ASCII艺术', 'text', '🚗', 0, 1, 102),
(103, '直升机文字', 'helicopter-ascii', '飞机ASCII艺术', 'text', '🚁', 0, 0, 103),
(104, '音乐文字', 'music-text', '音符符号文字', 'text', '🎵', 0, 1, 104),
(105, '发疯语录', 'crazy-quotes', '搞怪文案集合', 'text', '🤪', 0, 1, 105),
(106, '爱情公寓语录', 'love-apartment', '经典台词集合', 'text', '🏠', 0, 0, 106),
(107, '随机情话', 'random-love-words', '浪漫情话生成', 'text', '💕', 0, 1, 107),

-- 图片壁纸 (9个)
(108, '随机头像', 'random-avatar', '随机生成头像', 'wallpaper', '👤', 0, 0, 108),
(109, '随机加载图', 'random-loading-image', '随机占位图片', 'wallpaper', '🔄', 0, 0, 109),
(110, '随机手机壁纸', 'bing-daily-wallpaper', '精美手机壁纸', 'wallpaper', '📱', 0, 0, 110),
(111, '随机PC壁纸', 'random-pc-wallpaper', '电脑桌面壁纸', 'wallpaper', '🖥️', 0, 0, 111),
(114, '文字壁纸大全', 'text-wallpapers', '文字背景壁纸', 'wallpaper', '📝', 0, 1, 114),
(115, '动漫壁纸', 'anime-wallpapers', '高清动漫壁纸', 'wallpaper', '🎭', 0, 0, 115);

-- 插入系统配置
INSERT INTO t_system_config (config_key, config_value, description) VALUES
('wx_app_id', 'wx4c9fe19f2a8d5b9e', '微信小程序AppID'),
('wx_app_secret', '674407c944977e246e690b91968b0024', '微信小程序AppSecret'),
('wx_pay_mch_id', '1718176535', '微信支付商户号'),
('wx_pay_api_key', '8zX5p2rQ7wE9aS1dF3gH6jK8lZ0xV2nM4cB5vN7m', '微信支付API密钥'),
('vip_price_monthly', '9.90', '月度VIP价格'),
('vip_price_quarterly', '24.90', '季度VIP价格'),
('vip_price_yearly', '88.00', '年度VIP价格'),
('tool_daily_limit', '10', '非VIP用户每日工具使用限制'),
('file_max_size', '10485760', '文件上传最大大小(字节)'),
('oss_endpoint', 'https://tinyorder.top/', 'OSS存储端点'),
('oss_access_key', 'lYcN6wLvY06QnrDXaKOo8vgZqPtV50eGd27WFr7o', 'OSS访问密钥'),
('oss_secret_key', 'SxE_XeqMBBsF4XxoWbkusD11W9dQ1Yza-Ny_3m2C', 'OSS密钥'),
('oss_bucket_name', 'toolkit-storage', 'OSS存储桶名称');

-- 创建视图
CREATE VIEW v_tool_stats AS
SELECT 
    category,
    COUNT(*) as total_count,
    SUM(CASE WHEN require_vip = 1 THEN 1 ELSE 0 END) as vip_count,
    SUM(CASE WHEN is_hot = 1 THEN 1 ELSE 0 END) as hot_count,
    SUM(use_count) as total_usage
FROM t_tool 
WHERE is_deleted = 0 AND status = 0
GROUP BY category;

CREATE VIEW v_user_vip_stats AS
SELECT 
    DATE(create_time) as date,
    COUNT(*) as new_users,
    SUM(CASE WHEN is_vip = 1 THEN 1 ELSE 0 END) as new_vip_users
FROM t_user 
WHERE is_deleted = 0
GROUP BY DATE(create_time);

-- 添加外键约束
ALTER TABLE t_vip_order ADD CONSTRAINT fk_vip_order_user FOREIGN KEY (user_id) REFERENCES t_user(id);
ALTER TABLE t_usage_log ADD CONSTRAINT fk_usage_log_user FOREIGN KEY (user_id) REFERENCES t_user(id);
ALTER TABLE t_usage_log ADD CONSTRAINT fk_usage_log_tool FOREIGN KEY (tool_id) REFERENCES t_tool(id);
ALTER TABLE t_user_favorite ADD CONSTRAINT fk_user_favorite_user FOREIGN KEY (user_id) REFERENCES t_user(id);
ALTER TABLE t_user_like ADD CONSTRAINT fk_user_like_user FOREIGN KEY (user_id) REFERENCES t_user(id); 