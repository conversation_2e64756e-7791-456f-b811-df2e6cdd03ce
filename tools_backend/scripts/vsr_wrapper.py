#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
video-subtitle-remover包装器
避免命令行参数冲突
"""

import sys
import os
import cv2
import json

def process_image_with_vsr(input_path, output_path, watermark_area=None):
    """
    使用video-subtitle-remover处理单张图片
    跳过PaddleOCR检测，直接使用LAMA修复指定区域
    """
    try:
        # 添加video-subtitle-remover路径
        vsr_backend = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/backend"
        vsr_root = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover"
        sys.path.insert(0, vsr_backend)
        sys.path.insert(0, vsr_root)

        # 切换到backend目录
        original_cwd = os.getcwd()
        os.chdir(vsr_backend)

        # 清空命令行参数，避免PaddleOCR参数解析冲突
        original_argv = sys.argv.copy()
        sys.argv = [sys.argv[0]]  # 只保留脚本名

        try:
            # 直接导入LAMA相关模块，跳过PaddleOCR
            from inpaint.lama_inpaint import LamaInpaint
            from tools.inpaint_tools import create_mask

            print(f"开始处理图片: {input_path}")

            # 读取图片
            image = cv2.imread(input_path)
            if image is None:
                return {"success": False, "error": "无法读取图片"}

            h, w = image.shape[:2]
            print(f"图片尺寸: {w}x{h}")

            # 如果没有指定水印区域，使用默认的右下角区域（小红书水印常见位置）
            if watermark_area is None:
                # 小红书水印通常在右下角，大约280x238像素
                watermark_area = {
                    "x": w - 300,  # 右边距离边缘300像素
                    "y": h - 250,  # 下边距离边缘250像素
                    "width": 280,
                    "height": 238
                }

            # 确保坐标在图片范围内
            x = max(0, min(watermark_area["x"], w-1))
            y = max(0, min(watermark_area["y"], h-1))
            width = min(watermark_area["width"], w - x)
            height = min(watermark_area["height"], h - y)

            # 转换为坐标格式 (xmin, xmax, ymin, ymax)
            coordinates = [(x, x + width, y, y + height)]

            print(f"处理水印区域: x={x}, y={y}, width={width}, height={height}")

            # 创建LAMA修复器
            print("初始化LAMA修复器...")
            lama_inpaint = LamaInpaint()

            # 创建mask
            print("创建mask...")
            mask = create_mask(image.shape[:2], coordinates)
            print(f"Mask形状: {mask.shape}, 非零像素数: {cv2.countNonZero(mask)}")

            # 执行LAMA修复
            print("使用LAMA算法修复图片...")
            result_image = lama_inpaint(image, mask)

            # 创建输出目录
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # 保存结果
            success = cv2.imwrite(output_path, result_image)

            if success:
                print(f"图片处理成功，保存到: {output_path}")
                return {
                    "success": True,
                    "processed_regions": 1,
                    "coordinates": coordinates,
                    "output_path": output_path,
                    "watermark_area": watermark_area
                }
            else:
                return {"success": False, "error": "保存图片失败"}

        finally:
            # 恢复原始命令行参数和工作目录
            sys.argv = original_argv
            os.chdir(original_cwd)
            
    except Exception as e:
        import traceback
        error_msg = f"处理异常: {e}"
        print(error_msg)
        traceback.print_exc()
        return {"success": False, "error": error_msg}

def main():
    if len(sys.argv) < 3:
        print("用法: python3 vsr_wrapper.py <input_path> <output_path> [watermark_area_json]")
        print("watermark_area_json格式: '{\"x\": 800, \"y\": 1200, \"width\": 280, \"height\": 238}'")
        sys.exit(1)

    input_path = sys.argv[1]
    output_path = sys.argv[2]

    # 解析水印区域参数（可选）
    watermark_area = None
    if len(sys.argv) > 3:
        try:
            watermark_area = json.loads(sys.argv[3])
            print(f"使用指定的水印区域: {watermark_area}")
        except json.JSONDecodeError as e:
            print(f"水印区域JSON格式错误: {e}")
            sys.exit(1)

    if not os.path.exists(input_path):
        print(f"输入文件不存在: {input_path}")
        sys.exit(1)

    result = process_image_with_vsr(input_path, output_path, watermark_area)

    # 输出JSON结果
    print("=" * 50)
    print("处理结果:")
    print(json.dumps(result, indent=2, ensure_ascii=False))

    if result["success"]:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
