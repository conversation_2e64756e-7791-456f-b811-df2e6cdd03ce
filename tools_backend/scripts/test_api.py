#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试后端API接口
"""

import requests
import os
import time

def test_api():
    print("=" * 60)
    print("测试后端API接口")
    print("=" * 60)
    
    base_url = "http://localhost:8080"
    
    # 测试图片路径
    test_image = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/test/city.jpeg"
    
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return False
    
    print(f"📷 使用测试图片: {test_image}")
    
    try:
        # 1. 测试单张图片去水印API
        print("\n🔍 测试单张图片去水印API")
        print("-" * 40)
        
        url = f"{base_url}/api/tools/media/image-watermark-removal/single"
        
        with open(test_image, 'rb') as f:
            files = {'file': f}
            data = {
                'algorithm': 'lama',
                'autoDetect': 'true',
                'outputFormat': 'png',
                'quality': 'high'
            }
            
            print(f"POST {url}")
            print(f"参数: {data}")
            
            response = requests.post(url, files=files, data=data, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    task_id = result['data']['taskId']
                    print(f"✅ 任务创建成功，任务ID: {task_id}")
                    
                    # 2. 轮询任务进度
                    print(f"\n⏳ 轮询任务进度...")
                    print("-" * 40)
                    
                    max_attempts = 30
                    for attempt in range(max_attempts):
                        progress_url = f"{base_url}/api/tools/media/image-watermark-removal/progress/{task_id}"
                        progress_response = requests.get(progress_url, timeout=10)
                        
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            if progress_result.get('success'):
                                progress_data = progress_result['data']
                                progress = progress_data.get('progress', 0)
                                status = progress_data.get('status', 'UNKNOWN')
                                
                                print(f"进度: {progress}%, 状态: {status}")
                                
                                if status == 'COMPLETED':
                                    print("✅ 任务完成！")
                                    break
                                elif status == 'FAILED':
                                    print("❌ 任务失败！")
                                    return False
                        
                        time.sleep(2)
                    
                    # 3. 获取处理结果
                    print(f"\n📋 获取处理结果...")
                    print("-" * 40)
                    
                    result_url = f"{base_url}/api/tools/media/image-watermark-removal/result/{task_id}"
                    result_response = requests.get(result_url, timeout=10)
                    
                    if result_response.status_code == 200:
                        final_result = result_response.json()
                        if final_result.get('success'):
                            result_data = final_result['data']
                            print(f"总数: {result_data.get('totalCount')}")
                            print(f"成功: {result_data.get('successCount')}")
                            print(f"失败: {result_data.get('failedCount')}")
                            print(f"处理时间: {result_data.get('processingTimeSeconds')}秒")
                            
                            results = result_data.get('results', [])
                            if results:
                                for i, img_result in enumerate(results):
                                    print(f"\n图片 {i+1}:")
                                    print(f"  原始文件名: {img_result.get('originalFileName')}")
                                    print(f"  状态: {img_result.get('status')}")
                                    print(f"  处理后URL: {img_result.get('processedUrl')}")
                                    print(f"  文件大小: {img_result.get('fileSize')} bytes")
                                    print(f"  尺寸: {img_result.get('dimensions')}")
                                    print(f"  质量评分: {img_result.get('qualityScore')}")
                            
                            print("✅ API测试完全成功！")
                            return True
                        else:
                            print(f"❌ 获取结果失败: {final_result.get('message')}")
                    else:
                        print(f"❌ 获取结果请求失败: {result_response.status_code}")
                else:
                    print(f"❌ 任务创建失败: {result.get('message')}")
            else:
                print(f"❌ API请求失败: {response.status_code}")
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    
    return False

if __name__ == '__main__':
    success = test_api()
    if success:
        print("\n🎉 所有API测试通过！")
    else:
        print("\n💥 API测试失败！")
