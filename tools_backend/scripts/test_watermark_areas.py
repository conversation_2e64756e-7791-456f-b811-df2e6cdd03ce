#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试水印区域功能
"""

import requests
import json
import os
import time

def test_watermark_areas():
    """测试水印区域选择功能"""
    
    print("=" * 60)
    print("测试水印区域选择功能")
    print("=" * 60)
    
    # 后端API地址
    base_url = "http://localhost:8080"
    api_url = f"{base_url}/api/tools/media/image-watermark-removal/batch"
    
    # 测试图片
    test_image_path = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/test/city.jpeg"
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图片不存在: {test_image_path}")
        return False
    
    print(f"📷 使用测试图片: {test_image_path}")
    
    try:
        # 模拟前端选择的水印区域（小红书水印位置）
        watermark_areas = [
            {
                "imageIndex": 0,
                "x": 800,
                "y": 1200, 
                "width": 280,
                "height": 238
            }
        ]
        
        # 构建FormData
        with open(test_image_path, 'rb') as image_file:
            files = {'files': ('city.jpeg', image_file, 'image/jpeg')}
            data = {
                'algorithm': 'lama',
                'outputFormat': 'png',
                'quality': 'high',
                'autoDetect': 'false',  # 使用手动选择的区域
                'preserveDetails': 'true',
                'watermarkAreas': json.dumps(watermark_areas)
            }
            
            print(f"📤 发送请求到: {api_url}")
            print(f"📋 水印区域: {json.dumps(watermark_areas, indent=2)}")
            
            # 发送请求
            response = requests.post(api_url, files=files, data=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 检查后端返回的格式
            if result.get('code') == 200:
                task_id = result.get('data', {}).get('taskId')
                if task_id:
                    print(f"✅ 任务创建成功！任务ID: {task_id}")
                    
                    # 轮询进度
                    print(f"\n🔍 轮询处理进度...")
                    progress_url = f"{base_url}/api/tools/media/image-watermark-removal/progress/{task_id}"
                    
                    max_attempts = 30
                    for attempt in range(max_attempts):
                        time.sleep(2)
                        
                        progress_response = requests.get(progress_url, timeout=10)
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            if progress_result.get('code') == 200:
                                progress_data = progress_result.get('data', {})
                                progress = progress_data.get('progress', 0)
                                status = progress_data.get('status', 'UNKNOWN')
                                
                                print(f"   进度: {progress}%, 状态: {status}")
                                
                                if status == 'COMPLETED':
                                    print("✅ 任务完成！")
                                    
                                    # 获取结果
                                    result_url = f"{base_url}/api/tools/media/image-watermark-removal/result/{task_id}"
                                    result_response = requests.get(result_url, timeout=10)
                                    
                                    if result_response.status_code == 200:
                                        final_result = result_response.json()
                                        print(f"🎉 最终结果:")
                                        print(json.dumps(final_result, indent=2, ensure_ascii=False))
                                        
                                        if final_result.get('code') == 200:
                                            results = final_result.get('data', {}).get('results', [])
                                            if results:
                                                print("🎊 水印区域选择功能测试成功！")
                                                print(f"📁 处理结果: {len(results)} 张图片")
                                                for i, res in enumerate(results):
                                                    print(f"   图片 {i+1}: {res.get('processedUrl', 'N/A')}")
                                                return True
                                    break
                                elif status == 'FAILED':
                                    print("❌ 任务失败！")
                                    break
                        
                    if attempt >= max_attempts - 1:
                        print("⏰ 等待超时")
                        
                else:
                    print("❌ 响应中没有taskId")
            else:
                print(f"❌ 请求失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败！请确保后端服务正在运行 (http://localhost:8080)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return False

def main():
    success = test_watermark_areas()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 水印区域选择功能测试 - 成功！")
        print("✅ 功能验证:")
        print("   - 前端数据格式正确")
        print("   - 后端API接收正常") 
        print("   - 水印区域解析成功")
        print("   - LAMA算法执行成功")
        print("   - 结果返回完整")
        print("\n🔥 前后端完全打通！")
    else:
        print("💥 水印区域选择功能测试 - 失败！")
        print("❌ 需要检查的问题:")
        print("   - 后端服务状态")
        print("   - JSON格式解析")
        print("   - 坐标传递正确性")
        print("   - LAMA算法环境")
    print("=" * 60)

if __name__ == "__main__":
    main()
