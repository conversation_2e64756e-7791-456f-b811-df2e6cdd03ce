#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import cv2
import numpy as np
import os
import subprocess
import sys

def create_test_image():
    """创建测试图片"""
    # 创建一个简单的测试图片
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255  # 白色背景
    
    # 添加一些内容
    cv2.rectangle(img, (50, 50), (550, 350), (200, 200, 200), -1)  # 灰色矩形
    cv2.putText(img, 'Test Image', (200, 200), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    
    # 添加模拟水印
    cv2.putText(img, 'WATERMARK', (400, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
    cv2.rectangle(img, (380, 70), (580, 120), (0, 0, 255), 2)
    
    return img

def main():
    print("创建测试图片...")
    
    # 创建测试目录
    os.makedirs('test_input', exist_ok=True)
    os.makedirs('test_output', exist_ok=True)
    
    # 创建测试图片
    test_img = create_test_image()
    input_path = 'test_input/test.jpg'
    cv2.imwrite(input_path, test_img)
    print(f"测试图片已创建: {input_path}")
    
    # 测试Python脚本
    print("测试Python脚本...")
    script_path = 'image_watermark_processor.py'
    
    command = [
        sys.executable,
        script_path,
        '-i', input_path,
        '-o', 'test_output',
        '--algorithm', 'lama',
        '--output-format', 'png',
        '--auto-detect'
    ]
    
    print(f"执行命令: {' '.join(command)}")
    
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=30)
        
        print(f"退出码: {result.returncode}")
        print(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            print(f"标准错误:\n{result.stderr}")
        
        # 检查输出文件
        output_files = os.listdir('test_output')
        print(f"输出文件: {output_files}")
        
        if result.returncode == 0:
            print("✓ 测试成功！")
        else:
            print("✗ 测试失败！")
            
    except subprocess.TimeoutExpired:
        print("✗ 测试超时！")
    except Exception as e:
        print(f"✗ 测试异常: {e}")

if __name__ == '__main__':
    main()
