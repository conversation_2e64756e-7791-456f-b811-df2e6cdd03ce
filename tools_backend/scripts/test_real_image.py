#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实图片测试去水印功能
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path

def main():
    print("=" * 60)
    print("使用真实图片测试去水印功能")
    print("=" * 60)
    
    # 源图片路径
    source_image = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/test/city.jpeg"
    
    # 检查源图片是否存在
    if not os.path.exists(source_image):
        print(f"❌ 源图片不存在: {source_image}")
        return False
    
    print(f"📷 使用测试图片: {source_image}")
    print("   (该图片右下角有小红书水印)")
    
    # 创建测试目录
    test_dir = Path("test_real")
    input_dir = test_dir / "input"
    output_dir = test_dir / "output"
    
    # 清理并创建目录
    if test_dir.exists():
        shutil.rmtree(test_dir)
    
    input_dir.mkdir(parents=True, exist_ok=True)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制测试图片到输入目录
    test_input = input_dir / "city.jpeg"
    shutil.copy2(source_image, test_input)
    print(f"✅ 已复制测试图片到: {test_input}")
    
    # 测试1: 自动检测模式
    print("\n🔍 测试1: 自动检测水印模式")
    print("-" * 40)
    
    output1_dir = output_dir / "auto_detect"
    output1_dir.mkdir(exist_ok=True)
    
    command1 = [
        sys.executable,
        "image_watermark_processor.py",
        "-i", str(test_input),
        "-o", str(output1_dir),
        "--algorithm", "lama",
        "--output-format", "png",
        "--quality", "high",
        "--auto-detect"
    ]
    
    print(f"执行命令: {' '.join(command1)}")
    
    try:
        result1 = subprocess.run(command1, capture_output=True, text=True, timeout=60)
        
        print(f"退出码: {result1.returncode}")
        if result1.stdout:
            print(f"输出:\n{result1.stdout}")
        if result1.stderr:
            print(f"错误:\n{result1.stderr}")
        
        # 检查输出文件
        output_files1 = list(output1_dir.glob("*"))
        print(f"生成的文件: {[f.name for f in output_files1]}")
        
        if result1.returncode == 0 and output_files1:
            print("✅ 自动检测模式测试成功")
        else:
            print("❌ 自动检测模式测试失败")
            
    except subprocess.TimeoutExpired:
        print("❌ 自动检测模式测试超时")
    except Exception as e:
        print(f"❌ 自动检测模式测试异常: {e}")
    
    # 测试2: 手动指定水印区域模式（右下角区域）
    print("\n🎯 测试2: 手动指定水印区域模式")
    print("-" * 40)
    print("   指定右下角区域去除小红书水印")
    
    output2_dir = output_dir / "manual_area"
    output2_dir.mkdir(exist_ok=True)
    
    # 假设图片尺寸大约是1000x800，水印在右下角
    # 指定右下角200x100的区域
    watermark_areas = '[{"x": 800, "y": 700, "width": 200, "height": 100}]'
    
    command2 = [
        sys.executable,
        "image_watermark_processor.py",
        "-i", str(test_input),
        "-o", str(output2_dir),
        "--algorithm", "lama",
        "--output-format", "png",
        "--quality", "high",
        "--skip-detection",
        "--watermark-areas", watermark_areas
    ]
    
    print(f"执行命令: {' '.join(command2)}")
    print(f"水印区域: {watermark_areas}")
    
    try:
        result2 = subprocess.run(command2, capture_output=True, text=True, timeout=60)
        
        print(f"退出码: {result2.returncode}")
        if result2.stdout:
            print(f"输出:\n{result2.stdout}")
        if result2.stderr:
            print(f"错误:\n{result2.stderr}")
        
        # 检查输出文件
        output_files2 = list(output2_dir.glob("*"))
        print(f"生成的文件: {[f.name for f in output_files2]}")
        
        if result2.returncode == 0 and output_files2:
            print("✅ 手动指定区域模式测试成功")
        else:
            print("❌ 手动指定区域模式测试失败")
            
    except subprocess.TimeoutExpired:
        print("❌ 手动指定区域模式测试超时")
    except Exception as e:
        print(f"❌ 手动指定区域模式测试异常: {e}")
    
    # 测试3: 不同输出格式
    print("\n🖼️  测试3: 不同输出格式")
    print("-" * 40)
    
    formats = ["png", "jpg", "webp"]
    
    for fmt in formats:
        print(f"   测试 {fmt.upper()} 格式...")
        
        output3_dir = output_dir / f"format_{fmt}"
        output3_dir.mkdir(exist_ok=True)
        
        command3 = [
            sys.executable,
            "image_watermark_processor.py",
            "-i", str(test_input),
            "-o", str(output3_dir),
            "--algorithm", "lama",
            "--output-format", fmt,
            "--quality", "high",
            "--auto-detect"
        ]
        
        try:
            result3 = subprocess.run(command3, capture_output=True, text=True, timeout=30)
            
            output_files3 = list(output3_dir.glob("*"))
            
            if result3.returncode == 0 and output_files3:
                print(f"   ✅ {fmt.upper()} 格式测试成功")
            else:
                print(f"   ❌ {fmt.upper()} 格式测试失败")
                
        except Exception as e:
            print(f"   ❌ {fmt.upper()} 格式测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    all_output_files = list(output_dir.rglob("*"))
    image_files = [f for f in all_output_files if f.is_file() and f.suffix.lower() in ['.png', '.jpg', '.jpeg', '.webp']]
    
    print(f"📁 测试目录: {test_dir}")
    print(f"📷 源图片: {source_image}")
    print(f"🎯 生成的图片文件数量: {len(image_files)}")
    
    if image_files:
        print("\n生成的文件:")
        for img_file in image_files:
            rel_path = img_file.relative_to(test_dir)
            file_size = img_file.stat().st_size
            print(f"   📄 {rel_path} ({file_size:,} bytes)")
        
        print(f"\n✅ 测试完成！请检查输出目录: {output_dir}")
        print("   可以对比原图和处理后的图片，查看水印去除效果")
    else:
        print("\n❌ 没有生成任何输出文件")
    
    return len(image_files) > 0

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
