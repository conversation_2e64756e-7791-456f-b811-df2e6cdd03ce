#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并video-subtitle-remover的分片模型文件
"""

import os
import sys
import csv

def merge_files(input_dir):
    """简单的文件合并功能，替代Filesplit"""
    manifest_path = os.path.join(input_dir, 'fs_manifest.csv')
    if not os.path.exists(manifest_path):
        print(f"未找到manifest文件: {manifest_path}")
        return False
    
    print(f"开始合并文件，目录: {input_dir}")
    
    with open(manifest_path, 'r') as f:
        reader = csv.reader(f)
        for row in reader:
            if len(row) >= 2:
                filename = row[0]
                output_path = os.path.join(input_dir, filename)
                
                if os.path.exists(output_path):
                    print(f"文件已存在，跳过: {filename}")
                    continue
                
                print(f"合并文件: {filename}")
                
                # 查找所有分片文件
                base_name = os.path.splitext(filename)[0]
                part_files = []
                i = 1
                while True:
                    part_file = os.path.join(input_dir, f"{base_name}_{i}.pt")
                    if os.path.exists(part_file):
                        part_files.append(part_file)
                        i += 1
                    else:
                        break
                
                if not part_files:
                    print(f"未找到分片文件: {base_name}_*.pt")
                    continue
                
                print(f"找到 {len(part_files)} 个分片文件")
                
                # 合并文件
                try:
                    with open(output_path, 'wb') as output_file:
                        for part_file in part_files:
                            print(f"  合并: {os.path.basename(part_file)}")
                            with open(part_file, 'rb') as input_file:
                                output_file.write(input_file.read())
                    
                    print(f"✅ 合并完成: {filename}")
                    
                    # 验证文件大小
                    file_size = os.path.getsize(output_path)
                    print(f"   文件大小: {file_size:,} bytes")
                    
                except Exception as e:
                    print(f"❌ 合并失败: {e}")
                    if os.path.exists(output_path):
                        os.remove(output_path)
                    return False
    
    return True

def main():
    # video-subtitle-remover backend路径
    vsr_backend = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/backend"
    
    # 需要合并的模型目录
    model_dirs = [
        os.path.join(vsr_backend, 'models', 'big-lama'),
        os.path.join(vsr_backend, 'models', 'V4', 'ch_det'),
        os.path.join(vsr_backend, 'models', 'video'),
    ]
    
    print("开始合并video-subtitle-remover模型文件...")
    
    for model_dir in model_dirs:
        if os.path.exists(model_dir):
            print(f"\n处理目录: {model_dir}")
            success = merge_files(model_dir)
            if success:
                print(f"✅ 目录处理完成: {model_dir}")
            else:
                print(f"❌ 目录处理失败: {model_dir}")
        else:
            print(f"⚠️  目录不存在: {model_dir}")
    
    print("\n模型文件合并完成！")

if __name__ == "__main__":
    main()
