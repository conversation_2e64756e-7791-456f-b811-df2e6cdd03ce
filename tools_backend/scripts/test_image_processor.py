#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片去水印处理器测试脚本
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from image_watermark_processor import ImageWatermarkProcessor


def create_test_image():
    """创建测试图片"""
    try:
        import cv2
        import numpy as np
        
        # 创建一个简单的测试图片
        img = np.ones((400, 600, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 添加一些内容
        cv2.rectangle(img, (50, 50), (550, 350), (200, 200, 200), -1)  # 灰色矩形
        cv2.putText(img, 'Test Image', (200, 200), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
        
        # 添加模拟水印
        cv2.putText(img, 'WATERMARK', (400, 100), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        cv2.rectangle(img, (380, 70), (580, 120), (0, 0, 255), 2)
        
        return img
        
    except ImportError:
        print("OpenCV not available, skipping test image creation")
        return None


def test_processor():
    """测试图片处理器"""
    print("开始测试图片去水印处理器...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_path = temp_path / "test_input.jpg"
        output_dir = temp_path / "output"
        output_dir.mkdir(exist_ok=True)
        output_path = output_dir / "test_output.jpg"
        
        # 创建测试图片
        test_img = create_test_image()
        if test_img is not None:
            import cv2
            cv2.imwrite(str(input_path), test_img)
            print(f"创建测试图片: {input_path}")
        else:
            print("无法创建测试图片，请手动提供测试图片")
            return False
        
        # 创建处理器
        processor = ImageWatermarkProcessor(algorithm='lama')
        
        # 测试自动检测模式
        print("\n测试自动检测模式...")
        result1 = processor.process_image(
            input_path=str(input_path),
            output_path=str(output_path),
            auto_detect=True
        )
        
        print("自动检测结果:")
        print(json.dumps(result1, ensure_ascii=False, indent=2))
        
        if result1['success']:
            print(f"✓ 自动检测模式测试成功")
            print(f"  - 处理时间: {result1['processing_time']:.2f}秒")
            print(f"  - 检测到水印数量: {len(result1['detected_watermarks'])}")
            print(f"  - 输出文件: {result1['output_path']}")
        else:
            print(f"✗ 自动检测模式测试失败: {result1['error']}")
        
        # 测试手动指定区域模式
        print("\n测试手动指定区域模式...")
        watermark_areas = json.dumps([
            {"x": 380, "y": 70, "width": 200, "height": 50}
        ])
        
        output_path2 = output_dir / "test_output_manual.jpg"
        result2 = processor.process_image(
            input_path=str(input_path),
            output_path=str(output_path2),
            auto_detect=False,
            watermark_areas=watermark_areas
        )
        
        print("手动指定区域结果:")
        print(json.dumps(result2, ensure_ascii=False, indent=2))
        
        if result2['success']:
            print(f"✓ 手动指定区域模式测试成功")
            print(f"  - 处理时间: {result2['processing_time']:.2f}秒")
            print(f"  - 输出文件: {result2['output_path']}")
        else:
            print(f"✗ 手动指定区域模式测试失败: {result2['error']}")
        
        # 检查输出文件
        if result1['success'] and os.path.exists(result1['output_path']):
            file_size = os.path.getsize(result1['output_path'])
            print(f"  - 输出文件大小: {file_size} 字节")
        
        return result1['success'] and result2['success']


def test_command_line():
    """测试命令行接口"""
    print("\n测试命令行接口...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        input_path = temp_path / "test_input.jpg"
        output_dir = temp_path / "output"
        output_dir.mkdir(exist_ok=True)
        
        # 创建测试图片
        test_img = create_test_image()
        if test_img is not None:
            import cv2
            cv2.imwrite(str(input_path), test_img)
        else:
            print("无法创建测试图片，跳过命令行测试")
            return True
        
        # 构建命令
        script_path = os.path.join(current_dir, 'image_watermark_processor.py')
        command = [
            sys.executable,
            script_path,
            '-i', str(input_path),
            '-o', str(output_dir),
            '--algorithm', 'lama',
            '--auto-detect'
        ]
        
        print(f"执行命令: {' '.join(command)}")
        
        # 执行命令
        import subprocess
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            print(f"退出码: {result.returncode}")
            print(f"标准输出:\n{result.stdout}")
            
            if result.stderr:
                print(f"标准错误:\n{result.stderr}")
            
            if result.returncode == 0:
                print("✓ 命令行接口测试成功")
                return True
            else:
                print("✗ 命令行接口测试失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ 命令行接口测试超时")
            return False
        except Exception as e:
            print(f"✗ 命令行接口测试异常: {e}")
            return False


def main():
    """主函数"""
    print("=" * 60)
    print("图片去水印处理器测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试处理器
    if test_processor():
        success_count += 1
    
    # 测试命令行接口
    if test_command_line():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_tests} 通过")
    print("=" * 60)
    
    if success_count == total_tests:
        print("✓ 所有测试通过！")
        sys.exit(0)
    else:
        print("✗ 部分测试失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
