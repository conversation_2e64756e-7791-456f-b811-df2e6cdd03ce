#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前后端完整打通
模拟前端发送的请求格式
"""

import requests
import json
import os

def test_frontend_backend_integration():
    """测试前后端完整集成"""
    
    print("=" * 60)
    print("测试前后端完整打通")
    print("=" * 60)
    
    # 后端API地址
    base_url = "http://localhost:8080"
    api_url = f"{base_url}/api/tools/media/image-watermark-removal/batch"
    
    # 测试图片
    test_image_path = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover/test/city.jpeg"
    
    if not os.path.exists(test_image_path):
        print(f"❌ 测试图片不存在: {test_image_path}")
        return False
    
    print(f"📷 使用测试图片: {test_image_path}")
    
    try:
        # 模拟前端发送的数据
        print("\n🔄 准备请求数据...")
        
        # 模拟前端选择的水印区域（小红书水印位置）
        watermark_areas = [
            {
                "imageIndex": 0,
                "x": 800,
                "y": 1200, 
                "width": 280,
                "height": 238
            }
        ]
        
        # 构建FormData
        image_file = open(test_image_path, 'rb')
        files = {'files': ('city.jpeg', image_file, 'image/jpeg')}
        data = {
            'algorithm': 'lama',
            'outputFormat': 'png',
            'quality': 'high',
            'autoDetect': 'false',  # 使用手动选择的区域
            'preserveDetails': 'true',
            'watermarkAreas': json.dumps(watermark_areas)
        }
        
        print(f"📤 发送请求到: {api_url}")
        print(f"📋 请求参数:")
        for key, value in data.items():
            if key == 'watermarkAreas':
                print(f"   {key}: {value}")
            else:
                print(f"   {key}: {value}")
        
        # 发送请求
        print(f"\n⏳ 发送POST请求...")
        response = requests.post(api_url, files=files, data=data, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

            # 检查后端返回的格式
            if result.get('code') == 200:  # 后端使用code字段
                task_id = result.get('data', {}).get('taskId')
                if task_id:
                    print(f"✅ 任务创建成功！任务ID: {task_id}")
                    
                    # 测试进度查询
                    print(f"\n🔍 测试进度查询...")
                    progress_url = f"{base_url}/api/tools/media/image-watermark-removal/progress/{task_id}"
                    
                    import time
                    max_attempts = 30
                    for attempt in range(max_attempts):
                        progress_response = requests.get(progress_url, timeout=10)
                        if progress_response.status_code == 200:
                            progress_result = progress_response.json()
                            if progress_result.get('success'):
                                progress_data = progress_result.get('data', {})
                                progress = progress_data.get('progress', 0)
                                status = progress_data.get('status', 'UNKNOWN')
                                
                                print(f"   进度: {progress}%, 状态: {status}")
                                
                                if status == 'COMPLETED':
                                    print("✅ 任务完成！")
                                    
                                    # 测试结果获取
                                    print(f"\n📋 获取处理结果...")
                                    result_url = f"{base_url}/api/tools/media/image-watermark-removal/result/{task_id}"
                                    result_response = requests.get(result_url, timeout=10)
                                    
                                    if result_response.status_code == 200:
                                        final_result = result_response.json()
                                        print(f"🎉 最终结果:")
                                        print(json.dumps(final_result, indent=2, ensure_ascii=False))
                                        
                                        if final_result.get('success'):
                                            print("🎊 前后端完全打通成功！")
                                            return True
                                    break
                                elif status == 'FAILED':
                                    print("❌ 任务失败！")
                                    break
                        
                        time.sleep(2)
                    
                    if attempt >= max_attempts - 1:
                        print("⏰ 等待超时")
                        
                else:
                    print("❌ 响应中没有taskId")
            else:
                print(f"❌ 请求失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败！请确保后端服务正在运行 (http://localhost:8080)")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    finally:
        # 关闭文件
        if 'files' in locals():
            files['files'].close()
    
    return False

def main():
    success = test_frontend_backend_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 前后端完全打通测试 - 成功！")
        print("✅ 所有功能正常工作:")
        print("   - 前端数据格式正确")
        print("   - 后端API接收正常") 
        print("   - 水印区域处理正确")
        print("   - LAMA算法执行成功")
        print("   - 结果返回完整")
    else:
        print("💥 前后端打通测试 - 失败！")
        print("❌ 请检查:")
        print("   - 后端服务是否启动")
        print("   - API路径是否正确")
        print("   - 数据格式是否匹配")
        print("   - 依赖环境是否完整")
    print("=" * 60)

if __name__ == "__main__":
    main()
