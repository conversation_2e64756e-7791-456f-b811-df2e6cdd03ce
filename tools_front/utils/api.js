// API请求工具类
const BASE_URL = 'http://localhost:8080/api';

// 统一请求方法
const request = async (url, options = {}) => {
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    ...options
  };

  try {
    const response = await uni.request({
      url: `${BASE_URL}${url}`,
      ...defaultOptions
    });

    if (response.statusCode === 200) {
      return response.data;
    } else {
      throw new Error(`请求失败: ${response.statusCode}`);
    }
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// 工具API类
export const toolAPI = {
  executeTool: async (params) => {
    return request('/tools/execute', {
      method: 'POST',
      data: params
    });
  }
};

// 壁纸API类
export const wallpaperAPI = {
  getRandomWallpaper: async (params) => {
    return request('/wallpaper/random', {
      method: 'GET',
      data: params
    });
  },
  getBingDaily: async () => {
    return request('/wallpaper/bing-daily');
  },
  getWallpapersByCategory: async (category, params) => {
    return request(`/wallpaper/${category}`, {
      method: 'GET',
      data: params
    });
  }
};

// 文件API类
export const fileAPI = {
  upload: async (file) => {
    return request('/file/upload', {
      method: 'POST',
      data: file
    });
  },
  download: async (fileId) => {
    return request(`/file/download/${fileId}`);
  }
};

// 工具API类
export class ToolsAPI {
  
  // 好玩推荐类工具
  static async funImageGenerator(params) {
    return request('/tools/fun/image-generator', {
      method: 'POST',
      data: params
    });
  }

  static async getVipMembership(params) {
    return request('/tools/fun/vip-membership', {
      method: 'POST',
      data: params
    });
  }

  // 媒体工具类
  static async videoWatermarkRemover(params) {
    return request('/tools/media/video-watermark-remover', {
      method: 'POST',
      data: params
    });
  }

  static async imageWatermarkRemover(params) {
    return request('/tools/media/image-watermark-remover', {
      method: 'POST',
      data: params
    });
  }

  static async videoDownloader(params) {
    return request('/tools/media/video-downloader', {
      method: 'POST',
      data: params
    });
  }

  static async musicDownloader(params) {
    return request('/tools/media/music-downloader', {
      method: 'POST',
      data: params
    });
  }

  static async textExtractor(params) {
    return request('/tools/media/text-extractor', {
      method: 'POST',
      data: params
    });
  }

  static async gameVoiceSynthesizer(params) {
    return request('/tools/media/game-voice-synthesizer', {
      method: 'POST',
      data: params
    });
  }

  static async soundEffectsLibrary(params) {
    return request('/tools/media/sound-effects-library', {
      method: 'GET',
      data: params
    });
  }

  static async magicWatermarkRemover(params) {
    return request('/tools/media/magic-watermark-remover', {
      method: 'POST',
      data: params
    });
  }

  static async idPhotoMaker(params) {
    return request('/tools/media/id-photo-maker', {
      method: 'POST',
      data: params
    });
  }

  static async websiteSnapshot(params) {
    return request('/tools/media/website-snapshot', {
      method: 'POST',
      data: params
    });
  }

  // 实用工具类
  static async vehiclePriceQuery(params) {
    return request('/tools/utility/vehicle-price-query', {
      method: 'GET',
      data: params
    });
  }

  static async gasPriceQuery(params) {
    return request('/tools/utility/gas-price-query', {
      method: 'GET',
      data: params
    });
  }

  static async weatherQuery(params) {
    return request('/tools/utility/weather-query', {
      method: 'GET',
      data: params
    });
  }

  // 趣味工具类
  static async beastLanguage(params) {
    return request('/tools/fun/beast-language', {
      method: 'POST',
      data: params
    });
  }

  static async historyToday(params) {
    return request('/tools/fun/history-today', {
      method: 'GET',
      data: params
    });
  }

  static async gpuLadder(params) {
    return request('/tools/fun/gpu-ladder', {
      method: 'GET',
      data: params
    });
  }

  static async cpuLadder(params) {
    return request('/tools/fun/cpu-ladder', {
      method: 'GET',
      data: params
    });
  }

  // 图片壁纸类
  static async randomAvatar(params) {
    return request('/tools/wallpaper/random-avatar', {
      method: 'GET',
      data: params
    });
  }

  static async randomLoadingImage(params) {
    return request('/tools/wallpaper/random-loading-image', {
      method: 'GET',
      data: params
    });
  }

  static async randomMobileWallpaper(params) {
    return request('/tools/wallpaper/random-mobile-wallpaper', {
      method: 'GET',
      data: params
    });
  }

  static async randomPcWallpaper(params) {
    return request('/tools/wallpaper/random-pc-wallpaper', {
      method: 'GET',
      data: params
    });
  }

  static async animeWallpapers(params) {
    return request('/tools/wallpaper/anime-wallpapers', {
      method: 'GET',
      data: params
    });
  }
}

// 根据toolIdentifier调用对应的API
export const callToolAPI = async (toolIdentifier, params) => {
  const apiMap = {
    'fun-image-generator': ToolsAPI.funImageGenerator,
    'get-vip-membership': ToolsAPI.getVipMembership,
    'video-watermark-remover': ToolsAPI.videoWatermarkRemover,
    'image-watermark-remover': ToolsAPI.imageWatermarkRemover,
    'video-downloader': ToolsAPI.videoDownloader,
    'music-downloader': ToolsAPI.musicDownloader,
    'text-extractor': ToolsAPI.textExtractor,
    'game-voice-synthesizer': ToolsAPI.gameVoiceSynthesizer,
    'sound-effects-library': ToolsAPI.soundEffectsLibrary,
    'magic-watermark-remover': ToolsAPI.magicWatermarkRemover,
    'id-photo-maker': ToolsAPI.idPhotoMaker,
    'website-snapshot': ToolsAPI.websiteSnapshot,
    'vehicle-price-query': ToolsAPI.vehiclePriceQuery,
    'gas-price-query': ToolsAPI.gasPriceQuery,
    'weather-query': ToolsAPI.weatherQuery,
    'beast-language': ToolsAPI.beastLanguage,
    'history-today': ToolsAPI.historyToday,
    'gpu-ladder': ToolsAPI.gpuLadder,
    'cpu-ladder': ToolsAPI.cpuLadder,
    'random-avatar': ToolsAPI.randomAvatar,
    'random-loading-image': ToolsAPI.randomLoadingImage,
    'bing-daily-wallpaper': ToolsAPI.randomMobileWallpaper,
    'random-pc-wallpaper': ToolsAPI.randomPcWallpaper,
    'anime-wallpapers': ToolsAPI.animeWallpapers
  };

  const apiFunction = apiMap[toolIdentifier];
  if (!apiFunction) {
    throw new Error(`未找到工具 ${toolIdentifier} 对应的API`);
  }

  return await apiFunction(params);
};

export default ToolsAPI; 