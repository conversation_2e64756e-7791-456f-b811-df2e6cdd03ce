// API配置文件
const config = {
  // 开发环境API地址
  development: {
    baseURL: 'http://localhost:8080',
    timeout: 30000
  },
  
  // 生产环境API地址
  production: {
    baseURL: 'https://your-production-domain.com',
    timeout: 30000
  }
}

// 获取当前环境
const env = process.env.NODE_ENV || 'development'

// 导出当前环境配置
export const API_CONFIG = config[env]

// API接口地址
export const API_ENDPOINTS = {
  // 图片去水印相关接口
  IMAGE_WATERMARK_REMOVAL: {
    BATCH: '/api/tools/media/image-watermark-removal/batch',
    SINGLE: '/api/tools/media/image-watermark-removal/single',
    PROGRESS: '/api/tools/media/image-watermark-removal/progress',
    RESULT: '/api/tools/media/image-watermark-removal/result',
    CLEANUP: '/api/tools/media/image-watermark-removal/cleanup',
    ALGORITHMS: '/api/tools/media/image-watermark-removal/algorithms'
  },
  
  // 视频去水印相关接口
  VIDEO_WATERMARK_REMOVAL: {
    UPLOAD: '/api/tools/media/video-subtitle-remover/upload',
    PROGRESS: '/api/tools/media/video-subtitle-remover/progress',
    RESULT: '/api/tools/media/video-subtitle-remover/result',
    DOWNLOAD: '/api/tools/media/video-subtitle-remover/download',
    CLEANUP: '/api/tools/media/video-subtitle-remover/cleanup',
    ALGORITHMS: '/api/tools/media/video-subtitle-remover/algorithms'
  },
  
  // 文件服务接口
  FILE_SERVICE: {
    PROCESSED_IMAGE: '/file/processed/image',
    PROCESSED_VIDEO: '/file/processed/video'
  }
}

// 构建完整的API URL
export function buildApiUrl(endpoint) {
  return API_CONFIG.baseURL + endpoint
}

// HTTP请求工具函数
export const httpRequest = {
  // GET请求
  async get(url, options = {}) {
    const fullUrl = buildApiUrl(url)
    
    // #ifdef H5
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    return await response.json()
    // #endif
    
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      uni.request({
        url: fullUrl,
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      })
    })
    // #endif
  },

  // POST请求（JSON）
  async post(url, data = {}, options = {}) {
    const fullUrl = buildApiUrl(url)
    
    // #ifdef H5
    const response = await fetch(fullUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: JSON.stringify(data),
      ...options
    })
    return await response.json()
    // #endif
    
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      uni.request({
        url: fullUrl,
        method: 'POST',
        data: data,
        header: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      })
    })
    // #endif
  },

  // POST请求（FormData）
  async postFormData(url, formData, options = {}) {
    const fullUrl = buildApiUrl(url)
    
    // #ifdef H5
    const response = await fetch(fullUrl, {
      method: 'POST',
      body: formData,
      ...options
    })
    return await response.json()
    // #endif
    
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      uni.request({
        url: fullUrl,
        method: 'POST',
        data: formData,
        header: {
          'Content-Type': 'multipart/form-data',
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      })
    })
    // #endif
  },

  // DELETE请求
  async delete(url, options = {}) {
    const fullUrl = buildApiUrl(url)
    
    // #ifdef H5
    const response = await fetch(fullUrl, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    })
    return await response.json()
    // #endif
    
    // #ifdef MP-WEIXIN
    return new Promise((resolve, reject) => {
      uni.request({
        url: fullUrl,
        method: 'DELETE',
        header: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      })
    })
    // #endif
  }
}

export default {
  API_CONFIG,
  API_ENDPOINTS,
  buildApiUrl,
  httpRequest
}
