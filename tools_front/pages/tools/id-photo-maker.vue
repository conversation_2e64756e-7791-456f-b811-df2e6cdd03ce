<template>
  <view class="id-photo-container">
    <!-- 标题区域 -->
    <view class="header">
      <view class="header-content">
        <view class="title-wrap">
          <text class="title">证件照制作</text>
          <view class="subtitle-wrap">
            <text class="subtitle">AI智能抠图，</text>
            <text class="subtitle">一键生成标准证件照</text>
          </view>
        </view>
      </view>
    </view>

    <view class="main-content">
      <!-- 上传区域改版 -->
      <view class="upload-section" v-if="!selectedImage">
        <view class="upload-card" @tap="selectImage">
          <view class="upload-icon">📷</view>
          <text class="upload-text">选择照片</text>
          <text class="upload-desc">支持 JPG、PNG 格式，建议分辨率 ≥ 300x300</text>
        </view>
        <view class="take-photo-btn" @tap="takePhoto">
          <text class="camera-icon">📸</text>
          <text>立即拍照</text>
        </view>
      </view>

      <!-- 尺寸选择 -->
      <view class="size-section" v-if="selectedImage">
        <text class="section-title">选择尺寸</text>
        <view class="size-list">
          <view 
            v-for="size in photoSizes" 
            :key="size.id"
            class="size-item"
            :class="{ active: selectedSize.id === size.id }"
            @tap="selectSize(size)"
          >
            <text class="size-name">{{ size.name }}</text>
            <text class="size-dimensions">{{ size.width }}×{{ size.height }}cm</text>
          </view>
        </view>
      </view>

      <!-- 背景颜色选择 -->
      <view class="color-section" v-if="selectedImage">
        <view class="color-list">
          <view 
            v-for="color in backgroundColors" 
            :key="color.id"
            class="color-item"
            :class="{ active: selectedBgColor.id === color.id }"
            @tap="selectBgColor(color)"
          >
            <view class="color-preview" :style="{ backgroundColor: color.value }"></view>
            <text class="color-name">{{ color.name }}</text>
          </view>
        </view>
      </view>

      <!-- 生成按钮 -->
      <view class="action-section" v-if="selectedImage">
        <button class="generate-btn" @tap="generatePhoto">
          制作证件照
        </button>
      </view>

      <!-- 预览弹窗 -->
      <view class="preview-modal" v-if="showPreview" @tap="closePreview">
        <view class="preview-content" @tap.stop>
          <view class="preview-header">
            <text class="preview-title">预览效果</text>
            <text class="close-btn" @tap="closePreview">×</text>
          </view>
          <view class="preview-image-wrap">
            <image :src="processedImage" mode="aspectFit" class="preview-image"></image>
          </view>
          <view class="preview-info">
            <view class="info-item">
              <text class="info-label">尺寸规格：</text>
              <text class="info-value">{{ selectedSize.name }} ({{ selectedSize.width }}×{{ selectedSize.height }}cm)</text>
            </view>
            <view class="info-item">
              <text class="info-label">背景颜色：</text>
              <text class="info-value">{{ selectedBgColor.name }}</text>
            </view>
          </view>
          <view class="preview-actions">
            <button class="preview-btn save-btn" @tap="savePhoto">保存到相册</button>
            <button class="preview-btn reset-btn" @tap="resetProcess">重新制作</button>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="instructions-section" v-if="!selectedImage">
        <view class="instructions-card">
          <view class="instructions-header">
            <view class="header-line"></view>
            <text class="instructions-title">使用说明</text>
          </view>
          <view class="instruction-list">
            <view class="instruction-item" v-for="(item, index) in instructions" :key="index">
              <view class="instruction-number">{{ index + 1 }}</view>
              <text class="instruction-text">{{ item }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.header {
  padding: 24rpx;
  
  .header-content {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    padding: 40rpx;
    border-radius: 24rpx;
    text-align: center;
    
    .title-wrap {
      .title {
        font-size: 44rpx;
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 20rpx;
      }
      
      .subtitle-wrap {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8rpx;
        
        .subtitle {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.9);
          line-height: 1.4;
        }
      }
    }
  }
}

.upload-section {
  margin: 24rpx;
  
  .upload-card {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 48rpx 32rpx;
    text-align: center;
    margin-bottom: 24rpx;
    
    .upload-icon {
      font-size: 80rpx;
      color: #6366f1;
      margin-bottom: 24rpx;
    }
    
    .upload-text {
      font-size: 32rpx;
      color: #1e293b;
      font-weight: 600;
      margin-bottom: 16rpx;
      display: block;
    }
    
    .upload-desc {
      font-size: 26rpx;
      color: #64748b;
      line-height: 1.5;
    }
  }
  
  .take-photo-btn {
    background: #f8fafc;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    
    .camera-icon {
      font-size: 36rpx;
    }
    
    text {
      font-size: 28rpx;
      color: #475569;
    }
  }
}

.size-section, .color-section {
  margin: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  
  .section-title {
    font-size: 32rpx;
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 24rpx;
  }
}

.size-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  
  .size-item {
    padding: 24rpx;
    border-radius: 16rpx;
    background: #f8fafc;
    border: 2rpx solid #e2e8f0;
    
    &.active {
      background: #f0f7ff;
      border-color: #6366f1;
    }
    
    .size-name {
      font-size: 28rpx;
      color: #1e293b;
      font-weight: 500;
      margin-bottom: 8rpx;
      display: block;
    }
    
    .size-dimensions {
      font-size: 24rpx;
      color: #64748b;
    }
  }
}

.color-section {
  margin: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  
  .section-title {
    font-size: 32rpx;
    color: #1e293b;
    font-weight: 600;
    margin-bottom: 32rpx;
  }
  
  .color-list {
    display: flex;
    justify-content: space-around;
    padding: 0 20rpx;
    
    .color-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12rpx;
      
      .color-preview {
        width: 100rpx;
        height: 100rpx;
        border-radius: 20rpx;
        border: 2rpx solid #e2e8f0;
        transition: all 0.3s ease;
        
        &:active {
          transform: scale(0.95);
        }
      }
      
      .color-name {
        font-size: 28rpx;
        color: #1e293b;
      }
      
      &.active {
        .color-preview {
          border: 3rpx solid #6366f1;
          box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
        }
        
        .color-name {
          color: #6366f1;
          font-weight: 500;
        }
      }
    }
  }
}

.action-section {
  margin: 24rpx;
  
  .generate-btn {
    width: 100%;
    background: #0f172a;
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    padding: 28rpx;
    border-radius: 16rpx;
    text-align: center;
  }
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  
  .preview-content {
    width: 90%;
    max-width: 600rpx;
    background: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx;
    
    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      
      .preview-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1e293b;
      }
      
      .close-btn {
        font-size: 40rpx;
        color: #64748b;
        padding: 10rpx;
      }
    }
    
    .preview-image-wrap {
      width: 100%;
      height: 600rpx;
      background: #f8fafc;
      border-radius: 16rpx;
      overflow: hidden;
      margin-bottom: 24rpx;
      
      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    
    .preview-info {
      margin-bottom: 32rpx;
      
      .info-item {
        display: flex;
        margin-bottom: 16rpx;
        
        .info-label {
          font-size: 28rpx;
          color: #64748b;
          min-width: 140rpx;
        }
        
        .info-value {
          font-size: 28rpx;
          color: #1e293b;
          flex: 1;
        }
      }
    }
    
    .preview-actions {
      display: flex;
      gap: 16rpx;
      
      .preview-btn {
        flex: 1;
        height: 88rpx;
        border-radius: 16rpx;
        font-size: 28rpx;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.save-btn {
          background: #6366f1;
          color: #ffffff;
        }
        
        &.reset-btn {
          background: #f1f5f9;
          color: #475569;
        }
      }
    }
  }
}

.instructions-section {
  margin: 24rpx;
  
  .instructions-card {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 32rpx;
    
    .instructions-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 32rpx;
      
      .header-line {
        width: 8rpx;
        height: 36rpx;
        background: #6366f1;
        border-radius: 4rpx;
      }
      
      .instructions-title {
        font-size: 32rpx;
        color: #1e293b;
        font-weight: 600;
      }
    }
  }
  
  .instruction-list {
    .instruction-item {
      display: flex;
      align-items: center;
      gap: 16rpx;
      margin-bottom: 24rpx;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .instruction-number {
        width: 48rpx;
        height: 48rpx;
        background: #6366f1;
        color: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-weight: 600;
      }
      
      .instruction-text {
        flex: 1;
        font-size: 28rpx;
        color: #475569;
        line-height: 1.5;
      }
    }
  }
}
</style>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ToolService } from '../../utils/toolService.js'
import { showSuccess, showError, showLoading, hideLoading } from '../../utils/index.js'

const toolService = new ToolService()

const selectedImage = ref(null)
const processedImage = ref(null)
const showPreview = ref(false)
const selectedSize = reactive({ id: 1, name: '一寸照', width: '2.5', height: '3.5' }) // 设置默认选中的尺寸
const selectedBgColor = reactive({ id: 1, name: '白色', value: '#ffffff' }) // 设置默认选中的背景色
const photoSizes = [
  { id: 1, name: '一寸照', width: '2.5', height: '3.5' },
  { id: 2, name: '二寸照', width: '3.5', height: '5.3' },
  { id: 3, name: '护照照', width: '3.3', height: '4.8' },
  { id: 4, name: '驾照照', width: '2.2', height: '3.2' }
]
const backgroundColors = [
  { id: 1, name: '白色', value: '#ffffff' },
  { id: 2, name: '蓝色', value: '#6366f1' },
  { id: 3, name: '红色', value: '#ef4444' }
]
const instructions = [
  '选择或拍摄一张清晰的正面照片',
  '选择合适的证件照尺寸规格',
  '选择所需的背景颜色',
  '调整美颜和优化设置',
  '点击制作并保存证件照'
]

// 选择图片
const selectImage = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1, // 最多选择1张图片
      sizeType: ['original', 'compressed'], // 可选择原图或压缩后的图片
      sourceType: ['album'], // 从相册选择
    })
    
    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      const tempFilePath = res.tempFilePaths[0]
      selectedImage.value = tempFilePath
      
      // 上传图片到七牛云
      await uploadImageToQiniu(tempFilePath)
      
      uni.showToast({
        title: '选择成功',
        icon: 'success',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('选择图片失败:', error)
    uni.showToast({
      title: '选择失败',
      icon: 'error',
      duration: 2000
    })
  }
}

// 拍照
const takePhoto = async () => {
  try {
    const res = await uni.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['camera'], // 使用相机
    })
    
    if (res.tempFilePaths && res.tempFilePaths.length > 0) {
      const tempFilePath = res.tempFilePaths[0]
      selectedImage.value = tempFilePath
      
      // 上传图片到七牛云
      await uploadImageToQiniu(tempFilePath)
      
      uni.showToast({
        title: '拍照成功',
        icon: 'success',
        duration: 2000
      })
    }
  } catch (error) {
    console.error('拍照失败:', error)
    uni.showToast({
      title: '拍照失败',
      icon: 'error',
      duration: 2000
    })
  }
}

// 上传图片到七牛云
const uploadImageToQiniu = async (filePath) => {
  try {
    showLoading('正在上传图片...')

    console.log('开始上传图片:', filePath)
    console.log('上传URL:', toolService.baseUrl + '/api/upload/idphoto')

    // 使用uni.uploadFile上传到后端
    const uploadResult = await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: toolService.baseUrl + '/api/upload/idphoto',
        filePath: filePath,
        name: 'file',
        timeout: 60000, // 设置60秒超时
        header: {
          'Accept': 'application/json'
        },
        success: (res) => {
          console.log('上传响应:', res)
          try {
            const result = JSON.parse(res.data)
            if (result.code === 200) {
              resolve(result.data)
            } else {
              reject(new Error(result.message || '上传失败'))
            }
          } catch (parseError) {
            console.error('解析响应失败:', parseError)
            reject(new Error('服务器响应格式错误'))
          }
        },
        fail: (err) => {
          console.error('上传请求失败:', err)
          reject(err)
        }
      })
    })

    // 保存七牛云URL，用于后续证件照制作
    uploadedImageUrl.value = uploadResult.url

    console.log('图片上传成功:', uploadResult)
    hideLoading()

  } catch (error) {
    console.error('图片上传失败:', error)
    hideLoading()

    // 根据错误类型提供更具体的错误信息
    let errorMessage = '图片上传失败'
    if (error.errMsg) {
      if (error.errMsg.includes('timeout')) {
        errorMessage = '上传超时，请检查网络连接'
      } else if (error.errMsg.includes('fail')) {
        errorMessage = '网络连接失败，请检查服务器状态'
      } else {
        errorMessage = `上传失败：${error.errMsg}`
      }
    } else if (error.message) {
      errorMessage = `上传失败：${error.message}`
    }

    showError(errorMessage)
    throw error
  }
}

// 选择尺寸
const selectSize = (size) => {
  selectedSize.id = size.id;
  selectedSize.name = size.name;
  selectedSize.width = size.width;
  selectedSize.height = size.height;
  uni.showToast({
    title: `已选择${size.name}`,
    icon: 'none',
    duration: 1500
  });
}

// 选择背景色
const selectBgColor = (color) => {
  selectedBgColor.id = color.id;
  selectedBgColor.name = color.name;
  selectedBgColor.value = color.value;
  uni.showToast({
    title: `已选择${color.name}背景`,
    icon: 'none',
    duration: 1500
  });
}

// 将颜色值转换为颜色名称
const getColorName = (colorValue) => {
  const colorMap = {
    '#ffffff': '白色',
    '#6366f1': '蓝色', 
    '#ef4444': '红色'
  }
  return colorMap[colorValue] || '白色'
}

// 生成证件照
const generatePhoto = async () => {
  if (!selectedImage.value) {
    showError('请先选择照片')
    return
  }

  if (!uploadedImageUrl.value) {
    showError('图片还未上传完成，请稍后重试')
    return
  }

  let loadingTimer = null
  let tipIndex = 0
  const loadingTips = [
    'AI智能抠图中，请耐心等待...',
    '正在添加背景色...',
    '正在调整尺寸，即将完成...'
  ]

  try {
    // 显示第一个提示
    showLoading(loadingTips[0])
    
    // 设置定时器更新提示
    loadingTimer = setInterval(() => {
      tipIndex = (tipIndex + 1) % loadingTips.length
      hideLoading()
      showLoading(loadingTips[tipIndex])
    }, 2000)
    
    // 构建请求参数，使用七牛云URL
    const requestParams = {
      imageUrl: uploadedImageUrl.value, // 使用七牛云URL
      photoType: selectedSize.name,
      backgroundColor: getColorName(selectedBgColor.value), // 使用颜色名称
      width: Math.round(parseFloat(selectedSize.width) * 118), // 转换为像素 (300DPI)
      height: Math.round(parseFloat(selectedSize.height) * 118), // 转换为像素 (300DPI)
      autoAdjust: true,
      beautify: false,
      beautifyLevel: 0,
      outputFormat: 'jpg',
      dpi: 300,
      extra: null
    }

    console.log('发送证件照制作请求:', requestParams)
    
    // 调用后端API
    const result = await toolService.callIdPhotoAPI(requestParams)
    
    console.log('证件照制作返回结果:', result)
    console.log('result.data:', result.data)
    console.log('result.data.processResult:', result.data?.processResult)
    console.log('result.data.processResult.processedUrl:', result.data?.processResult?.processedUrl)

    // 检查返回结果 - 后端返回的结构是 {code: 200, message: "操作成功", data: {...}}
    if (result.code === 200 && result.data && result.data.success) {
      // 从正确的路径获取processedUrl - 在processResult对象中
      if (result.data.processResult && result.data.processResult.processedUrl) {
        // 使用七牛云URL
        console.log('设置processedImage为:', result.data.processResult.processedUrl)
        processedImage.value = result.data.processResult.processedUrl
        showPreview.value = true
        showSuccess('证件照制作成功！')
      } else {
        console.error('返回结果中缺少processedUrl字段')
        console.error('完整的processResult:', result.data.processResult)
        console.error('完整的result.data:', result.data)
        throw new Error('返回结果中缺少处理后的图片URL')
      }
    } else {
      console.error('API调用失败，完整结果:', result)
      throw new Error(result.message || '制作失败')
    }
  } catch (error) {
    console.error('证件照制作失败:', error)
    
    // 检查是否是超时错误
    if (error.errMsg && error.errMsg.includes('timeout')) {
      showError('处理时间较长，请稍后重试')
    } else {
      // 后端API失败时，使用本地模拟处理
      try {
        await simulatePhotoProcessing()
        showSuccess('证件照制作成功！（本地处理）')
      } catch (localError) {
        showError('证件照制作失败，请重试')
      }
    }
  } finally {
    // 清除定时器和Loading
    if (loadingTimer) {
      clearInterval(loadingTimer)
    }
    hideLoading()
  }
}

// 本地模拟处理（后端API失败时的回退方案）
const simulatePhotoProcessing = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟处理结果
      processedImage.value = selectedImage.value
      showPreview.value = true
      resolve()
    }, 2000)
  })
}

// 关闭预览
const closePreview = () => {
  showPreview.value = false;
}

// 保存照片
const savePhoto = async () => {
  if (!processedImage.value) {
    showError('没有可保存的照片')
    return
  }

  try {
    showLoading('正在保存照片...')
    
    // 如果是七牛云URL，需要先下载到本地
    if (processedImage.value.startsWith('http')) {
      // 下载图片到本地
      const downloadResult = await uni.downloadFile({
        url: processedImage.value
      })
      
      if (downloadResult.statusCode === 200) {
        // 保存到相册
        await uni.saveImageToPhotosAlbum({
          filePath: downloadResult.tempFilePath
        })
        
        hideLoading()
        showSuccess('保存成功')
      } else {
        throw new Error('下载图片失败')
      }
    } else {
      // 本地图片直接保存
      await uni.saveImageToPhotosAlbum({
        filePath: processedImage.value
      })
      
      hideLoading()
      showSuccess('保存成功')
    }
  } catch (error) {
    console.error('保存照片失败:', error)
    hideLoading()
    showError('保存失败，请重试')
  }
}

// 重新制作
const resetProcess = () => {
  selectedImage.value = null
  uploadedImageUrl.value = null
  processedImage.value = null
  showPreview.value = false
  uni.showToast({
    title: '已重置',
    icon: 'none'
  })
}

// 添加响应式变量
const uploadedImageUrl = ref(null) // 存储七牛云URL
</script> 