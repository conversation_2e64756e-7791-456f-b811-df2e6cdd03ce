<template>
  <view class="magic-watermark-remover">
    <view class="content">
      <!-- 图片上传区域 -->
      <view class="section-card">
        <view class="card-header">
          <text class="header-icon">🪄</text>
          <text class="header-title">魔法去水印</text>
        </view>
        <view class="card-content">
          <view class="upload-area" @tap="chooseImage">
            <view v-if="!selectedImage">
              <view class="upload-icon">
                <text class="icon-text">📷</text>
              </view>
              <text class="upload-title">上传带水印的图片</text>
              <view class="upload-desc-container">
                <text class="upload-desc">支持</text>
                <text class="upload-format">JPG、PNG、WebP</text>
                <text class="upload-desc">格式，</text>
                <text class="upload-feature">AI智能识别去除</text>
              </view>
            </view>
            
            <view v-else class="selected-file-container">
              <view class="file-info glass-effect">
                <view class="file-icon">📄</view>
                <view class="file-details">
                  <text class="file-name">{{ selectedImage.name }}</text>
                  <text class="file-size">{{ formatFileSize(selectedImage.size) }}</text>
                </view>
                <view class="change-button" @tap.stop="chooseImage">
                  <text class="change-text">更换</text>
                </view>
              </view>
              
              <view class="preview-container glass-effect">
                <view class="preview-header">
                  <text class="preview-label">原图预览</text>
                  <view class="preview-actions">
                    <view class="action-button" @tap.stop="openWatermarkSelector">
                      <text class="action-text">选择水印区域</text>
                    </view>
                    <view class="action-button secondary" @tap.stop="chooseImage">
                      <text class="action-text">更换</text>
                    </view>
                  </view>
                </view>
                <view class="preview-image-container" @tap="openWatermarkSelector">
                  <image
                    :src="selectedImage.url"
                    class="preview-image"
                    mode="aspectFit"
                  />
                  <!-- 水印区域覆盖层 -->
                  <view
                    v-if="watermarkArea"
                    class="watermark-overlay"
                    :style="getWatermarkOverlayStyle()"
                  >
                    <text class="watermark-label">水印区域</text>
                  </view>
                  <!-- 点击提示 -->
                  <view class="click-hint">
                    <text class="hint-text">点击选择水印区域</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 处理模式选择 -->
      <view v-if="selectedImage" class="section-card mode-selection-card">
        <view class="card-header glass-effect">
          <text class="header-icon">🎯</text>
          <text class="header-title">选择处理模式</text>
        </view>
        <view class="card-content">
          <view class="mode-grid">
            <view 
              v-for="mode in processingModes" 
              :key="mode.id"
              class="mode-item glass-effect"
              :class="{ active: selectedMode === mode.id }"
              @tap="selectMode(mode.id)"
            >
              <view class="mode-icon-wrapper">
                <view class="mode-icon" :style="{ background: mode.gradient }">
                  <text class="icon">{{ mode.icon }}</text>
                </view>
              </view>
              <view class="mode-content">
                <text class="mode-name">{{ mode.name }}</text>
                <text class="mode-desc">{{ mode.desc }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 高级设置 -->
      <view v-if="selectedImage && selectedMode" class="section-card">
        <view class="card-header">
          <text class="header-title">高级设置</text>
        </view>
        <view class="card-content">
          <!-- 处理强度 -->
          <view class="setting-group">
            <view class="setting-header">
              <text class="setting-label">处理强度</text>
              <text class="setting-value">{{ strengthLabels[Math.floor((processingStrength - 10) / 18)] }}</text>
            </view>
            <slider 
              :value="processingStrength" 
              :min="10" 
              :max="100" 
              :step="5"
              activeColor="#3b82f6"
              backgroundColor="#e2e8f0"
              block-color="#ffffff"
              block-size="24"
              @change="onStrengthChange"
            />
          </view>

          <!-- 边缘处理 -->
          <view class="setting-group">
            <text class="setting-label mb-4">边缘处理</text>
            <view class="edge-options mt-6">
              <view 
                v-for="edge in edgeOptions" 
                :key="edge.id"
                class="edge-item"
                :class="{ active: edgeMode === edge.id }"
                @tap="selectEdge(edge.id)"
              >
                <text class="edge-name">{{ edge.name }}</text>
              </view>
            </view>
          </view>

          <!-- 质量优化 -->
          <view class="setting-group">
            <text class="setting-label mb-4">质量优化</text>
            <view class="quality-toggles mt-6">
              <view 
                class="toggle-item"
                :class="{ active: enableDenoising }"
                @tap="enableDenoising = !enableDenoising"
              >
                <text class="toggle-icon">{{ enableDenoising ? '✓' : '' }}</text>
                <text class="toggle-text">降噪处理</text>
              </view>
              <view 
                class="toggle-item"
                :class="{ active: enableSharpening }"
                @tap="enableSharpening = !enableSharpening"
              >
                <text class="toggle-icon">{{ enableSharpening ? '✓' : '' }}</text>
                <text class="toggle-text">锐化增强</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 开始处理按钮 -->
      <view v-if="selectedImage && selectedMode" class="section-card">
        <view class="card-content">
          <button 
            class="process-button"
            :class="{ disabled: isProcessing }"
            @tap="handleProcess"
            :disabled="isProcessing"
          >
            {{ isProcessing ? '处理中...' : '开始魔法去水印' }}
          </button>
        </view>
      </view>

      <!-- 处理进度 -->
      <view v-if="isProcessing" class="section-card">
        <view class="card-header glass-effect">
          <text class="header-icon">⚡</text>
          <text class="header-title">处理进度</text>
        </view>
        <view class="card-content">
          <view class="progress-container glass-effect">
            <view class="progress-info">
              <view class="progress-status">
                <text class="progress-percentage">{{ progress }}%</text>
                <text class="current-step">{{ currentStep }}</text>
              </view>
              <view class="progress-track">
                <view 
                  class="progress-fill"
                  :style="{ 
                    width: progress + '%',
                    background: `linear-gradient(135deg, #3b82f6 0%, #2563eb ${progress}%)`
                  }"
                >
                  <view class="progress-glow"></view>
                </view>
              </view>
              <text class="progress-eta">预计剩余时间：{{ estimatedTime }}秒</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 处理结果 -->
      <view v-if="processedImageUrl" class="section-card">
        <view class="card-header glass-effect">
          <text class="header-icon">✨</text>
          <text class="header-title">处理结果</text>
        </view>
        <view class="card-content">
          <!-- 统计信息 -->
          <view class="result-stats glass-effect">
            <view class="stat-item">
              <text class="stat-value">{{ detectedWatermarks }}</text>
              <text class="stat-label">检测水印</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ removedWatermarks }}</text>
              <text class="stat-label">成功去除</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ processingTime }}s</text>
              <text class="stat-label">处理时间</text>
            </view>
            <view class="stat-item">
              <text class="stat-value">{{ qualityScore }}%</text>
              <text class="stat-label">质量评分</text>
            </view>
          </view>

          <!-- 对比展示 -->
          <view class="comparison-container glass-effect">
            <view class="comparison-group">
              <view class="comparison-item">
                <text class="comparison-label">处理前</text>
                <view class="image-wrapper">
                  <image 
                    :src="selectedImage.url" 
                    class="comparison-image"
                    mode="aspectFit"
                    :style="{
                      width: '100%',
                      height: '400rpx'
                    }"
                  />
                </view>
              </view>
              <view class="comparison-divider">
                <text class="divider-icon">➡️</text>
              </view>
              <view class="comparison-item">
                <text class="comparison-label">处理后</text>
                <view class="image-wrapper">
                  <image 
                    :src="processedImageUrl" 
                    class="comparison-image"
                    mode="aspectFit"
                    :style="{
                      width: '100%',
                      height: '400rpx'
                    }"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="result-actions">
            <button class="action-button secondary glass-effect" @tap="resetProcess">
              <text class="button-icon">🔄</text>
              <text class="button-text">重新处理</text>
            </button>
            <button class="action-button primary glass-effect" @tap="saveImage">
              <text class="button-icon">💾</text>
              <text class="button-text">保存图片</text>
            </button>
          </view>
        </view>
      </view>

      <!-- AI技术特色 -->
      <view class="section-card">
        <view class="card-header">
          <text class="header-title">AI技术特色</text>
        </view>
        <view class="card-content">
          <view class="feature-grid">
            <view class="feature-item">
              <view class="feature-icon">👁️</view>
              <text class="feature-name">智能识别</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">🔄</view>
              <text class="feature-name">背景修复</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">✨</view>
              <text class="feature-name">质量保持</text>
            </view>
            <view class="feature-item">
              <view class="feature-icon">⚡</view>
              <text class="feature-name">快速处理</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用说明 -->
      <view class="section-card instruction-card">
        <view class="card-header">
          <text class="header-icon">📖</text>
          <text class="header-title">使用说明</text>
        </view>
        <view class="card-content">
          <view class="usage-steps">
            <view class="step-item">
              <view class="step-icon">
                <text class="icon">📤</text>
              </view>
              <view class="step-content">
                <text class="step-title">上传图片</text>
                <text class="step-desc">上传带有水印的图片，支持多种格式</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-icon">
                <text class="icon">🎯</text>
              </view>
              <view class="step-content">
                <text class="step-title">选择模式</text>
                <text class="step-desc">选择合适的处理模式（快速/精确/深度）</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-icon">
                <text class="icon">⚙️</text>
              </view>
              <view class="step-content">
                <text class="step-title">调整参数</text>
                <text class="step-desc">调节处理强度和边缘处理方式</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-icon">
                <text class="icon">✨</text>
              </view>
              <view class="step-content">
                <text class="step-title">优化质量</text>
                <text class="step-desc">开启质量优化选项获得更好效果</text>
              </view>
            </view>
            
            <view class="step-item">
              <view class="step-icon">
                <text class="icon">👀</text>
              </view>
              <view class="step-content">
                <text class="step-title">预览保存</text>
                <text class="step-desc">预览处理结果并保存到相册</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 隐藏的Canvas用于图片处理 -->
    <canvas
      canvas-id="watermarkCanvas"
      class="hidden-canvas"
      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
    ></canvas>

    <!-- 水印区域选择弹窗 -->
    <view v-if="showWatermarkSelector" class="watermark-selector-modal">
      <view class="watermark-selector-content" @tap.stop>
        <!-- 标题栏 -->
        <view class="selector-header">
          <text class="selector-title">选择水印区域</text>
          <view class="selector-actions">
            <button @tap="clearWatermarkArea" class="action-btn clear-btn">清除</button>
            <button @tap="confirmWatermarkArea" class="action-btn confirm-btn" :disabled="!selectionBox.visible">确认</button>
            <button @tap="closeWatermarkSelector" class="action-btn close-btn">×</button>
          </view>
        </view>

        <!-- 图片容器 -->
        <view
          class="image-container"
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
        >
          <image
            :src="selectedImage.url"
            class="selector-image"
            mode="aspectFit"
            @load="onImageLoad"
          />

          <!-- 选择框 -->
          <view
            v-if="selectionBox.visible"
            class="selection-box"
            :style="selectionBoxStyle"
          >
            <view class="selection-border"></view>
            <text class="selection-label">水印区域</text>
          </view>
        </view>

        <!-- 提示信息 -->
        <view class="selector-tips">
          <text class="tip-text">
            {{ selectionBox.visible ? '已选择水印区域，点击"确认"完成选择' : '在图片上拖拽绘制矩形框来选择水印区域' }}
          </text>
        </view>
      </view>
      <view class="watermark-selector-mask" @tap="closeWatermarkSelector"></view>
    </view>
  </view>
</template>

<script>
import { ToolService } from '../../utils/toolService.js'
import { showSuccess, showError, showLoading, hideLoading } from '../../utils/index.js'

export default {
  data() {
    return {
      selectedImage: null,
      selectedMode: '',
      processingStrength: 70,
      edgeMode: 'smooth',
      enableDenoising: true,
      enableSharpening: false,
      isProcessing: false,
      progress: 0,
      currentStep: '',
      estimatedTime: 0,
      processedImageUrl: '',
      canvasWidth: 800,
      canvasHeight: 600,
      detectedWatermarks: 0,
      removedWatermarks: 0,
      processingTime: 0,
      qualityScore: 0,
      toolService: new ToolService(),

      // 水印区域选择相关
      showWatermarkSelector: false,
      watermarkArea: null, // 存储选择的水印区域
      selectionBox: {
        visible: false,
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0
      },
      isDragging: false,
      imageInfo: {
        width: 0,
        height: 0,
        displayWidth: 0,
        displayHeight: 0
      },
      processingModes: [
        {
          id: 'quick',
          name: '快速模式',
          desc: '快速处理，适合简单水印',
          icon: '⚡',
          gradient: 'linear-gradient(135deg, #10b981, #059669)'
        },
        {
          id: 'precise',
          name: '精确模式',
          desc: '精确识别，适合复杂水印',
          icon: '🎯',
          gradient: 'linear-gradient(135deg, #3b82f6, #2563eb)'
        },
        {
          id: 'deep',
          name: '深度模式',
          desc: '深度学习，最佳效果',
          icon: '🧠',
          gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
        }
      ],
      strengthLabels: ['轻度', '中度', '重度', '极强', '最强'],
      edgeOptions: [
        { id: 'smooth', name: '平滑' },
        { id: 'sharp', name: '锐化' },
        { id: 'natural', name: '自然' }
      ]
    }
  },

  onLoad() {
    uni.setNavigationBarTitle({
      title: '魔法抹除水印'
    })
  },

  computed: {
    selectionBoxStyle() {
      if (!this.selectionBox.visible) return {}

      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX)
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY)
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX)
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY)

      return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
      }
    }
  },

  methods: {
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          
          // 获取图片信息
          uni.getImageInfo({
            src: tempFilePath,
            success: (info) => {
              this.selectedImage = {
                url: tempFilePath,
                filePath: tempFilePath, // 微信小程序使用filePath
                name: `image_${Date.now()}.jpg`,
                size: info.width * info.height * 3, // 估算文件大小
                width: info.width,
                height: info.height
              }
              
              // 设置Canvas尺寸
              this.canvasWidth = info.width
              this.canvasHeight = info.height
              
              showSuccess('图片上传成功')
            },
            fail: (err) => {
              console.error('获取图片信息失败:', err)
              showError('图片信息获取失败')
            }
          })
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          showError('图片选择失败')
        }
      })
    },

    selectMode(mode) {
      this.selectedMode = mode
      showSuccess(`已选择${this.processingModes.find(m => m.id === mode).name}`)
    },

    onStrengthChange(e) {
      this.processingStrength = e.detail.value
    },

    selectEdge(edge) {
      this.edgeMode = edge
    },

    async handleProcess() {
      if (!this.selectedImage || !this.selectedMode) {
        showError('请先选择图片和处理模式')
        return
      }

      this.isProcessing = true
      this.progress = 0
      this.currentStep = '准备处理...'
      this.estimatedTime = 30

      try {
        // 模拟处理进度
        this.simulateProgress()

        // 准备处理参数
        const params = {
          mode: this.selectedMode,
          strength: this.processingStrength,
          edgeMode: this.edgeMode,
          enableDenoising: this.enableDenoising,
          enableSharpening: this.enableSharpening
        }

        // 如果用户选择了水印区域，添加到参数中
        if (this.watermarkArea) {
          params.watermarkAreas = [{
            imageIndex: 0,
            x: this.watermarkArea.x,
            y: this.watermarkArea.y,
            width: this.watermarkArea.width,
            height: this.watermarkArea.height
          }]
          console.log('传递水印区域到后端:', params.watermarkAreas)
        }

        // 调用后端API
        // 传递图片文件或路径，以及处理参数
        const imageSource = this.selectedImage.file || this.selectedImage.filePath || this.selectedImage.url
        const result = await this.toolService.removeMagicWatermark(imageSource, params)

        if (result.success) {
          this.processedImageUrl = result.data.processedImageUrl
          this.detectedWatermarks = result.data.detectedWatermarks || 3
          this.removedWatermarks = result.data.removedWatermarks || 3
          this.processingTime = result.data.processingTime || 15
          this.qualityScore = result.data.qualityScore || 95
          
          this.progress = 100
          this.currentStep = '处理完成'
          this.estimatedTime = 0
          
          showSuccess('水印去除成功！')
        } else {
          throw new Error(result.message || '处理失败')
        }
      } catch (error) {
        console.error('魔法去水印失败:', error)
        showError(error.message || '处理失败，请重试')
        
        // 如果后端失败，使用本地模拟处理
        await this.simulateLocalProcessing()
      } finally {
        this.isProcessing = false
      }
    },

    async simulateLocalProcessing() {
      // 模拟本地处理结果
      this.processedImageUrl = this.selectedImage.url
      this.detectedWatermarks = 2
      this.removedWatermarks = 2
      this.processingTime = 12
      this.qualityScore = 88
      
      this.progress = 100
      this.currentStep = '本地处理完成'
      this.estimatedTime = 0
      
      showSuccess('本地处理完成（模拟）')
    },

    simulateProgress() {
      const steps = [
        { progress: 20, step: '分析图片内容...', time: 25 },
        { progress: 40, step: '检测水印位置...', time: 20 },
        { progress: 60, step: '智能去除水印...', time: 15 },
        { progress: 80, step: '修复背景区域...', time: 10 },
        { progress: 95, step: '优化图片质量...', time: 5 }
      ]

      let currentStepIndex = 0
      const updateProgress = () => {
        if (currentStepIndex < steps.length && this.isProcessing) {
          const currentStepData = steps[currentStepIndex]
          this.progress = currentStepData.progress
          this.currentStep = currentStepData.step
          this.estimatedTime = currentStepData.time
          
          currentStepIndex++
          setTimeout(updateProgress, 2000)
        }
      }
      
      updateProgress()
    },

    saveImage() {
      if (!this.processedImageUrl) {
        showError('没有可保存的图片')
        return
      }

      uni.downloadFile({
        url: this.processedImageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                showSuccess('图片已保存到相册')
              },
              fail: (err) => {
                console.error('保存图片失败:', err)
                showError('保存图片失败')
              }
            })
          } else {
            showError('下载图片失败')
          }
        },
        fail: (err) => {
          console.error('下载图片失败:', err)
          showError('下载图片失败')
        }
      })
    },

    resetProcess() {
      this.selectedImage = null
      this.selectedMode = ''
      this.processingStrength = 70
      this.edgeMode = 'smooth'
      this.enableDenoising = true
      this.enableSharpening = false
      this.isProcessing = false
      this.progress = 0
      this.currentStep = ''
      this.estimatedTime = 0
      this.processedImageUrl = ''
      this.detectedWatermarks = 0
      this.removedWatermarks = 0
      this.processingTime = 0
      this.qualityScore = 0

      // 清除水印区域选择
      this.watermarkArea = null
      this.showWatermarkSelector = false
      this.selectionBox.visible = false

      showSuccess('已重置，可重新处理')
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 水印区域选择相关方法
    openWatermarkSelector() {
      if (!this.selectedImage) {
        showError('请先选择图片')
        return
      }

      this.showWatermarkSelector = true
      this.selectionBox.visible = false
      this.isDragging = false

      console.log('打开水印区域选择器')
    },

    closeWatermarkSelector() {
      this.showWatermarkSelector = false
      this.selectionBox.visible = false
      this.isDragging = false
    },

    onImageLoad(e) {
      const { width, height } = e.detail
      this.imageInfo.width = width
      this.imageInfo.height = height
      this.imageInfo.displayWidth = width
      this.imageInfo.displayHeight = height
      console.log('图片加载完成:', { width, height })
    },

    onTouchStart(e) {
      if (!this.showWatermarkSelector) return

      e.preventDefault()
      e.stopPropagation()

      const touch = e.touches[0]
      const query = uni.createSelectorQuery()
      query.select('.image-container').boundingClientRect((rect) => {
        if (rect) {
          const relativeX = touch.clientX - rect.left
          const relativeY = touch.clientY - rect.top

          const boundedX = Math.max(0, Math.min(relativeX, rect.width))
          const boundedY = Math.max(0, Math.min(relativeY, rect.height))

          this.isDragging = true
          this.selectionBox.startX = boundedX
          this.selectionBox.startY = boundedY
          this.selectionBox.endX = boundedX
          this.selectionBox.endY = boundedY
          this.selectionBox.visible = true

          console.log('开始选择水印区域:', { x: boundedX, y: boundedY })
        }
      }).exec()
    },

    onTouchMove(e) {
      if (!this.isDragging || !this.showWatermarkSelector) return

      e.preventDefault()
      e.stopPropagation()

      const touch = e.touches[0]
      const query = uni.createSelectorQuery()
      query.select('.image-container').boundingClientRect((rect) => {
        if (rect) {
          const relativeX = touch.clientX - rect.left
          const relativeY = touch.clientY - rect.top

          const boundedX = Math.max(0, Math.min(relativeX, rect.width))
          const boundedY = Math.max(0, Math.min(relativeY, rect.height))

          this.selectionBox.endX = boundedX
          this.selectionBox.endY = boundedY
        }
      }).exec()
    },

    onTouchEnd(e) {
      if (!this.isDragging || !this.showWatermarkSelector) return

      e.preventDefault()
      e.stopPropagation()

      this.isDragging = false

      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX)
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY)

      if (width < 20 || height < 20) {
        this.selectionBox.visible = false
        showError('选择区域太小，请重新选择')
      } else {
        console.log('水印区域选择完成:', { width, height })
        showSuccess(`已选择 ${Math.round(width)}×${Math.round(height)} 区域`)
      }
    },

    confirmWatermarkArea() {
      if (!this.selectionBox.visible) {
        showError('请先选择水印区域')
        return
      }

      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX)
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY)
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX)
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY)

      // 保存水印区域信息
      this.watermarkArea = {
        x: Math.round(left),
        y: Math.round(top),
        width: Math.round(width),
        height: Math.round(height)
      }

      console.log('确认水印区域:', this.watermarkArea)
      showSuccess('水印区域已保存')

      this.closeWatermarkSelector()
    },

    clearWatermarkArea() {
      this.selectionBox.visible = false
      this.watermarkArea = null
      showSuccess('已清除选择')
    },

    getWatermarkOverlayStyle() {
      if (!this.watermarkArea) return {}

      return {
        left: `${this.watermarkArea.x}px`,
        top: `${this.watermarkArea.y}px`,
        width: `${this.watermarkArea.width}px`,
        height: `${this.watermarkArea.height}px`
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.magic-watermark-remover {
  min-height: 100vh;
  background: #f8fafc;
  padding: 32rpx;
  box-sizing: border-box;
  
  .content {
    max-width: 1200rpx;
    margin: 0 auto;
  }
  
  .section-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    border: 2rpx solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
    }
    
    .card-header {
      padding: 24rpx;
      border-bottom: 2rpx solid rgba(226, 232, 240, 0.6);
      display: flex;
      align-items: center;
      gap: 12rpx;
      
      .header-icon {
        font-size: 32rpx;
      }
      
      .header-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #1f2937;
      }
    }
    
    .card-content {
      padding: 24rpx;
      
      .upload-area {
        border: 2rpx dashed #e2e8f0;
        border-radius: 16rpx;
        padding: 48rpx 24rpx;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8fafc;
        
        &:active {
          background: #f1f5f9;
          border-color: #cbd5e0;
        }
        
        .upload-icon {
          margin-bottom: 16rpx;
          
          .icon-text {
            font-size: 64rpx;
          }
        }
        
        .upload-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 12rpx;
          display: block;
        }
        
        .upload-desc-container {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          gap: 4rpx;
          padding: 0 32rpx;
          
          .upload-desc {
            font-size: 24rpx;
            color: #64748b;
            line-height: 1.6;
          }
          
          .upload-format {
            font-size: 24rpx;
            color: #3b82f6;
            font-weight: 500;
            line-height: 1.6;
          }
          
          .upload-feature {
            font-size: 24rpx;
            color: #10b981;
            font-weight: 500;
            line-height: 1.6;
          }
        }
      }
      
      .image-preview {
        margin-top: 24rpx;
        
        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12rpx;
          
          .preview-title {
            font-size: 26rpx;
            font-weight: 600;
            color: #1f2937;
          }
          
          .change-button {
            padding: 6rpx 16rpx;
            background: #3b82f6;
            border-radius: 8rpx;
            
            .change-text {
              font-size: 22rpx;
              color: #ffffff;
            }
          }
        }
        
        .preview-image {
          width: 100%;
          height: 360rpx;
          border-radius: 12rpx;
          object-fit: contain;
          background: #f8fafc;
        }
      }
    }
  }
  
  .mode-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    padding: 8rpx;
    
    @media screen and (max-width: 768rpx) {
      grid-template-columns: 1fr;
      gap: 16rpx;
    }
    
    .mode-item {
      position: relative;
      padding: 32rpx 24rpx;
      border-radius: 20rpx;
      background: #ffffff;
      transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      border: 1px solid rgba(226, 232, 240, 0.8);
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: transparent;
        transition: background 0.3s ease;
      }
      
      &.active {
        background: #f8fafc;
        border-color: #3b82f6;
        
        &::before {
          background: #3b82f6;
        }
        
        .mode-icon {
          transform: scale(1.05);
        }
      }
      
      &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
      }
      
      .mode-icon {
        width: 72rpx;
        height: 72rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20rpx;
        transition: transform 0.3s ease;
        
        .icon {
          font-size: 36rpx;
          color: #ffffff;
        }
      }
      
      .mode-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8rpx;
        
        .mode-name {
          font-size: 28rpx;
          font-weight: 600;
          color: #1f2937;
        }
        
        .mode-time {
          font-size: 24rpx;
          color: #3b82f6;
          font-weight: 500;
          padding: 4rpx 12rpx;
          background: rgba(59, 130, 246, 0.1);
          border-radius: 20rpx;
        }
      }
      
      .mode-desc {
        font-size: 24rpx;
        color: #64748b;
        display: block;
      }
    }
  }
  
  .setting-group {
    padding: 32rpx;
    border-radius: 16rpx;
    background: #f8fafc;
    margin-bottom: 24rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .setting-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16rpx;
      
      .setting-label {
        font-size: 28rpx;
        font-weight: 600;
        color: #1f2937;
      }
      
      .setting-value {
        font-size: 24rpx;
        color: #3b82f6;
        font-weight: 500;
        padding: 4rpx 12rpx;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 20rpx;
      }
    }
    
    .slider-container {
      display: flex;
      align-items: center;
      gap: 12rpx;
      
      .slider-value {
        font-size: 22rpx;
        color: #3b82f6;
        min-width: 80rpx;
        text-align: right;
      }
    }
    
    .edge-options {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 16rpx;
      
      .edge-item {
        padding: 20rpx 24rpx;
        border-radius: 12rpx;
        background: #ffffff;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
        border: 0.5px solid rgba(226, 232, 240, 0.8);
        
        &.active {
          background: #3b82f6;
          border-color: #3b82f6;
          
          .edge-name {
            color: #ffffff;
          }
        }
        
        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        }
        
        .edge-name {
          font-size: 26rpx;
          font-weight: 500;
          color: #1f2937;
        }
      }
    }
    
    .quality-toggles {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16rpx;
      
      .toggle-item {
        position: relative;
        padding: 24rpx;
        border-radius: 12rpx;
        background: #ffffff;
        display: flex;
        align-items: center;
        gap: 12rpx;
        transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
        border: 0.5px solid rgba(226, 232, 240, 0.8);
        
        &.active {
          background: rgba(16, 185, 129, 0.1);
          border-color: #10b981;
          
          .toggle-icon {
            color: #10b981;
          }
          
          .toggle-text {
            color: #10b981;
          }
        }
        
        &:hover {
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        }
        
        .toggle-icon {
          font-size: 28rpx;
          color: transparent;
          width: 28rpx;
        }
        
        .toggle-text {
          font-size: 26rpx;
          font-weight: 500;
          color: #1f2937;
        }
      }
    }
  }
  
  .process-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: none;
    
    &:active:not(.disabled) {
      transform: translateY(2rpx);
      opacity: 0.9;
    }
    
    &.disabled {
      background: #94a3b8;
      cursor: not-allowed;
    }
  }
  
  .progress-container {
    padding: 32rpx;
    border-radius: 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 0.5px solid rgba(226, 232, 240, 0.8);
    backdrop-filter: blur(12px);
    
    .progress-info {
      width: 100%;
    }
    
    .progress-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;
      
      .progress-percentage {
        font-size: 48rpx;
        font-weight: 700;
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        -webkit-background-clip: text;
        color: transparent;
      }
      
      .current-step {
        font-size: 28rpx;
        color: #64748b;
        font-weight: 500;
        padding: 8rpx 20rpx;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 24rpx;
      }
    }
    
    .progress-track {
      width: 100%;
      height: 12rpx;
      background: #e2e8f0;
      border-radius: 6rpx;
      overflow: hidden;
      margin-bottom: 16rpx;
      position: relative;
      
      .progress-fill {
        height: 100%;
        border-radius: 6rpx;
        transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        position: relative;
        overflow: hidden;
        
        .progress-glow {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.15) 50%,
            transparent 100%
          );
          animation: glow 2s linear infinite;
        }
      }
    }
    
    .progress-eta {
      font-size: 24rpx;
      color: #64748b;
      display: block;
      text-align: right;
    }
  }
  
  .result-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
    margin-bottom: 32rpx;
    padding: 24rpx;
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 0.5px solid rgba(226, 232, 240, 0.8);
    backdrop-filter: blur(12px);
    
    @media screen and (max-width: 768rpx) {
      grid-template-columns: repeat(2, 1fr);
      gap: 24rpx;
    }
    
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20rpx;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 16rpx;
      border: 0.5px solid rgba(226, 232, 240, 0.6);
      transition: transform 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      
      &:hover {
        transform: translateY(-2rpx);
      }
      
      .stat-value {
        font-size: 40rpx;
        font-weight: 700;
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        -webkit-background-clip: text;
        color: transparent;
        margin-bottom: 8rpx;
        line-height: 1.2;
      }
      
      .stat-label {
        font-size: 24rpx;
        color: #64748b;
        line-height: 1.4;
        text-align: center;
      }
    }
  }
  
  .comparison-container {
    margin-bottom: 32rpx;
    padding: 32rpx;
    border-radius: 24rpx;
    background: rgba(255, 255, 255, 0.8);
    border: 0.5px solid rgba(226, 232, 240, 0.8);
    backdrop-filter: blur(12px);
    
    .comparison-group {
      display: flex;
      gap: 24rpx;
      align-items: flex-start;
      
      @media screen and (max-width: 768rpx) {
        flex-direction: column;
        align-items: center;
        
        .comparison-divider {
          transform: rotate(90deg);
          margin: 16rpx 0;
        }
      }
    }
    
    .comparison-item {
      flex: 1;
      width: 100%;
      
      .comparison-label {
        font-size: 26rpx;
        color: #64748b;
        text-align: center;
        margin-bottom: 16rpx;
        display: block;
        font-weight: 500;
      }

      .image-wrapper {
        width: 100%;
        background: #f8fafc;
        border-radius: 16rpx;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .comparison-image {
        display: block;
        object-fit: contain;
        background: #f8fafc;
      }
    }
    
    .comparison-divider {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16rpx;
      flex-shrink: 0;
      
      .divider-icon {
        font-size: 32rpx;
      }
    }
  }
  
  .result-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    
    .action-button {
      height: 88rpx;
      border-radius: 16rpx;
      font-size: 28rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      border: none;
      
      &.primary {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: #ffffff;
        
        &:active {
          background: linear-gradient(135deg, #2563eb, #1d4ed8);
        }
      }
      
      &.secondary {
        background: rgba(255, 255, 255, 0.8);
        border: 0.5px solid rgba(59, 130, 246, 0.3);
        color: #3b82f6;
        
        &:active {
          background: rgba(255, 255, 255, 0.9);
        }
      }
      
      &:active {
        transform: translateY(2rpx);
      }
      
      .button-icon {
        font-size: 32rpx;
      }
      
      .button-text {
        font-size: 28rpx;
      }
    }
  }
  
  .tech-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16rpx;
    
    @media screen and (max-width: 768rpx) {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .tech-item {
      padding: 24rpx;
      background: #f8fafc;
      border-radius: 16rpx;
      text-align: center;
      
      .tech-icon {
        width: 64rpx;
        height: 64rpx;
        border-radius: 16rpx;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 12rpx;
        border: 2rpx solid #e2e8f0;
        
        .icon {
          font-size: 32rpx;
        }
      }
      
      .tech-name {
        font-size: 26rpx;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 4rpx;
      }
      
      .tech-desc {
        font-size: 22rpx;
        color: #64748b;
        line-height: 1.4;
      }
    }
  }
  
  .usage-list {
    .usage-item {
      font-size: 24rpx;
      color: #1f2937;
      line-height: 1.6;
      margin-bottom: 8rpx;
      padding-left: 24rpx;
      position: relative;
      
      &:before {
        content: '';
        position: absolute;
        left: 8rpx;
        top: 12rpx;
        width: 6rpx;
        height: 6rpx;
        border-radius: 50%;
        background: #3b82f6;
      }
    }
  }
  
  .hidden-canvas {
    position: fixed;
    left: -9999rpx;
    top: -9999rpx;
    visibility: hidden;
  }
}

@keyframes glow {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .progress-container {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
    
    .progress-status {
      .progress-percentage {
        background: linear-gradient(135deg, #60a5fa, #3b82f6);
        -webkit-background-clip: text;
      }
      
      .current-step {
        color: #9ca3af;
        background: rgba(59, 130, 246, 0.15);
      }
    }
    
    .progress-track {
      background: rgba(55, 65, 81, 0.5);
    }
    
    .progress-eta {
      color: #9ca3af;
    }
  }
}

.instruction-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 0.5px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  
  .card-header {
    border-bottom: 0.5px solid rgba(226, 232, 240, 0.8);
    padding: 24rpx 32rpx;
    
    .header-icon {
      font-size: 36rpx;
      margin-right: 12rpx;
    }
    
    .header-title {
      font-size: 32rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #1a1a1a, #333333);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
  
  .usage-steps {
    padding: 24rpx 0;
    
    .step-item {
      display: flex;
      align-items: center;
      padding: 20rpx 24rpx;
      margin: 0 24rpx 16rpx;
      border-radius: 16rpx;
      background: rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(8px);
      transition: transform 0.3s cubic-bezier(0.2, 0, 0.1, 1);
      border: 0.5px solid rgba(226, 232, 240, 0.8);
      
      &:hover {
        transform: translateY(-2rpx);
        background: rgba(255, 255, 255, 0.8);
      }
      
      .step-icon {
        width: 64rpx;
        height: 64rpx;
        margin-right: 20rpx;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 0.5px solid rgba(226, 232, 240, 0.8);
        
        .icon {
          font-size: 32rpx;
        }
      }
      
      .step-content {
        flex: 1;
        
        .step-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4rpx;
          display: block;
        }
        
        .step-desc {
          font-size: 24rpx;
          color: #64748b;
          line-height: 1.5;
        }
      }
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .instruction-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
    border-color: rgba(55, 65, 81, 0.6);
    
    .card-header {
      border-color: rgba(55, 65, 81, 0.8);
      
      .header-title {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        -webkit-background-clip: text;
      }
    }
    
    .usage-steps {
      .step-item {
        background: rgba(31, 41, 55, 0.6);
        border-color: rgba(55, 65, 81, 0.8);
        
        &:hover {
          background: rgba(31, 41, 55, 0.8);
        }
        
        .step-icon {
          background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
          border-color: rgba(55, 65, 81, 0.8);
        }
        
        .step-content {
          .step-title {
            color: #f9fafb;
          }
          
          .step-desc {
            color: #9ca3af;
          }
        }
      }
    }
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 16rpx;
  
  @media screen and (max-width: 768rpx) {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
  }
  
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 32rpx 16rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
    border-radius: 20rpx;
    border: 0.5px solid rgba(226, 232, 240, 0.6);
    backdrop-filter: blur(8px);
    transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
    width: 100%;
    
    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    }
    
    .feature-icon {
      font-size: 48rpx;
      margin-bottom: 12rpx;
      background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
      width: 72rpx;
      height: 72rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 0.5px solid rgba(226, 232, 240, 0.8);
    }
    
    .feature-name {
      font-size: 24rpx;
      font-weight: 600;
      background: linear-gradient(135deg, #1a1a1a, #333333);
      -webkit-background-clip: text;
      color: transparent;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: center;
    }
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .feature-grid {
    .feature-item {
      background: linear-gradient(135deg, rgba(31, 41, 55, 0.6), rgba(17, 24, 39, 0.6));
      border-color: rgba(55, 65, 81, 0.6);
      
      .feature-icon {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        border-color: rgba(55, 65, 81, 0.8);
      }
      
      .feature-name {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        -webkit-background-clip: text;
      }
      
      &:hover {
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
      }
    }
  }
}

.mode-selection-card {
  margin: 32rpx 0;
  overflow: visible;
}

.mode-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 12rpx;
  
  @media screen and (max-width: 768rpx) {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
}

.mode-item {
  position: relative;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
  overflow: visible;
  
  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 24rpx 48rpx rgba(59, 130, 246, 0.15);
    
    .mode-icon-wrapper {
      transform: translateY(-8rpx);
      
      .mode-icon {
        box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
      }
    }
    
    .mode-name {
      background: linear-gradient(135deg, #1a1a1a, #333333);
      -webkit-background-clip: text;
      color: transparent;
    }
  }
}

.mode-icon-wrapper {
  position: relative;
  margin-bottom: 24rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}

.mode-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
  
  .icon {
    font-size: 40rpx;
    color: #ffffff;
  }
}

.mode-content {
  text-align: left;
}

.mode-name {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
  transition: all 0.3s ease;
}

.mode-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  display: block;
}

@media (prefers-color-scheme: dark) {
  .mode-item {
    background: rgba(31, 41, 55, 0.7);
    border-color: rgba(55, 65, 81, 0.3);
    
    &.active {
      background: rgba(31, 41, 55, 0.9);
      border-color: rgba(59, 130, 246, 0.3);
      
      .mode-name {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        -webkit-background-clip: text;
      }
    }
  }
  
  .mode-name {
    color: #f3f4f6;
  }
  
  .mode-desc {
    color: #9ca3af;
  }
}

.selected-file-container {
  width: 100%;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}

.file-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-details {
  flex: 1;
  margin-right: 16rpx;
}

.file-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4rpx;
  display: block;
  word-break: break-all;
}

.file-size {
  font-size: 24rpx;
  color: #64748b;
}

.change-button {
  padding: 12rpx 24rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  
  &:hover {
    background: rgba(59, 130, 246, 0.15);
  }
  
  .change-text {
    font-size: 24rpx;
    color: #3b82f6;
    font-weight: 500;
  }
}

.preview-container {
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}

.preview-label {
  font-size: 26rpx;
  color: #64748b;
  margin-bottom: 16rpx;
  display: block;
}

.preview-image {
  width: 100%;
  height: 480rpx;
  border-radius: 12rpx;
  object-fit: contain;
  background: #f8fafc;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.preview-actions {
  display: flex;
  gap: 12rpx;
}

.action-button {
  padding: 8rpx 16rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  
  &:hover {
    background: rgba(59, 130, 246, 0.15);
  }
  
  .action-text {
    font-size: 24rpx;
    color: #3b82f6;
    font-weight: 500;
  }
}

@media (prefers-color-scheme: dark) {
  .file-info {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
  }
  
  .file-icon {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
  
  .file-name {
    color: #f3f4f6;
  }
  
  .file-size {
    color: #9ca3af;
  }
  
  .change-button {
    background: rgba(59, 130, 246, 0.15);
    
    &:hover {
      background: rgba(59, 130, 246, 0.2);
    }
  }
  
  .preview-container {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
  }
  
  .preview-label {
    color: #9ca3af;
  }
  
  .preview-image {
    background: rgba(17, 24, 39, 0.8);
  }
}

/* 预览图片容器样式 */
.preview-image-container {
  position: relative;
  cursor: pointer;

  .watermark-overlay {
    position: absolute;
    border: 2px solid #ef4444;
    background-color: rgba(239, 68, 68, 0.2);
    pointer-events: none;

    .watermark-label {
      position: absolute;
      top: -24px;
      left: 0;
      background-color: #ef4444;
      color: white;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .click-hint {
    position: absolute;
    inset: 0;
    background-color: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.1);

      .hint-text {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .hint-text {
      background-color: #3b82f6;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      opacity: 0;
      transform: translateY(8px);
      transition: all 0.3s ease;
    }
  }
}

/* 水印选择器弹窗样式 */
.watermark-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watermark-selector-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
}

.watermark-selector-content {
  position: relative;
  width: 95%;
  height: 90%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background-color: #f8f9fa;
}

.selector-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.selector-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: none;

  &.clear-btn {
    background-color: #f56565;
    color: white;
  }

  &.confirm-btn {
    background-color: #48bb78;
    color: white;

    &:disabled {
      background-color: #a0aec0;
      cursor: not-allowed;
    }
  }

  &.close-btn {
    background-color: #a0aec0;
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
  }
}

.image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.selector-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.selection-box {
  position: absolute;
  border: 2px solid #ff4757;
  background-color: rgba(255, 71, 87, 0.2);
  pointer-events: none;
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #ff4757;
  animation: dash 1s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

.selection-label {
  position: absolute;
  top: -24px;
  left: 0;
  background-color: #ff4757;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.selector-tips {
  padding: 12px 16px;
  background-color: #e3f2fd;
  border-top: 1px solid #e5e5e5;
}

.tip-text {
  font-size: 14px;
  color: #1976d2;
  text-align: center;
}

/* 预览操作按钮样式优化 */
.preview-actions {
  display: flex;
  gap: 8px;

  .action-button {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    background-color: #3b82f6;
    color: white;
    transition: all 0.2s ease;

    &:hover {
      background-color: #2563eb;
    }

    &.secondary {
      background-color: #6b7280;

      &:hover {
        background-color: #4b5563;
      }
    }
  }
}
</style>