/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.text-center {
  text-align: center;
}
.rounded {
  border-radius: 3px;
}
.rounded-lg {
  border-radius: 6px;
}
.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4 {
  padding: 16rpx;
}
.m-4 {
  margin: 16rpx;
}
.mb-4 {
  margin-bottom: 16rpx;
}
.mt-4 {
  margin-top: 16rpx;
}
/*每个页面公共css */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.text-center {
  text-align: center;
}
.rounded {
  border-radius: 3px;
}
.rounded-lg {
  border-radius: 6px;
}
.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4 {
  padding: 16rpx;
}
.m-4 {
  margin: 16rpx;
}
.mb-4 {
  margin-bottom: 16rpx;
}
.mt-4 {
  margin-top: 16rpx;
}
page {
  background-color: #f8f8f8;
}
.container {
  padding: 20rpx;
}
.card {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: 500;
}
.btn-primary:active {
  opacity: 0.8;
}
.text-primary {
  color: #3b82f6;
}
.text-success {
  color: #10b981;
}
.text-warning {
  color: #f59e0b;
}
.text-error {
  color: #ef4444;
}
.text-gray {
  color: #6b7280;
}
.text-sm {
  font-size: 24rpx;
}
.text-base {
  font-size: 28rpx;
}
.text-lg {
  font-size: 32rpx;
}
.text-xl {
  font-size: 36rpx;
}
.text-2xl {
  font-size: 42rpx;
}
.font-bold {
  font-weight: bold;
}
.grid {
  display: flex;
  flex-wrap: wrap;
}
.grid-cols-2 .grid-item {
  width: 50%;
}
.grid-cols-3 .grid-item {
  width: 33.333%;
}
.grid-cols-4 .grid-item {
  width: 25%;
}
.gap-2 {
  margin: -4rpx;
}
.gap-2 .grid-item {
  padding: 4rpx;
}
.gap-4 {
  margin: -8rpx;
}
.gap-4 .grid-item {
  padding: 8rpx;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}