"use strict";
const common_vendor = require("../common/vendor.js");
const config = {
  // 开发环境API地址
  development: {
    baseURL: "http://localhost:8080",
    timeout: 3e4
  },
  // 生产环境API地址
  production: {
    baseURL: "https://your-production-domain.com",
    timeout: 3e4
  }
};
const env = "development";
const API_CONFIG = config[env];
const API_ENDPOINTS = {
  // 图片去水印相关接口
  IMAGE_WATERMARK_REMOVAL: {
    BATCH: "/api/tools/media/image-watermark-removal/batch",
    SINGLE: "/api/tools/media/image-watermark-removal/single",
    PROGRESS: "/api/tools/media/image-watermark-removal/progress",
    RESULT: "/api/tools/media/image-watermark-removal/result",
    CLEANUP: "/api/tools/media/image-watermark-removal/cleanup",
    ALGORITHMS: "/api/tools/media/image-watermark-removal/algorithms"
  },
  // 视频去水印相关接口
  VIDEO_WATERMARK_REMOVAL: {
    UPLOAD: "/api/tools/media/video-subtitle-remover/upload",
    PROGRESS: "/api/tools/media/video-subtitle-remover/progress",
    RESULT: "/api/tools/media/video-subtitle-remover/result",
    DOWNLOAD: "/api/tools/media/video-subtitle-remover/download",
    CLEANUP: "/api/tools/media/video-subtitle-remover/cleanup",
    ALGORITHMS: "/api/tools/media/video-subtitle-remover/algorithms"
  },
  // 文件服务接口
  FILE_SERVICE: {
    PROCESSED_IMAGE: "/file/processed/image",
    PROCESSED_VIDEO: "/file/processed/video"
  }
};
function buildApiUrl(endpoint) {
  return API_CONFIG.baseURL + endpoint;
}
const httpRequest = {
  // GET请求
  async get(url, options = {}) {
    const fullUrl = buildApiUrl(url);
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: fullUrl,
        method: "GET",
        header: {
          "Content-Type": "application/json",
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      });
    });
  },
  // POST请求（JSON）
  async post(url, data = {}, options = {}) {
    const fullUrl = buildApiUrl(url);
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: fullUrl,
        method: "POST",
        data,
        header: {
          "Content-Type": "application/json",
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      });
    });
  },
  // POST请求（FormData）
  async postFormData(url, formData, options = {}) {
    const fullUrl = buildApiUrl(url);
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: fullUrl,
        method: "POST",
        data: formData,
        header: {
          "Content-Type": "multipart/form-data",
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      });
    });
  },
  // DELETE请求
  async delete(url, options = {}) {
    const fullUrl = buildApiUrl(url);
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: fullUrl,
        method: "DELETE",
        header: {
          "Content-Type": "application/json",
          ...options.headers
        },
        success: (res) => resolve(res.data),
        fail: reject,
        ...options
      });
    });
  }
};
exports.API_ENDPOINTS = API_ENDPOINTS;
exports.httpRequest = httpRequest;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/api.js.map
