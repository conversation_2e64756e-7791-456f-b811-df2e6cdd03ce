"use strict";
const common_vendor = require("../common/vendor.js");
const utils_index = require("./index.js");
const utils_authManager = require("./authManager.js");
const BASE_URL = "http://localhost:8080";
class ToolService {
  constructor() {
    this.baseUrl = BASE_URL;
  }
  /**
   * 执行工具
   * @param {string} toolIdentifier 工具标识符
   * @param {object} params 工具参数
   * @returns {Promise} 执行结果
   */
  async executeTool(toolIdentifier, params = {}) {
    try {
      utils_index.showLoading("处理中...");
      await this.ensureAuthenticated();
      const result = await this.callToolAPI(`/api/tools/${toolIdentifier}`, "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 确保用户已认证
   */
  async ensureAuthenticated() {
    try {
      if (utils_authManager.authManager.isLoggedIn()) {
        return true;
      }
      common_vendor.index.__f__("log", "at utils/toolService.js:49", "尝试无感登录...");
      const loginSuccess = await utils_authManager.authManager.silentLogin();
      if (!loginSuccess) {
        common_vendor.index.__f__("warn", "at utils/toolService.js:53", "无感登录失败，但继续执行（工具接口已开放）");
      }
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:58", "认证失败:", error);
      return true;
    }
  }
  // ============ 好玩推荐工具 ============
  /**
   * 趣味图片生成器
   */
  async generateFunImage(params) {
    try {
      utils_index.showLoading("生成中...");
      await this.ensureAuthenticated();
      const result = await this.callToolAPI("/api/tools/fun/fun-image-generator", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 获取永久会员
   */
  async getVipMembership(params) {
    try {
      utils_index.showLoading("处理中...");
      const result = await this.callToolAPI("/api/tools/fun/get-vip-membership", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  // ============ 媒体工具 ============
  /**
   * 视频解析去水印
   */
  async removeVideoWatermark(videoUrl) {
    try {
      utils_index.showLoading("解析中...");
      const result = await this.callToolAPI("/api/tools/media/video-watermark-remover", "POST", { videoUrl });
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 图集解析去水印
   */
  async removeImageWatermark(imageUrls) {
    try {
      utils_index.showLoading("处理中...");
      const result = await this.callToolAPI("/api/tools/media/image-watermark-remover", "POST", { imageUrls });
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 视频下载
   */
  async downloadVideo(params) {
    try {
      utils_index.showLoading("解析中...");
      const requestParams = {
        videoUrl: params.videoUrl || params.url,
        // 优先使用videoUrl参数
        quality: params.quality || "high",
        format: params.format || "mp4"
      };
      common_vendor.index.__f__("log", "at utils/toolService.js:145", "发送视频解析请求:", requestParams);
      const result = await this.callToolAPI("/api/tools/media/video-downloader", "POST", requestParams);
      common_vendor.index.__f__("log", "at utils/toolService.js:147", "视频解析返回结果:", result);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:152", "视频解析失败:", error);
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 下载视频文件到本地
   */
  async downloadVideoFile(params) {
    try {
      utils_index.showLoading("下载中...");
      let fileName = params.fileName || `video_${Date.now()}.mp4`;
      if (!fileName.toLowerCase().endsWith(".mp4")) {
        const lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0) {
          fileName = fileName.substring(0, lastDotIndex);
        }
        fileName += ".mp4";
      }
      fileName = fileName.replace(/[/\\:*?"<>|]/g, "_");
      const requestParams = {
        videoUrl: params.videoUrl,
        fileName,
        quality: params.quality
      };
      if (!this.baseUrl) {
        common_vendor.index.__f__("error", "at utils/toolService.js:189", "baseUrl未定义");
        throw new Error("服务器地址未配置");
      }
      try {
        const queryString = Object.keys(requestParams).filter((key) => requestParams[key] !== void 0 && requestParams[key] !== null).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(requestParams[key])}`).join("&");
        const downloadUrl = `${this.baseUrl}/api/tools/media/download?${queryString}`;
        common_vendor.index.__f__("log", "at utils/toolService.js:202", "下载请求URL:", downloadUrl);
        const downloadRes = await common_vendor.index.downloadFile({
          url: downloadUrl,
          header: {
            "Accept": "video/mp4, */*"
          }
        });
        common_vendor.index.__f__("log", "at utils/toolService.js:212", "下载结果:", downloadRes);
        if (downloadRes.statusCode === 200) {
          await common_vendor.index.saveVideoToPhotosAlbum({
            filePath: downloadRes.tempFilePath
          });
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
          return {
            success: true,
            fileName
          };
        } else {
          throw new Error(`下载失败: ${downloadRes.statusCode}`);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/toolService.js:233", "下载或保存失败:", error);
        if (error.errMsg && error.errMsg.includes("auth deny")) {
          common_vendor.index.showModal({
            title: "授权提示",
            content: "需要授权保存到相册才能下载视频，是否去授权？",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting();
              }
            }
          });
          throw new Error("需要授权保存到相册");
        }
        throw error;
      }
    } catch (error) {
      utils_index.hideLoading();
      common_vendor.index.__f__("error", "at utils/toolService.js:282", "视频下载失败:", error);
      throw error;
    } finally {
      utils_index.hideLoading();
    }
  }
  /**
   * H5端触发文件下载
   */
  triggerDownload(data, filename) {
  }
  /**
   * 音乐下载器（搜索）
   */
  async downloadMusic(params) {
    try {
      utils_index.showLoading("搜索中...");
      const result = await this.callToolAPI("/api/tools/media/music-downloader", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 音乐文件下载
   */
  async downloadMusicFile(params) {
    try {
      utils_index.showLoading("准备下载...");
      const result = await this.callToolAPI("/api/tools/media/music-download-file", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  // 获取音乐字节流（用于微信小程序直接下载）
  async downloadMusicStream(params) {
    try {
      utils_index.showLoading("准备下载...");
      const result = await this.callToolAPI("/api/tools/media/download-music-stream", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 获取音乐预览URL
   */
  async getPreviewUrl(params) {
    try {
      utils_index.showLoading("获取预览链接...");
      const result = await this.callToolAPI("/api/tools/media/get-preview-url", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 直接下载音乐文件到本地
   */
  async downloadMusicToLocal(filePath, fileName) {
    try {
      const downloadUrl = `${this.baseUrl}/api/tools/media/download-file?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`;
      const downloadRes = await common_vendor.index.downloadFile({
        url: downloadUrl,
        header: {
          "Accept": "audio/mpeg, */*"
        }
      });
      if (downloadRes.statusCode === 200) {
        common_vendor.index.showModal({
          title: "下载完成",
          content: "文件已下载到临时目录，请注意及时保存",
          showCancel: false
        });
        return { success: true, tempFilePath: downloadRes.tempFilePath };
      } else {
        throw new Error(`下载失败: ${downloadRes.statusCode}`);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:416", "下载失败:", error);
      throw error;
    }
  }
  /**
   * 文案提取器
   */
  async extractText(imageUrl) {
    try {
      utils_index.showLoading("识别中...");
      const result = await this.callToolAPI("/api/tools/media/text-extractor", "POST", { imageUrl });
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 游戏语音合成
   */
  async synthesizeGameVoice(params) {
    try {
      utils_index.showLoading("合成中...");
      const result = await this.callToolAPI("/api/tools/media/game-voice-synthesizer", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 音效大全
   */
  async getSoundEffects(category) {
    try {
      utils_index.showLoading("加载中...");
      const result = await this.callToolAPI("/api/tools/media/sound-effects-library", "GET", { category });
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 魔法抹除水印
   */
  async magicWatermarkRemover(imageUrl) {
    try {
      utils_index.showLoading("处理中...");
      const result = await this.callToolAPI("/api/tools/media/magic-watermark-remover", "POST", { imageUrl });
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 制作证件照
   */
  async makeIdPhoto(params) {
    try {
      common_vendor.index.__f__("log", "at utils/toolService.js:486", "开始制作证件照，参数:", params);
      const result = await this.callIdPhotoAPI(params);
      common_vendor.index.__f__("log", "at utils/toolService.js:491", "证件照制作完成:", result);
      return result;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:494", "证件照制作失败:", error);
      throw error;
    }
  }
  /**
   * 上传证件照原图
   * @param {File} file - 图片文件
   * @returns {Promise} 上传结果
   */
  async uploadIdPhoto(file) {
    const formData = new FormData();
    formData.append("file", file);
    const result = await this.request({
      url: "/api/upload/idphoto",
      method: "POST",
      data: formData,
      header: {
        "Content-Type": "multipart/form-data"
      },
      timeout: 6e4
      // 60秒超时
    });
    return result;
  }
  /**
   * 上传头像
   * @param {File} file - 图片文件
   * @param {string} userId - 用户ID（可选）
   * @returns {Promise} 上传结果
   */
  async uploadAvatar(file, userId = null) {
    const formData = new FormData();
    formData.append("file", file);
    if (userId) {
      formData.append("userId", userId);
    }
    const result = await this.request({
      url: "/api/upload/avatar",
      method: "POST",
      data: formData,
      header: {
        "Content-Type": "multipart/form-data"
      },
      timeout: 6e4
    });
    return result;
  }
  /**
   * 上传壁纸
   * @param {File} file - 图片文件
   * @param {string} category - 分类（可选）
   * @returns {Promise} 上传结果
   */
  async uploadWallpaper(file, category = null) {
    const formData = new FormData();
    formData.append("file", file);
    if (category) {
      formData.append("category", category);
    }
    const result = await this.request({
      url: "/api/upload/wallpaper",
      method: "POST",
      data: formData,
      header: {
        "Content-Type": "multipart/form-data"
      },
      timeout: 6e4
    });
    return result;
  }
  /**
   * 通用文件上传
   * @param {File} file - 文件
   * @param {string} type - 文件类型（可选）
   * @returns {Promise} 上传结果
   */
  async uploadCommon(file, type = null) {
    const formData = new FormData();
    formData.append("file", file);
    if (type) {
      formData.append("type", type);
    }
    const result = await this.request({
      url: "/api/upload/common",
      method: "POST",
      data: formData,
      header: {
        "Content-Type": "multipart/form-data"
      },
      timeout: 6e4
    });
    return result;
  }
  /**
   * 获取上传凭证
   * @param {string} type - 文件类型（可选）
   * @returns {Promise} 上传凭证
   */
  async getUploadToken(type = null) {
    const params = type ? { type } : {};
    const result = await this.request({
      url: "/api/upload/token",
      method: "GET",
      data: params
    });
    return result;
  }
  /**
   * 删除文件
   * @param {string} url - 文件URL
   * @returns {Promise} 删除结果
   */
  async deleteFile(url) {
    const result = await this.request({
      url: "/api/upload/delete",
      method: "DELETE",
      data: { url }
    });
    return result;
  }
  /**
   * 专门用于证件照制作的API调用（支持超时设置）
   * @param {Object} params - 请求参数
   * @returns {Promise} 处理结果
   */
  async callIdPhotoAPI(params) {
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: this.baseUrl + "/api/tools/media/id-photo-maker",
        method: "POST",
        data: params,
        header: {
          "Content-Type": "application/json"
        },
        timeout: 18e4,
        // 3分钟超时
        success: (res) => {
          var _a;
          if (res.statusCode === 200) {
            resolve(res.data);
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${((_a = res.data) == null ? void 0 : _a.message) || "请求失败"}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
  // ============ 实用工具 ============
  /**
   * 车辆价格查询
   */
  async queryVehiclePrice(params) {
    try {
      utils_index.showLoading("查询中...");
      const result = await this.callToolAPI("/api/tools/utility/vehicle-price-query", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 全国油价查询
   */
  async queryGasPrice(params) {
    try {
      utils_index.showLoading("查询中...");
      const result = await this.callToolAPI("/api/tools/utility/gas-price-query", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 全国天气查询
   */
  async queryWeather(params) {
    try {
      utils_index.showLoading("查询中...");
      const result = await this.callToolAPI("/api/tools/utility/weather-query", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  // ============ 趣味工具 ============
  /**
   * 兽语加密解密
   */
  async beastLanguage(params) {
    try {
      utils_index.showLoading("处理中...");
      const result = await this.callToolAPI("/api/tools/fun/beast-language", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 历史上的今天
   */
  async getHistoryToday(params = {}) {
    try {
      utils_index.showLoading("查询历史事件...");
      const result = await this.callToolAPI("/api/tools/fun/history-today", "POST", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 随机头像
   */
  async getRandomAvatars(params = {}) {
    try {
      utils_index.showLoading("生成头像中...");
      const gender = params.gender === "male" ? "boy" : params.gender === "female" ? "girl" : "boy";
      let girlType = 1;
      if (params.gender === "female" && params.girlType) {
        girlType = params.girlType;
      } else {
        girlType = params.style === "cartoon" ? 2 : params.style === "realistic" ? 1 : params.style === "pixel" ? 4 : 1;
      }
      const queryParams = `gender=${gender}&girlType=${girlType}&returnType=json`;
      const result = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, "GET");
      utils_index.hideLoading();
      if (result.code === 200 && result.data && result.data.success) {
        const avatars = [];
        const count = params.count || 3;
        for (let i = 0; i < count; i++) {
          const singleResult = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, "GET");
          if (singleResult.code === 200 && singleResult.data && singleResult.data.avatar) {
            avatars.push(singleResult.data.avatar);
          }
          if (i < count - 1) {
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
        }
        return {
          success: true,
          data: {
            avatars,
            gender: result.data.gender,
            type: result.data.type
          },
          message: "生成成功"
        };
      } else {
        throw new Error(result.message || "生成失败");
      }
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 显卡天梯图
   */
  async getGpuLadder() {
    try {
      utils_index.showLoading("加载中...");
      const result = await this.callToolAPI("/api/tools/fun/gpu-ladder", "GET");
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * CPU天梯图
   */
  async getCpuLadder() {
    try {
      utils_index.showLoading("加载中...");
      const result = await this.callToolAPI("/api/tools/fun/cpu-ladder", "GET");
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  // ============ 程序员工具 ============
  /**
   * 生成网站快照
   */
  async generateWebsiteSnapshot(params) {
    try {
      utils_index.showLoading("生成中...");
      const requestParams = {
        url: params.url,
        device: params.device || "desktop",
        format: params.format || "png",
        quality: params.quality || 90,
        delay: params.delay || 2,
        fullPage: params.fullPage || false,
        removeAds: params.removeAds || false
      };
      common_vendor.index.__f__("log", "at utils/toolService.js:852", "发送网站快照请求:", requestParams);
      const result = await this.callToolAPI("/api/tools/dev/website-snapshot", "POST", requestParams);
      common_vendor.index.__f__("log", "at utils/toolService.js:854", "网站快照返回结果:", result);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:859", "网站快照生成失败:", error);
      utils_index.hideLoading();
      throw error;
    }
  }
  // ============ 图片壁纸工具 ============
  /**
   * 随机手机壁纸
   */
  async getRandomMobileWallpaper(params) {
    try {
      utils_index.showLoading("加载中...");
      const result = await this.callToolAPI("/api/tools/wallpaper/random-mobile-wallpaper", "GET", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 获取随机头像
   */
  async getRandomAvatar(params) {
    try {
      utils_index.showLoading("获取中...");
      const result = await this.callToolAPI("/api/tools/wallpaper/random-avatar", "GET", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 获取随机加载图
   */
  async getRandomLoadingImage(params) {
    try {
      utils_index.showLoading("获取中...");
      const result = await this.callToolAPI("/api/tools/wallpaper/random-loading-image", "GET", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 随机PC壁纸
   */
  async getRandomPcWallpaper(params) {
    try {
      utils_index.showLoading("获取中...");
      const result = await this.callToolAPI("/api/tools/wallpaper/random-pc-wallpaper", "GET", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  /**
   * 动漫壁纸
   */
  async getAnimeWallpapers(params) {
    try {
      utils_index.showLoading("获取中...");
      const result = await this.callToolAPI("/api/tools/wallpaper/anime-wallpapers", "GET", params);
      utils_index.hideLoading();
      return result;
    } catch (error) {
      utils_index.hideLoading();
      throw error;
    }
  }
  // ============ 通用API调用方法 ============
  /**
   * 调用工具API
   */
  async callToolAPI(url, method = "GET", params = {}) {
    try {
      const requestUrl = this.baseUrl + url;
      common_vendor.index.__f__("log", "at utils/toolService.js:952", `发起${method}请求:`, requestUrl, params);
      let timeout = 1e4;
      let maxRetries = 1;
      if (url.includes("id-photo-maker")) {
        timeout = 12e4;
        maxRetries = 0;
      } else if (url.includes("website-snapshot")) {
        timeout = 12e4;
        maxRetries = 0;
      } else if (url.includes("video") || url.includes("image")) {
        timeout = 6e4;
      }
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          const response = await common_vendor.index.request({
            url: requestUrl,
            method,
            data: method === "GET" ? params : JSON.stringify(params),
            header: {
              "Content-Type": "application/json"
            },
            timeout
          });
          common_vendor.index.__f__("log", "at utils/toolService.js:981", "API响应:", response);
          if (response.statusCode !== 200) {
            throw new Error(`请求失败: ${response.statusCode}`);
          }
          const result = response.data;
          if (!result) {
            throw new Error("响应数据为空");
          }
          return result;
        } catch (error) {
          common_vendor.index.__f__("log", "at utils/toolService.js:996", `第${attempt + 1}次请求失败:`, error);
          if (attempt === maxRetries) {
            throw error;
          }
          await new Promise((resolve) => setTimeout(resolve, 1e3));
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/toolService.js:1008", "API调用失败:", error);
      throw error;
    }
  }
  // ============ 文件处理方法 ============
  /**
   * 选择并上传图片
   */
  async selectAndUploadImage() {
    return new Promise((resolve, reject) => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          resolve({
            path: tempFilePath,
            size: res.tempFiles[0].size
          });
        },
        fail: (err) => {
          reject(new Error("选择图片失败"));
        }
      });
    });
  }
  /**
   * 选择并上传音频
   */
  async selectAndUploadAudio() {
    return new Promise((resolve, reject) => {
      common_vendor.index.chooseFile({
        count: 1,
        type: "file",
        extension: ["mp3", "wav", "aac"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          resolve({
            path: tempFilePath,
            size: res.tempFiles[0].size
          });
        },
        fail: (err) => {
          reject(new Error("选择音频失败"));
        }
      });
    });
  }
  /**
   * 选择并上传视频
   */
  async selectAndUploadVideo() {
    return new Promise((resolve, reject) => {
      common_vendor.index.chooseVideo({
        sourceType: ["album", "camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          resolve({
            path: res.tempFilePath,
            duration: res.duration,
            size: res.size
          });
        },
        fail: (err) => {
          reject(new Error("选择视频失败"));
        }
      });
    });
  }
  /**
   * 下载文件
   */
  async downloadFile(url, filename) {
    return new Promise((resolve, reject) => {
      common_vendor.index.downloadFile({
        url,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                resolve(res.tempFilePath);
              },
              fail: (err) => {
                reject(new Error("保存失败"));
              }
            });
          } else {
            reject(new Error("下载失败"));
          }
        },
        fail: (err) => {
          reject(new Error("下载失败"));
        }
      });
    });
  }
  /**
   * 预览图片
   */
  previewImage(urls, current = 0) {
    common_vendor.index.previewImage({
      urls: Array.isArray(urls) ? urls : [urls],
      current
    });
  }
  /**
   * 判断是否为图片URL
   */
  isImageUrl(url) {
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  }
  /**
   * 复制URL到剪贴板
   */
  async copyUrl(url) {
    return new Promise((resolve, reject) => {
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          utils_index.showSuccess("链接已复制到剪贴板");
          resolve();
        },
        fail: (err) => {
          reject(new Error("复制失败"));
        }
      });
    });
  }
  /**
   * 分享内容
   */
  async shareContent(title, url, imageUrl) {
    return new Promise((resolve, reject) => {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: url,
        title,
        summary: title,
        imageUrl,
        success: () => {
          resolve();
        },
        fail: (err) => {
          reject(new Error("分享失败"));
        }
      });
    });
  }
  /**
   * 魔法去水印
   * @param {string} imageUrl - 图片URL
   * @param {object} params - 处理参数
   * @returns {Promise} 处理结果
   */
  async removeMagicWatermark(imageUrl, params = {}) {
    try {
      utils_index.showLoading("魔法去水印处理中...");
      await this.ensureAuthenticated();
      let imageFile = null;
      imageFile = imageUrl;
      const requestParams = {
        algorithm: "lama",
        autoDetect: "true",
        outputFormat: "png",
        quality: params.mode === "quick" ? "medium" : "high"
      };
      if (params.watermarkAreas) {
        requestParams.watermarkAreas = JSON.stringify(params.watermarkAreas);
        requestParams.autoDetect = "false";
      }
      const result = await this.callImageWatermarkAPI("/api/tools/media/image-watermark-removal/single", imageFile, requestParams);
      utils_index.hideLoading();
      if (result.success) {
        const taskId = result.data.taskId;
        const finalResult = await this.pollImageProcessResult(taskId);
        return {
          success: true,
          data: {
            processedImageUrl: finalResult.processedUrl,
            detectedWatermarks: finalResult.detectedWatermarks || 1,
            removedWatermarks: finalResult.removedWatermarks || 1,
            processingTime: finalResult.processingTimeMs ? Math.round(finalResult.processingTimeMs / 1e3) : 15,
            qualityScore: finalResult.qualityScore || 95
          }
        };
      } else {
        throw new Error(result.message || "处理失败");
      }
    } catch (error) {
      utils_index.hideLoading();
      common_vendor.index.__f__("error", "at utils/toolService.js:1250", "魔法去水印失败:", error);
      throw error;
    }
  }
  /**
   * 调用图片水印处理API
   * @param {string} endpoint - API端点
   * @param {File|string} imageFile - 图片文件或路径
   * @param {object} params - 请求参数
   * @returns {Promise} API响应
   */
  async callImageWatermarkAPI(endpoint, imageFile, params) {
    return new Promise((resolve, reject) => {
      common_vendor.index.uploadFile({
        url: this.baseUrl + endpoint,
        filePath: imageFile,
        name: "file",
        formData: params,
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            resolve(data);
          } catch (error) {
            reject(new Error("响应解析失败"));
          }
        },
        fail: reject
      });
    });
  }
  /**
   * 轮询图片处理结果
   * @param {string} taskId - 任务ID
   * @returns {Promise} 处理结果
   */
  async pollImageProcessResult(taskId) {
    const maxAttempts = 30;
    const interval = 2e3;
    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        const progressResult = await this.request({
          url: `/api/tools/media/image-watermark-removal/progress/${taskId}`,
          method: "GET"
        });
        if (progressResult.success) {
          const progressData = progressResult.data;
          if (progressData.status === "COMPLETED") {
            const finalResult = await this.request({
              url: `/api/tools/media/image-watermark-removal/result/${taskId}`,
              method: "GET"
            });
            if (finalResult.success && finalResult.data.results && finalResult.data.results.length > 0) {
              return finalResult.data.results[0];
            } else {
              throw new Error("获取处理结果失败");
            }
          } else if (progressData.status === "FAILED") {
            throw new Error("图片处理失败");
          }
        }
        await new Promise((resolve) => setTimeout(resolve, interval));
      } catch (error) {
        common_vendor.index.__f__("error", "at utils/toolService.js:1342", "轮询处理结果失败:", error);
        if (attempt === maxAttempts - 1) {
          throw error;
        }
      }
    }
    throw new Error("处理超时");
  }
  /**
   * 获取工具配置
   */
  getToolConfig(toolIdentifier) {
    const toolConfigs = {
      "fun-image-generator": {
        name: "趣味图片生成器",
        icon: "🎨",
        needsVip: false,
        maxFileSize: 10 * 1024 * 1024,
        // 10MB
        supportedFormats: ["jpg", "png", "gif"]
      },
      "get-vip-membership": {
        name: "获取永久会员",
        icon: "👑",
        needsVip: false,
        description: "获取永久会员权益"
      },
      "video-watermark-remover": {
        name: "视频去水印",
        icon: "🎬",
        needsVip: true,
        maxFileSize: 100 * 1024 * 1024,
        // 100MB
        supportedFormats: ["mp4", "avi", "mov"]
      },
      "magic-watermark-remover": {
        name: "魔法去水印",
        icon: "🪄",
        needsVip: false,
        maxFileSize: 10 * 1024 * 1024,
        // 10MB
        supportedFormats: ["jpg", "png", "gif", "webp"]
      }
      // 可以继续添加其他工具的配置...
    };
    return toolConfigs[toolIdentifier] || {};
  }
}
const toolService = new ToolService();
exports.ToolService = ToolService;
exports.toolService = toolService;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/toolService.js.map
