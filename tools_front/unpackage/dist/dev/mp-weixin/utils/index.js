"use strict";
const common_vendor = require("../common/vendor.js");
function showToast(options) {
  if (typeof options === "string") {
    options = { title: options };
  }
  const defaultOptions = {
    title: "",
    icon: "none",
    duration: 2e3,
    mask: false
  };
  const finalOptions = { ...defaultOptions, ...options };
  common_vendor.index.showToast(finalOptions);
}
function showSuccess(title) {
  showToast({
    title,
    icon: "success"
  });
}
function showError(title) {
  showToast({
    title,
    icon: "error"
  });
}
function showLoading(title = "加载中...") {
  common_vendor.index.showLoading({
    title,
    mask: true
  });
}
function hideLoading() {
  common_vendor.index.hideLoading();
}
exports.hideLoading = hideLoading;
exports.showError = showError;
exports.showLoading = showLoading;
exports.showSuccess = showSuccess;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/index.js.map
