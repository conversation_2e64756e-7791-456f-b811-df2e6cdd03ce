"use strict";
const common_vendor = require("../common/vendor.js");
const BASE_URL = "http://localhost:8080";
class AuthManager {
  constructor() {
    this.baseUrl = BASE_URL;
    this.token = null;
    this.userInfo = null;
    this.loginPromise = null;
  }
  /**
   * 获取存储的token
   */
  getToken() {
    if (!this.token) {
      try {
        this.token = common_vendor.index.getStorageSync("token");
      } catch (e) {
        common_vendor.index.__f__("error", "at utils/authManager.js:26", "获取token失败:", e);
      }
    }
    return this.token;
  }
  /**
   * 设置token
   */
  setToken(token) {
    this.token = token;
    try {
      common_vendor.index.setStorageSync("token", token);
    } catch (e) {
      common_vendor.index.__f__("error", "at utils/authManager.js:40", "保存token失败:", e);
    }
  }
  /**
   * 清除token
   */
  clearToken() {
    this.token = null;
    try {
      common_vendor.index.removeStorageSync("token");
      common_vendor.index.removeStorageSync("userInfo");
    } catch (e) {
      common_vendor.index.__f__("error", "at utils/authManager.js:53", "清除token失败:", e);
    }
  }
  /**
   * 获取用户信息
   */
  getUserInfo() {
    if (!this.userInfo) {
      try {
        const userInfoStr = common_vendor.index.getStorageSync("userInfo");
        if (userInfoStr) {
          this.userInfo = JSON.parse(userInfoStr);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at utils/authManager.js:68", "获取用户信息失败:", e);
      }
    }
    return this.userInfo;
  }
  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    this.userInfo = userInfo;
    try {
      common_vendor.index.setStorageSync("userInfo", JSON.stringify(userInfo));
    } catch (e) {
      common_vendor.index.__f__("error", "at utils/authManager.js:82", "保存用户信息失败:", e);
    }
  }
  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    const token = this.getToken();
    return token && token.length > 0;
  }
  /**
   * 微信登录
   */
  async wxLogin() {
    return new Promise((resolve, reject) => {
      if (this.loginPromise) {
        return this.loginPromise;
      }
      this.loginPromise = new Promise(async (loginResolve, loginReject) => {
        try {
          const loginResult = await this.getWxLoginCode();
          const userProfileResult = await this.getWxUserProfile();
          const loginData = {
            code: loginResult.code,
            nickName: userProfileResult.userInfo.nickName,
            avatarUrl: userProfileResult.userInfo.avatarUrl,
            gender: userProfileResult.userInfo.gender,
            country: userProfileResult.userInfo.country,
            province: userProfileResult.userInfo.province,
            city: userProfileResult.userInfo.city
          };
          const response = await this.callLoginAPI(loginData);
          this.setToken(response.token);
          this.setUserInfo(response);
          common_vendor.index.__f__("log", "at utils/authManager.js:129", "微信登录成功:", response);
          loginResolve(response);
        } catch (error) {
          common_vendor.index.__f__("error", "at utils/authManager.js:132", "微信登录失败:", error);
          loginReject(error);
        } finally {
          this.loginPromise = null;
        }
      });
      this.loginPromise.then(resolve).catch(reject);
    });
  }
  /**
   * 获取微信登录code
   */
  getWxLoginCode() {
    return new Promise((resolve, reject) => {
      common_vendor.index.login({
        provider: "weixin",
        success: (result) => {
          resolve(result);
        },
        fail: (error) => {
          reject(new Error("获取微信登录code失败"));
        }
      });
    });
  }
  /**
   * 获取微信用户信息
   */
  getWxUserProfile() {
    return new Promise((resolve, reject) => {
      common_vendor.index.getUserProfile({
        desc: "用于完善用户资料",
        success: (result) => {
          resolve(result);
        },
        fail: (error) => {
          resolve({
            userInfo: {
              nickName: "微信用户",
              avatarUrl: "/static/images/default-avatar.png",
              gender: 0,
              country: "中国",
              province: "",
              city: ""
            }
          });
        }
      });
    });
  }
  /**
   * 调用登录API
   */
  async callLoginAPI(loginData) {
    return new Promise((resolve, reject) => {
      common_vendor.index.request({
        url: `${this.baseUrl}/auth/login`,
        method: "POST",
        data: loginData,
        header: {
          "Content-Type": "application/json"
        },
        success: (response) => {
          const result = response.data;
          if (result.code === 200) {
            resolve(result.data);
          } else {
            reject(new Error(result.message || "登录失败"));
          }
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at utils/authManager.js:208", "登录API调用失败:", error);
          reject(new Error("网络请求失败"));
        }
      });
    });
  }
  /**
   * 无感登录（如果需要的话）
   */
  async silentLogin() {
    try {
      if (this.isLoggedIn()) {
        return true;
      }
      await this.wxLogin();
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/authManager.js:229", "无感登录失败:", error);
      return false;
    }
  }
  /**
   * 登出
   */
  logout() {
    this.clearToken();
    this.userInfo = null;
    common_vendor.index.__f__("log", "at utils/authManager.js:240", "用户已登出");
  }
  /**
   * 获取请求头（包含token）
   */
  getAuthHeaders() {
    const token = this.getToken();
    const headers = {
      "Content-Type": "application/json"
    };
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }
    return headers;
  }
}
const authManager = new AuthManager();
exports.authManager = authManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/authManager.js.map
