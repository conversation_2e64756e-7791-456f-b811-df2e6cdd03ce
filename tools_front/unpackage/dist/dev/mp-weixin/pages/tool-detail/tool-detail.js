"use strict";
const common_vendor = require("../../common/vendor.js");
const data_toolsData = require("../../data/toolsData.js");
const _sfc_main = {
  data() {
    return {
      toolId: null,
      toolInfo: null,
      features: [],
      usageSteps: [],
      // 预设渐变色方案
      gradients: {
        media: "linear-gradient(135deg, #a855f7 0%, #ec4899 100%)",
        // 紫粉渐变 - 媒体工具
        fun: "linear-gradient(135deg, #22c55e 0%, #0ea5e9 100%)",
        // 绿蓝渐变 - 趣味工具
        color: "linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%)",
        // 蓝紫渐变 - 颜色工具
        text: "linear-gradient(135deg, #f97316 0%, #ef4444 100%)",
        // 橙红渐变 - 文字工具
        dev: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
        // 蓝色渐变 - 程序员工具
        util: "linear-gradient(135deg, #14b8a6 0%, #0d9488 100%)",
        // 青色渐变 - 实用工具
        image: "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)",
        // 金色渐变 - 图片壁纸
        recommend: "linear-gradient(135deg, #ec4899 0%, #f43f5e 100%)"
        // 粉红渐变 - 好玩推荐
      }
    };
  },
  computed: {
    // 根据工具类别返回对应的渐变色
    bannerGradient() {
      if (!this.toolInfo)
        return this.gradients.recommend;
      const categoryGradients = {
        "媒体工具": this.gradients.media,
        "趣味工具": this.gradients.fun,
        "颜色工具": this.gradients.color,
        "文字工具": this.gradients.text,
        "程序员工具": this.gradients.dev,
        "实用工具": this.gradients.util,
        "图片壁纸": this.gradients.image,
        "好玩推荐": this.gradients.recommend
      };
      return categoryGradients[this.toolInfo.category] || this.gradients.recommend;
    }
  },
  onLoad(options) {
    if (options.id) {
      this.toolId = parseInt(options.id);
      this.loadToolDetail();
    }
  },
  methods: {
    loadToolDetail() {
      this.toolInfo = data_toolsData.getToolById(this.toolId);
      if (this.toolInfo) {
        common_vendor.index.setNavigationBarTitle({
          title: this.toolInfo.name
        });
        this.loadFeatures();
        this.loadUsageSteps();
      }
    },
    getToolIntroduction() {
      if (!this.toolInfo)
        return "";
      const introductions = {
        1: "使用人工智能技术，根据您的描述生成各种有趣、创意的图片。支持多种风格，包括卡通、写实、抽象等，让您的创意想法变成精美的视觉作品。",
        2: "制作浪漫表白代码，支持多种编程语言和样式。无论是向心爱的人表白，还是制作有趣的程序，都能帮您实现创意想法。"
      };
      return introductions[this.toolId] || this.toolInfo.description;
    },
    loadFeatures() {
      const featureMap = {
        1: [
          // 趣味图片生成器
          "AI智能生成",
          "多种艺术风格",
          "高清图片输出",
          "快速生成"
        ],
        2: [
          // 表白代码制作
          "简单易用，无需复杂操作",
          "完全免费，无广告干扰",
          "支持多种格式和选项",
          "实时预览和即时反馈"
        ]
      };
      this.features = featureMap[this.toolId] || [
        "简单易用，无需复杂操作",
        "完全免费，无广告干扰",
        "支持多种格式和选项",
        "实时预览和即时反馈"
      ];
    },
    loadUsageSteps() {
      const stepsMap = {
        1: [
          // 趣味图片生成器
          "输入图片描述",
          "选择生成风格",
          "点击生成图片",
          "下载保存图片"
        ],
        2: [
          // 表白代码制作
          '点击"立即使用"按钮进入工具页面',
          "根据提示输入或选择相关参数",
          "点击执行按钮获取结果",
          "复制或保存处理结果"
        ]
      };
      this.usageSteps = stepsMap[this.toolId] || [
        '点击"立即使用"按钮进入工具页面',
        "根据提示输入或选择相关参数",
        "点击执行按钮获取结果",
        "复制或保存处理结果"
      ];
    },
    useTool() {
      const toolPageMap = {
        // 好玩推荐
        1: "/pages/tools/fun-image-generator",
        // 趣味图片生成器
        2: "/pages/tools/permanent-membership",
        // 获取永久会员
        3: "/pages/tools/confession-code-maker",
        // 表白代码制作
        // 媒体工具
        4: "/pages/tools/video-watermark-remover",
        // 视频解析去水印
        5: "/pages/tools/image-watermark-remover",
        // 图集解析去水印
        6: "/pages/tools/video-downloader",
        // 视频H下载
        7: "/pages/tools/audio-editor",
        // 音频剪辑
        8: "/pages/tools/video-to-audio",
        // 视频转音频
        9: "/pages/tools/music-downloader",
        // 音乐下载器
        10: "/pages/tools/text-extractor",
        // 文案提取器
        11: "/pages/tools/nine-grid-cutter",
        // 九宫格切图
        12: "/pages/tools/image-compressor",
        // 图片压缩
        13: "/pages/tools/md5-modifier",
        // 修改MD5
        14: "/pages/tools/screenshot-frame",
        // 截图加壳
        15: "/pages/tools/game-voice-synthesis",
        // 游戏语音合成
        16: "/pages/tools/sound-effects",
        // 音效大全
        17: "/pages/tools/image-stitcher",
        // 图片拼接
        18: "/pages/tools/black-white-converter",
        // 黑白图转换
        19: "/pages/tools/image-watermarker",
        // 图片加水印
        20: "/pages/tools/image-mosaic-maker",
        // 图片打码
        21: "/pages/tools/magic-watermark-remover",
        // 魔法抹除水印
        22: "/pages/tools/id-photo-maker",
        // 证件照制作
        23: "/pages/tools/video-compressor",
        // 视频压缩
        24: "/pages/tools/image-mirror-flipper",
        // 图片镜像翻转
        25: "/pages/tools/image-pixelizer",
        // 图片像素化
        26: "/pages/tools/hidden-image-maker",
        // 隐藏图制作
        // 颜色工具
        28: "/pages/tools/color-scheme-generator",
        // 色卡配色
        29: "/pages/tools/rgb-hex-converter",
        // RGB与HEX
        30: "/pages/tools/image-color-picker",
        // 图片取色
        31: "/pages/tools/gradient-palette",
        // 渐变色卡
        32: "/pages/tools/gradient-code-generator",
        // 渐变代码生成
        33: "/pages/tools/contrast-checker",
        // 对比度检测
        35: "/pages/tools/hue-generator",
        // 色调生成器
        // 实用工具
        36: "/pages/tools/vehicle-price-query",
        // 车辆价格查询
        37: "/pages/tools/national-gas-price",
        // 全国油价
        38: "/pages/tools/common-numbers",
        // 常用号码 
        39: "/pages/tools/phone-cleaner",
        // 手机清灰
        40: "/pages/tools/handheld-barrage",
        // 手持弹幕
        41: "/pages/tools/bmi-calculator",
        // BMI计算器
        42: "/pages/tools/national-weather-query",
        // 全国天气查询
        43: "/pages/tools/random-number-generator",
        // 生成随机数
        44: "/pages/tools/safe-period-calculator",
        // 安全期计算
        // 趣味工具
        45: "/pages/tools/beast-language",
        // 兽语加密解密
        46: "/pages/tools/biased-wheel",
        // 偏心大转盘
        47: "/pages/tools/time-calculator",
        // 起始时间计算
        48: "/pages/tools/history-today",
        // 历史上的今天
        49: "/pages/tools/eating-gif-avatar",
        // 吃掉GIF头像
        50: "/pages/tools/ip-signature",
        // IP签名档
        51: "/pages/tools/fullscreen-clock",
        // 全屏时钟
        52: "/pages/tools/mock-call",
        // 模拟来电
        53: "/pages/tools/phone-detector",
        // 手机检测
        54: "/pages/tools/life-time",
        // 一生时间
        55: "/pages/tools/gpu-ladder",
        // 显卡天梯图
        56: "/pages/tools/cpu-ladder",
        // CPU天梯图
        57: "/pages/tools/virtual-wooden-fish",
        // 功德木鱼
        58: "/pages/tools/portable-fan",
        // 便携风扇 
        59: "/pages/tools/dice-roller",
        // 摇骰子
        // 程序员工具
        60: "/pages/tools/px-em-converter",
        // PX与EM
        61: "/pages/tools/timestamp-converter",
        // 时间戳工具
        62: "/pages/tools/md5-encryptor",
        // MD5加密
        63: "/pages/tools/ascii-converter",
        // ASCII转换 
        64: "/pages/tools/animation-code-generator",
        // 动画代码生成 
        65: "/pages/tools/grid-layout-generator",
        // Grid布局生成
        66: "/pages/tools/flex-layout-generator",
        // Flex布局生成
        67: "/pages/tools/glass-effect-generator",
        // 磨砂玻璃生成
        68: "/pages/tools/css-text-effects",
        // CSS文字效果 
        69: "/pages/tools/website-snapshot",
        // 生成网站快照
        70: "/pages/tools/port-checker",
        // 端口检测
        71: "/pages/tools/unicode-converter",
        // Unicode
        72: "/pages/tools/http-status-codes",
        // HTTP状态码 
        73: "/pages/tools/http-headers",
        // HTTP请求头
        74: "/pages/tools/key-code-checker",
        // keyCode按键码
        75: "/pages/tools/linux-commands",
        // Linux常用命令
        76: "/pages/tools/password-generator",
        // 密码image.png生成器
        77: "/pages/tools/common-ports",
        // 常用端口大全
        78: "/pages/tools/user-agent-list",
        // UA标识大全
        79: "/pages/tools/uuid-generator",
        // UUID生成器
        // 文字工具
        80: "/pages/tools/sad-text-library",
        // 伤感文案库 
        81: "/pages/tools/crazy-thursday-text",
        // 疯狂星期四文案
        82: "/pages/tools/answer-book",
        // 答案之书
        83: "/pages/tools/famous-quotes",
        // 名言警句
        84: "/pages/tools/text-library",
        // 文案大全
        85: "/pages/tools/wifi-nickname",
        // WiFi昵称
        86: "/pages/tools/special-tail",
        // 特殊小尾巴 
        87: "/pages/tools/braid-nickname",
        // 小辫子昵称
        88: "/pages/tools/text-reverser",
        // 文本逆序
        89: "/pages/tools/superscript-phone",
        // 上标电话
        90: "/pages/tools/subscript-phone",
        // 下标电话 
        91: "/pages/tools/scream-text",
        // 尖叫文字
        92: "/pages/tools/small-letter-nickname",
        // 小字母昵称
        93: "/pages/tools/case-converter",
        // 大小写转换
        94: "/pages/tools/heart-text",
        // 爱心文字
        95: "/pages/tools/text-nine-grid",
        // 文字九宫格
        96: "/pages/tools/bordered-text",
        // 带框文字
        97: "/pages/tools/emoji-collection",
        // 颜文字合集
        98: "/pages/tools/strike-through-text",
        // 删除线文字
        99: "/pages/tools/love-emoji",
        // 爱心颜文字
        100: "/pages/tools/love-text-520",
        // 520字
        101: "/pages/tools/arrow-text",
        // 箭头文字
        102: "/pages/tools/life-counter",
        // 人间凑数的日子
        103: "/pages/tools/tank-text",
        // 坦克文字
        104: "/pages/tools/helicopter-text",
        // 直升机文字
        105: "/pages/tools/music-text",
        // 音乐文字
        106: "/pages/tools/crazy-quotes",
        // 发疯语录
        107: "/pages/tools/love-apartment-quotes",
        // 爱情公寓语录
        108: "/pages/tools/love-quotes",
        // 随机情话
        // 图片壁纸
        109: "/pages/tools/random-avatar",
        // 随机头像
        110: "/pages/tools/random-loading-image",
        // 随机加载图
        111: "/pages/tools/random-mobile-wallpaper",
        // 随机手机壁纸
        112: "/pages/tools/random-pc-wallpaper",
        // 随机PC壁纸
        115: "/pages/tools/text-wallpaper",
        // 文字壁纸大全
        116: "/pages/tools/anime-wallpaper"
        // 动漫壁纸
      };
      if (toolPageMap[this.toolId]) {
        common_vendor.index.navigateTo({
          url: toolPageMap[this.toolId]
        });
      } else {
        common_vendor.index.showToast({
          title: "该工具正在开发中",
          icon: "none"
        });
      }
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "4c139954": _ctx.bannerGradient
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.toolInfo
  }, $data.toolInfo ? {
    b: common_vendor.t($data.toolInfo.icon),
    c: common_vendor.t($data.toolInfo.name),
    d: common_vendor.t($data.toolInfo.description)
  } : {}, {
    e: common_vendor.t($options.getToolIntroduction()),
    f: common_vendor.f($data.features, (feature, index, i0) => {
      return {
        a: common_vendor.t(feature),
        b: index
      };
    }),
    g: common_vendor.f($data.usageSteps, (step, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(step),
        c: index
      };
    }),
    h: common_vendor.o((...args) => $options.useTool && $options.useTool(...args)),
    i: common_vendor.s(_ctx.__cssVars())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7915bd95"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tool-detail/tool-detail.js.map
