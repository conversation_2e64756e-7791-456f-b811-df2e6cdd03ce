<view class="md5-encryptor data-v-c2cde938"><view class="header-card data-v-c2cde938"><view class="card-content data-v-c2cde938"><view class="header-info data-v-c2cde938"><text class="header-icon data-v-c2cde938">🔐</text><view class="header-text data-v-c2cde938"><text class="header-title data-v-c2cde938">MD5加密工具</text><text class="header-desc data-v-c2cde938">安全可靠的MD5散列值生成器，支持单个和批量加密</text></view></view></view></view><view class="md5-main-card data-v-c2cde938"><view class="card-header data-v-c2cde938"><text class="card-title data-v-c2cde938">🔒 文本MD5加密</text><button wx:if="{{a}}" bindtap="{{b}}" class="clear-btn data-v-c2cde938"><text class="data-v-c2cde938">🗑️ 清空</text></button></view><view class="card-content data-v-c2cde938"><view class="input-section data-v-c2cde938"><text class="input-label data-v-c2cde938">输入文本（支持单行或多行，每行一个）</text><block wx:if="{{r0}}"><textarea placeholder="请输入要加密的文本，每行一个" class="text-input data-v-c2cde938" maxlength="{{5000}}" auto-height value="{{c}}" bindinput="{{d}}"/></block><view class="input-footer data-v-c2cde938"><text class="line-count data-v-c2cde938">{{e}}行文本</text><text class="char-count data-v-c2cde938">{{f}}/5000</text></view></view><button bindtap="{{g}}" disabled="{{h}}" class="{{['data-v-c2cde938', 'encrypt-btn', i]}}"><text class="data-v-c2cde938">🔐 生成MD5</text></button><view wx:if="{{j}}" class="batch-results data-v-c2cde938"><view class="results-header data-v-c2cde938"><text class="results-title data-v-c2cde938">加密结果 ({{k}}项)</text><view class="results-actions data-v-c2cde938"><button bindtap="{{l}}" class="export-btn data-v-c2cde938"><text class="data-v-c2cde938">📥 导出</text></button><button bindtap="{{m}}" class="copy-all-btn data-v-c2cde938"><text class="data-v-c2cde938">📋 全部复制</text></button></view></view><view class="results-list data-v-c2cde938"><view wx:for="{{n}}" wx:for-item="result" wx:key="d" class="result-item data-v-c2cde938" bindtap="{{result.e}}"><view class="result-header data-v-c2cde938"><text class="original-text data-v-c2cde938">{{result.a}}</text><text class="result-index data-v-c2cde938">#{{result.b}}</text></view><view class="result-hash data-v-c2cde938"><text class="hash-value data-v-c2cde938">{{result.c}}</text><view class="copy-icon data-v-c2cde938">📋</view></view></view></view></view></view></view><view class="tips-card data-v-c2cde938"><view class="card-header data-v-c2cde938"><text class="card-title data-v-c2cde938">💡 使用说明</text></view><view class="card-content data-v-c2cde938"><view class="tips-list data-v-c2cde938"><view class="tip-item data-v-c2cde938"><text class="tip-bullet data-v-c2cde938">•</text><text class="tip-text data-v-c2cde938"><text class="tip-bold data-v-c2cde938">MD5算法:</text> 消息摘要算法，生成128位散列值</text></view><view class="tip-item data-v-c2cde938"><text class="tip-bullet data-v-c2cde938">•</text><text class="tip-text data-v-c2cde938"><text class="tip-bold data-v-c2cde938">不可逆性:</text> 无法从MD5值推导出原始文本</text></view><view class="tip-item data-v-c2cde938"><text class="tip-bullet data-v-c2cde938">•</text><text class="tip-text data-v-c2cde938"><text class="tip-bold data-v-c2cde938">唯一性:</text> 相同输入始终产生相同的MD5值</text></view><view class="tip-item data-v-c2cde938"><text class="tip-bullet data-v-c2cde938">•</text><text class="tip-text data-v-c2cde938"><text class="tip-bold data-v-c2cde938">应用场景:</text> 密码加密、数据完整性校验</text></view><view class="tip-item data-v-c2cde938"><text class="tip-bullet data-v-c2cde938">•</text><text class="tip-text data-v-c2cde938"><text class="tip-bold data-v-c2cde938">安全提示:</text> 重要数据建议使用更强的加密算法</text></view></view></view></view></view>