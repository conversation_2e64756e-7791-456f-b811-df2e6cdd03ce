<view class="color-picker-modal data-v-19c31dd0" bindtap="{{o}}"><view class="color-picker-container data-v-19c31dd0" catchtap="{{n}}"><view class="picker-header data-v-19c31dd0"><text class="picker-title data-v-19c31dd0">选择颜色</text><view class="picker-close data-v-19c31dd0" bindtap="{{a}}"><text class="close-icon data-v-19c31dd0">×</text></view></view><view class="preset-colors data-v-19c31dd0"><view wx:for="{{b}}" wx:for-item="color" wx:key="a" class="preset-color data-v-19c31dd0" style="{{'background-color:' + color.b + ';' + ('border:' + color.c)}}" bindtap="{{color.d}}"/></view><view class="hex-input-group data-v-19c31dd0"><text class="hex-label data-v-19c31dd0">HEX</text><input class="hex-input data-v-19c31dd0" maxlength="7" bindinput="{{c}}" placeholder="#FFFFFF" value="{{d}}"/><view class="preview-color data-v-19c31dd0" style="{{'background-color:' + e}}"></view></view><view class="rgb-inputs data-v-19c31dd0"><view class="rgb-group data-v-19c31dd0"><text class="rgb-label data-v-19c31dd0">R</text><input class="rgb-input data-v-19c31dd0" type="number" bindinput="{{f}}" min="0" max="255" value="{{g}}"/></view><view class="rgb-group data-v-19c31dd0"><text class="rgb-label data-v-19c31dd0">G</text><input class="rgb-input data-v-19c31dd0" type="number" bindinput="{{h}}" min="0" max="255" value="{{i}}"/></view><view class="rgb-group data-v-19c31dd0"><text class="rgb-label data-v-19c31dd0">B</text><input class="rgb-input data-v-19c31dd0" type="number" bindinput="{{j}}" min="0" max="255" value="{{k}}"/></view></view><view class="picker-actions data-v-19c31dd0"><view class="picker-btn cancel data-v-19c31dd0" bindtap="{{l}}">取消</view><view class="picker-btn confirm data-v-19c31dd0" bindtap="{{m}}">确认</view></view></view></view>