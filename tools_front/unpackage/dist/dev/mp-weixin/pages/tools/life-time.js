"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "TimeFlows",
  data() {
    return {
      birthDate: "",
      lifeExpectancy: 80,
      currentQuoteIndex: 0,
      quotes: [
        { text: "时间是最公平的，每个人每天都只有24小时。", author: "鲁迅" },
        { text: "一寸光阴一寸金，寸金难买寸光阴。", author: "古语" },
        { text: "时间就是生命，无故的空耗别人的时间，实在无异于谋财害命。", author: "鲁迅" },
        { text: "盛年不重来，一日难再晨。", author: "陶渊明" },
        { text: "时间最不偏私，给任何人都是二十四小时；时间也是偏私，给任何人都不是二十四小时。", author: "赫胥黎" },
        { text: "你热爱生命吗？那么别浪费时间，因为时间是组成生命的材料。", author: "富兰克林" },
        { text: "时间是世界上一切成就的土壤。", author: "麦金西" },
        { text: "完成工作的方法是爱惜每一分钟。", author: "达尔文" }
      ],
      lifeStages: [
        { name: "幼儿期", startAge: 0, endAge: 6, description: "探索世界，学习基础技能" },
        { name: "学童期", startAge: 7, endAge: 17, description: "接受教育，建立世界观" },
        { name: "青年期", startAge: 18, endAge: 30, description: "追求梦想，建立事业" },
        { name: "成年期", startAge: 31, endAge: 50, description: "承担责任，积累经验" },
        { name: "中年期", startAge: 51, endAge: 65, description: "成熟稳重，传承智慧" },
        { name: "老年期", startAge: 66, endAge: 120, description: "享受生活，回顾人生" }
      ],
      milestones: [
        { age: 18, label: "成年" },
        { age: 25, label: "青春" },
        { age: 30, label: "而立" },
        { age: 40, label: "不惑" },
        { age: 50, label: "知命" },
        { age: 60, label: "花甲" },
        { age: 70, label: "古稀" }
      ]
    };
  },
  computed: {
    currentAge() {
      if (!this.birthDate)
        return 0;
      const birth = new Date(this.birthDate);
      const today = /* @__PURE__ */ new Date();
      return (today - birth) / (1e3 * 60 * 60 * 24 * 365.25);
    },
    livedYears() {
      return this.currentAge;
    },
    remainingYears() {
      return Math.max(0, this.lifeExpectancy - this.currentAge);
    },
    progressPercentage() {
      return Math.min(100, Math.max(0, this.currentAge / this.lifeExpectancy * 100)).toFixed(1);
    },
    livedDays() {
      if (!this.birthDate)
        return 0;
      const birth = new Date(this.birthDate);
      const today = /* @__PURE__ */ new Date();
      return Math.floor((today - birth) / (1e3 * 60 * 60 * 24));
    },
    livedWeeks() {
      return Math.floor(this.livedDays / 7);
    },
    livedMonths() {
      if (!this.birthDate)
        return 0;
      const birth = new Date(this.birthDate);
      const today = /* @__PURE__ */ new Date();
      return (today.getFullYear() - birth.getFullYear()) * 12 + (today.getMonth() - birth.getMonth());
    },
    livedHours() {
      return Math.floor(this.livedDays * 24);
    },
    remainingDays() {
      return Math.max(0, Math.floor(this.remainingYears * 365.25));
    },
    totalExpectedDays() {
      return Math.floor(this.lifeExpectancy * 365.25);
    },
    currentQuote() {
      return this.quotes[this.currentQuoteIndex];
    },
    currentStage() {
      const stage = this.lifeStages.find(
        (stage2) => this.currentAge >= stage2.startAge && this.currentAge <= stage2.endAge
      );
      return stage || this.lifeStages[0];
    }
  },
  mounted() {
    this.loadSettings();
    this.setRandomQuote();
  },
  methods: {
    onBirthDateChange(e) {
      this.birthDate = e.detail.value;
      this.saveSettings();
    },
    onLifeExpectancyChange(e) {
      this.lifeExpectancy = parseInt(e.detail.value);
      this.saveSettings();
    },
    refreshQuote() {
      this.setRandomQuote();
      common_vendor.index.showToast({
        title: "语录已刷新",
        icon: "success",
        duration: 1500
      });
    },
    setRandomQuote() {
      this.currentQuoteIndex = Math.floor(Math.random() * this.quotes.length);
    },
    loadSettings() {
      const settings = common_vendor.index.getStorageSync("timeFlowsSettings");
      if (settings) {
        this.birthDate = settings.birthDate || "";
        this.lifeExpectancy = settings.lifeExpectancy || 80;
      }
    },
    saveSettings() {
      const settings = {
        birthDate: this.birthDate,
        lifeExpectancy: this.lifeExpectancy
      };
      common_vendor.index.setStorageSync("timeFlowsSettings", settings);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.birthDate || "请选择出生日期"),
    b: $data.birthDate,
    c: common_vendor.o((...args) => $options.onBirthDateChange && $options.onBirthDateChange(...args)),
    d: $data.lifeExpectancy,
    e: common_vendor.o((...args) => $options.onLifeExpectancyChange && $options.onLifeExpectancyChange(...args)),
    f: common_vendor.t($data.lifeExpectancy),
    g: $data.birthDate
  }, $data.birthDate ? {
    h: common_vendor.t($options.progressPercentage),
    i: $options.progressPercentage + "%",
    j: common_vendor.t($options.livedYears.toFixed(1)),
    k: common_vendor.t($options.remainingYears.toFixed(1)),
    l: common_vendor.f($data.milestones, (milestone, k0, i0) => {
      return {
        a: common_vendor.t(milestone.label),
        b: common_vendor.n({
          "reached": $options.currentAge >= milestone.age
        }),
        c: milestone.age / $data.lifeExpectancy * 100 + "%",
        d: milestone.age
      };
    })
  } : {}, {
    m: $data.birthDate
  }, $data.birthDate ? {
    n: common_vendor.t($options.livedDays.toLocaleString()),
    o: common_vendor.t($options.livedWeeks.toLocaleString()),
    p: common_vendor.t($options.livedMonths),
    q: common_vendor.t($options.livedHours.toLocaleString()),
    r: common_vendor.t($options.remainingDays.toLocaleString()),
    s: common_vendor.t($options.totalExpectedDays.toLocaleString())
  } : {}, {
    t: common_vendor.o((...args) => $options.refreshQuote && $options.refreshQuote(...args)),
    v: common_vendor.t($options.currentQuote.text),
    w: common_vendor.t($options.currentQuote.author),
    x: $data.birthDate
  }, $data.birthDate ? {
    y: common_vendor.t($options.currentStage.name),
    z: common_vendor.t($options.currentStage.description),
    A: common_vendor.f($data.lifeStages, (stage, k0, i0) => {
      return {
        a: common_vendor.t(stage.name),
        b: common_vendor.t(stage.startAge),
        c: common_vendor.t(stage.endAge),
        d: stage.name,
        e: common_vendor.n({
          "completed": $options.currentAge > stage.endAge,
          "current": $options.currentAge >= stage.startAge && $options.currentAge <= stage.endAge,
          "future": $options.currentAge < stage.startAge
        })
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-78b447dc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/life-time.js.map
