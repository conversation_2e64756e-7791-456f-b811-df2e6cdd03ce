/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a470eddc {
  display: flex;
}
.flex-1.data-v-a470eddc {
  flex: 1;
}
.items-center.data-v-a470eddc {
  align-items: center;
}
.justify-center.data-v-a470eddc {
  justify-content: center;
}
.justify-between.data-v-a470eddc {
  justify-content: space-between;
}
.text-center.data-v-a470eddc {
  text-align: center;
}
.rounded.data-v-a470eddc {
  border-radius: 3px;
}
.rounded-lg.data-v-a470eddc {
  border-radius: 6px;
}
.shadow.data-v-a470eddc {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a470eddc {
  padding: 16rpx;
}
.m-4.data-v-a470eddc {
  margin: 16rpx;
}
.mb-4.data-v-a470eddc {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a470eddc {
  margin-top: 16rpx;
}
.header.data-v-a470eddc {
  padding: 24rpx;
}
.header .header-content.data-v-a470eddc {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  padding: 40rpx;
  border-radius: 24rpx;
  text-align: center;
}
.header .header-content .title-wrap .title.data-v-a470eddc {
  font-size: 44rpx;
  color: #ffffff;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.header .header-content .title-wrap .subtitle-wrap.data-v-a470eddc {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.header .header-content .title-wrap .subtitle-wrap .subtitle.data-v-a470eddc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}
.upload-section.data-v-a470eddc {
  margin: 24rpx;
}
.upload-section .upload-card.data-v-a470eddc {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  margin-bottom: 24rpx;
}
.upload-section .upload-card .upload-icon.data-v-a470eddc {
  font-size: 80rpx;
  color: #6366f1;
  margin-bottom: 24rpx;
}
.upload-section .upload-card .upload-text.data-v-a470eddc {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}
.upload-section .upload-card .upload-desc.data-v-a470eddc {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.5;
}
.upload-section .take-photo-btn.data-v-a470eddc {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.upload-section .take-photo-btn .camera-icon.data-v-a470eddc {
  font-size: 36rpx;
}
.upload-section .take-photo-btn text.data-v-a470eddc {
  font-size: 28rpx;
  color: #475569;
}
.size-section.data-v-a470eddc, .color-section.data-v-a470eddc {
  margin: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.size-section .section-title.data-v-a470eddc, .color-section .section-title.data-v-a470eddc {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 24rpx;
}
.size-list.data-v-a470eddc {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.size-list .size-item.data-v-a470eddc {
  padding: 24rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
}
.size-list .size-item.active.data-v-a470eddc {
  background: #f0f7ff;
  border-color: #6366f1;
}
.size-list .size-item .size-name.data-v-a470eddc {
  font-size: 28rpx;
  color: #1e293b;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}
.size-list .size-item .size-dimensions.data-v-a470eddc {
  font-size: 24rpx;
  color: #64748b;
}
.color-section.data-v-a470eddc {
  margin: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
}
.color-section .section-title.data-v-a470eddc {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
  margin-bottom: 32rpx;
}
.color-section .color-list.data-v-a470eddc {
  display: flex;
  justify-content: space-around;
  padding: 0 20rpx;
}
.color-section .color-list .color-item.data-v-a470eddc {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}
.color-section .color-list .color-item .color-preview.data-v-a470eddc {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  border: 2rpx solid #e2e8f0;
  transition: all 0.3s ease;
}
.color-section .color-list .color-item .color-preview.data-v-a470eddc:active {
  transform: scale(0.95);
}
.color-section .color-list .color-item .color-name.data-v-a470eddc {
  font-size: 28rpx;
  color: #1e293b;
}
.color-section .color-list .color-item.active .color-preview.data-v-a470eddc {
  border: 3rpx solid #6366f1;
  box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.1);
}
.color-section .color-list .color-item.active .color-name.data-v-a470eddc {
  color: #6366f1;
  font-weight: 500;
}
.action-section.data-v-a470eddc {
  margin: 24rpx;
}
.action-section .generate-btn.data-v-a470eddc {
  width: 100%;
  background: #0f172a;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  padding: 28rpx;
  border-radius: 16rpx;
  text-align: center;
}
.preview-modal.data-v-a470eddc {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}
.preview-modal .preview-content.data-v-a470eddc {
  width: 90%;
  max-width: 600rpx;
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.preview-modal .preview-content .preview-header.data-v-a470eddc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.preview-modal .preview-content .preview-header .preview-title.data-v-a470eddc {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}
.preview-modal .preview-content .preview-header .close-btn.data-v-a470eddc {
  font-size: 40rpx;
  color: #64748b;
  padding: 10rpx;
}
.preview-modal .preview-content .preview-image-wrap.data-v-a470eddc {
  width: 100%;
  height: 600rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 24rpx;
}
.preview-modal .preview-content .preview-image-wrap .preview-image.data-v-a470eddc {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.preview-modal .preview-content .preview-info.data-v-a470eddc {
  margin-bottom: 32rpx;
}
.preview-modal .preview-content .preview-info .info-item.data-v-a470eddc {
  display: flex;
  margin-bottom: 16rpx;
}
.preview-modal .preview-content .preview-info .info-item .info-label.data-v-a470eddc {
  font-size: 28rpx;
  color: #64748b;
  min-width: 140rpx;
}
.preview-modal .preview-content .preview-info .info-item .info-value.data-v-a470eddc {
  font-size: 28rpx;
  color: #1e293b;
  flex: 1;
}
.preview-modal .preview-content .preview-actions.data-v-a470eddc {
  display: flex;
  gap: 16rpx;
}
.preview-modal .preview-content .preview-actions .preview-btn.data-v-a470eddc {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-modal .preview-content .preview-actions .preview-btn.save-btn.data-v-a470eddc {
  background: #6366f1;
  color: #ffffff;
}
.preview-modal .preview-content .preview-actions .preview-btn.reset-btn.data-v-a470eddc {
  background: #f1f5f9;
  color: #475569;
}
.instructions-section.data-v-a470eddc {
  margin: 24rpx;
}
.instructions-section .instructions-card.data-v-a470eddc {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.instructions-section .instructions-card .instructions-header.data-v-a470eddc {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.instructions-section .instructions-card .instructions-header .header-line.data-v-a470eddc {
  width: 8rpx;
  height: 36rpx;
  background: #6366f1;
  border-radius: 4rpx;
}
.instructions-section .instructions-card .instructions-header .instructions-title.data-v-a470eddc {
  font-size: 32rpx;
  color: #1e293b;
  font-weight: 600;
}
.instructions-section .instruction-list .instruction-item.data-v-a470eddc {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.instructions-section .instruction-list .instruction-item.data-v-a470eddc:last-child {
  margin-bottom: 0;
}
.instructions-section .instruction-list .instruction-item .instruction-number.data-v-a470eddc {
  width: 48rpx;
  height: 48rpx;
  background: #6366f1;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
}
.instructions-section .instruction-list .instruction-item .instruction-text.data-v-a470eddc {
  flex: 1;
  font-size: 28rpx;
  color: #475569;
  line-height: 1.5;
}