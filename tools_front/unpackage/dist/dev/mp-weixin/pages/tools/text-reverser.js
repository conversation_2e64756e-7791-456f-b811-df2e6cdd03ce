"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      outputText: "",
      reverseType: "char"
      // 'char', 'word', 'line'
    };
  },
  methods: {
    handleInput() {
      if (this.inputText.length > 5e3) {
        this.inputText = this.inputText.substring(0, 5e3);
        common_vendor.index.showToast({
          title: "已达到最大字符限制",
          icon: "none",
          duration: 2e3
        });
      }
    },
    reverseText() {
      if (!this.inputText.trim()) {
        common_vendor.index.showToast({
          title: "请输入文本",
          icon: "none"
        });
        return;
      }
      let reversed = "";
      switch (this.reverseType) {
        case "char":
          reversed = this.inputText.split("").reverse().join("");
          break;
        case "word":
          reversed = this.inputText.split(/\s+/).reverse().join(" ");
          break;
        case "line":
          reversed = this.inputText.split(/\r?\n/).reverse().join("\n");
          break;
        default:
          reversed = this.inputText.split("").reverse().join("");
      }
      this.outputText = reversed;
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort({
          type: "light"
        });
      }
      common_vendor.index.showToast({
        title: "反转完成",
        icon: "success",
        duration: 1500
      });
    },
    reverseAgain() {
      if (!this.outputText)
        return;
      const input = this.outputText;
      this.inputText = input;
      this.reverseText();
    },
    clearInput() {
      this.inputText = "";
      this.outputText = "";
    },
    copyToClipboard(text) {
      const content = text || this.outputText;
      if (!content) {
        common_vendor.index.showToast({
          title: "没有可复制的内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: content,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.reverseType === "char" ? 1 : "",
    b: common_vendor.o(($event) => $data.reverseType = "char"),
    c: $data.reverseType === "word" ? 1 : "",
    d: common_vendor.o(($event) => $data.reverseType = "word"),
    e: $data.reverseType === "line" ? 1 : "",
    f: common_vendor.o(($event) => $data.reverseType = "line"),
    g: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.handleInput && $options.handleInput(...args)]),
    h: $data.inputText,
    i: common_vendor.t($data.inputText.length),
    j: $data.inputText.length > 0
  }, $data.inputText.length > 0 ? {
    k: common_vendor.o((...args) => $options.clearInput && $options.clearInput(...args))
  } : {}, {
    l: $data.inputText.length > 0
  }, $data.inputText.length > 0 ? {
    m: common_vendor.o((...args) => $options.reverseText && $options.reverseText(...args))
  } : {}, {
    n: $data.outputText
  }, $data.outputText ? {
    o: common_vendor.t($data.outputText),
    p: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    q: common_vendor.o((...args) => $options.reverseAgain && $options.reverseAgain(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4c2051de"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/text-reverser.js.map
