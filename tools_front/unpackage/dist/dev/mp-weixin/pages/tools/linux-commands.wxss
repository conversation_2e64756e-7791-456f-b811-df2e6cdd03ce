/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-fae3112e {
  display: flex;
}
.flex-1.data-v-fae3112e {
  flex: 1;
}
.items-center.data-v-fae3112e {
  align-items: center;
}
.justify-center.data-v-fae3112e {
  justify-content: center;
}
.justify-between.data-v-fae3112e {
  justify-content: space-between;
}
.text-center.data-v-fae3112e {
  text-align: center;
}
.rounded.data-v-fae3112e {
  border-radius: 3px;
}
.rounded-lg.data-v-fae3112e {
  border-radius: 6px;
}
.shadow.data-v-fae3112e {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-fae3112e {
  padding: 16rpx;
}
.m-4.data-v-fae3112e {
  margin: 16rpx;
}
.mb-4.data-v-fae3112e {
  margin-bottom: 16rpx;
}
.mt-4.data-v-fae3112e {
  margin-top: 16rpx;
}
.linux-commands.data-v-fae3112e {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-fae3112e {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-fae3112e {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-fae3112e {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-fae3112e {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-fae3112e {
  padding: 30rpx;
}
.search-card.data-v-fae3112e {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.search-card .search-container.data-v-fae3112e {
  margin-bottom: 30rpx;
}
.search-card .search-container .search-input.data-v-fae3112e {
  width: 100%;
  height: 72rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f8f9fa;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.search-card .search-container .search-input.data-v-fae3112e:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 4rpx rgba(0, 123, 255, 0.08);
  outline: none;
}
.search-card .category-buttons.data-v-fae3112e {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.search-card .category-buttons .category-btn.data-v-fae3112e {
  padding: 20rpx 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 25rpx;
  background: white;
  transition: all 0.3s ease;
}
.search-card .category-buttons .category-btn.active.data-v-fae3112e {
  background: #007bff;
  border-color: #007bff;
}
.search-card .category-buttons .category-btn.active .category-text.data-v-fae3112e {
  color: white;
}
.search-card .category-buttons .category-btn .category-text.data-v-fae3112e {
  font-size: 26rpx;
  color: #666;
}
.commands-list .command-card.data-v-fae3112e {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.commands-list .command-card .command-header.data-v-fae3112e {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.commands-list .command-card .command-header .command-name-section.data-v-fae3112e {
  display: flex;
  align-items: center;
  gap: 15rpx;
}
.commands-list .command-card .command-header .command-name-section .command-icon.data-v-fae3112e {
  font-size: 32rpx;
}
.commands-list .command-card .command-header .command-name-section .command-name.data-v-fae3112e {
  font-size: 36rpx;
  font-weight: bold;
  color: #007bff;
  font-family: monospace;
}
.commands-list .command-card .command-header .command-name-section .command-category.data-v-fae3112e {
  padding: 8rpx 16rpx;
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border-radius: 20rpx;
  font-size: 20rpx;
}
.commands-list .command-card .command-description.data-v-fae3112e {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}
.commands-list .command-card .command-details .usage-section.data-v-fae3112e, .commands-list .command-card .command-details .example-section.data-v-fae3112e {
  margin-bottom: 15rpx;
}
.commands-list .command-card .command-details .usage-section .usage-label.data-v-fae3112e, .commands-list .command-card .command-details .usage-section .example-label.data-v-fae3112e, .commands-list .command-card .command-details .example-section .usage-label.data-v-fae3112e, .commands-list .command-card .command-details .example-section .example-label.data-v-fae3112e {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}
.commands-list .command-card .command-details .usage-section .usage-text.data-v-fae3112e, .commands-list .command-card .command-details .example-section .usage-text.data-v-fae3112e {
  font-size: 26rpx;
  color: #333;
  font-family: monospace;
}
.commands-list .command-card .command-details .usage-section .example-code.data-v-fae3112e, .commands-list .command-card .command-details .example-section .example-code.data-v-fae3112e {
  padding: 8rpx 12rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #16a34a;
  font-family: monospace;
}
.no-results.data-v-fae3112e {
  background: white;
  border-radius: 16rpx;
  padding: 80rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.no-results .no-results-text.data-v-fae3112e {
  font-size: 28rpx;
  color: #999;
}
.info-card.data-v-fae3112e {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.info-card .info-header.data-v-fae3112e {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-card .info-header .info-title.data-v-fae3112e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.info-card .info-content.data-v-fae3112e {
  padding: 30rpx;
}
.info-card .info-content .info-item.data-v-fae3112e {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}