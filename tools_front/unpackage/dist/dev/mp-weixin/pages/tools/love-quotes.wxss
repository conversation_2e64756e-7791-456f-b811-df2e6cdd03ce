/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-56231a0c {
  display: flex;
}
.flex-1.data-v-56231a0c {
  flex: 1;
}
.items-center.data-v-56231a0c {
  align-items: center;
}
.justify-center.data-v-56231a0c {
  justify-content: center;
}
.justify-between.data-v-56231a0c {
  justify-content: space-between;
}
.text-center.data-v-56231a0c {
  text-align: center;
}
.rounded.data-v-56231a0c {
  border-radius: 3px;
}
.rounded-lg.data-v-56231a0c {
  border-radius: 6px;
}
.shadow.data-v-56231a0c {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-56231a0c {
  padding: 16rpx;
}
.m-4.data-v-56231a0c {
  margin: 16rpx;
}
.mb-4.data-v-56231a0c {
  margin-bottom: 16rpx;
}
.mt-4.data-v-56231a0c {
  margin-top: 16rpx;
}
.love-quotes.data-v-56231a0c {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 30rpx;
}
.content.data-v-56231a0c {
  max-width: 750rpx;
  margin: 0 auto;
}
.header.data-v-56231a0c {
  margin-bottom: 30rpx;
}
.title-container.data-v-56231a0c {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.title-icon.data-v-56231a0c {
  font-size: 40rpx;
}
.title-text.data-v-56231a0c {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.quote-card.data-v-56231a0c {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.quote-content.data-v-56231a0c {
  background: #fff2f5;
  padding: 40rpx 30rpx;
  border-radius: 20rpx 20rpx 0 0;
}
.quote-text.data-v-56231a0c {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  text-align: center;
  display: block;
}
.card-actions.data-v-56231a0c {
  display: flex;
  border-top: 2rpx solid #f0f0f0;
}
.action-btn.data-v-56231a0c {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 0;
  gap: 8rpx;
}
.action-btn.refresh.data-v-56231a0c {
  background: #4e80f0;
  color: white;
}
.action-btn.copy.data-v-56231a0c {
  background: white;
  color: #666;
}
.btn-icon.data-v-56231a0c {
  font-size: 28rpx;
}
.btn-text.data-v-56231a0c {
  font-size: 28rpx;
}
.quotes-section.data-v-56231a0c {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.section-title.data-v-56231a0c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.quotes-list.data-v-56231a0c {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.quote-item.data-v-56231a0c {
  background: #fff2f5;
  padding: 24rpx;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}
.quote-item.active.data-v-56231a0c {
  transform: scale(0.98);
  background: #ffebf0;
}
.quote-item.data-v-56231a0c::after {
  content: "";
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M8.59 16.59L13.17 12L8.59 7.41L10 6l6 6l-6 6l-1.41-1.41z'/%3E%3C/svg%3E") no-repeat center;
  background-size: contain;
  opacity: 0.5;
}
.quote-item .item-text.data-v-56231a0c {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  padding-right: 40rpx;
}
.scenes-section.data-v-56231a0c {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.scenes-grid.data-v-56231a0c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.scene-item.data-v-56231a0c {
  background: #fff2f5;
  padding: 24rpx;
  border-radius: 16rpx;
  text-align: center;
}
.scene-item .scene-icon.data-v-56231a0c {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  display: block;
}
.scene-item .scene-name.data-v-56231a0c {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}
.scene-item .scene-desc.data-v-56231a0c {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.help-section.data-v-56231a0c {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
}
.help-list.data-v-56231a0c {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.help-item.data-v-56231a0c {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}