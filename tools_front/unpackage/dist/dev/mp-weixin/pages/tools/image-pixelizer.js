"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: "",
      processedImage: "",
      processing: false,
      processProgress: 0,
      pixelSize: 8,
      keepOriginalQuality: true,
      addPixelBorder: false,
      enhanceContrast: false,
      processingTime: 0,
      imageSize: "",
      canvasWidth: 300,
      canvasHeight: 300,
      pixelPresets: [
        { id: "fine", name: "精细", size: 4, icon: "🔍" },
        { id: "normal", name: "普通", size: 8, icon: "🎯" },
        { id: "rough", name: "粗糙", size: 16, icon: "🎮" },
        { id: "ultra", name: "超粗", size: 24, icon: "🎨" }
      ]
    };
  },
  methods: {
    // 选择图片
    selectImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album"],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0];
          this.getImageInfo(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "error"
          });
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["camera"],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0];
          this.getImageInfo(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "error"
          });
        }
      });
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      common_vendor.index.getImageInfo({
        src: imagePath,
        success: (res) => {
          this.imageSize = `${res.width} × ${res.height}`;
          this.canvasWidth = res.width;
          this.canvasHeight = res.height;
        }
      });
    },
    // 像素大小变化
    onPixelSizeChange(e) {
      this.pixelSize = e.detail.value;
    },
    // 设置像素大小
    setPixelSize(size) {
      this.pixelSize = size;
    },
    // 质量选项变化
    onQualityChange(e) {
      this.keepOriginalQuality = e.detail.value;
    },
    // 边框选项变化
    onBorderChange(e) {
      this.addPixelBorder = e.detail.value;
    },
    // 对比度选项变化
    onContrastChange(e) {
      this.enhanceContrast = e.detail.value;
    },
    // 处理图片
    async processImage() {
      this.processing = true;
      this.processProgress = 0;
      const startTime = Date.now();
      try {
        const progressInterval = setInterval(() => {
          if (this.processProgress < 90) {
            this.processProgress = Math.floor(this.processProgress + Math.random() * 15);
          }
        }, 200);
        const processedImagePath = await this.pixelizeImageWithCanvas();
        clearInterval(progressInterval);
        this.processProgress = 100;
        const endTime = Date.now();
        this.processingTime = Math.round((endTime - startTime) / 1e3 * 10) / 10;
        this.processedImage = processedImagePath;
        common_vendor.index.showToast({
          title: "像素化完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "处理失败",
          icon: "error"
        });
      } finally {
        this.processing = false;
      }
    },
    // 使用Canvas像素化图片
    pixelizeImageWithCanvas() {
      return new Promise((resolve, reject) => {
        const ctx = common_vendor.index.createCanvasContext("pixelCanvas", this);
        common_vendor.index.getImageInfo({
          src: this.selectedImage,
          success: async (imageInfo) => {
            const { width, height } = imageInfo;
            this.canvasWidth = width;
            this.canvasHeight = height;
            ctx.clearRect(0, 0, width, height);
            ctx.drawImage(this.selectedImage, 0, 0, width, height);
            ctx.draw(true, () => {
              common_vendor.index.canvasGetImageData({
                canvasId: "pixelCanvas",
                x: 0,
                y: 0,
                width,
                height,
                success: (res) => {
                  ctx.clearRect(0, 0, width, height);
                  const pixelSize = this.pixelSize;
                  for (let x = 0; x < width; x += pixelSize) {
                    for (let y = 0; y < height; y += pixelSize) {
                      let r = 0, g = 0, b = 0, a = 0;
                      let count = 0;
                      for (let px = x; px < Math.min(x + pixelSize, width); px++) {
                        for (let py = y; py < Math.min(y + pixelSize, height); py++) {
                          const i = (py * width + px) * 4;
                          r += res.data[i];
                          g += res.data[i + 1];
                          b += res.data[i + 2];
                          a += res.data[i + 3];
                          count++;
                        }
                      }
                      r = Math.round(r / count);
                      g = Math.round(g / count);
                      b = Math.round(b / count);
                      a = Math.round(a / count);
                      if (this.enhanceContrast) {
                        const factor = 1.2;
                        r = Math.min(255, Math.round(r * factor));
                        g = Math.min(255, Math.round(g * factor));
                        b = Math.min(255, Math.round(b * factor));
                      }
                      ctx.setFillStyle(`rgba(${r},${g},${b},${a / 255})`);
                      ctx.fillRect(
                        x,
                        y,
                        Math.min(pixelSize, width - x),
                        Math.min(pixelSize, height - y)
                      );
                      if (this.addPixelBorder) {
                        ctx.setStrokeStyle("rgba(0,0,0,0.2)");
                        ctx.setLineWidth(0.5);
                        ctx.strokeRect(
                          x,
                          y,
                          Math.min(pixelSize, width - x),
                          Math.min(pixelSize, height - y)
                        );
                      }
                    }
                  }
                  ctx.draw(false, () => {
                    setTimeout(() => {
                      common_vendor.index.canvasToTempFilePath({
                        canvasId: "pixelCanvas",
                        fileType: this.keepOriginalQuality ? "png" : "jpg",
                        quality: this.keepOriginalQuality ? 1 : 0.8,
                        success: (res2) => {
                          resolve(res2.tempFilePath);
                        },
                        fail: (err) => {
                          reject(err);
                        }
                      }, this);
                    }, 100);
                  });
                },
                fail: (err) => {
                  reject(err);
                }
              }, this);
            });
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },
    // 保存图片
    saveImage() {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.processedImage,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      });
    },
    // 分享图片
    shareImage() {
      common_vendor.index.showActionSheet({
        itemList: ["分享到微信", "分享到QQ", "分享到微博"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享功能开发中",
            icon: "none"
          });
        }
      });
    },
    // 重置流程
    resetProcess() {
      this.selectedImage = "";
      this.processedImage = "";
      this.processing = false;
      this.processProgress = 0;
      this.processingTime = 0;
      this.imageSize = "";
      this.pixelSize = 8;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedImage
  }, !$data.selectedImage ? {
    b: common_vendor.o((...args) => $options.selectImage && $options.selectImage(...args)),
    c: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args))
  } : {}, {
    d: $data.selectedImage && !$data.processedImage
  }, $data.selectedImage && !$data.processedImage ? common_vendor.e({
    e: $data.selectedImage,
    f: common_vendor.t($data.pixelSize),
    g: $data.pixelSize,
    h: common_vendor.o((...args) => $options.onPixelSizeChange && $options.onPixelSizeChange(...args)),
    i: common_vendor.f($data.pixelPresets, (preset, k0, i0) => {
      return {
        a: common_vendor.t(preset.icon),
        b: common_vendor.t(preset.name),
        c: common_vendor.t(preset.size),
        d: preset.id,
        e: $data.pixelSize === preset.size ? 1 : "",
        f: common_vendor.o(($event) => $options.setPixelSize(preset.size), preset.id)
      };
    }),
    j: $data.keepOriginalQuality,
    k: common_vendor.o((...args) => $options.onQualityChange && $options.onQualityChange(...args)),
    l: $data.addPixelBorder,
    m: common_vendor.o((...args) => $options.onBorderChange && $options.onBorderChange(...args)),
    n: $data.enhanceContrast,
    o: common_vendor.o((...args) => $options.onContrastChange && $options.onContrastChange(...args)),
    p: !$data.processing
  }, !$data.processing ? {} : {
    q: common_vendor.t($data.processProgress)
  }, {
    r: common_vendor.o((...args) => $options.processImage && $options.processImage(...args)),
    s: $data.processing
  }) : {}, {
    t: $data.processedImage
  }, $data.processedImage ? {
    v: $data.selectedImage,
    w: common_vendor.t($data.pixelSize),
    x: $data.processedImage,
    y: common_vendor.t($data.pixelSize),
    z: common_vendor.t($data.processingTime),
    A: common_vendor.t($data.imageSize),
    B: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args)),
    C: common_vendor.o((...args) => $options.shareImage && $options.shareImage(...args)),
    D: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args))
  } : {}, {
    E: $data.canvasWidth + "px",
    F: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a46fb513"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-pixelizer.js.map
