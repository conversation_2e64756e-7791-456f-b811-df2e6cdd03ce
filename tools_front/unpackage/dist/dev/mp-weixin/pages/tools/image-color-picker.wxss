
.image-color-picker.data-v-61ae3098 {
  min-height: 100vh;
  background-color: #ffffff;
}
.container.data-v-61ae3098 {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.header-section.data-v-61ae3098 {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 40rpx;
}
.icon-wrapper.data-v-61ae3098 {
  width: 120rpx;
  height: 120rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}
.tool-icon.data-v-61ae3098 {
  font-size: 60rpx;
}
.tool-title.data-v-61ae3098 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 20rpx;
}
.tool-desc.data-v-61ae3098 {
  font-size: 28rpx;
  color: #6b7280;
}
.upload-section.data-v-61ae3098 {
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.upload-area.data-v-61ae3098 {
  text-align: center;
  padding: 80rpx 40rpx;
  border: 4rpx dashed #d1d5db;
  border-radius: 20rpx;
  background-color: #f9fafb;
  transition: all 0.3s ease;
}
.upload-area.data-v-61ae3098:active {
  background-color: #f3f4f6;
  border-color: #3b82f6;
}
.upload-icon.data-v-61ae3098 {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}
.upload-title.data-v-61ae3098 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 15rpx;
}
.upload-desc.data-v-61ae3098 {
  font-size: 26rpx;
  color: #6b7280;
}
.image-preview.data-v-61ae3098 {
  text-align: center;
}
.preview-wrapper.data-v-61ae3098 {
  position: relative;
  width: 100%;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 24rpx;
}
.preview-image.data-v-61ae3098 {
  width: 100%;
  height: 800rpx;
  display: block;
}
.color-picker-cursor.data-v-61ae3098 {
  position: absolute;
  width: 40rpx;
  height: 40rpx;
  transform: translate(-50%, -50%);
  border: 2rpx solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  pointer-events: none;
  z-index: 10;
}
.cursor-inner.data-v-61ae3098 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4rpx;
  height: 4rpx;
  background: #ffffff;
  border-radius: 50%;
}
.picked-color-info.data-v-61ae3098 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}
.color-preview.data-v-61ae3098 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
}
.color-value.data-v-61ae3098 {
  flex: 1;
  font-size: 28rpx;
  font-family: 'Monaco', monospace;
  color: #1f2937;
}
.copy-btn.data-v-61ae3098 {
  padding: 12rpx;
  background: #e5e7eb;
  border-radius: 8rpx;
}
.copy-btn.data-v-61ae3098:active {
  background: #d1d5db;
}
.copy-icon.data-v-61ae3098 {
  font-size: 32rpx;
}
.image-actions.data-v-61ae3098 {
  display: flex;
  justify-content: center;
  gap: 24rpx;
  margin-top: 32rpx;
}
.action-btn.data-v-61ae3098 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx 36rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 200rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.action-btn.primary.data-v-61ae3098 {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
  color: #ffffff;
  font-weight: 600;
}
.action-btn.primary.data-v-61ae3098:active {
  transform: translateY(2rpx) scale(0.98);
  background: linear-gradient(135deg, #4338ca 0%, #2563eb 100%);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}
.action-btn.secondary.data-v-61ae3098 {
  background: #ffffff;
  color: #4b5563;
  border: 2rpx solid #e5e7eb;
  font-weight: 500;
}
.action-btn.secondary.data-v-61ae3098:active {
  transform: translateY(2rpx) scale(0.98);
  background: #f9fafb;
  border-color: #d1d5db;
}
.action-btn.processing.data-v-61ae3098 {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: #ffffff;
  font-weight: 600;
  cursor: not-allowed;
  opacity: 0.9;
}
.action-icon.data-v-61ae3098 {
  font-size: 36rpx;
  line-height: 1;
}
.action-text.data-v-61ae3098 {
  font-size: 28rpx;
  line-height: 1;
  letter-spacing: 0.5rpx;
}
.results-section.data-v-61ae3098 {
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.results-header.data-v-61ae3098 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.results-title.data-v-61ae3098 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}
.results-count.data-v-61ae3098 {
  font-size: 24rpx;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}
.colors-grid.data-v-61ae3098 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 40rpx;
}
.color-item.data-v-61ae3098 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  border: 2rpx solid #f3f4f6;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  position: relative;
}
.color-item.data-v-61ae3098:active {
  transform: scale(0.98);
  background-color: #f9fafb;
}
.color-block.data-v-61ae3098 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  flex-shrink: 0;
}
.color-code.data-v-61ae3098 {
  font-size: 26rpx;
  color: #1f2937;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  flex: 1;
}
.copy-indicator.data-v-61ae3098 {
  position: absolute;
  top: -10rpx;
  right: 10rpx;
  background-color: #1f2937;
  color: #ffffff;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.color-item:hover .copy-indicator.data-v-61ae3098 {
  opacity: 1;
}
.copy-text.data-v-61ae3098 {
  font-size: 20rpx;
}
.batch-actions.data-v-61ae3098 {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}
.analysis-section.data-v-61ae3098 {
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.section-title.data-v-61ae3098 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 30rpx;
}
.analysis-grid.data-v-61ae3098 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}
.analysis-item.data-v-61ae3098 {
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
}
.analysis-label.data-v-61ae3098 {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 15rpx;
}
.dominant-color.data-v-61ae3098 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}
.color-sample.data-v-61ae3098 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
}
.color-name.data-v-61ae3098 {
  font-size: 22rpx;
  color: #1f2937;
  font-weight: 500;
}
.analysis-value.data-v-61ae3098 {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 600;
}
.instructions-section.data-v-61ae3098 {
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
}
.instructions-list.data-v-61ae3098 {
  margin-top: 30rpx;
}
.instruction-item.data-v-61ae3098 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 15rpx;
}
.instruction-item.data-v-61ae3098:last-child {
  margin-bottom: 0;
}
.highlight.data-v-61ae3098 {
  color: #3b82f6;
  font-weight: 600;
}
.hidden-canvas.data-v-61ae3098 {
  position: fixed;
  top: -1000rpx;
  left: -1000rpx;
  width: 500px;
  height: 500px;
  z-index: -1;
}
