/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-070343ce {
  display: flex;
}
.flex-1.data-v-070343ce {
  flex: 1;
}
.items-center.data-v-070343ce {
  align-items: center;
}
.justify-center.data-v-070343ce {
  justify-content: center;
}
.justify-between.data-v-070343ce {
  justify-content: space-between;
}
.text-center.data-v-070343ce {
  text-align: center;
}
.rounded.data-v-070343ce {
  border-radius: 3px;
}
.rounded-lg.data-v-070343ce {
  border-radius: 6px;
}
.shadow.data-v-070343ce {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-070343ce {
  padding: 16rpx;
}
.m-4.data-v-070343ce {
  margin: 16rpx;
}
.mb-4.data-v-070343ce {
  margin-bottom: 16rpx;
}
.mt-4.data-v-070343ce {
  margin-top: 16rpx;
}
.game-voice-synthesis.data-v-070343ce {
  min-height: 100vh;
  background: #f8f9fa;
}
.game-voice-synthesis .content.data-v-070343ce {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.game-voice-synthesis .section-card.data-v-070343ce {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.game-voice-synthesis .section-card .card-header.data-v-070343ce {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.game-voice-synthesis .section-card .card-header .header-icon.data-v-070343ce {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.game-voice-synthesis .section-card .card-header .header-title.data-v-070343ce {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.game-voice-synthesis .section-card .card-content .label.data-v-070343ce {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.game-voice-synthesis .section-card .card-content .text-input.data-v-070343ce {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: #f9fafb;
  margin-bottom: 16rpx;
  box-sizing: border-box;
}
.game-voice-synthesis .section-card .card-content .text-input.data-v-070343ce:focus {
  border-color: #3b82f6;
  background: #ffffff;
}
.game-voice-synthesis .section-card .card-content .char-count.data-v-070343ce {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #9ca3af;
}
.game-voice-synthesis .voice-list .voice-item.data-v-070343ce {
  padding: 32rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  background: white;
  transition: all 0.2s;
}
.game-voice-synthesis .voice-list .voice-item.data-v-070343ce:last-child {
  margin-bottom: 0;
}
.game-voice-synthesis .voice-list .voice-item.active.data-v-070343ce {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.game-voice-synthesis .voice-list .voice-item .voice-name.data-v-070343ce {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.game-voice-synthesis .voice-list .voice-item .voice-desc.data-v-070343ce {
  display: block;
  font-size: 26rpx;
  color: #666;
}
.game-voice-synthesis .generate-button.data-v-070343ce {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.game-voice-synthesis .generate-button.data-v-070343ce:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
}
.game-voice-synthesis .generate-button.disabled.data-v-070343ce {
  opacity: 0.5;
  background: #d1d5db;
  box-shadow: none;
}
.game-voice-synthesis .audio-player .audio-info.data-v-070343ce {
  margin-bottom: 32rpx;
}
.game-voice-synthesis .audio-player .audio-info .audio-title.data-v-070343ce {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.game-voice-synthesis .audio-player .audio-info .audio-desc.data-v-070343ce {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.game-voice-synthesis .audio-player .audio-controls.data-v-070343ce {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.game-voice-synthesis .audio-player .audio-controls .play-button.data-v-070343ce {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
}
.game-voice-synthesis .audio-player .audio-controls .play-button .play-icon.data-v-070343ce {
  font-size: 32rpx;
  color: white;
}
.game-voice-synthesis .audio-player .audio-controls .progress-container.data-v-070343ce {
  flex: 1;
}
.game-voice-synthesis .audio-player .audio-controls .progress-container .progress-bar.data-v-070343ce {
  height: 8rpx;
  background: #e5e7eb;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
}
.game-voice-synthesis .audio-player .audio-controls .progress-container .progress-bar .progress-fill.data-v-070343ce {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 4rpx;
  transition: width 0.1s;
}
.game-voice-synthesis .audio-player .audio-controls .progress-container .time-info.data-v-070343ce {
  font-size: 24rpx;
  color: #666;
}
.game-voice-synthesis .audio-player .audio-controls .volume-control.data-v-070343ce {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.game-voice-synthesis .audio-player .audio-controls .volume-control .volume-icon.data-v-070343ce {
  font-size: 28rpx;
  color: #666;
}
.game-voice-synthesis .audio-player .audio-actions.data-v-070343ce {
  display: flex;
  gap: 16rpx;
}
.game-voice-synthesis .audio-player .audio-actions .action-button.data-v-070343ce {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.game-voice-synthesis .audio-player .audio-actions .action-button.secondary.data-v-070343ce {
  background: #f3f4f6;
  color: #374151;
}
.game-voice-synthesis .audio-player .audio-actions .action-button.secondary.data-v-070343ce:active {
  background: #e5e7eb;
}
.game-voice-synthesis .audio-player .audio-actions .action-button.primary.data-v-070343ce {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.game-voice-synthesis .audio-player .audio-actions .action-button.primary.data-v-070343ce:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.game-voice-synthesis .audio-player .audio-actions .action-button .button-icon.data-v-070343ce {
  font-size: 24rpx;
}
.game-voice-synthesis .audio-player .audio-actions .action-button .button-text.data-v-070343ce {
  font-size: 28rpx;
}
.game-voice-synthesis .usage-list .usage-item.data-v-070343ce {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.game-voice-synthesis .usage-list .usage-item.data-v-070343ce:last-child {
  margin-bottom: 0;
}