<view class="ascii-converter data-v-b72d3fdb"><view class="header-card data-v-b72d3fdb"><view class="card-content data-v-b72d3fdb"><view class="header-info data-v-b72d3fdb"><text class="header-icon data-v-b72d3fdb">🔤</text><view class="header-text data-v-b72d3fdb"><text class="header-title data-v-b72d3fdb">ASCII转换器</text><text class="header-desc data-v-b72d3fdb">文本与ASCII码之间的双向转换工具，支持编程学习和数据处理</text></view></view></view></view><view class="mode-card data-v-b72d3fdb"><view class="card-header data-v-b72d3fdb"><text class="card-title data-v-b72d3fdb">🔄 转换模式</text></view><view class="card-content data-v-b72d3fdb"><view class="mode-selector data-v-b72d3fdb"><button class="{{['data-v-b72d3fdb', 'mode-btn', a]}}" bindtap="{{b}}"> 字符 → ASCII </button><view class="mode-switch data-v-b72d3fdb"><button bindtap="{{c}}" class="swap-btn data-v-b72d3fdb"><text class="swap-icon data-v-b72d3fdb">⇄</text></button></view><button class="{{['data-v-b72d3fdb', 'mode-btn', d]}}" bindtap="{{e}}"> ASCII → 字符 </button></view></view></view><view class="input-card data-v-b72d3fdb"><view class="card-content data-v-b72d3fdb"><view class="input-section data-v-b72d3fdb"><text class="section-label data-v-b72d3fdb">输入字符</text><block wx:if="{{r0}}"><textarea placeholder="{{f}}" class="text-input data-v-b72d3fdb" maxlength="{{g}}" auto-height value="{{h}}" bindinput="{{i}}"></textarea></block></view><button bindtap="{{j}}" disabled="{{k}}" class="{{['data-v-b72d3fdb', 'convert-btn', l]}}"> 转换 </button></view></view><view wx:if="{{m}}" class="output-card data-v-b72d3fdb"><view class="card-content data-v-b72d3fdb"><view class="output-section data-v-b72d3fdb"><view class="output-header data-v-b72d3fdb"><text class="section-label data-v-b72d3fdb">转换结果</text><button bindtap="{{n}}" class="copy-btn data-v-b72d3fdb"> 复制 </button></view><view class="output-display data-v-b72d3fdb"><text class="output-text data-v-b72d3fdb">{{o}}</text></view></view></view></view><view class="ascii-table-card data-v-b72d3fdb"><view class="card-header ascii-table-header data-v-b72d3fdb"><text class="card-title data-v-b72d3fdb">📚 ASCII码表</text><button bindtap="{{q}}" class="toggle-btn right-btn data-v-b72d3fdb"><text class="data-v-b72d3fdb">{{p}}</text></button></view><view class="card-content data-v-b72d3fdb"><view class="table-container data-v-b72d3fdb"><scroll-view scroll-x="true" class="table-scroll data-v-b72d3fdb"><view class="ascii-table data-v-b72d3fdb"><view class="table-header data-v-b72d3fdb"><text class="table-cell header data-v-b72d3fdb">字符</text><text class="table-cell header data-v-b72d3fdb">ASCII</text><text class="table-cell header data-v-b72d3fdb">十六进制</text><text class="table-cell header data-v-b72d3fdb">说明</text></view><view wx:for="{{r}}" wx:for-item="item" wx:key="e" class="{{['data-v-b72d3fdb', 'table-row', item.f]}}" bindtap="{{item.g}}"><text class="table-cell char data-v-b72d3fdb">{{item.a}}</text><text class="table-cell code data-v-b72d3fdb">{{item.b}}</text><text class="table-cell hex data-v-b72d3fdb">{{item.c}}</text><text class="table-cell desc data-v-b72d3fdb">{{item.d}}</text></view></view></scroll-view></view></view></view><view class="tips-card data-v-b72d3fdb"><view class="card-header data-v-b72d3fdb"><text class="card-title data-v-b72d3fdb">💡 使用说明</text></view><view class="card-content data-v-b72d3fdb"><view class="tips-list data-v-b72d3fdb"><view class="tip-item data-v-b72d3fdb"><text class="tip-bullet data-v-b72d3fdb">•</text><text class="tip-text data-v-b72d3fdb"><text class="tip-bold data-v-b72d3fdb">ASCII码:</text> 美国信息交换标准代码，范围0-127</text></view><view class="tip-item data-v-b72d3fdb"><text class="tip-bullet data-v-b72d3fdb">•</text><text class="tip-text data-v-b72d3fdb"><text class="tip-bold data-v-b72d3fdb">文本转ASCII:</text> 每个字符转换为对应的数字代码</text></view><view class="tip-item data-v-b72d3fdb"><text class="tip-bullet data-v-b72d3fdb">•</text><text class="tip-text data-v-b72d3fdb"><text class="tip-bold data-v-b72d3fdb">ASCII转文本:</text> 数字代码转换为对应字符</text></view><view class="tip-item data-v-b72d3fdb"><text class="tip-bullet data-v-b72d3fdb">•</text><text class="tip-text data-v-b72d3fdb"><text class="tip-bold data-v-b72d3fdb">应用场景:</text> 编程学习、数据处理、字符编码</text></view><view class="tip-item data-v-b72d3fdb"><text class="tip-bullet data-v-b72d3fdb">•</text><text class="tip-text data-v-b72d3fdb"><text class="tip-bold data-v-b72d3fdb">注意事项:</text> 仅支持标准ASCII字符，不支持中文等Unicode字符</text></view></view></view></view></view>