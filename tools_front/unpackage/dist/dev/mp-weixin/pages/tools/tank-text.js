"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedTank: "",
      favorites: [],
      randomTank: "",
      tankAscii: [
        `    ___
 __|___|__
|---------|
 O-------O`,
        `      _____
  ,__/_____\\__,
 [_==========_]
  \\_:______:_/
    O      O`,
        `    ________
 ,__/        \\__,
[________________]
 \\_:__________:_/
   O          O`,
        `        ______
    ___/      \\___
   [______________]
    \\_:________:_/
      O        O`,
        `   ██████████
 ██░░░░░░░░░░██
██▓▓▓▓▓▓▓▓▓▓▓▓██
██▓▓▓▓▓▓▓▓▓▓▓▓██
 ██░░░░░░░░░░██
   ██████████`,
        `    ╔════════╗
 ╔══╝        ╚══╗
 ║              ║
 ╚══╗        ╔══╝
    ╚════════╝
     ○      ○`,
        `   ┌─────────────┐
 ┌─┘             └─┐
 │                 │
 └─┐             ─┘
   └─────────────┘
    ●         ●`,
        `      ______________
 ___/              \\___
|______________________|
 \\__________________/
   ○              ○`
      ],
      militarySymbols: [
        "▓▓▓▓▓▓▓▓▓▓",
        "██████████",
        "░░░░░░░░░░",
        "▒▒▒▒▒▒▒▒▒▒",
        "████░░████",
        "▓▓▓▒▒▒▓▓▓",
        "███▓▓▓███",
        "░░▓▓▓▓▓▓░░"
      ],
      tankEmojis: [
        "🚗💨",
        "🚙💥",
        "🚚⚡",
        "🏎️💨",
        "🚜💪",
        "🚛⚡",
        "🏁🚗",
        "💥🚙💥"
      ]
    };
  },
  onLoad() {
    this.loadFavorites();
  },
  methods: {
    selectTank(tank) {
      this.selectedTank = tank;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    addToFavorites(tank) {
      if (!this.favorites.includes(tank)) {
        this.favorites.push(tank);
        this.saveFavorites();
        common_vendor.index.showToast({
          title: "已添加到收藏",
          icon: "success",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "已存在收藏中",
          icon: "none",
          duration: 1500
        });
      }
    },
    removeFromFavorites(index) {
      this.favorites.splice(index, 1);
      this.saveFavorites();
      common_vendor.index.showToast({
        title: "已从收藏中移除",
        icon: "success",
        duration: 1500
      });
    },
    generateRandomTank() {
      const allTanks = [...this.tankAscii, ...this.militarySymbols, ...this.tankEmojis];
      const randomIndex = Math.floor(Math.random() * allTanks.length);
      this.randomTank = allTanks[randomIndex];
    },
    saveFavorites() {
      common_vendor.index.setStorageSync("tank-text-favorites", this.favorites);
    },
    loadFavorites() {
      const favorites = common_vendor.index.getStorageSync("tank-text-favorites");
      if (favorites) {
        this.favorites = favorites;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedTank
  }, $data.selectedTank ? {
    b: common_vendor.t($data.selectedTank),
    c: common_vendor.o(($event) => $options.copyText($data.selectedTank)),
    d: common_vendor.o(($event) => $options.addToFavorites($data.selectedTank))
  } : {}, {
    e: $data.favorites.length > 0
  }, $data.favorites.length > 0 ? {
    f: common_vendor.t($data.favorites.length),
    g: common_vendor.f($data.favorites, (favorite, index, i0) => {
      return {
        a: common_vendor.t(favorite),
        b: common_vendor.o(($event) => $options.copyText(favorite), index),
        c: common_vendor.o(($event) => $options.removeFromFavorites(index), index),
        d: index,
        e: common_vendor.o(($event) => $options.selectTank(favorite), index)
      };
    })
  } : {}, {
    h: common_vendor.f($data.tankAscii, (tank, index, i0) => {
      return {
        a: common_vendor.t(tank),
        b: common_vendor.o(($event) => $options.copyText(tank), index),
        c: common_vendor.o(($event) => $options.addToFavorites(tank), index),
        d: index,
        e: common_vendor.o(($event) => $options.selectTank(tank), index)
      };
    }),
    i: common_vendor.f($data.militarySymbols, (symbol, index, i0) => {
      return {
        a: common_vendor.t(symbol),
        b: common_vendor.o(($event) => $options.copyText(symbol), index),
        c: index,
        d: common_vendor.o(($event) => $options.selectTank(symbol), index)
      };
    }),
    j: common_vendor.f($data.tankEmojis, (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: common_vendor.o(($event) => $options.copyText(emoji), index),
        c: index,
        d: common_vendor.o(($event) => $options.selectTank(emoji), index)
      };
    }),
    k: common_vendor.o((...args) => $options.generateRandomTank && $options.generateRandomTank(...args)),
    l: $data.randomTank
  }, $data.randomTank ? {
    m: common_vendor.t($data.randomTank),
    n: common_vendor.o(($event) => $options.copyText($data.randomTank)),
    o: common_vendor.o(($event) => $options.addToFavorites($data.randomTank))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8af08aa3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/tank-text.js.map
