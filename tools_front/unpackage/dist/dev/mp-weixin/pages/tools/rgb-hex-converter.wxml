<view class="color-converter-container data-v-d47e139d"><view class="header-card data-v-d47e139d"><view class="header-content data-v-d47e139d"><view class="header-icon data-v-d47e139d">🎨</view><view class="header-info data-v-d47e139d"><text class="header-title data-v-d47e139d">颜色转换器</text><text class="header-subtitle data-v-d47e139d">RGB、HEX、HSL多格式转换</text></view></view></view><view class="color-display-card data-v-d47e139d"><view class="main-color-preview data-v-d47e139d" style="{{'background-color:' + c}}" bindtap="{{d}}"><text class="{{['color-text', 'data-v-d47e139d', b && 'light']}}">{{a}}</text></view><view class="color-actions data-v-d47e139d"><button class="action-btn data-v-d47e139d" bindtap="{{e}}"><text class="btn-icon data-v-d47e139d">📋</text><text class="btn-text data-v-d47e139d">复制</text></button><button class="action-btn data-v-d47e139d" bindtap="{{f}}"><text class="btn-icon data-v-d47e139d">🎲</text><text class="btn-text data-v-d47e139d">随机</text></button><button class="action-btn data-v-d47e139d" bindtap="{{g}}"><text class="btn-icon data-v-d47e139d">🔄</text><text class="btn-text data-v-d47e139d">重置</text></button></view></view><view class="control-card data-v-d47e139d"><view class="card-header data-v-d47e139d"><text class="card-title data-v-d47e139d">🔴 RGB 调节</text></view><view class="rgb-controls data-v-d47e139d"><view class="rgb-item data-v-d47e139d"><view class="rgb-header data-v-d47e139d"><text class="rgb-label data-v-d47e139d">R (红色)</text><view class="rgb-input-wrapper data-v-d47e139d"><input type="number" bindinput="{{h}}" min="0" max="255" class="rgb-input data-v-d47e139d" value="{{i}}"/></view></view><view class="slider-container data-v-d47e139d"><slider value="{{j}}" bindchange="{{k}}" min="0" max="255" block-size="24" activeColor="#ff6b6b" backgroundColor="#ffd5d5" class="rgb-slider data-v-d47e139d" block-color="#ff0000"/></view></view><view class="rgb-item data-v-d47e139d"><view class="rgb-header data-v-d47e139d"><text class="rgb-label data-v-d47e139d">G (绿色)</text><view class="rgb-input-wrapper data-v-d47e139d"><input type="number" bindinput="{{l}}" min="0" max="255" class="rgb-input data-v-d47e139d" value="{{m}}"/></view></view><view class="slider-container data-v-d47e139d"><slider value="{{n}}" bindchange="{{o}}" min="0" max="255" block-size="24" activeColor="#51cf66" backgroundColor="#d3f9d8" class="rgb-slider data-v-d47e139d" block-color="#00ff00"/></view></view><view class="rgb-item data-v-d47e139d"><view class="rgb-header data-v-d47e139d"><text class="rgb-label data-v-d47e139d">B (蓝色)</text><view class="rgb-input-wrapper data-v-d47e139d"><input type="number" bindinput="{{p}}" min="0" max="255" class="rgb-input data-v-d47e139d" value="{{q}}"/></view></view><view class="slider-container data-v-d47e139d"><slider value="{{r}}" bindchange="{{s}}" min="0" max="255" block-size="24" activeColor="#339af0" backgroundColor="#d0ebff" class="rgb-slider data-v-d47e139d" block-color="#0000ff"/></view></view></view></view><view class="control-card data-v-d47e139d"><view class="card-header data-v-d47e139d"><text class="card-title data-v-d47e139d"># HEX 值</text></view><view class="hex-input-container data-v-d47e139d"><input type="text" bindinput="{{t}}" placeholder="#FF0000" class="hex-input data-v-d47e139d" value="{{v}}"/><button class="copy-hex-btn data-v-d47e139d" bindtap="{{w}}"><text class="copy-icon data-v-d47e139d">📋</text></button></view></view><view class="info-card data-v-d47e139d"><view class="card-header data-v-d47e139d"><text class="card-title data-v-d47e139d">📊 颜色信息</text></view><view class="color-formats data-v-d47e139d"><view class="format-item data-v-d47e139d" bindtap="{{A}}"><text class="format-label data-v-d47e139d">RGB</text><text class="format-value data-v-d47e139d">rgb({{x}}, {{y}}, {{z}})</text><text class="copy-hint data-v-d47e139d">点击复制</text></view><view class="format-item data-v-d47e139d" bindtap="{{C}}"><text class="format-label data-v-d47e139d">HEX</text><text class="format-value data-v-d47e139d">{{B}}</text><text class="copy-hint data-v-d47e139d">点击复制</text></view><view class="format-item data-v-d47e139d" bindtap="{{G}}"><text class="format-label data-v-d47e139d">HSL</text><text class="format-value data-v-d47e139d">hsl({{D}}, {{E}}%, {{F}}%)</text><text class="copy-hint data-v-d47e139d">点击复制</text></view><view class="format-item data-v-d47e139d"><text class="format-label data-v-d47e139d">亮度</text><text class="format-value data-v-d47e139d">{{H}}%</text><text class="brightness-desc data-v-d47e139d">{{I}}</text></view></view></view><view class="palette-card data-v-d47e139d"><view class="card-header data-v-d47e139d"><text class="card-title data-v-d47e139d">🎨 预设颜色</text></view><view class="preset-colors data-v-d47e139d"><view wx:for="{{J}}" wx:for-item="color" wx:key="b" class="preset-color data-v-d47e139d" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"><text class="color-code data-v-d47e139d">{{color.a}}</text></view></view></view><view class="tips-card data-v-d47e139d"><view class="card-header data-v-d47e139d"><text class="card-title data-v-d47e139d">💡 使用说明</text></view><view class="tips-content data-v-d47e139d"><view class="tip-item data-v-d47e139d"><text class="tip-title data-v-d47e139d">🎛️ 实时调节</text><text class="tip-desc data-v-d47e139d">拖动滑块或输入数值实时调节颜色</text></view><view class="tip-item data-v-d47e139d"><text class="tip-title data-v-d47e139d">📋 一键复制</text><text class="tip-desc data-v-d47e139d">点击颜色值即可复制到剪贴板</text></view><view class="tip-item data-v-d47e139d"><text class="tip-title data-v-d47e139d">🎨 快速选择</text><text class="tip-desc data-v-d47e139d">点击预设颜色快速设置</text></view><view class="tip-item data-v-d47e139d"><text class="tip-title data-v-d47e139d">🎲 随机生成</text><text class="tip-desc data-v-d47e139d">点击主颜色或随机按钮生成随机颜色</text></view></view></view><view wx:if="{{K}}" class="color-picker-modal data-v-d47e139d" bindtap="{{ae}}"><view class="color-picker-container data-v-d47e139d" catchtap="{{ad}}"><view class="picker-header data-v-d47e139d"><text class="picker-title data-v-d47e139d">选择颜色</text><view class="picker-close data-v-d47e139d" bindtap="{{L}}">×</view></view><view class="color-canvas-container data-v-d47e139d"><block wx:if="{{r0}}"><canvas class="color-canvas data-v-d47e139d" canvas-id="colorCanvas" width="300" height="200" style="{{'background-color:' + M}}" bindtouchstart="{{N}}" bindtouchmove="{{O}}"></canvas></block><view class="canvas-cursor data-v-d47e139d" style="{{P}}"></view></view><view class="hue-slider-container data-v-d47e139d"><block wx:if="{{r0}}"><canvas class="hue-slider data-v-d47e139d" canvas-id="hueCanvas" width="300" height="30" bindtouchstart="{{Q}}" bindtouchmove="{{R}}"></canvas></block><view class="hue-cursor data-v-d47e139d" style="{{S}}"></view></view><view class="rgb-inputs data-v-d47e139d"><view class="rgb-group data-v-d47e139d"><text class="rgb-label data-v-d47e139d">R</text><input class="rgb-input data-v-d47e139d" type="number" bindinput="{{T}}" min="0" max="255" value="{{U}}"/></view><view class="rgb-group data-v-d47e139d"><text class="rgb-label data-v-d47e139d">G</text><input class="rgb-input data-v-d47e139d" type="number" bindinput="{{V}}" min="0" max="255" value="{{W}}"/></view><view class="rgb-group data-v-d47e139d"><text class="rgb-label data-v-d47e139d">B</text><input class="rgb-input data-v-d47e139d" type="number" bindinput="{{X}}" min="0" max="255" value="{{Y}}"/></view></view><view class="picker-preview data-v-d47e139d"><view class="preview-color data-v-d47e139d" style="{{'background-color:' + Z}}"></view><text class="hex-value data-v-d47e139d">{{aa}}</text></view><view class="picker-actions data-v-d47e139d"><view class="picker-btn cancel data-v-d47e139d" bindtap="{{ab}}">取消</view><view class="picker-btn confirm data-v-d47e139d" bindtap="{{ac}}">确认</view></view></view></view></view>