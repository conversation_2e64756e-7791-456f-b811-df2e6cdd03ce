/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b15160cf {
  display: flex;
}
.flex-1.data-v-b15160cf {
  flex: 1;
}
.items-center.data-v-b15160cf {
  align-items: center;
}
.justify-center.data-v-b15160cf {
  justify-content: center;
}
.justify-between.data-v-b15160cf {
  justify-content: space-between;
}
.text-center.data-v-b15160cf {
  text-align: center;
}
.rounded.data-v-b15160cf {
  border-radius: 3px;
}
.rounded-lg.data-v-b15160cf {
  border-radius: 6px;
}
.shadow.data-v-b15160cf {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b15160cf {
  padding: 16rpx;
}
.m-4.data-v-b15160cf {
  margin: 16rpx;
}
.mb-4.data-v-b15160cf {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b15160cf {
  margin-top: 16rpx;
}
.container.data-v-b15160cf {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 30rpx;
}

/* 区块通用样式 */
.input-section.data-v-b15160cf,
.info-section.data-v-b15160cf,
.quality-section.data-v-b15160cf,
.platforms-section.data-v-b15160cf,
.usage-section.data-v-b15160cf {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 标题样式 */
.section-header.data-v-b15160cf {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.title-wrap.data-v-b15160cf {
  display: flex;
  align-items: center;
}
.icon.data-v-b15160cf {
  margin-right: 12rpx;
  font-size: 40rpx;
}
.title.data-v-b15160cf,
.section-title.data-v-b15160cf {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  letter-spacing: 0.5rpx;
}

/* 模式切换样式 */
.mode-switch.data-v-b15160cf {
  display: flex;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 8rpx;
  margin-bottom: 24rpx;
  border: 2rpx solid #e5e7eb;
}
.mode-item.data-v-b15160cf {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
}
.mode-item.active.data-v-b15160cf {
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.15);
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}
.mode-item.active .mode-text.data-v-b15160cf {
  color: #3b82f6;
  font-weight: 600;
}
.mode-item.data-v-b15160cf:active {
  transform: scale(0.98);
}
.mode-icon.data-v-b15160cf {
  font-size: 32rpx;
  margin-right: 8rpx;
}
.mode-text.data-v-b15160cf {
  font-size: 28rpx;
  color: #666666;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 输入框样式 */
.search-inputs.data-v-b15160cf,
.url-inputs.data-v-b15160cf {
  margin-bottom: 20rpx;
}
.input-row.data-v-b15160cf {
  margin-bottom: 16rpx;
}
.input-row.data-v-b15160cf:last-child {
  margin-bottom: 0;
}
.search-input.data-v-b15160cf,
.url-input.data-v-b15160cf {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  background-color: #ffffff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}
.search-input.data-v-b15160cf:focus,
.url-input.data-v-b15160cf:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2rpx rgba(59, 130, 246, 0.1);
}
.search-input.data-v-b15160cf::-webkit-input-placeholder, .url-input.data-v-b15160cf::-webkit-input-placeholder {
  color: #9ca3af;
}
.search-input.data-v-b15160cf::placeholder,
.url-input.data-v-b15160cf::placeholder {
  color: #9ca3af;
}
.search-input.data-v-b15160cf:first-child {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #fafbff 0%, #ffffff 100%);
}

/* 按钮样式 */
.action-button-wrap.data-v-b15160cf {
  margin-bottom: 16rpx;
}
.action-btn.data-v-b15160cf {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.action-btn.data-v-b15160cf:active {
  transform: scale(0.98);
}
.action-btn .btn-text.data-v-b15160cf {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.action-btn-disabled.data-v-b15160cf {
  background: #e5e7eb;
  cursor: not-allowed;
  box-shadow: none;
}
.action-btn-disabled .btn-text.data-v-b15160cf {
  color: #94a3b8;
}
.action-btn-disabled.data-v-b15160cf:active {
  transform: none;
}

/* 加载动画 */
.loading-dots.data-v-b15160cf {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
}
.dot.data-v-b15160cf {
  width: 8rpx;
  height: 8rpx;
  background: #ffffff;
  border-radius: 50%;
  animation: bounce-b15160cf 0.5s infinite alternate;
}
.dot.data-v-b15160cf:nth-child(2) {
  animation-delay: 0.2s;
}
.dot.data-v-b15160cf:nth-child(3) {
  animation-delay: 0.4s;
}
@keyframes bounce-b15160cf {
0% {
    transform: translateY(0);
}
100% {
    transform: translateY(-6rpx);
}
}
.support-text.data-v-b15160cf {
  font-size: 24rpx;
  color: #666666;
  margin-top: 16rpx;
}

/* 音乐卡片样式优化 */
.music-card.data-v-b15160cf {
  position: relative;
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 24rpx;
  margin-top: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid rgba(59, 130, 246, 0.1);
}
.music-card.data-v-b15160cf::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.2), transparent);
}
.music-card.data-v-b15160cf::after {
  content: "";
  position: absolute;
  top: -60rpx;
  right: -60rpx;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.08));
  border-radius: 0 0 0 100%;
  transform: rotate(45deg);
}
.music-cover.data-v-b15160cf {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  background: #ffffff;
  cursor: pointer;
  transform: rotate(-2deg);
  transition: all 0.3s ease;
}
.music-cover.data-v-b15160cf::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  z-index: 1;
}
.music-cover.data-v-b15160cf::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.music-cover.data-v-b15160cf:active {
  transform: rotate(-2deg) scale(0.98);
}
.music-cover.data-v-b15160cf:active::after {
  opacity: 1;
}
.cover-img.data-v-b15160cf {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.play-button.data-v-b15160cf {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}
.play-button.data-v-b15160cf::before {
  content: "";
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}
.play-button.data-v-b15160cf:active {
  transform: translate(-50%, -50%) scale(0.95);
  background: white;
}
.play-button.data-v-b15160cf:active::before {
  opacity: 1;
}
.play-button .play-icon.data-v-b15160cf {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 12rpx 0 12rpx 20rpx;
  border-color: transparent transparent transparent #3b82f6;
  margin-left: 4rpx;
  transition: all 0.3s ease;
}
.play-button.is-playing .play-icon.data-v-b15160cf {
  width: 20rpx;
  height: 20rpx;
  border: none;
  margin-left: 0;
  position: relative;
}
.play-button.is-playing .play-icon.data-v-b15160cf::before, .play-button.is-playing .play-icon.data-v-b15160cf::after {
  content: "";
  position: absolute;
  width: 6rpx;
  height: 100%;
  background: #3b82f6;
  border-radius: 2rpx;
}
.play-button.is-playing .play-icon.data-v-b15160cf::before {
  left: 2rpx;
}
.play-button.is-playing .play-icon.data-v-b15160cf::after {
  right: 2rpx;
}
.music-playing-icon.data-v-b15160cf {
  position: absolute;
  bottom: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  gap: 4rpx;
  height: 24rpx;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  z-index: 2;
}
.music-playing-icon .bar.data-v-b15160cf {
  width: 4rpx;
  background: #3b82f6;
  border-radius: 2rpx;
  animation: playing-b15160cf 1s ease infinite;
}
.music-playing-icon .bar.data-v-b15160cf:nth-child(1) {
  height: 10rpx;
  animation-delay: 0s;
}
.music-playing-icon .bar.data-v-b15160cf:nth-child(2) {
  height: 18rpx;
  animation-delay: 0.2s;
}
.music-playing-icon .bar.data-v-b15160cf:nth-child(3) {
  height: 12rpx;
  animation-delay: 0.4s;
}
@keyframes playing-b15160cf {
0%, 100% {
    transform: scaleY(1);
}
50% {
    transform: scaleY(0.5);
}
}
.music-details.data-v-b15160cf {
  flex: 1;
  margin-left: 32rpx;
  position: relative;
}
.music-details.data-v-b15160cf::before {
  content: "";
  position: absolute;
  top: 8rpx;
  left: -16rpx;
  width: 4rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, #3b82f6, rgba(59, 130, 246, 0.3));
  border-radius: 2rpx;
}
.music-title.data-v-b15160cf {
  font-size: 38rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  letter-spacing: 0.5rpx;
}
.music-title.data-v-b15160cf::after {
  content: "";
  display: block;
  width: 40rpx;
  height: 3rpx;
  background: linear-gradient(90deg, #3b82f6, transparent);
  margin-top: 12rpx;
  border-radius: 2rpx;
}
.music-artist.data-v-b15160cf {
  font-size: 28rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}
.music-artist.data-v-b15160cf::before {
  content: "🎤";
  font-size: 24rpx;
  margin-right: 10rpx;
  opacity: 0.9;
}
.music-duration.data-v-b15160cf {
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
}
.music-duration.data-v-b15160cf::before {
  content: "⏱️";
  font-size: 22rpx;
  margin-right: 8rpx;
  opacity: 0.9;
}

/* 搜索结果列表样式 */
.results-section.data-v-b15160cf {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.music-list.data-v-b15160cf {
  margin-top: 20rpx;
}
.music-item.data-v-b15160cf {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2rpx solid transparent;
}
.music-item.data-v-b15160cf:last-child {
  margin-bottom: 0;
}
.music-item.selected.data-v-b15160cf {
  background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
  border-color: #3b82f6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}
.music-item.data-v-b15160cf:active {
  transform: scale(0.98);
}
.music-cover-small.data-v-b15160cf {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 20rpx;
  background: #ffffff;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.cover-img-small.data-v-b15160cf {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.music-index.data-v-b15160cf {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  background: rgba(0, 0, 0, 0.7);
  color: #ffffff;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
}
.music-info.data-v-b15160cf {
  flex: 1;
  margin-right: 16rpx;
}
.song-title.data-v-b15160cf {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.song-artist.data-v-b15160cf {
  font-size: 26rpx;
  color: #666666;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.song-meta.data-v-b15160cf {
  display: flex;
  gap: 12rpx;
  font-size: 22rpx;
  color: #999999;
}
.song-size.data-v-b15160cf,
.song-duration.data-v-b15160cf,
.song-source.data-v-b15160cf {
  padding: 2rpx 8rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6rpx;
}
.music-actions.data-v-b15160cf {
  display: flex;
  align-items: center;
}
.play-btn-small.data-v-b15160cf {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(59, 130, 246, 0.2);
}
.play-btn-small.playing.data-v-b15160cf {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 4rpx 8rpx rgba(239, 68, 68, 0.2);
}
.play-btn-small.data-v-b15160cf:active {
  transform: scale(0.95);
}
.play-icon-small.data-v-b15160cf {
  font-size: 24rpx;
  color: #ffffff;
}

/* 选中歌曲详细信息样式 */
.selected-song-section.data-v-b15160cf {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.selected-song-card.data-v-b15160cf {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 20rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.2);
  margin-top: 20rpx;
}
.song-cover.data-v-b15160cf {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-right: 24rpx;
  background: #ffffff;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
}
.cover-img.data-v-b15160cf {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.play-button-large.data-v-b15160cf {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}
.play-button-large.data-v-b15160cf:active {
  transform: translate(-50%, -50%) scale(0.95);
}
.play-icon-large.data-v-b15160cf {
  font-size: 20rpx;
  color: #3b82f6;
}
.song-details.data-v-b15160cf {
  flex: 1;
}
.selected-title.data-v-b15160cf {
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.selected-artist.data-v-b15160cf {
  font-size: 28rpx;
  color: #3b82f6;
  margin-bottom: 8rpx;
  font-weight: 500;
}
.selected-album.data-v-b15160cf {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.selected-meta.data-v-b15160cf {
  display: flex;
  gap: 8rpx;
  flex-wrap: wrap;
}
.meta-item.data-v-b15160cf {
  font-size: 22rpx;
  color: #999999;
  padding: 4rpx 12rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

/* 音质选择样式优化 */
.quality-list.data-v-b15160cf {
  margin-top: 20rpx;
}
.quality-item.data-v-b15160cf {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}
.quality-item.data-v-b15160cf::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 24rpx;
  right: 24rpx;
  height: 1rpx;
  background: #e5e7eb;
}
.quality-item.data-v-b15160cf:last-child::after {
  display: none;
}
.quality-item.data-v-b15160cf:active {
  transform: scale(0.98);
}
.quality-header.data-v-b15160cf {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.quality-icon.data-v-b15160cf {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.quality-info.data-v-b15160cf {
  display: flex;
  flex-direction: column;
  flex: 1;
}
.quality-name.data-v-b15160cf {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}
.quality-size.data-v-b15160cf {
  font-size: 24rpx;
  color: #666666;
}
.download-btn.data-v-b15160cf {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 32rpx;
  padding: 0 24rpx;
  border: none;
  transition: all 0.3s ease;
  margin-left: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.download-btn.data-v-b15160cf:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.15);
}
.download-icon.data-v-b15160cf {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.download-text.data-v-b15160cf {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 1rpx;
}
.quality-item-premium.data-v-b15160cf {
  background: linear-gradient(135deg, #fef3c7 0%, #fff7ed 100%);
  border: 1rpx solid #fde68a;
}
.quality-item-premium.data-v-b15160cf::before {
  content: "推荐";
  position: absolute;
  top: 12rpx;
  right: -28rpx;
  background: #92400e;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 32rpx;
  transform: rotate(45deg);
}
.quality-item-premium .quality-name.data-v-b15160cf,
.quality-item-premium .quality-size.data-v-b15160cf {
  color: #92400e;
}
.quality-item-premium .download-btn.data-v-b15160cf {
  background: linear-gradient(135deg, #92400e 0%, #78350f 100%);
  box-shadow: 0 4rpx 12rpx rgba(146, 64, 14, 0.2);
}
.quality-item-premium .download-btn.data-v-b15160cf:active {
  box-shadow: 0 2rpx 8rpx rgba(146, 64, 14, 0.15);
}

/* 平台支持样式 */
.platform-grid.data-v-b15160cf {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 20rpx;
}
.platform-item.data-v-b15160cf {
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2rpx solid transparent;
}
.platform-item.data-v-b15160cf:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
  transform: translateY(-2rpx);
}
.platform-item.data-v-b15160cf:active {
  transform: scale(0.98);
}
.platform-header.data-v-b15160cf {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}
.platform-icon.data-v-b15160cf {
  font-size: 36rpx;
  margin-right: 12rpx;
}
.platform-name.data-v-b15160cf {
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}
.platform-quality.data-v-b15160cf {
  display: flex;
  flex-wrap: wrap;
  gap: 6rpx;
}
.quality-tag.data-v-b15160cf {
  font-size: 20rpx;
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

/* 使用说明样式 */
.usage-list.data-v-b15160cf {
  margin-top: 20rpx;
}
.usage-item.data-v-b15160cf {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.step-number.data-v-b15160cf {
  width: 36rpx;
  height: 36rpx;
  background: #3b82f6;
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 12rpx;
  flex-shrink: 0;
}
.step-text.data-v-b15160cf {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.8;
  padding-top: 4rpx;
}
.wechat-notice.data-v-b15160cf {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 16rpx;
  background: #fff7ed;
  border-radius: 8rpx;
}
.notice-icon.data-v-b15160cf {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.notice-content.data-v-b15160cf {
  flex: 1;
}
.notice-title.data-v-b15160cf {
  font-size: 24rpx;
  font-weight: 600;
  color: #92400e;
  margin-bottom: 8rpx;
}
.notice-text.data-v-b15160cf {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.6;
}
.usage-warning.data-v-b15160cf {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 16rpx;
  background: #fff7ed;
  border-radius: 8rpx;
}
.warning-icon.data-v-b15160cf {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.warning-text.data-v-b15160cf {
  font-size: 24rpx;
  color: #92400e;
  font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
.container.data-v-b15160cf {
    padding: 20rpx;
}
.title.data-v-b15160cf,
.section-title.data-v-b15160cf {
    font-size: 32rpx;
}
.url-input.data-v-b15160cf {
    height: 80rpx;
}
.analyze-btn.data-v-b15160cf {
    width: 120rpx;
    height: 80rpx;
}
.music-card.data-v-b15160cf {
    padding: 16rpx;
}
.music-cover.data-v-b15160cf {
    width: 100rpx;
    height: 100rpx;
}
.platform-grid.data-v-b15160cf {
    grid-template-columns: 1fr;
}
}