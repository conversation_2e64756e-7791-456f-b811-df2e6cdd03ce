"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentText: "",
      activeTab: "marketing",
      categoryLabels: {
        marketing: "营销文案",
        social: "朋友圈",
        festival: "节日祝福",
        emotion: "情感表达"
      },
      textCategories: {
        marketing: [
          "限时特惠，错过就是损失！",
          "品质生活，从这里开始。",
          "不只是产品，更是生活态度。",
          "用心做好每一个细节。",
          "让美好，触手可及。",
          "匠心品质，值得信赖。",
          "创新设计，引领潮流。",
          "专业服务，贴心体验。",
          "性价比之选，不容错过。",
          "全新升级，超越期待。"
        ],
        social: [
          "生活不止眼前的苟且，还有诗和远方。",
          "愿你被这个世界温柔以待。",
          "做自己生活的主角，而不是别人故事的配角。",
          "每一天都是新的开始。",
          "相信美好的事情即将发生。",
          "岁月不老，友情不散。",
          "慢慢来，不着急，生活给你出了难题，但也会给你答案。",
          "愿你三冬暖，愿你春不寒。",
          "温柔半两，从容一生。",
          "把平凡的日子过得闪闪发光。"
        ],
        festival: [
          "新年快乐，万事如意！",
          "愿你在新的一年里，所有的梦想都能实现。",
          "祝福你，生日快乐，天天开心！",
          "中秋佳节，团团圆圆。",
          "圣诞快乐，愿你被爱包围。",
          "春节到，福气到，愿你笑口常开。",
          "端午安康，粽子香甜。",
          "七夕快乐，愿天下有情人终成眷属。",
          "国庆节快乐，祖国繁荣昌盛。",
          "元宵节快乐，团团圆圆。"
        ],
        emotion: [
          "有些话，只能说给懂的人听。",
          "时间会告诉我们，简单的喜欢最长远。",
          "愿所有的相遇，都是久别重逢。",
          "你是我见过最美的意外。",
          "陪伴是最长情的告白。",
          "想你的时候，就像雪花飘在心田。",
          "遇见你，是我最美的意外。",
          "愿得一人心，白首不分离。",
          "山河不如你，万千星辰不如你。",
          "余生很长，我想和你一起走。"
        ]
      }
    };
  },
  methods: {
    getCategoryIcon(category) {
      const icons = {
        marketing: "📈",
        social: "🌟",
        festival: "🎉",
        emotion: "💕"
      };
      return icons[category] || "📝";
    },
    selectText(text) {
      this.currentText = text;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "文案已复制",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    shareText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "文案已复制，可直接分享",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    getRandomText(category) {
      const texts = this.textCategories[category];
      const randomText = texts[Math.floor(Math.random() * texts.length)];
      this.currentText = randomText;
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort();
      }
    }
  },
  // 配置页面的分享行为
  onShareAppMessage(res) {
    const sharedText = this.currentText || "精选各类文案，助你表达更精彩";
    return {
      title: "文案库 - " + sharedText,
      path: "/pages/tools/text-library",
      imageUrl: "/static/share-cover.png"
      // 可选：设置分享图片
    };
  },
  // 开启分享到朋友圈
  onShareTimeline() {
    return {
      title: "文案库 - 精选各类文案，助你表达更精彩",
      path: "/pages/tools/text-library",
      imageUrl: "/static/share-cover.png"
      // 可选：设置分享图片
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.categoryLabels, (label, category, i0) => {
      return {
        a: common_vendor.t($options.getCategoryIcon(category)),
        b: common_vendor.t(label),
        c: category,
        d: common_vendor.o(($event) => $data.activeTab = category, category),
        e: common_vendor.n({
          active: $data.activeTab === category
        })
      };
    }),
    b: common_vendor.t($options.getCategoryIcon($data.activeTab)),
    c: common_vendor.t($data.categoryLabels[$data.activeTab]),
    d: common_vendor.o(($event) => $options.getRandomText($data.activeTab)),
    e: common_vendor.f($data.textCategories[$data.activeTab], (text, index, i0) => {
      return {
        a: common_vendor.t(text),
        b: common_vendor.o(($event) => $options.copyText(text), index),
        c: common_vendor.o(($event) => $options.shareText(text), index),
        d: index,
        e: common_vendor.o(($event) => $options.selectText(text), index)
      };
    }),
    f: $data.currentText
  }, $data.currentText ? {
    g: common_vendor.t($data.currentText),
    h: common_vendor.o(($event) => $options.copyText($data.currentText)),
    i: common_vendor.o(($event) => $options.shareText($data.currentText))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-fc4ba18f"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/text-library.js.map
