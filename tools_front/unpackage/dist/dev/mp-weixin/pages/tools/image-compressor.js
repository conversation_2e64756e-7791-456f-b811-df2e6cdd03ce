"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedFile: null,
      quality: 80,
      isCompressing: false,
      compressedInfo: null,
      compressedImage: null,
      // 存储压缩后的图片路径
      canvasContext: null,
      canvasId: "compressCanvas"
    };
  },
  computed: {
    originalSize() {
      if (!this.selectedFile)
        return "0";
      return (this.selectedFile.size / 1024 / 1024).toFixed(2);
    }
  },
  onReady() {
    const query = common_vendor.index.createSelectorQuery().in(this);
    query.select("#compressCanvas").fields({ node: true, size: true }).exec((res) => {
      const canvas = res[0].node;
      this.canvasContext = canvas.getContext("2d");
    });
  },
  methods: {
    selectImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getFileInfo({
            filePath: tempFilePath,
            success: (fileInfo) => {
              this.selectedFile = {
                name: tempFilePath.split("/").pop(),
                size: fileInfo.size,
                path: tempFilePath
              };
              this.compressedInfo = null;
              this.compressedImage = null;
            }
          });
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    onQualityChange(e) {
      this.quality = e.detail.value;
    },
    setQuality(value) {
      this.quality = value;
    },
    async handleCompress() {
      if (!this.selectedFile || this.isCompressing)
        return;
      this.isCompressing = true;
      try {
        const imageInfo = await this.getImageInfo(this.selectedFile.path);
        let { width, height } = imageInfo;
        const maxSize = 2048;
        if (width > maxSize || height > maxSize) {
          const ratio = Math.min(maxSize / width, maxSize / height);
          width = Math.floor(width * ratio);
          height = Math.floor(height * ratio);
        }
        const canvas = await this.createOffscreenCanvas(width, height);
        const ctx = canvas.getContext("2d");
        const image = canvas.createImage();
        await new Promise((resolve, reject) => {
          image.onload = resolve;
          image.onerror = reject;
          image.src = this.selectedFile.path;
        });
        ctx.drawImage(image, 0, 0, width, height);
        let quality = this.quality / 100;
        let format = "jpg";
        if (this.quality <= 30) {
          quality = 0.3;
        } else if (this.quality <= 60) {
          quality = 0.6;
        }
        const compressedImagePath = await this.canvasToTempFilePath(canvas, {
          quality,
          fileType: format
        });
        const compressedFileInfo = await this.getFileInfo(compressedImagePath);
        if (compressedFileInfo.size > this.selectedFile.size) {
          quality = Math.min(quality * 0.8, 0.5);
          const recompressedImagePath = await this.canvasToTempFilePath(canvas, {
            quality,
            fileType: format
          });
          const recompressedFileInfo = await this.getFileInfo(recompressedImagePath);
          this.compressedImage = recompressedImagePath;
          this.compressedInfo = {
            originalSize: this.originalSize,
            compressedSize: (recompressedFileInfo.size / 1024 / 1024).toFixed(2),
            compressionRatio: ((1 - recompressedFileInfo.size / this.selectedFile.size) * 100).toFixed(1)
          };
        } else {
          this.compressedImage = compressedImagePath;
          this.compressedInfo = {
            originalSize: this.originalSize,
            compressedSize: (compressedFileInfo.size / 1024 / 1024).toFixed(2),
            compressionRatio: ((1 - compressedFileInfo.size / this.selectedFile.size) * 100).toFixed(1)
          };
        }
        common_vendor.index.showToast({
          title: "压缩完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-compressor.vue:308", "压缩失败:", error);
        common_vendor.index.showToast({
          title: "压缩失败",
          icon: "none"
        });
      } finally {
        this.isCompressing = false;
      }
    },
    // 获取图片信息
    getImageInfo(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getImageInfo({
          src: filePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 创建离屏 canvas
    createOffscreenCanvas(width, height) {
      const canvas = common_vendor.index.createOffscreenCanvas({
        type: "2d",
        width,
        height
      });
      return canvas;
    },
    // Canvas 转临时文件
    canvasToTempFilePath(canvas, options) {
      return new Promise((resolve, reject) => {
        common_vendor.index.canvasToTempFilePath({
          canvas,
          quality: options.quality,
          fileType: options.fileType,
          success: (res) => resolve(res.tempFilePath),
          fail: reject
        });
      });
    },
    // 获取文件信息
    getFileInfo(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getFileInfo({
          filePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 下载图片
    downloadImage() {
      if (!this.compressedImage)
        return;
      common_vendor.index.showLoading({
        title: "正在下载"
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.compressedImage,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
          common_vendor.index.__f__("error", "at pages/tools/image-compressor.vue:387", "保存失败:", err);
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.selectImage && $options.selectImage(...args)),
    b: $data.selectedFile
  }, $data.selectedFile ? {
    c: common_vendor.t($data.selectedFile.name),
    d: common_vendor.t($options.originalSize)
  } : {}, {
    e: $data.selectedFile
  }, $data.selectedFile ? {
    f: common_vendor.t($data.quality),
    g: $data.quality,
    h: common_vendor.o((...args) => $options.onQualityChange && $options.onQualityChange(...args)),
    i: $data.quality === 30 ? 1 : "",
    j: common_vendor.o(($event) => $options.setQuality(30)),
    k: $data.quality === 60 ? 1 : "",
    l: common_vendor.o(($event) => $options.setQuality(60)),
    m: $data.quality === 80 ? 1 : "",
    n: common_vendor.o(($event) => $options.setQuality(80))
  } : {}, {
    o: $data.selectedFile
  }, $data.selectedFile ? {
    p: common_vendor.t($data.isCompressing ? "压缩中..." : "开始压缩"),
    q: $data.isCompressing ? 1 : "",
    r: common_vendor.o((...args) => $options.handleCompress && $options.handleCompress(...args))
  } : {}, {
    s: $data.compressedInfo
  }, $data.compressedInfo ? {
    t: common_vendor.t($data.compressedInfo.originalSize),
    v: common_vendor.t($data.compressedInfo.compressedSize),
    w: common_vendor.t($data.compressedInfo.compressionRatio),
    x: common_vendor.o((...args) => $options.downloadImage && $options.downloadImage(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6f26eb81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-compressor.js.map
