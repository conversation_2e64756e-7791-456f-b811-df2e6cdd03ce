/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-4978cc18 {
  display: flex;
}
.flex-1.data-v-4978cc18 {
  flex: 1;
}
.items-center.data-v-4978cc18 {
  align-items: center;
}
.justify-center.data-v-4978cc18 {
  justify-content: center;
}
.justify-between.data-v-4978cc18 {
  justify-content: space-between;
}
.text-center.data-v-4978cc18 {
  text-align: center;
}
.rounded.data-v-4978cc18 {
  border-radius: 3px;
}
.rounded-lg.data-v-4978cc18 {
  border-radius: 6px;
}
.shadow.data-v-4978cc18 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-4978cc18 {
  padding: 16rpx;
}
.m-4.data-v-4978cc18 {
  margin: 16rpx;
}
.mb-4.data-v-4978cc18 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-4978cc18 {
  margin-top: 16rpx;
}
.crazy-quotes.data-v-4978cc18 {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-4978cc18 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-4978cc18 {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-4978cc18 {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-4978cc18 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-4978cc18 {
  padding: 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.selected-card.data-v-4978cc18 {
  background: white;
  border-radius: 20rpx;
  padding: 0;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.selected-card .card-header.data-v-4978cc18 {
  padding: 30rpx 30rpx 0;
}
.selected-card .card-header .card-title.data-v-4978cc18 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.selected-card .card-header .card-title .title-content.data-v-4978cc18 {
  display: flex;
  align-items: center;
}
.selected-card .card-header .card-title .title-content .title-icon.data-v-4978cc18 {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #fbbf24;
}
.selected-card .card-header .card-title .title-content .title-text.data-v-4978cc18 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.selected-card .card-header .card-title .refresh-btn.data-v-4978cc18 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
}
.selected-card .card-header .card-title .refresh-btn .refresh-icon.data-v-4978cc18 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.selected-card .card-header .card-title .refresh-btn .refresh-text.data-v-4978cc18 {
  font-size: 24rpx;
  color: #666;
}
.selected-card .card-content.data-v-4978cc18 {
  padding: 30rpx;
}
.selected-card .card-content .quote-display.data-v-4978cc18 {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  padding: 48rpx;
  border-radius: 16rpx;
  border-left: 8rpx solid #fbbf24;
  margin-bottom: 30rpx;
}
.selected-card .card-content .quote-display .quote-text.data-v-4978cc18 {
  display: block;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #92400e;
  line-height: 1.5;
}
.selected-card .card-content .copy-btn.data-v-4978cc18 {
  width: 100%;
  margin: 0;
  padding: 24rpx 0;
  background: #3b82f6;
  border-radius: 0 0 20rpx 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
}
.selected-card .card-content .copy-btn.data-v-4978cc18:active {
  transform: scale(0.98);
}
.selected-card .card-content .copy-btn .copy-icon.data-v-4978cc18 {
  font-size: 28rpx;
  margin-right: 12rpx;
  color: white;
}
.selected-card .card-content .copy-btn .copy-text.data-v-4978cc18 {
  font-size: 28rpx;
  color: white;
  font-weight: 500;
}
.quote-section.data-v-4978cc18 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.quote-section .section-header.data-v-4978cc18 {
  margin-bottom: 30rpx;
}
.quote-section .section-header .section-title.data-v-4978cc18 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.quote-section .quote-list .quote-item.data-v-4978cc18 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
}
.quote-section .quote-list .quote-item .quote-content.data-v-4978cc18 {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.4;
}
.quote-section .quote-list .quote-item .item-copy-btn.data-v-4978cc18 {
  margin-left: 20rpx;
}
.quote-section .quote-list .quote-item .item-copy-btn .copy-icon.data-v-4978cc18 {
  font-size: 28rpx;
  color: #666;
}
.quote-section .quote-list .quote-item.classic-item.data-v-4978cc18 {
  background: #fef2f2;
  border: 2rpx solid #fecaca;
}
.quote-section .quote-list .quote-item.classic-item .quote-content.data-v-4978cc18 {
  color: #b91c1c;
}
.quote-section .quote-list .quote-item.emoji-item.data-v-4978cc18 {
  background: #faf5ff;
  border: 2rpx solid #e9d5ff;
}
.quote-section .quote-list .quote-item.emoji-item .emoji-content.data-v-4978cc18 {
  color: #7c3aed;
  font-size: 36rpx;
}
.quote-section .quote-list .quote-item.action-item.data-v-4978cc18 {
  background: #fff7ed;
  border: 2rpx solid #fed7aa;
}
.quote-section .quote-list .quote-item.action-item .action-content.data-v-4978cc18 {
  color: #ea580c;
  font-weight: bold;
  font-size: 36rpx;
}
.info-section.data-v-4978cc18 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.info-section .section-header.data-v-4978cc18 {
  margin-bottom: 30rpx;
}
.info-section .section-header .section-title.data-v-4978cc18 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.info-section .info-content .info-item.data-v-4978cc18 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}