"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "MD5Encryptor",
  data() {
    return {
      inputText: "",
      results: []
    };
  },
  computed: {
    lineCount() {
      if (!this.inputText.trim())
        return 0;
      return this.inputText.trim().split("\n").filter((line) => line.trim()).length;
    }
  },
  methods: {
    generateMD5() {
      if (!this.inputText.trim())
        return;
      const lines = this.inputText.trim().split("\n").filter((line) => line.trim());
      this.results = lines.map((line) => ({
        original: line.trim(),
        hash: this.calculateMD5(line.trim())
      }));
      common_vendor.index.showToast({
        title: `成功加密${this.results.length}个文本`,
        icon: "success"
      });
    },
    calculateMD5(text) {
      return common_vendor.CryptoJS.MD5(text).toString();
    },
    copyResult(text) {
      if (!text)
        return;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "MD5值已复制",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    copyAllResults() {
      if (this.results.length === 0)
        return;
      const allResults = this.results.map((result, index) => `${index + 1}. ${result.original} -> ${result.hash}`).join("\n");
      common_vendor.index.setClipboardData({
        data: allResults,
        success: () => {
          common_vendor.index.showToast({
            title: "全部结果已复制",
            icon: "success"
          });
        }
      });
    },
    exportResults() {
      if (this.results.length === 0)
        return;
      const exportData = {
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        count: this.results.length,
        results: this.results
      };
      const jsonString = JSON.stringify(exportData, null, 2);
      common_vendor.index.setClipboardData({
        data: jsonString,
        success: () => {
          common_vendor.index.showToast({
            title: "数据已复制为JSON格式",
            icon: "success"
          });
        }
      });
    },
    clearAll() {
      this.inputText = "";
      this.results = [];
      common_vendor.index.showToast({
        title: "已清空",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputText || $data.results.length > 0
  }, $data.inputText || $data.results.length > 0 ? {
    b: common_vendor.o((...args) => $options.clearAll && $options.clearAll(...args))
  } : {}, {
    c: $data.inputText,
    d: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    e: common_vendor.t($options.lineCount),
    f: common_vendor.t($data.inputText.length),
    g: common_vendor.o((...args) => $options.generateMD5 && $options.generateMD5(...args)),
    h: !$data.inputText.trim(),
    i: common_vendor.n(!$data.inputText.trim() ? "disabled" : ""),
    j: $data.results.length > 0
  }, $data.results.length > 0 ? {
    k: common_vendor.t($data.results.length),
    l: common_vendor.o((...args) => $options.exportResults && $options.exportResults(...args)),
    m: common_vendor.o((...args) => $options.copyAllResults && $options.copyAllResults(...args)),
    n: common_vendor.f($data.results, (result, index, i0) => {
      return {
        a: common_vendor.t(result.original),
        b: common_vendor.t(index + 1),
        c: common_vendor.t(result.hash),
        d: index,
        e: common_vendor.o(($event) => $options.copyResult(result.hash), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c2cde938"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/md5-encryptor.js.map
