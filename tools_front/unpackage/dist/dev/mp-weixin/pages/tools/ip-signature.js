"use strict";
const common_vendor = require("../../common/vendor.js");
const ColorPicker = () => "../../components/ColorPicker.js";
const _sfc_main = {
  name: "IPSignature",
  components: {
    ColorPicker
  },
  data() {
    return {
      currentIP: "",
      location: "",
      isp: "",
      currentTime: "",
      signatureText: "",
      loading: false,
      // 添加定时器管理
      timeInterval: null,
      // 样式设置
      fontSize: 32,
      fontColor: "#1a1a1a",
      bgColor: "#f8f9fa",
      selectedFont: "Arial",
      selectedTemplate: "simple",
      // 颜色选择器状态
      showColorPicker: false,
      currentEditType: "",
      // 'font' 或 'bg'
      // 模拟IP信息
      mockIPInfo: {
        ip: "*************",
        location: "中国 广东省 深圳市",
        isp: "中国电信"
      },
      fontOptions: [
        { value: "Arial", label: "Arial" },
        { value: "Microsoft YaHei", label: "雅黑" },
        { value: "monospace", label: "等宽" }
      ],
      colorPresets: [
        { name: "经典黑白", font: "#1a1a1a", bg: "#ffffff" },
        { name: "深色主题", font: "#ffffff", bg: "#1a1a1a" },
        { name: "蓝色科技", font: "#ffffff", bg: "#1e40af" },
        { name: "绿色清新", font: "#ffffff", bg: "#059669" },
        { name: "紫色优雅", font: "#ffffff", bg: "#7c3aed" },
        { name: "橙色活力", font: "#ffffff", bg: "#ea580c" }
      ],
      templates: [
        { id: "simple", name: "简洁", preview: "📝" },
        { id: "tech", name: "科技", preview: "💻" },
        { id: "card", name: "卡片", preview: "🃏" },
        { id: "banner", name: "横幅", preview: "🏷️" }
      ]
    };
  },
  mounted() {
    this.initializeComponent();
  },
  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
  },
  methods: {
    // 初始化组件
    initializeComponent() {
      this.updateTime();
      this.fetchIPInfo();
      this.timeInterval = setInterval(() => {
        this.updateTime();
      }, 1e3);
    },
    // 更新时间 - 修复递归更新问题
    updateTime() {
      const now = /* @__PURE__ */ new Date();
      const newTime = now.toLocaleString();
      if (newTime !== this.currentTime) {
        this.currentTime = newTime;
        this.$nextTick(() => {
          this.generateSignature();
        });
      }
    },
    // 获取IP信息
    fetchIPInfo() {
      this.loading = true;
      setTimeout(() => {
        this.currentIP = this.mockIPInfo.ip;
        this.location = this.mockIPInfo.location;
        this.isp = this.mockIPInfo.isp;
        this.generateSignature();
        this.loading = false;
        common_vendor.index.showToast({
          title: "IP信息已更新",
          icon: "success",
          duration: 1500
        });
      }, 1500);
    },
    // 生成签名档
    generateSignature() {
      const ip = this.currentIP || "获取中...";
      const location = this.location || "获取中...";
      const isp = this.isp || "获取中...";
      const time = this.currentTime || "获取中...";
      const safePadEnd = (str, length, fillStr = " ") => {
        if (!str)
          return fillStr.repeat(length);
        str = String(str);
        if (str.length >= length)
          return str.substring(0, length);
        return str + fillStr.repeat(length - str.length);
      };
      const templates = {
        simple: `IP: ${ip}
位置: ${location}
ISP: ${isp}
时间: ${time}`,
        tech: `[IP] ${ip}
[LOC] ${location}
[ISP] ${isp}
[TIME] ${time}`,
        card: `╭─────────────────────────────╮
│ IP: ${safePadEnd(ip, 18)} │
│ 位置: ${safePadEnd(location, 16)} │
│ ISP: ${safePadEnd(isp, 18)} │
│ 时间: ${safePadEnd(time, 16)} │
╰─────────────────────────────╯`,
        banner: `🌐 ${ip} | 📍 ${location} | 📡 ${isp}`
      };
      this.signatureText = templates[this.selectedTemplate] || templates.simple;
    },
    // 重新生成签名档
    regenerateSignature() {
      this.updateTime();
      this.generateSignature();
      common_vendor.index.showToast({
        title: "签名档已更新",
        icon: "success",
        duration: 1500
      });
    },
    // 字体大小变化
    onFontSizeChange(e) {
      this.fontSize = parseInt(e.detail.value);
    },
    // 字体样式变化
    onFontChange(e) {
      this.selectedFont = e.detail.value;
    },
    // 处理颜色点击事件
    handleColorClick(type) {
      common_vendor.index.__f__("log", "at pages/tools/ip-signature.vue:409", "颜色点击:", type);
      if (this.showColorPicker)
        return;
      this.currentEditType = type;
      this.showColorPicker = true;
    },
    // 颜色选择器确认
    onColorConfirm(color) {
      common_vendor.index.__f__("log", "at pages/tools/ip-signature.vue:418", "颜色确认:", color);
      if (!color || !this.currentEditType)
        return;
      try {
        if (this.currentEditType === "font") {
          this.fontColor = color;
        } else if (this.currentEditType === "bg") {
          this.bgColor = color;
        }
        this.showColorPicker = false;
        this.currentEditType = "";
        this.$nextTick(() => {
          this.generateSignature();
        });
        common_vendor.index.showToast({
          title: "颜色已更新",
          icon: "success",
          duration: 1e3
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/ip-signature.vue:442", "颜色确认错误:", error);
        this.showColorPicker = false;
        this.currentEditType = "";
      }
    },
    // 颜色选择器关闭
    onColorClose() {
      common_vendor.index.__f__("log", "at pages/tools/ip-signature.vue:450", "颜色选择器关闭");
      this.showColorPicker = false;
      this.currentEditType = "";
    },
    // 应用预设配色
    applyColorPreset(preset) {
      this.fontColor = preset.font;
      this.bgColor = preset.bg;
      common_vendor.index.showToast({
        title: `已应用${preset.name}配色`,
        icon: "success",
        duration: 1500
      });
    },
    // 选择模板
    selectTemplate(templateId) {
      this.selectedTemplate = templateId;
      this.generateSignature();
      const template = this.templates.find((t) => t.id === templateId);
      common_vendor.index.showToast({
        title: `已选择${template.name}模板`,
        icon: "success",
        duration: 1500
      });
    },
    // 复制签名档
    copySignature() {
      common_vendor.index.setClipboardData({
        data: this.signatureText,
        success: () => {
          common_vendor.index.showToast({
            title: "签名档已复制",
            icon: "success",
            duration: 2e3
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    // 下载图片
    downloadImage() {
      common_vendor.index.showLoading({
        title: "正在生成图片...",
        mask: true
      });
      try {
        const canvasWidth = 800;
        const canvasHeight = 600;
        const baseFontSize = 28;
        const ctx = common_vendor.index.createCanvasContext("signatureCanvas", this);
        ctx.width = canvasWidth;
        ctx.height = canvasHeight;
        ctx.setFillStyle(this.bgColor);
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        ctx.setFillStyle(this.fontColor);
        ctx.setFontSize(baseFontSize);
        ctx.setTextAlign("center");
        ctx.setTextBaseline("middle");
        let fontFamily = "Arial";
        if (this.selectedFont === "Microsoft YaHei") {
          fontFamily = "Microsoft YaHei, sans-serif";
        } else if (this.selectedFont === "monospace") {
          fontFamily = "Consolas, Monaco, monospace";
        }
        const lines = this.signatureText.split("\n");
        const lineHeight = baseFontSize * 1.5;
        const totalTextHeight = lineHeight * lines.length;
        const startY = (canvasHeight - totalTextHeight) / 2;
        lines.forEach((line, index) => {
          const y = startY + index * lineHeight + lineHeight / 2;
          if (this.selectedTemplate === "card") {
            ctx.setFontSize(baseFontSize);
            ctx.setTextAlign("center");
            ctx.fillText(line, canvasWidth / 2, y);
          } else {
            ctx.setFontSize(baseFontSize);
            ctx.setTextAlign("center");
            ctx.fillText(line, canvasWidth / 2, y);
          }
        });
        ctx.draw(false, () => {
          setTimeout(() => {
            common_vendor.index.canvasToTempFilePath({
              canvasId: "signatureCanvas",
              x: 0,
              y: 0,
              width: canvasWidth,
              height: canvasHeight,
              destWidth: canvasWidth,
              destHeight: canvasHeight,
              fileType: "png",
              quality: 1,
              success: (res) => {
                common_vendor.index.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    common_vendor.index.hideLoading();
                    common_vendor.index.showToast({
                      title: "图片已保存到相册",
                      icon: "success"
                    });
                  },
                  fail: (err) => {
                    common_vendor.index.__f__("error", "at pages/tools/ip-signature.vue:583", "保存图片失败:", err);
                    common_vendor.index.hideLoading();
                    common_vendor.index.showToast({
                      title: "保存图片失败",
                      icon: "none"
                    });
                  }
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/tools/ip-signature.vue:593", "生成图片失败:", err);
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "生成图片失败",
                  icon: "none"
                });
              }
            });
          }, 500);
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/ip-signature.vue:604", "下载图片错误:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "生成图片失败",
          icon: "none"
        });
      }
    },
    // 分享签名档
    shareSignature() {
      common_vendor.index.setClipboardData({
        data: this.signatureText,
        success: () => {
          common_vendor.index.showToast({
            title: "内容已复制，可分享给朋友",
            icon: "success",
            duration: 2e3
          });
        }
      });
    }
  }
};
if (!Array) {
  const _component_ColorPicker = common_vendor.resolveComponent("ColorPicker");
  _component_ColorPicker();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n({
      spinning: $data.loading
    }),
    b: common_vendor.t($data.loading ? "获取中" : "刷新"),
    c: common_vendor.o((...args) => $options.fetchIPInfo && $options.fetchIPInfo(...args)),
    d: $data.loading,
    e: common_vendor.n({
      loading: $data.loading
    }),
    f: $data.loading
  }, $data.loading ? {} : {
    g: common_vendor.t($data.currentIP),
    h: common_vendor.t($data.location),
    i: common_vendor.t($data.isp),
    j: common_vendor.t($data.currentTime)
  }, {
    k: common_vendor.o((...args) => $options.regenerateSignature && $options.regenerateSignature(...args)),
    l: common_vendor.t($data.signatureText),
    m: $data.bgColor,
    n: $data.fontColor,
    o: $data.fontSize + "rpx",
    p: $data.selectedFont,
    q: $data.fontSize,
    r: common_vendor.o((...args) => $options.onFontSizeChange && $options.onFontSizeChange(...args)),
    s: common_vendor.t($data.fontSize),
    t: common_vendor.f($data.fontOptions, (font, k0, i0) => {
      return {
        a: font.value,
        b: $data.selectedFont === font.value,
        c: common_vendor.t(font.label),
        d: font.value,
        e: font.value
      };
    }),
    v: common_vendor.o((...args) => $options.onFontChange && $options.onFontChange(...args)),
    w: $data.fontColor,
    x: common_vendor.o(($event) => $options.handleColorClick("font")),
    y: common_vendor.t($data.fontColor),
    z: $data.bgColor,
    A: common_vendor.o(($event) => $options.handleColorClick("bg")),
    B: common_vendor.t($data.bgColor),
    C: common_vendor.f($data.colorPresets, (preset, k0, i0) => {
      return {
        a: preset.font,
        b: preset.bg,
        c: common_vendor.t(preset.name),
        d: preset.name,
        e: common_vendor.o(($event) => $options.applyColorPreset(preset), preset.name)
      };
    }),
    D: common_vendor.f($data.templates, (template, k0, i0) => {
      return {
        a: common_vendor.t(template.preview),
        b: common_vendor.t(template.name),
        c: template.id,
        d: common_vendor.n({
          active: $data.selectedTemplate === template.id
        }),
        e: common_vendor.o(($event) => $options.selectTemplate(template.id), template.id)
      };
    }),
    E: common_vendor.o((...args) => $options.copySignature && $options.copySignature(...args)),
    F: common_vendor.o((...args) => $options.downloadImage && $options.downloadImage(...args)),
    G: $data.showColorPicker
  }, $data.showColorPicker ? {
    H: common_vendor.o($options.onColorConfirm),
    I: common_vendor.o($options.onColorClose),
    J: common_vendor.p({
      visible: true,
      value: $data.currentEditType === "font" ? $data.fontColor : $data.bgColor
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-099d725b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/ip-signature.js.map
