"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "FlexLayoutGenerator",
  data() {
    return {
      flexDirection: "row",
      justifyContent: "flex-start",
      alignItems: "stretch",
      flexWrap: "nowrap",
      gap: 16,
      itemCount: 3,
      containerHeight: 300,
      highlightedItem: null,
      directionOptions: [
        { value: "row", label: "水平", icon: "→" },
        { value: "row-reverse", label: "水平反转", icon: "←" },
        { value: "column", label: "垂直", icon: "↓" },
        { value: "column-reverse", label: "垂直反转", icon: "↑" }
      ],
      justifyOptions: [
        { value: "flex-start", label: "起点对齐" },
        { value: "flex-end", label: "终点对齐" },
        { value: "center", label: "居中" },
        { value: "space-between", label: "两端对齐" },
        { value: "space-around", label: "环绕" },
        { value: "space-evenly", label: "均匀" }
      ],
      alignOptions: [
        { value: "stretch", label: "拉伸" },
        { value: "flex-start", label: "起点" },
        { value: "flex-end", label: "终点" },
        { value: "center", label: "居中" },
        { value: "baseline", label: "基线" }
      ],
      wrapOptions: [
        { value: "nowrap", label: "不换行" },
        { value: "wrap", label: "换行" },
        { value: "wrap-reverse", label: "反向换行" }
      ],
      flexTemplates: [
        {
          name: "水平居中",
          direction: "row",
          justify: "center",
          align: "center",
          wrap: "nowrap",
          preview: [
            { style: "background: #ff6b6b; margin: 0 4px;" },
            { style: "background: #4ecdc4; margin: 0 4px;" },
            { style: "background: #45b7d1; margin: 0 4px;" }
          ]
        },
        {
          name: "两端对齐",
          direction: "row",
          justify: "space-between",
          align: "center",
          wrap: "nowrap",
          preview: [
            { style: "background: #ff6b6b;" },
            { style: "background: #4ecdc4; margin: 0 8px;" },
            { style: "background: #45b7d1;" }
          ]
        },
        {
          name: "垂直堆叠",
          direction: "column",
          justify: "flex-start",
          align: "stretch",
          wrap: "nowrap",
          preview: [
            { style: "background: #ff6b6b; width: 100%; margin-bottom: 4px;" },
            { style: "background: #4ecdc4; width: 100%; margin-bottom: 4px;" },
            { style: "background: #45b7d1; width: 100%;" }
          ]
        },
        {
          name: "网格布局",
          direction: "row",
          justify: "space-around",
          align: "flex-start",
          wrap: "wrap",
          preview: [
            { style: "background: #ff6b6b; width: 45%; margin-bottom: 4px;" },
            { style: "background: #4ecdc4; width: 45%; margin-bottom: 4px;" },
            { style: "background: #45b7d1; width: 45%;" },
            { style: "background: #96ceb4; width: 45%;" }
          ]
        }
      ]
    };
  },
  computed: {
    previewStyle() {
      return {
        display: "flex",
        flexDirection: this.flexDirection,
        justifyContent: this.justifyContent,
        alignItems: this.alignItems,
        flexWrap: this.flexWrap,
        gap: `${this.gap}rpx`,
        height: `${this.containerHeight}rpx`,
        border: "2rpx solid #e5e7eb",
        borderRadius: "12rpx",
        padding: "24rpx"
      };
    },
    generatedCSS() {
      return `.flex-container {
  display: flex;
  flex-direction: ${this.flexDirection};
  justify-content: ${this.justifyContent};
  align-items: ${this.alignItems};
  flex-wrap: ${this.flexWrap};
  gap: ${this.gap}px;
  padding: 20px;
  min-height: ${this.containerHeight}px;
}

.flex-item {
  background: #3b82f6;
  color: white;
  padding: 16px;
  border-radius: 8px;
  min-width: 100px;
  text-align: center;
  transition: all 0.3s ease;
}

.flex-item:hover {
  background: #2563eb;
  transform: translateY(-2px);
}`;
    }
  },
  methods: {
    setFlexDirection(direction) {
      this.flexDirection = direction;
      this.highlightedItem = null;
    },
    setJustifyContent(justify) {
      this.justifyContent = justify;
      this.highlightedItem = null;
    },
    setAlignItems(align) {
      this.alignItems = align;
      this.highlightedItem = null;
    },
    setFlexWrap(wrap) {
      this.flexWrap = wrap;
      this.highlightedItem = null;
    },
    onGapChange(e) {
      this.gap = e.detail.value * 2;
    },
    onItemCountChange(e) {
      this.itemCount = e.detail.value;
      this.highlightedItem = null;
    },
    onHeightChange(e) {
      this.containerHeight = 200 + e.detail.value * 10;
    },
    applyTemplate(template) {
      this.flexDirection = template.direction;
      this.justifyContent = template.justify;
      this.alignItems = template.align;
      this.flexWrap = template.wrap;
      this.highlightedItem = null;
      common_vendor.index.showToast({
        title: "模板已应用",
        icon: "success"
      });
    },
    refreshPreview() {
      this.highlightedItem = null;
      common_vendor.index.showToast({
        title: "预览已刷新",
        icon: "success"
      });
    },
    highlightItem(index) {
      this.highlightedItem = this.highlightedItem === index ? null : index;
    },
    copyToClipboard() {
      common_vendor.index.setClipboardData({
        data: this.generatedCSS,
        success: () => {
          common_vendor.index.showToast({
            title: "CSS代码已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.directionOptions, (direction, k0, i0) => {
      return {
        a: common_vendor.t(direction.icon),
        b: common_vendor.t(direction.label),
        c: direction.value,
        d: $data.flexDirection === direction.value ? 1 : "",
        e: common_vendor.o(($event) => $options.setFlexDirection(direction.value), direction.value)
      };
    }),
    b: common_vendor.f($data.justifyOptions, (justify, k0, i0) => {
      return {
        a: common_vendor.t(justify.label),
        b: justify.value,
        c: $data.justifyContent === justify.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setJustifyContent(justify.value), justify.value)
      };
    }),
    c: common_vendor.f($data.alignOptions, (align, k0, i0) => {
      return {
        a: common_vendor.t(align.label),
        b: align.value,
        c: $data.alignItems === align.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setAlignItems(align.value), align.value)
      };
    }),
    d: common_vendor.f($data.wrapOptions, (wrap, k0, i0) => {
      return {
        a: common_vendor.t(wrap.label),
        b: wrap.value,
        c: $data.flexWrap === wrap.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setFlexWrap(wrap.value), wrap.value)
      };
    }),
    e: common_vendor.t($data.gap),
    f: $data.gap / 2,
    g: common_vendor.o((...args) => $options.onGapChange && $options.onGapChange(...args)),
    h: common_vendor.t($data.itemCount),
    i: $data.itemCount,
    j: common_vendor.o((...args) => $options.onItemCountChange && $options.onItemCountChange(...args)),
    k: common_vendor.t($data.containerHeight),
    l: ($data.containerHeight - 200) / 10,
    m: common_vendor.o((...args) => $options.onHeightChange && $options.onHeightChange(...args)),
    n: common_vendor.f($data.flexTemplates, (template, k0, i0) => {
      return {
        a: common_vendor.f(template.preview, (item, index, i1) => {
          return {
            a: index,
            b: common_vendor.s(item.style)
          };
        }),
        b: common_vendor.t(template.name),
        c: template.name,
        d: common_vendor.o(($event) => $options.applyTemplate(template), template.name)
      };
    }),
    o: common_vendor.o((...args) => $options.refreshPreview && $options.refreshPreview(...args)),
    p: common_vendor.f($data.itemCount, (i, k0, i0) => {
      return {
        a: common_vendor.t(i),
        b: i,
        c: common_vendor.o(($event) => $options.highlightItem(i), i),
        d: $data.highlightedItem === i ? 1 : ""
      };
    }),
    q: common_vendor.s($options.previewStyle),
    r: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    s: common_vendor.t($options.generatedCSS)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e3128b2a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/flex-layout-generator.js.map
