/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-3e5e6762 {
  display: flex;
}
.flex-1.data-v-3e5e6762 {
  flex: 1;
}
.items-center.data-v-3e5e6762 {
  align-items: center;
}
.justify-center.data-v-3e5e6762 {
  justify-content: center;
}
.justify-between.data-v-3e5e6762 {
  justify-content: space-between;
}
.text-center.data-v-3e5e6762 {
  text-align: center;
}
.rounded.data-v-3e5e6762 {
  border-radius: 3px;
}
.rounded-lg.data-v-3e5e6762 {
  border-radius: 6px;
}
.shadow.data-v-3e5e6762 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-3e5e6762 {
  padding: 16rpx;
}
.m-4.data-v-3e5e6762 {
  margin: 16rpx;
}
.mb-4.data-v-3e5e6762 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-3e5e6762 {
  margin-top: 16rpx;
}
.strike-through-text.data-v-3e5e6762 {
  min-height: 100vh;
  background: #fafbfc;
}
.container.data-v-3e5e6762 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-3e5e6762 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-3e5e6762 {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-3e5e6762 {
  font-size: 40rpx;
  font-weight: 700;
  color: #222;
}
.subtitle.data-v-3e5e6762 {
  font-size: 28rpx;
  color: #888;
  line-height: 1.5;
}
.input-section.data-v-3e5e6762, .result-section.data-v-3e5e6762, .history-section.data-v-3e5e6762, .example-section.data-v-3e5e6762, .mapping-section.data-v-3e5e6762, .help-section.data-v-3e5e6762 {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #ececec;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.section-header.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 24rpx;
}
.section-icon.data-v-3e5e6762 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-3e5e6762 {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
}
.input-wrapper.data-v-3e5e6762 {
  position: relative;
}
.text-input.data-v-3e5e6762 {
  width: 100%;
  min-height: 96rpx;
  padding: 24rpx;
  border: 1.5rpx solid #d1d5db;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #222;
  background: #f6f7f9;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
  font-weight: 500;
  letter-spacing: 1rpx;
  outline: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.text-input.data-v-3e5e6762:focus {
  border-color: #bfc6d1;
  background: #fff;
  box-shadow: 0 0 0 3rpx rgba(180, 190, 210, 0.1);
}
.char-count.data-v-3e5e6762 {
  position: absolute;
  bottom: 8rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #b0b0b0;
}
.result-grid.data-v-3e5e6762 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.result-item.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  padding: 28rpx 24rpx;
  background: #f7f7fa;
  border-radius: 14rpx;
  border: 1.5rpx solid #e5e7eb;
  gap: 16rpx;
}
.result-item.data-v-3e5e6762:active {
  background: #ececec;
  transform: scale(0.98);
}
.result-text.data-v-3e5e6762 {
  font-size: 28rpx;
  color: #222;
  font-weight: 600;
  width: -webkit-fit-content;
  width: fit-content;
  padding: 8rpx 0;
  position: relative;
}
.result-text.unicode-strike.data-v-3e5e6762::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background: #ff4d4f;
  top: 50%;
  left: 0;
  transform: translateY(-50%) rotate(-5deg);
}
.result-text.double-strike.data-v-3e5e6762::before, .result-text.double-strike.data-v-3e5e6762::after {
  content: "";
  position: absolute;
  width: 100%;
  left: 0;
  height: 1.5px;
  transform-origin: center;
  background: #1890ff;
}
.result-text.double-strike.data-v-3e5e6762::before {
  top: 40%;
  transform: rotate(-8deg);
}
.result-text.double-strike.data-v-3e5e6762::after {
  top: 60%;
  transform: rotate(8deg);
}
.result-text.combined-strike.data-v-3e5e6762::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 3px;
  background: repeating-linear-gradient(-45deg, #722ed1, #722ed1 5px, transparent 5px, transparent 10px);
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
.result-text.strong-strike.data-v-3e5e6762::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 3px;
  background: #000;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}
.result-text.special-strike.data-v-3e5e6762::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #f5222d 0%, #fa8c16 25%, #52c41a 50%, #fa8c16 75%, #f5222d 100%);
  background-size: 200% 100%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  animation: gradientMove-3e5e6762 3s linear infinite;
}
.result-text.entity-strike.data-v-3e5e6762::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 2px;
  background: #13c2c2;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  opacity: 0.8;
  filter: blur(0.5px);
}
.result-text.entity-strike.data-v-3e5e6762::after {
  content: "";
  position: absolute;
  width: 100%;
  height: 1px;
  background: #13c2c2;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}
@keyframes gradientMove-3e5e6762 {
0% {
    background-position: 0% 50%;
}
100% {
    background-position: 100% 50%;
}
}
.result-label.data-v-3e5e6762 {
  font-size: 24rpx;
  color: #666;
  background: rgba(0, 0, 0, 0.04);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-weight: 500;
  margin-left: auto;
}
.result-item-actions.data-v-3e5e6762 {
  display: flex;
  gap: 8rpx;
}
.mini-btn.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.18s cubic-bezier(0.2, 0, 0.1, 1);
  font-size: 22rpx;
  color: #666;
}
.mini-btn.data-v-3e5e6762:active {
  background: #ececec;
  transform: scale(0.96);
}
.mini-icon.data-v-3e5e6762 {
  font-size: 20rpx;
  margin-right: 4rpx;
}
.history-grid.data-v-3e5e6762 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.history-item.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}
.history-item.data-v-3e5e6762:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.history-text.data-v-3e5e6762 {
  font-size: 26rpx;
  color: #374151;
  flex: 1;
  word-break: break-all;
  margin-right: 16rpx;
}
.history-actions.data-v-3e5e6762 {
  display: flex;
  gap: 8rpx;
}
.example-grid.data-v-3e5e6762 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}
.example-item.data-v-3e5e6762 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}
.example-item.data-v-3e5e6762:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.example-text.data-v-3e5e6762 {
  font-size: 26rpx;
  color: #374151;
  font-weight: 500;
}
.mapping-grid.data-v-3e5e6762 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.mapping-item.data-v-3e5e6762 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}
.mapping-label.data-v-3e5e6762 {
  font-size: 26rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
}
.mapping-desc.data-v-3e5e6762 {
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}
.help-content.data-v-3e5e6762 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-3e5e6762 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-3e5e6762:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-3e5e6762 {
    padding: 30rpx 10rpx;
}
.result-item.data-v-3e5e6762 {
    padding: 20rpx 10rpx;
}
}