<view class="min-h-screen bg-gray-50 data-v-0ecd2679"><view class="p-4 space-y-4 data-v-0ecd2679"><view class="bg-white rounded-lg shadow-sm data-v-0ecd2679"><view class="p-4 border-b border-gray-200 data-v-0ecd2679"><view class="text-base font-medium flex items-center data-v-0ecd2679"><keyboard class="mr-2 w-5 h-5 data-v-0ecd2679" u-i="0ecd2679-0" bind:__l="__l"/> 常用按键 </view></view><view class="p-4 data-v-0ecd2679"><view class="grid grid-cols-2 gap-2 data-v-0ecd2679"><view wx:for="{{a}}" wx:for-item="key" wx:key="c" class="p-3 border rounded-lg text-center data-v-0ecd2679"><view class="font-bold text-blue-600 data-v-0ecd2679">{{key.a}}</view><view class="text-sm data-v-0ecd2679">{{key.b}}</view></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-0ecd2679"><view class="p-4 border-b border-gray-200 data-v-0ecd2679"><view class="text-base font-medium data-v-0ecd2679">数字键 (48-57)</view></view><view class="p-4 data-v-0ecd2679"><view class="grid grid-cols-5 gap-2 data-v-0ecd2679"><view wx:for="{{b}}" wx:for-item="key" wx:key="c" class="p-3 border rounded-lg text-center data-v-0ecd2679"><view class="font-bold text-blue-600 data-v-0ecd2679">{{key.a}}</view><view class="text-sm data-v-0ecd2679">{{key.b}}</view></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-0ecd2679"><view class="p-4 border-b border-gray-200 data-v-0ecd2679"><view class="text-base font-medium data-v-0ecd2679">字母键 (65-90)</view></view><view class="p-4 data-v-0ecd2679"><view class="grid grid-cols-6 gap-2 data-v-0ecd2679"><view wx:for="{{c}}" wx:for-item="key" wx:key="c" class="p-3 border rounded-lg text-center data-v-0ecd2679"><view class="font-bold text-blue-600 data-v-0ecd2679">{{key.a}}</view><view class="text-sm data-v-0ecd2679">{{key.b}}</view></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-0ecd2679"><view class="p-4 border-b border-gray-200 data-v-0ecd2679"><view class="text-base font-medium data-v-0ecd2679">使用说明</view></view><view class="p-4 text-sm text-gray-600 space-y-2 data-v-0ecd2679"><view class="data-v-0ecd2679">• keyCode 是传统的键盘事件属性</view><view class="data-v-0ecd2679">• code 属性提供更精确的键盘位置信息</view><view class="data-v-0ecd2679">• 现代开发建议使用 key 属性而不是 keyCode</view><view class="data-v-0ecd2679">• 适用于 JavaScript 键盘事件处理</view></view></view></view></view>