"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      gradientType: "linear",
      direction: "to right",
      colors: [
        { color: "#8B5CF6", position: 0 },
        { color: "#06B6D4", position: 100 }
      ],
      directions: [
        { value: "to right", label: "向右" },
        { value: "to left", label: "向左" },
        { value: "to top", label: "向上" },
        { value: "to bottom", label: "向下" },
        { value: "to top right", label: "右上" },
        { value: "to top left", label: "左上" },
        { value: "to bottom right", label: "右下" },
        { value: "to bottom left", label: "左下" }
      ],
      // 颜色选择器相关
      showColorPicker: false,
      selectedColor: "#8B5CF6",
      currentColorIndex: 0,
      currentHue: "#FF0000",
      canvasPosition: { x: 150, y: 100 },
      huePosition: 0,
      rgbValues: { r: 139, g: 92, b: 246 },
      canvasSize: { width: 300, height: 200 }
    };
  },
  computed: {
    gradientStyle() {
      const colorStops = this.colors.sort((a, b) => a.position - b.position).map((color) => `${color.color} ${color.position}%`).join(", ");
      if (this.gradientType === "radial") {
        return {
          background: `radial-gradient(circle, ${colorStops})`
        };
      } else {
        return {
          background: `linear-gradient(${this.direction}, ${colorStops})`
        };
      }
    },
    gradientCSS() {
      const colorStops = this.colors.sort((a, b) => a.position - b.position).map((color) => `${color.color} ${color.position}%`).join(", ");
      if (this.gradientType === "radial") {
        return `background: radial-gradient(circle, ${colorStops});`;
      } else {
        return `background: linear-gradient(${this.direction}, ${colorStops});`;
      }
    },
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "px",
        top: this.canvasPosition.y + "px"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "px"
      };
    }
  },
  methods: {
    setGradientType(type) {
      this.gradientType = type;
    },
    setDirection(dir) {
      this.direction = dir;
    },
    openColorPicker(index) {
      this.currentColorIndex = index;
      this.selectedColor = this.colors[index].color;
      this.updateRgbFromHex(this.selectedColor);
      this.showColorPicker = true;
      this.$nextTick(() => {
        this.initColorCanvas();
        this.initHueSlider();
      });
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      this.colors[this.currentColorIndex].color = this.selectedColor;
      this.closeColorPicker();
    },
    initColorCanvas() {
      const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
      const { width, height } = this.canvasSize;
      const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
      gradient1.addColorStop(0, "#FFFFFF");
      gradient1.addColorStop(1, this.currentHue);
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, width, height);
      const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
      gradient2.addColorStop(0, "rgba(0,0,0,0)");
      gradient2.addColorStop(1, "#000000");
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    initHueSlider() {
      const ctx = common_vendor.index.createCanvasContext("hueCanvas", this);
      const width = 300;
      const height = 30;
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17, "#FFFF00");
      gradient.addColorStop(0.33, "#00FF00");
      gradient.addColorStop(0.5, "#00FFFF");
      gradient.addColorStop(0.67, "#0000FF");
      gradient.addColorStop(0.83, "#FF00FF");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    onCanvasTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;
          this.canvasPosition.x = Math.max(0, Math.min(rect.width, x));
          this.canvasPosition.y = Math.max(0, Math.min(rect.height, y));
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    onHueTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          this.huePosition = Math.max(0, Math.min(rect.width, x));
          this.updateHueFromSlider();
        }
      }).exec();
    },
    updateColorFromCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = this.canvasPosition.x / rect.width;
          const y = this.canvasPosition.y / rect.height;
          const hue = this.huePosition / rect.width * 360;
          const saturation = x;
          const value = 1 - y;
          const rgb = this.hsvToRgb(hue, saturation, value);
          this.rgbValues = rgb;
          this.selectedColor = this.rgbToHex(rgb);
        }
      }).exec();
    },
    updateHueFromSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const hue = this.huePosition / rect.width * 360;
          this.currentHue = this.hsvToRgb(hue, 1, 1);
          this.currentHue = this.rgbToHex(this.hsvToRgb(hue, 1, 1));
          this.initColorCanvas();
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    updateRgbFromHex(hex) {
      const rgb = this.hexToRgb(hex);
      if (rgb) {
        this.rgbValues = rgb;
        const hsv = this.rgbToHsv(rgb);
        this.huePosition = hsv.h / 360 * 300;
        this.canvasPosition.x = hsv.s * this.canvasSize.width;
        this.canvasPosition.y = (1 - hsv.v) * this.canvasSize.height;
        this.currentHue = this.rgbToHex(this.hsvToRgb(hsv.h, 1, 1));
      }
    },
    onRgbChange() {
      this.selectedColor = this.rgbToHex(this.rgbValues);
      this.updateRgbFromHex(this.selectedColor);
    },
    // 颜色转换工具方法
    hsvToRgb(h, s, v) {
      const c = v * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = v - c;
      let r, g, b;
      if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255)
      };
    },
    rgbToHsv(rgb) {
      const r = rgb.r / 255;
      const g = rgb.g / 255;
      const b = rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;
      let h = 0;
      if (diff !== 0) {
        if (max === r) {
          h = (g - b) / diff % 6;
        } else if (max === g) {
          h = (b - r) / diff + 2;
        } else {
          h = (r - g) / diff + 4;
        }
      }
      h = h * 60;
      if (h < 0)
        h += 360;
      const s = max === 0 ? 0 : diff / max;
      const v = max;
      return { h, s, v };
    },
    rgbToHex(rgb) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    updateDirection(direction) {
      this.direction = direction;
    },
    updateColorPosition(index, e) {
      this.colors[index].position = e.detail.value;
    },
    addColor() {
      const newPosition = Math.floor(Math.random() * 101);
      this.colors.push({
        color: "#" + Math.floor(Math.random() * 16777215).toString(16).padStart(6, "0").toUpperCase(),
        position: newPosition
      });
    },
    removeColor(index) {
      if (this.colors.length > 2) {
        this.colors.splice(index, 1);
      }
    },
    copyCSS() {
      common_vendor.index.setClipboardData({
        data: this.gradientCSS,
        success: () => {
          common_vendor.index.showToast({
            title: "代码已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.s($options.gradientStyle),
    b: $data.gradientType === "linear" ? 1 : "",
    c: common_vendor.o(($event) => $options.setGradientType("linear")),
    d: $data.gradientType === "radial" ? 1 : "",
    e: common_vendor.o(($event) => $options.setGradientType("radial")),
    f: $data.gradientType === "linear"
  }, $data.gradientType === "linear" ? {
    g: common_vendor.f($data.directions, (dir, k0, i0) => {
      return {
        a: common_vendor.t(dir.label),
        b: dir.value,
        c: $data.direction === dir.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setDirection(dir.value), dir.value)
      };
    })
  } : {}, {
    h: $data.colors.length >= 5 ? 1 : "",
    i: common_vendor.o((...args) => $options.addColor && $options.addColor(...args)),
    j: common_vendor.f($data.colors, (color, index, i0) => {
      return common_vendor.e({
        a: color.color,
        b: common_vendor.o(($event) => $options.openColorPicker(index), index),
        c: common_vendor.t(color.color.toUpperCase()),
        d: color.position,
        e: common_vendor.o(($event) => $options.updateColorPosition(index, $event), index),
        f: common_vendor.t(color.position)
      }, $data.colors.length > 2 ? {
        g: common_vendor.o(($event) => $options.removeColor(index), index)
      } : {}, {
        h: index
      });
    }),
    k: $data.colors.length > 2,
    l: common_vendor.o((...args) => $options.copyCSS && $options.copyCSS(...args)),
    m: common_vendor.t($options.gradientCSS),
    n: $data.showColorPicker
  }, $data.showColorPicker ? {
    o: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    p: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    q: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    r: common_vendor.s($options.canvasCursorStyle),
    s: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    t: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    v: common_vendor.s($options.hueCursorStyle),
    w: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    x: $data.rgbValues.r,
    y: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    z: $data.rgbValues.g,
    A: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    B: $data.rgbValues.b,
    C: $data.selectedColor,
    D: common_vendor.t($data.selectedColor),
    E: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    F: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    G: common_vendor.o(() => {
    }),
    H: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c5ef31c6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/gradient-code-generator.js.map
