"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedCategory: "开心",
      categories: ["开心", "伤心", "生气", "惊讶", "爱心", "动物", "其他"],
      emojis: {
        "开心": [
          "(＾◡＾)",
          "(≧∇≦)",
          "(◕‿◕)",
          "＼(^o^)／",
          "(｡◕‿◕｡)",
          "(´∀｀)",
          "(*^▽^*)",
          "(⌒‿⌒)",
          "(◡ ‿ ◡)",
          "(^_^)",
          "(◕‿◕)♡",
          "(´▽`)",
          "＼(＾▽＾)／",
          "(◉‿◉)",
          "(◕ᴗ◕)",
          "(≧◡≦)",
          "(◡‿◡)",
          "(^‿^)",
          "(◕‿◕)✿",
          "(◕▿◕)"
        ],
        "伤心": [
          "(╥﹏╥)",
          "(｡•́︿•̀｡)",
          "(╯︵╰)",
          "(T_T)",
          "(；′⌒`)",
          "(´；ω；`)",
          "(┬┬﹏┬┬)",
          "(╥_╥)",
          "(T＿T)",
          "(´Д`)",
          "(っ˘̩╭╮˘̩)っ",
          "(｡•́︿•̀｡)",
          "(ಥ﹏ಥ)",
          "(´∩｀)",
          "(｡◕︿◕｡)",
          "(╥﹏╥)",
          "(´；ω；`)",
          "(ಥ_ಥ)",
          "(╯_╰)",
          "(ಥ﹏ಥ)"
        ],
        "生气": [
          "(╯°□°）╯",
          "(ಠ_ಠ)",
          "(｀皿´)",
          "(▼皿▼#)",
          "(╬ಠ益ಠ)",
          "(ง •̀_•́)ง",
          "(ಠ益ಠ)",
          "(╯‵□′)╯",
          "(ノಠ益ಠ)ノ",
          "(ಠ╭╮ಠ)",
          "(╯°Д°）╯",
          "(ಠ_ಠ)凸",
          "(▼へ▼メ)",
          "(ಠ▃ಠ)",
          "(╬ಠ益ಠ)",
          "(ಠ皿ಠ¬)",
          "(ಠ╭╮ಠ)",
          "(｀Д´)",
          "(ಠ益ಠ╬)",
          "(╯‵□′)╯"
        ],
        "惊讶": [
          "(⊙_⊙)",
          "(◉_◉)",
          "(⊙.⊙)",
          "(°o°)",
          "(◎_◎)",
          "(⊙ω⊙)",
          "(°_°)",
          "(◉‿◉)",
          "(⊙▽⊙)",
          "(◎o◎)",
          "(⊙△⊙)",
          "(°_°)",
          "(◉_◉)",
          "(⊙﹏⊙)",
          "(◎_◎)",
          "(⊙_⊙;)",
          "(°o°;)",
          "(◉_◉;)",
          "(⊙ω⊙)",
          "(◎o◎)"
        ],
        "爱心": [
          "♡(˃͈ દ ˂͈ ༶ )",
          "(♡˙︶˙♡)",
          "(´∀｀)♡",
          "(◕‿◕)♡",
          "♡(◡‿◡)",
          "(♡‿♡)",
          "♡( ◡ ‿ ◡ )",
          "(◕‿◕)♡",
          "♡(˃͈ દ ˂͈ ༶ )",
          "(´♡‿♡`)",
          "♡(◕‿◕)♡",
          "(♡˙︶˙♡)",
          "♡(˃͈ દ ˂͈ ༶ )",
          "(◕‿◕)♡",
          "♡( ◡ ‿ ◡ )",
          "(♡‿♡)",
          "♡(◡‿◡)",
          "(´♡‿♡`)",
          "♡(◕‿◕)♡",
          "(♡˙︶˙♡)"
        ],
        "动物": [
          "ʕ•ᴥ•ʔ",
          "(=^･ω･^=)",
          "∪･ω･∪",
          "(◕‿◕)",
          "(´・ω・`)",
          "(｡◕‿◕｡)",
          "ʕ •́؈•̀ ₎",
          "(´∀｀)",
          "(◕‿◕)",
          "(=^･ｪ･^=)",
          "ʕ•ᴥ•ʔ",
          "(◕‿◕)",
          "(´・ω・`)",
          "(=^･ω･^=)",
          "∪･ω･∪",
          "ʕ •́؈•̀ ₎",
          "(◕‿◕)",
          "(=^･ｪ･^=)",
          "(´∀｀)",
          "(｡◕‿◕｡)"
        ],
        "其他": [
          "¯\\_(ツ)_/¯",
          "(╯°□°）╯︵ ┻━┻",
          "┬─┬ノ( º _ ºノ)",
          "(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧",
          "(｡◕‿◕｡)",
          "(づ｡◕‿‿◕｡)づ",
          "(っ˘ω˘ς )",
          "(つ✧ω✧)つ",
          "(づ￣ ³￣)づ",
          "(ﾉ≧∀≦)ﾉ",
          "(｡◕‿◕｡)",
          "(づ｡◕‿‿◕｡)づ",
          "¯\\_(ツ)_/¯",
          "(ﾉ◕ヮ◕)ﾉ*:･ﾟ✧",
          "(╯°□°）╯︵ ┻━┻",
          "┬─┬ノ( º _ ºノ)",
          "(っ˘ω˘ς )",
          "(つ✧ω✧)つ",
          "(づ￣ ³￣)づ"
        ]
      },
      popularEmojis: ["(＾◡＾)", "(╥﹏╥)", "(╯°□°）╯", "♡(˃͈ દ ˂͈ ༶ )", "ʕ•ᴥ•ʔ", "¯\\_(ツ)_/¯"]
    };
  },
  computed: {
    currentEmojis() {
      return this.emojis[this.selectedCategory] || [];
    }
  },
  methods: {
    selectCategory(category) {
      this.selectedCategory = category;
    },
    async copyEmoji(emoji) {
      try {
        await common_vendor.index.setClipboardData({
          data: emoji
        });
        common_vendor.index.showToast({
          title: "复制成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "error"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category),
        b: category,
        c: $data.selectedCategory === category ? 1 : "",
        d: common_vendor.o(($event) => $options.selectCategory(category), category)
      };
    }),
    b: common_vendor.t($data.selectedCategory),
    c: common_vendor.f($options.currentEmojis, (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: index,
        c: common_vendor.o(($event) => $options.copyEmoji(emoji), index)
      };
    }),
    d: common_vendor.f($data.popularEmojis, (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: index,
        c: common_vendor.o(($event) => $options.copyEmoji(emoji), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-958e0a5c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/emoji-collection.js.map
