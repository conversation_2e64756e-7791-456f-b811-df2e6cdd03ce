"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_Server = common_vendor.resolveComponent("Server");
  const _component_Search = common_vendor.resolveComponent("Search");
  (_component_Server + _component_Search)();
}
if (!Math) {
  (common_vendor.unref(common_vendor.Wifi) + common_vendor.unref(common_vendor.Activity) + common_vendor.unref(common_vendor.Shield))();
}
const _sfc_main = {
  __name: "port-checker",
  setup(__props) {
    const targetHost = common_vendor.ref("google.com");
    const targetPort = common_vendor.ref(80);
    const isChecking = common_vendor.ref(false);
    const checkResult = common_vendor.ref(null);
    const isScanning = common_vendor.ref(false);
    const isRangeScanning = common_vendor.ref(false);
    const scanProgress = common_vendor.ref(0);
    const startPort = common_vendor.ref(1);
    const endPort = common_vendor.ref(100);
    const openPorts = common_vendor.ref([]);
    const commonPorts = common_vendor.ref([
      { port: 21, service: "FTP", description: "文件传输协议", status: "unknown" },
      { port: 22, service: "SSH", description: "安全外壳协议", status: "unknown" },
      { port: 23, service: "Telnet", description: "远程登录协议", status: "unknown" },
      { port: 25, service: "SMTP", description: "邮件传输协议", status: "unknown" },
      { port: 53, service: "DNS", description: "域名解析服务", status: "unknown" },
      { port: 80, service: "HTTP", description: "超文本传输协议", status: "unknown" },
      { port: 110, service: "POP3", description: "邮局协议", status: "unknown" },
      { port: 143, service: "IMAP", description: "邮件访问协议", status: "unknown" },
      { port: 443, service: "HTTPS", description: "安全超文本传输协议", status: "unknown" },
      { port: 993, service: "IMAPS", description: "安全邮件访问协议", status: "unknown" },
      { port: 995, service: "POP3S", description: "安全邮局协议", status: "unknown" },
      { port: 3389, service: "RDP", description: "远程桌面协议", status: "unknown" }
    ]);
    const checkPort = async () => {
      if (!targetHost.value || !targetPort.value)
        return;
      isChecking.value = true;
      await new Promise((resolve) => setTimeout(resolve, 1e3 + Math.random() * 2e3));
      const isOpen = Math.random() > 0.3;
      const responseTime = Math.floor(Math.random() * 200) + 10;
      const serviceMap = {
        21: "FTP",
        22: "SSH",
        23: "Telnet",
        25: "SMTP",
        53: "DNS",
        80: "HTTP",
        110: "POP3",
        143: "IMAP",
        443: "HTTPS",
        993: "IMAPS",
        995: "POP3S",
        3389: "RDP"
      };
      checkResult.value = {
        isOpen,
        service: serviceMap[targetPort.value] || "未知服务",
        responseTime,
        timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
      };
      isChecking.value = false;
    };
    const scanCommonPorts = async () => {
      isScanning.value = true;
      for (let i = 0; i < commonPorts.value.length; i++) {
        await new Promise((resolve) => setTimeout(resolve, 300));
        commonPorts.value[i].status = Math.random() > 0.6 ? "open" : "closed";
      }
      isScanning.value = false;
    };
    const scanPortRange = async () => {
      if (!startPort.value || !endPort.value || startPort.value >= endPort.value)
        return;
      isRangeScanning.value = true;
      scanProgress.value = 0;
      openPorts.value = [];
      const totalPorts = endPort.value - startPort.value + 1;
      const maxPorts = Math.min(totalPorts, 50);
      for (let i = 0; i < maxPorts; i++) {
        const port = startPort.value + Math.floor(i * totalPorts / maxPorts);
        await new Promise((resolve) => setTimeout(resolve, 100));
        if (Math.random() > 0.85) {
          const serviceMap = {
            21: "FTP",
            22: "SSH",
            80: "HTTP",
            443: "HTTPS",
            25: "SMTP",
            53: "DNS",
            110: "POP3"
          };
          openPorts.value.push({
            port,
            service: serviceMap[port] || null
          });
        }
        scanProgress.value = Math.floor((i + 1) / maxPorts * 100);
      }
      isRangeScanning.value = false;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          size: 18
        }),
        b: targetHost.value,
        c: common_vendor.o(($event) => targetHost.value = $event.detail.value),
        d: targetPort.value,
        e: common_vendor.o(($event) => targetPort.value = $event.detail.value),
        f: common_vendor.t(isChecking.value ? "检测中..." : "检测"),
        g: common_vendor.o(checkPort),
        h: isChecking.value,
        i: checkResult.value
      }, checkResult.value ? {
        j: common_vendor.p({
          size: 18
        }),
        k: common_vendor.n(checkResult.value.isOpen ? "bg-green-500" : "bg-red-500"),
        l: common_vendor.t(targetHost.value),
        m: common_vendor.t(targetPort.value),
        n: common_vendor.t(checkResult.value.isOpen ? "开放" : "关闭"),
        o: common_vendor.t(checkResult.value.service),
        p: common_vendor.t(checkResult.value.responseTime),
        q: common_vendor.t(checkResult.value.timestamp),
        r: common_vendor.n(checkResult.value.isOpen ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200")
      } : {}, {
        s: common_vendor.p({
          size: 18
        }),
        t: common_vendor.t(isScanning.value ? "扫描中..." : "批量扫描"),
        v: common_vendor.o(scanCommonPorts),
        w: isScanning.value,
        x: common_vendor.f(commonPorts.value, (port, k0, i0) => {
          return {
            a: common_vendor.n(port.status === "open" ? "bg-green-500" : port.status === "closed" ? "bg-red-500" : "bg-gray-400"),
            b: common_vendor.t(port.port),
            c: common_vendor.t(port.service),
            d: common_vendor.t(port.description),
            e: common_vendor.t(port.status === "open" ? "开放" : port.status === "closed" ? "关闭" : "未检测"),
            f: port.port
          };
        }),
        y: common_vendor.p({
          size: 18
        }),
        z: startPort.value,
        A: common_vendor.o(($event) => startPort.value = $event.detail.value),
        B: endPort.value,
        C: common_vendor.o(($event) => endPort.value = $event.detail.value),
        D: common_vendor.t(isRangeScanning.value ? "扫描中..." : "开始扫描"),
        E: common_vendor.o(scanPortRange),
        F: isRangeScanning.value,
        G: scanProgress.value > 0
      }, scanProgress.value > 0 ? {
        H: common_vendor.t(scanProgress.value),
        I: scanProgress.value + "%"
      } : {}, {
        J: openPorts.value.length > 0
      }, openPorts.value.length > 0 ? {
        K: common_vendor.p({
          size: 18
        }),
        L: common_vendor.f(openPorts.value, (port, k0, i0) => {
          return {
            a: common_vendor.t(port.port),
            b: common_vendor.t(port.service || "未知服务"),
            c: port.port
          };
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-85ea2c4b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/port-checker.js.map
