
.bordered-text.data-v-4138af9f {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.navbar.data-v-4138af9f {
  background-color: #ffffff;
  padding: 32rpx 32rpx 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav-back.data-v-4138af9f {
  margin-right: 24rpx;
  padding: 8rpx;
}
.back-icon.data-v-4138af9f {
  font-size: 48rpx;
  color: #666666;
}
.nav-title.data-v-4138af9f {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}
.content.data-v-4138af9f {
  padding: 32rpx;
}
.card.data-v-4138af9f {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.card-header.data-v-4138af9f {
  padding: 32rpx 32rpx 0;
}
.header-title.data-v-4138af9f {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}
.card-content.data-v-4138af9f {
  padding: 24rpx 32rpx 32rpx;
}
.text-input.data-v-4138af9f {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  border: 1.5rpx solid #a5b4fc;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #2563eb;
  background: rgba(239, 246, 255, 0.85);
  text-align: center;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
  font-weight: 600;
  letter-spacing: 1rpx;
  outline: none;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.04);
}
.text-input.data-v-4138af9f:focus {
  border-color: #6366f1;
  background: #fff;
  box-shadow: 0 0 0 3rpx rgba(99, 102, 241, 0.15);
  color: #4338ca;
}
.results-container.data-v-4138af9f {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.result-item.data-v-4138af9f {
  padding: 32rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
  transition: all 0.3s ease;
}
.result-item.data-v-4138af9f:active {
  background-color: #e9ecef;
}
.result-content.data-v-4138af9f {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.result-text.data-v-4138af9f {
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  color: #333333;
  white-space: pre-wrap;
  flex: 1;
  line-height: 1.4;
}
.copy-btn.data-v-4138af9f {
  padding: 16rpx;
  background-color: #ffffff;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
  margin-left: 16rpx;
}
.copy-icon.data-v-4138af9f {
  font-size: 24rpx;
}
.examples-grid.data-v-4138af9f {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.example-btn.data-v-4138af9f {
  padding: 24rpx;
  background-color: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.example-btn.data-v-4138af9f:active {
  background-color: #f8f9fa;
  border-color: #007bff;
}
.example-text.data-v-4138af9f {
  font-size: 28rpx;
  color: #333333;
}
.instructions.data-v-4138af9f {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.instruction-item.data-v-4138af9f {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}
