"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "HelicopterText",
  components: {
    Plane: common_vendor.Plane,
    Copy: common_vendor.Copy
  },
  data() {
    return {
      helicopterAscii: [
        `
       ___
    ___/   \\___
   |___________|
      |  |  |
      o  o  o`,
        `
    ~~~~~~~~~~~~~~~~
        _______
       |       |
    ___|       |___
   |_______________|
       |   |   |
       o   o   o`,
        `
   ═══════════════
       ╔═════╗
    ╔══╝     ╚══╗
    ║           ║
    ╚═══════════╝
        │ │ │
        ○ ○ ○`,
        `
    ▓▓▓▓▓▓▓▓▓▓▓
       ▓▓▓▓▓
    ▓▓▓▓▓▓▓▓▓
   ▓▓▓▓▓▓▓▓▓▓▓
      ▓ ▓ ▓
      ● ● ●`,
        `
      ~~~~~~~~~
        _____
       /     \\
    ___| ◯ ◯ |___
   |_____________|
       |  |  |
       ●  ●  ●`,
        `
   ░░░░░░░░░░░░░
      ░░░░░░░
   ░░░░░░░░░░░
  ░░░░░░░░░░░░░
     ░ ░ ░
     ○ ○ ○`
      ],
      airplaneAscii: [
        `
        __|__
    *---o---*
         |`,
        `
      ___
   ___/   \\___
  /           \\
 <_____________>
       | |
       o o`,
        `
    ═══════
   ═══╤╤╤═══
      ├┼┤
      └┼┘
       │`,
        `
    ▓▓▓▓▓▓▓
   ▓▓▓▓▓▓▓▓▓
    ▓▓▓▓▓
     ▓▓▓
      ▓`,
        `
      ✈️
    -----
   /     \\
  <_______>
     | |
     o o`
      ],
      flyingEmojis: [
        "🚁",
        "✈️",
        "🛩️",
        "🚀",
        "🛸",
        "🪂",
        "🎈",
        "🪁"
      ],
      aviationSymbols: [
        "▲▼▲▼▲",
        "◄►◄►◄",
        "↗️↖️↗️",
        "⬆️⬇️⬆️",
        "🛫🛬🛫",
        "✈️💨✈️",
        "🚁🌪️🚁",
        "🛩️☁️🛩️"
      ]
    };
  },
  methods: {
    async copyText(text) {
      try {
        await common_vendor.index.setClipboardData({
          data: text,
          success: () => {
            common_vendor.index.showToast({
              title: "复制成功",
              icon: "success",
              duration: 1500
            });
          }
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "none",
          duration: 1500
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.helicopterAscii, (helicopter, index, i0) => {
      return {
        a: common_vendor.t(helicopter),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(helicopter), index)
      };
    }),
    b: common_vendor.f($data.airplaneAscii, (airplane, index, i0) => {
      return {
        a: common_vendor.t(airplane),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(airplane), index)
      };
    }),
    c: common_vendor.f($data.flyingEmojis, (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(emoji), index)
      };
    }),
    d: common_vendor.f($data.aviationSymbols, (symbol, index, i0) => {
      return {
        a: common_vendor.t(symbol),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(symbol), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-cfac2752"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/helicopter-text.js.map
