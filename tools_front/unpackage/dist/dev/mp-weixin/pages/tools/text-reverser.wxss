/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-4c2051de {
  display: flex;
}
.flex-1.data-v-4c2051de {
  flex: 1;
}
.items-center.data-v-4c2051de {
  align-items: center;
}
.justify-center.data-v-4c2051de {
  justify-content: center;
}
.justify-between.data-v-4c2051de {
  justify-content: space-between;
}
.text-center.data-v-4c2051de {
  text-align: center;
}
.rounded.data-v-4c2051de {
  border-radius: 3px;
}
.rounded-lg.data-v-4c2051de {
  border-radius: 6px;
}
.shadow.data-v-4c2051de {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-4c2051de {
  padding: 16rpx;
}
.m-4.data-v-4c2051de {
  margin: 16rpx;
}
.mb-4.data-v-4c2051de {
  margin-bottom: 16rpx;
}
.mt-4.data-v-4c2051de {
  margin-top: 16rpx;
}
.text-reverser.data-v-4c2051de {
  min-height: 100vh;
  background: #f9fafb;
}
.container.data-v-4c2051de {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-4c2051de {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-4c2051de {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-4c2051de {
  font-size: 48rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.title-text.data-v-4c2051de {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #4b5563 0%, #1f2937 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.subtitle.data-v-4c2051de {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.type-section.data-v-4c2051de, .input-section.data-v-4c2051de, .output-section.data-v-4c2051de, .help-section.data-v-4c2051de {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.type-section.data-v-4c2051de:hover, .input-section.data-v-4c2051de:hover, .output-section.data-v-4c2051de:hover, .help-section.data-v-4c2051de:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.section-header.data-v-4c2051de {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-4c2051de {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.section-title.data-v-4c2051de {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.type-options.data-v-4c2051de {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.type-option.data-v-4c2051de {
  flex: 1;
  min-width: 180rpx;
  padding: 24rpx;
  background: #f3f4f6;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.type-option.active.data-v-4c2051de {
  background: rgba(31, 41, 55, 0.05);
  border-color: #4b5563;
  box-shadow: 0 4rpx 12rpx rgba(31, 41, 55, 0.1);
  transform: translateY(-2rpx);
}
.type-option.data-v-4c2051de:hover:not(.active) {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.option-title.data-v-4c2051de {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}
.option-desc.data-v-4c2051de {
  font-size: 24rpx;
  color: #6b7280;
}
.input-wrapper.data-v-4c2051de {
  position: relative;
}
.text-input.data-v-4c2051de {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
}
.text-input.data-v-4c2051de:focus {
  border-color: #4b5563;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(75, 85, 99, 0.15);
}
.input-footer.data-v-4c2051de {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.char-count.data-v-4c2051de {
  font-size: 24rpx;
  color: #6b7280;
}
.input-actions.data-v-4c2051de {
  display: flex;
  gap: 16rpx;
}
.clear-btn.data-v-4c2051de, .reverse-btn.data-v-4c2051de {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.clear-btn.data-v-4c2051de {
  background: #f3f4f6;
  color: #4b5563;
  border: 1.5rpx solid #e5e7eb;
}
.clear-btn.data-v-4c2051de:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.clear-btn.data-v-4c2051de:active {
  transform: translateY(0) scale(0.98);
}
.reverse-btn.data-v-4c2051de {
  background: linear-gradient(135deg, #4b5563 0%, #1f2937 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(31, 41, 55, 0.25);
}
.reverse-btn.data-v-4c2051de:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(31, 41, 55, 0.35);
}
.reverse-btn.data-v-4c2051de:active {
  transform: translateY(0) scale(0.98);
}
.btn-icon.data-v-4c2051de {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-4c2051de {
  font-size: 24rpx;
}
.output-card.data-v-4c2051de {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #e5e7eb;
  margin-bottom: 24rpx;
}
.result-text.data-v-4c2051de {
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 24rpx;
  word-break: break-all;
  display: block;
  min-height: 100rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.output-actions.data-v-4c2051de {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-4c2051de {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 50rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-4c2051de {
  background: linear-gradient(135deg, #4b5563 0%, #1f2937 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(31, 41, 55, 0.25);
}
.action-btn.primary.data-v-4c2051de:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(31, 41, 55, 0.35);
}
.action-btn.primary.data-v-4c2051de:active {
  transform: translateY(0) scale(0.98);
}
.action-btn.secondary.data-v-4c2051de {
  background: rgba(255, 255, 255, 0.8);
  color: #4b5563;
  border: 1.5rpx solid #d1d5db;
}
.action-btn.secondary.data-v-4c2051de:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(31, 41, 55, 0.1);
}
.action-btn.secondary.data-v-4c2051de:active {
  transform: translateY(0) scale(0.98);
}
.help-content.data-v-4c2051de {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1rpx solid #e5e7eb;
}
.help-item.data-v-4c2051de {
  display: block;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 28rpx;
}
.help-item.data-v-4c2051de:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #4b5563;
  font-weight: bold;
}
.help-item.data-v-4c2051de:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-4c2051de {
    padding: 30rpx 20rpx;
}
.type-options.data-v-4c2051de {
    flex-direction: column;
    gap: 12rpx;
}
.output-actions.data-v-4c2051de {
    flex-direction: column;
    gap: 12rpx;
}
}