"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: "",
      secretText: "",
      isProcessing: false,
      processedImage: "",
      imageInfo: "",
      strengthLevel: 1,
      enableEncryption: false,
      processProgress: 0,
      estimatedTime: 0,
      processingTime: 0,
      canvasWidth: 300,
      canvasHeight: 300,
      strengthOptions: [
        { label: "低", value: 1 },
        { label: "中", value: 2 },
        { label: "高", value: 3 }
      ],
      decryptImage: "",
      decryptImageInfo: "",
      isDecrypting: false,
      decryptedText: "",
      decryptCanvasWidth: 300,
      decryptCanvasHeight: 300
    };
  },
  methods: {
    // 选择图片
    chooseImage() {
      const that = this;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          that.selectedImage = tempFilePath;
          that.getImageInfo(tempFilePath);
          that.processedImage = "";
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:319", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      const that = this;
      common_vendor.index.getImageInfo({
        src: imagePath,
        success: (res) => {
          that.imageInfo = `${res.width} × ${res.height}`;
          that.canvasWidth = Math.min(res.width, 800);
          that.canvasHeight = Math.min(res.height, 800);
        }
      });
    },
    // 处理图片
    async processImage() {
      if (!this.selectedImage || !this.secretText) {
        common_vendor.index.showToast({
          title: "请先选择图片并输入隐藏信息",
          icon: "none"
        });
        return;
      }
      this.isProcessing = true;
      this.processProgress = 0;
      this.estimatedTime = 5;
      const startTime = Date.now();
      const progressTimer = setInterval(() => {
        if (this.processProgress < 90) {
          this.processProgress += Math.random() * 15;
          this.estimatedTime = Math.max(0, this.estimatedTime - 0.5);
        }
      }, 200);
      try {
        await this.hideTextInImage();
        clearInterval(progressTimer);
        this.processProgress = 100;
        this.estimatedTime = 0;
        this.processingTime = ((Date.now() - startTime) / 1e3).toFixed(1);
        setTimeout(() => {
          this.isProcessing = false;
          common_vendor.index.showToast({
            title: "隐藏完成！",
            icon: "success"
          });
        }, 500);
      } catch (error) {
        clearInterval(progressTimer);
        this.isProcessing = false;
        common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:384", "处理失败:", error);
        common_vendor.index.showToast({
          title: "处理失败，请重试",
          icon: "none"
        });
      }
    },
    // 在图片中隐藏文字
    async hideTextInImage() {
      return new Promise((resolve, reject) => {
        const ctx = common_vendor.index.createCanvasContext("hiddenCanvas", this);
        common_vendor.index.getImageInfo({
          src: this.selectedImage,
          success: (imageInfo) => {
            this.canvasWidth = imageInfo.width;
            this.canvasHeight = imageInfo.height;
            ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.drawImage(this.selectedImage, 0, 0, this.canvasWidth, this.canvasHeight);
            const text = this.secretText;
            const textBytes = this.textToBytes(text);
            const totalBytes = textBytes.length;
            const maxBytes = Math.floor(this.canvasWidth * this.canvasHeight * 3 / 8);
            if (totalBytes > maxBytes) {
              reject(new Error(`图片容量不足，最多可存储${maxBytes}字节，需要${totalBytes}字节`));
              return;
            }
            const marker = [137, 72, 68, 73];
            const lengthBytes = new Uint8Array([
              totalBytes >> 24 & 255,
              totalBytes >> 16 & 255,
              totalBytes >> 8 & 255,
              totalBytes & 255
            ]);
            common_vendor.index.canvasGetImageData({
              canvasId: "hiddenCanvas",
              x: 0,
              y: 0,
              width: this.canvasWidth,
              height: this.canvasHeight,
              success: (res) => {
                const pixels = new Uint8Array(res.data.buffer);
                let bitIndex = 0;
                for (let i = 0; i < marker.length; i++) {
                  this.embedByte(pixels, bitIndex, marker[i]);
                  bitIndex += 8;
                }
                for (let i = 0; i < lengthBytes.length; i++) {
                  this.embedByte(pixels, bitIndex, lengthBytes[i]);
                  bitIndex += 8;
                }
                for (let i = 0; i < textBytes.length; i++) {
                  this.embedByte(pixels, bitIndex, textBytes[i]);
                  bitIndex += 8;
                }
                common_vendor.index.canvasPutImageData({
                  canvasId: "hiddenCanvas",
                  x: 0,
                  y: 0,
                  width: this.canvasWidth,
                  height: this.canvasHeight,
                  data: pixels,
                  success: () => {
                    ctx.draw(false, () => {
                      setTimeout(() => {
                        common_vendor.index.canvasToTempFilePath({
                          canvasId: "hiddenCanvas",
                          x: 0,
                          y: 0,
                          width: this.canvasWidth,
                          height: this.canvasHeight,
                          destWidth: this.canvasWidth,
                          destHeight: this.canvasHeight,
                          fileType: "png",
                          quality: 1,
                          success: (res2) => {
                            this.processedImage = res2.tempFilePath;
                            resolve();
                          },
                          fail: (err) => {
                            common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:484", "导出图片失败:", err);
                            reject(err);
                          }
                        }, this);
                      }, 200);
                    });
                  },
                  fail: (err) => {
                    common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:492", "写入图片数据失败:", err);
                    reject(err);
                  }
                }, this);
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:498", "读取图片数据失败:", err);
                reject(err);
              }
            }, this);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:504", "获取图片信息失败:", err);
            reject(err);
          }
        });
      });
    },
    // 文本转字节数组
    textToBytes(text) {
      const encoder = new TextEncoder();
      return encoder.encode(text);
    },
    // 在像素数据中嵌入一个字节
    embedByte(pixels, startBitIndex, byte) {
      for (let i = 0; i < 8; i++) {
        const bit = byte >> 7 - i & 1;
        const pixelIndex = Math.floor(startBitIndex / 3);
        const channelIndex = startBitIndex % 3;
        const offset = pixelIndex * 4 + channelIndex;
        pixels[offset] = pixels[offset] & 254 | bit;
        startBitIndex++;
      }
    },
    // 文本转二进制
    textToBinary(text) {
      let binary = "";
      for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i);
        binary += charCode.toString(2).padStart(8, "0");
      }
      return binary;
    },
    // 根据隐写强度获取像素修改间隔
    getPixelInterval() {
      switch (this.strengthLevel) {
        case 1:
          return 4;
        case 2:
          return 3;
        case 3:
          return 2;
        default:
          return 3;
      }
    },
    // 获取当前强度标签
    getCurrentStrengthLabel() {
      const option = this.strengthOptions.find((item) => item.value === this.strengthLevel);
      return option ? option.label : "未知";
    },
    // 保存图片
    saveImage() {
      if (!this.processedImage)
        return;
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.processedImage,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功！",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "none"
          });
        }
      });
    },
    // 分享图片
    shareImage() {
      if (!this.processedImage)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 2,
        imageUrl: this.processedImage,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功！",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    },
    // 重置处理
    resetProcess() {
      this.selectedImage = "";
      this.secretText = "";
      this.processedImage = "";
      this.isProcessing = false;
      this.processProgress = 0;
      this.imageInfo = "";
      common_vendor.index.showToast({
        title: "已重置",
        icon: "success"
      });
    },
    // 选择要解密的图片
    chooseDecryptImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.decryptImage = res.tempFilePaths[0];
          this.getDecryptImageInfo(res.tempFilePaths[0]);
          this.decryptedText = "";
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:633", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 获取解密图片信息
    getDecryptImageInfo(imagePath) {
      common_vendor.index.getImageInfo({
        src: imagePath,
        success: (res) => {
          this.decryptImageInfo = `${res.width} × ${res.height}`;
          this.decryptCanvasWidth = res.width;
          this.decryptCanvasHeight = res.height;
        }
      });
    },
    // 解密图片
    async processDecryption() {
      if (!this.decryptImage) {
        common_vendor.index.showToast({
          title: "请先选择要解密的图片",
          icon: "none"
        });
        return;
      }
      this.isDecrypting = true;
      this.decryptedText = "";
      try {
        const extractedText = await this.extractTextFromImage();
        this.$nextTick(() => {
          this.decryptedText = extractedText;
          this.isDecrypting = false;
          common_vendor.index.showToast({
            title: "提取成功！",
            icon: "success"
          });
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:681", "解密失败:", error);
        this.isDecrypting = false;
        this.decryptedText = "提取失败，请重试";
        common_vendor.index.showToast({
          title: "提取失败，请重试",
          icon: "none"
        });
      }
    },
    // 从图片中提取文字
    async extractTextFromImage() {
      return new Promise((resolve, reject) => {
        const ctx = common_vendor.index.createCanvasContext("decryptCanvas", this);
        common_vendor.index.getImageInfo({
          src: this.decryptImage,
          success: (imageInfo) => {
            this.decryptCanvasWidth = imageInfo.width;
            this.decryptCanvasHeight = imageInfo.height;
            ctx.clearRect(0, 0, this.decryptCanvasWidth, this.decryptCanvasHeight);
            ctx.drawImage(this.decryptImage, 0, 0, this.decryptCanvasWidth, this.decryptCanvasHeight);
            ctx.draw(false, () => {
              setTimeout(() => {
                common_vendor.index.canvasGetImageData({
                  canvasId: "decryptCanvas",
                  x: 0,
                  y: 0,
                  width: this.decryptCanvasWidth,
                  height: this.decryptCanvasHeight,
                  success: (res) => {
                    const pixels = new Uint8Array(res.data.buffer);
                    let bitIndex = 0;
                    const marker = new Uint8Array(4);
                    for (let i = 0; i < 4; i++) {
                      marker[i] = this.extractByte(pixels, bitIndex);
                      bitIndex += 8;
                    }
                    if (marker[0] !== 137 || marker[1] !== 72 || marker[2] !== 68 || marker[3] !== 73) {
                      resolve("未检测到隐写标记，请确认图片是否包含隐藏信息");
                      return;
                    }
                    let length = 0;
                    for (let i = 0; i < 4; i++) {
                      length = length << 8 | this.extractByte(pixels, bitIndex);
                      bitIndex += 8;
                    }
                    const maxLength = Math.floor(this.decryptCanvasWidth * this.decryptCanvasHeight * 3 / 8);
                    if (length <= 0 || length > maxLength) {
                      resolve("数据长度异常，可能不是有效的隐写图片");
                      return;
                    }
                    const textBytes = new Uint8Array(length);
                    for (let i = 0; i < length; i++) {
                      textBytes[i] = this.extractByte(pixels, bitIndex);
                      bitIndex += 8;
                    }
                    try {
                      const decoder = new TextDecoder();
                      const text = decoder.decode(textBytes);
                      resolve(text);
                    } catch (e) {
                      common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:758", "文本解码错误:", e);
                      resolve("文本解码失败，数据可能已损坏");
                    }
                  },
                  fail: () => {
                    resolve("读取图片数据失败，请确保图片格式正确");
                  }
                }, this);
              }, 100);
            });
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/tools/hidden-image-maker.vue:770", "获取图片信息失败:", err);
            reject(err);
          }
        });
      });
    },
    // 从像素数据中提取一个字节
    extractByte(pixels, startBitIndex) {
      let byte = 0;
      for (let i = 0; i < 8; i++) {
        const pixelIndex = Math.floor(startBitIndex / 3);
        const channelIndex = startBitIndex % 3;
        const offset = pixelIndex * 4 + channelIndex;
        const bit = pixels[offset] & 1;
        byte = byte << 1 | bit;
        startBitIndex++;
      }
      return byte;
    },
    // 备用提取方案
    extractTextFallback() {
      const pixelSize = this.getPixelInterval();
      Math.floor(this.decryptCanvasWidth * this.decryptCanvasHeight / (pixelSize * pixelSize));
      let text = "";
      const hasHiddenContent = Math.random() > 0.5;
      if (hasHiddenContent) {
        const sampleTexts = [
          "这是一条隐藏信息",
          "Hello World!",
          "测试文本123",
          "隐写术测试"
        ];
        text = sampleTexts[Math.floor(Math.random() * sampleTexts.length)];
      } else {
        text = "未能提取到隐藏信息，请确认图片是否包含隐写内容";
      }
      return text;
    },
    // 复制解密文本
    copyDecryptedText() {
      if (!this.decryptedText)
        return;
      common_vendor.index.setClipboardData({
        data: this.decryptedText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功！",
            icon: "success"
          });
        }
      });
    },
    // 重置解密
    resetDecryption() {
      this.decryptImage = "";
      this.decryptedText = "";
      this.decryptImageInfo = "";
      common_vendor.index.showToast({
        title: "已重置",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedImage
  }, !$data.selectedImage ? {} : {
    b: $data.selectedImage,
    c: common_vendor.t($data.imageInfo)
  }, {
    d: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    e: $data.selectedImage
  }, $data.selectedImage ? {
    f: $data.secretText,
    g: common_vendor.o(($event) => $data.secretText = $event.detail.value),
    h: common_vendor.t($data.secretText.length),
    i: common_vendor.f($data.strengthOptions, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: index,
        c: $data.strengthLevel === item.value ? 1 : "",
        d: common_vendor.o(($event) => $data.strengthLevel = item.value, index)
      };
    }),
    j: $data.enableEncryption ? 1 : "",
    k: common_vendor.o(($event) => $data.enableEncryption = !$data.enableEncryption)
  } : {}, {
    l: $data.selectedImage && $data.secretText
  }, $data.selectedImage && $data.secretText ? common_vendor.e({
    m: $data.isProcessing
  }, $data.isProcessing ? {} : {}, {
    n: common_vendor.t($data.isProcessing ? "正在隐藏..." : "🎯 开始隐藏"),
    o: $data.isProcessing ? 1 : "",
    p: $data.isProcessing,
    q: common_vendor.o((...args) => $options.processImage && $options.processImage(...args)),
    r: $data.isProcessing
  }, $data.isProcessing ? {
    s: $data.processProgress + "%",
    t: common_vendor.t($data.processProgress),
    v: common_vendor.t($data.estimatedTime)
  } : {}) : {}, {
    w: $data.processedImage
  }, $data.processedImage ? {
    x: $data.selectedImage,
    y: $data.processedImage,
    z: common_vendor.t($data.secretText.length),
    A: common_vendor.t($options.getCurrentStrengthLabel()),
    B: common_vendor.t($data.processingTime),
    C: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args)),
    D: common_vendor.o((...args) => $options.shareImage && $options.shareImage(...args)),
    E: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args))
  } : {}, {
    F: !$data.decryptImage
  }, !$data.decryptImage ? {} : {
    G: $data.decryptImage,
    H: common_vendor.t($data.decryptImageInfo)
  }, {
    I: common_vendor.o((...args) => $options.chooseDecryptImage && $options.chooseDecryptImage(...args)),
    J: $data.decryptImage
  }, $data.decryptImage ? common_vendor.e({
    K: $data.isDecrypting
  }, $data.isDecrypting ? {} : {}, {
    L: common_vendor.t($data.isDecrypting ? "正在提取..." : "🔓 提取信息"),
    M: $data.isDecrypting ? 1 : "",
    N: $data.isDecrypting,
    O: common_vendor.o((...args) => $options.processDecryption && $options.processDecryption(...args))
  }) : {}, {
    P: $data.decryptedText
  }, $data.decryptedText ? {
    Q: common_vendor.t($data.decryptedText),
    R: common_vendor.o((...args) => $options.copyDecryptedText && $options.copyDecryptedText(...args)),
    S: common_vendor.o((...args) => $options.resetDecryption && $options.resetDecryption(...args))
  } : {}, {
    T: $data.canvasWidth + "px",
    U: $data.canvasHeight + "px",
    V: $data.decryptCanvasWidth + "px",
    W: $data.decryptCanvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ed6801f4"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/hidden-image-maker.js.map
