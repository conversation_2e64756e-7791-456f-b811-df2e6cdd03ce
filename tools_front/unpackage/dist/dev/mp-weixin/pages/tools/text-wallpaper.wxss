/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-6c0592d9 {
  display: flex;
}
.flex-1.data-v-6c0592d9 {
  flex: 1;
}
.items-center.data-v-6c0592d9 {
  align-items: center;
}
.justify-center.data-v-6c0592d9 {
  justify-content: center;
}
.justify-between.data-v-6c0592d9 {
  justify-content: space-between;
}
.text-center.data-v-6c0592d9 {
  text-align: center;
}
.rounded.data-v-6c0592d9 {
  border-radius: 3px;
}
.rounded-lg.data-v-6c0592d9 {
  border-radius: 6px;
}
.shadow.data-v-6c0592d9 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-6c0592d9 {
  padding: 16rpx;
}
.m-4.data-v-6c0592d9 {
  margin: 16rpx;
}
.mb-4.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-6c0592d9 {
  margin-top: 16rpx;
}
.text-wallpaper-page.data-v-6c0592d9 {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
}
.content.data-v-6c0592d9 {
  flex: 1;
  padding: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}
.section-card.data-v-6c0592d9 {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.section-card.data-v-6c0592d9:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.section-header-wrapper.data-v-6c0592d9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 24rpx 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.section-header.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 0;
  border-bottom: none;
}
.header-actions.data-v-6c0592d9 {
  display: flex;
  align-items: center;
}
.section-title-wrapper.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.section-icon.data-v-6c0592d9 {
  font-size: 32rpx;
  color: #6c757d;
}
.section-title.data-v-6c0592d9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.refresh-btn.data-v-6c0592d9 {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 64rpx;
  min-width: 120rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  color: #495057;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
  margin: 0;
}
.refresh-btn.data-v-6c0592d9:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}
.refresh-btn.data-v-6c0592d9:active {
  transform: scale(0.98);
}
.refresh-btn.data-v-6c0592d9:disabled {
  opacity: 0.6;
  pointer-events: none;
}
.refresh-text.data-v-6c0592d9 {
  color: #495057;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1;
  margin: 0;
  padding: 0;
  text-align: center;
}
.category-grid.data-v-6c0592d9 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  padding: 32rpx;
}
.category-item.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 24rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.category-item.data-v-6c0592d9:hover {
  background: #e9ecef;
  border-color: #dee2e6;
}
.category-item.data-v-6c0592d9:active {
  transform: scale(0.98);
}
.category-item.active.data-v-6c0592d9 {
  background: #495057;
  border-color: #495057;
  color: #ffffff;
}
.category-item.active .category-name.data-v-6c0592d9 {
  color: #ffffff;
}
.category-icon.data-v-6c0592d9 {
  font-size: 32rpx;
}
.category-name.data-v-6c0592d9 {
  font-size: 28rpx;
  font-weight: 500;
  color: #495057;
}
.wallpaper-content.data-v-6c0592d9 {
  padding: 32rpx;
}
.loading-state.data-v-6c0592d9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  padding: 80rpx 32rpx;
}
.loading-icon.data-v-6c0592d9 {
  width: 48rpx;
  height: 48rpx;
  color: #6c757d;
}
.loading-icon.rotating.data-v-6c0592d9 {
  animation: rotate-6c0592d9 1s linear infinite;
}
.loading-icon svg.data-v-6c0592d9 {
  width: 100%;
  height: 100%;
}
.loading-text.data-v-6c0592d9 {
  font-size: 28rpx;
  color: #6c757d;
}
.wallpaper-grid.data-v-6c0592d9 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32rpx;
}
.wallpaper-item.data-v-6c0592d9 {
  border-radius: 16rpx;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f0f0f0;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.wallpaper-item.data-v-6c0592d9:hover {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.wallpaper-preview.data-v-6c0592d9 {
  position: relative;
  width: 100%;
  height: 384rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 48rpx;
  box-sizing: border-box;
}
.wallpaper-text-container.data-v-6c0592d9 {
  text-align: center;
  z-index: 1;
}
.wallpaper-text.data-v-6c0592d9 {
  font-weight: 700;
  line-height: 1.6;
  white-space: pre-line;
  display: block;
  margin-bottom: 16rpx;
}
.wallpaper-author.data-v-6c0592d9 {
  opacity: 0.8;
  font-weight: 400;
  display: block;
}
.wallpaper-actions.data-v-6c0592d9 {
  display: none;
}
.action-btn.data-v-6c0592d9 {
  display: none;
}
.wallpaper-info.data-v-6c0592d9 {
  padding: 24rpx;
  background: #ffffff;
}
.wallpaper-title.data-v-6c0592d9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}
.wallpaper-meta.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.wallpaper-category.data-v-6c0592d9 {
  font-size: 24rpx;
  color: #6c757d;
  background: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}
.favorite-list.data-v-6c0592d9 {
  padding: 32rpx;
}
.favorite-item.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  transition: all 0.2s;
}
.favorite-item.data-v-6c0592d9:hover {
  background: #e9ecef;
}
.favorite-item.data-v-6c0592d9:last-child {
  margin-bottom: 0;
}
.favorite-info.data-v-6c0592d9 {
  flex: 1;
  min-width: 0;
}
.favorite-title.data-v-6c0592d9 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: block;
}
.favorite-preview.data-v-6c0592d9 {
  font-size: 24rpx;
  color: #6c757d;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.favorite-download-btn.data-v-6c0592d9 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #28a745;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  margin-left: 16rpx;
  transition: all 0.2s;
}
.favorite-download-btn.data-v-6c0592d9:hover {
  background: #218838;
  transform: scale(1.1);
}
.favorite-download-btn.data-v-6c0592d9:active {
  transform: scale(0.95);
}
.favorite-download-btn svg.data-v-6c0592d9 {
  width: 32rpx;
  height: 32rpx;
}
.usage-card.data-v-6c0592d9 {
  background: #f8f9fa;
  border-color: #e9ecef;
}
.usage-list.data-v-6c0592d9 {
  padding: 32rpx;
}
.usage-item.data-v-6c0592d9 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.usage-item.data-v-6c0592d9:last-child {
  margin-bottom: 0;
}
.usage-bullet.data-v-6c0592d9 {
  color: #6c757d;
  font-size: 24rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}
.usage-text.data-v-6c0592d9 {
  font-size: 28rpx;
  color: #495057;
  line-height: 1.5;
  flex: 1;
}
@keyframes rotate-6c0592d9 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
@media (max-width: 750rpx) {
.content.data-v-6c0592d9 {
    padding: 24rpx;
}
.section-header.data-v-6c0592d9 {
    padding: 24rpx 24rpx 16rpx;
}
.category-grid.data-v-6c0592d9 {
    padding: 24rpx;
}
.wallpaper-content.data-v-6c0592d9 {
    padding: 24rpx;
}
.wallpaper-preview.data-v-6c0592d9 {
    height: 320rpx;
    padding: 32rpx;
}
.wallpaper-actions.data-v-6c0592d9 {
    gap: 16rpx;
}
.action-btn.data-v-6c0592d9 {
    width: 64rpx;
    height: 64rpx;
}
.action-btn svg.data-v-6c0592d9 {
    width: 24rpx;
    height: 24rpx;
}
}
.preview-modal.data-v-6c0592d9 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}
.preview-content.data-v-6c0592d9 {
  width: 100%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  position: relative;
}
.preview-close-btn.data-v-6c0592d9 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  padding: 0;
  z-index: 10;
  color: #ffffff;
  transition: all 0.2s;
}
.preview-close-btn.data-v-6c0592d9:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
.preview-close-btn.data-v-6c0592d9:active {
  transform: scale(0.9);
}
.preview-close-btn svg.data-v-6c0592d9 {
  width: 32rpx;
  height: 32rpx;
}
.preview-image.data-v-6c0592d9 {
  width: 100%;
  height: 500rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
  box-sizing: border-box;
}
.preview-actions.data-v-6c0592d9 {
  padding: 24rpx;
  display: flex;
  justify-content: center;
  border-top: 1rpx solid #f0f0f0;
}
.preview-download-btn.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 0 32rpx;
  height: 80rpx;
  background-color: #28a745;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.preview-download-btn.data-v-6c0592d9:hover {
  background-color: #218838;
}
.preview-download-btn.data-v-6c0592d9:active {
  transform: scale(0.98);
}
.preview-download-btn .download-icon.data-v-6c0592d9 {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-download-btn .download-icon svg.data-v-6c0592d9 {
  width: 100%;
  height: 100%;
}
.custom-creator.data-v-6c0592d9 {
  padding: 32rpx;
}
.creator-section.data-v-6c0592d9 {
  margin-bottom: 24rpx;
}
.creator-label.data-v-6c0592d9 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.custom-textarea.data-v-6c0592d9,
.custom-input.data-v-6c0592d9 {
  width: 100%;
  padding: 20rpx 24rpx;
  border: 1rpx solid #e3eaf5;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #495057;
  background: #fafbfc;
  box-sizing: border-box;
  transition: border-color 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1), background 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  outline: none;
  resize: none;
}
.custom-textarea.data-v-6c0592d9:focus,
.custom-input.data-v-6c0592d9:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2rpx rgba(25, 118, 210, 0.1);
  background: #fff;
}
.custom-textarea.data-v-6c0592d9::-webkit-input-placeholder, .custom-input.data-v-6c0592d9::-webkit-input-placeholder {
  color: #b0b8c9;
  font-weight: 400;
}
.custom-textarea.data-v-6c0592d9::placeholder,
.custom-input.data-v-6c0592d9::placeholder {
  color: #b0b8c9;
  font-weight: 400;
}
.char-count.data-v-6c0592d9 {
  font-size: 24rpx;
  color: #6c757d;
  text-align: right;
  margin-top: 8rpx;
}
.custom-input.data-v-6c0592d9 {
  min-height: 74rpx;
  line-height: 64rpx;
  width: 100%;
  padding: 20rpx 24rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #495057;
  background: #fafbfc;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
  outline: none;
}
.custom-input.data-v-6c0592d9:focus {
  border-color: #1976d2;
  box-shadow: 0 0 0 2rpx rgba(25, 118, 210, 0.08);
  background: #fff;
}
.custom-input.data-v-6c0592d9::-webkit-input-placeholder {
  color: #b0b8c9;
  font-weight: 400;
}
.custom-input.data-v-6c0592d9::placeholder {
  color: #b0b8c9;
  font-weight: 400;
}
.custom-preview.data-v-6c0592d9 {
  width: 100%;
  height: 240rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-bottom: 16rpx;
}
.custom-preview .wallpaper-text-container.data-v-6c0592d9 {
  text-align: center;
  max-width: 100%;
}
.custom-preview .wallpaper-text-container .wallpaper-text.data-v-6c0592d9 {
  font-weight: 600;
  line-height: 1.6;
  white-space: pre-line;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.custom-preview .wallpaper-text-container .wallpaper-author.data-v-6c0592d9 {
  opacity: 0.8;
  font-weight: 400;
  display: block;
  font-style: italic;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.bg-type-tabs.data-v-6c0592d9 {
  display: flex;
  gap: 16rpx;
}
.bg-type-tab.data-v-6c0592d9 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  background: #ffffff;
}
.bg-type-tab.data-v-6c0592d9:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: translateY(-1rpx);
}
.bg-type-tab.active.data-v-6c0592d9 {
  background: #495057;
  border-color: #495057;
  color: #ffffff;
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(73, 80, 87, 0.2);
}
.bg-type-tab.active .bg-type-name.data-v-6c0592d9 {
  color: #ffffff;
}
.bg-type-icon.data-v-6c0592d9 {
  font-size: 32rpx;
}
.bg-type-name.data-v-6c0592d9 {
  font-size: 28rpx;
  font-weight: 500;
}
.bg-options.data-v-6c0592d9 {
  margin-top: 16rpx;
}
.color-picker-section.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.color-grid.data-v-6c0592d9 {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.color-item.data-v-6c0592d9 {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  border: 2rpx solid #f0f0f0;
  position: relative;
}
.color-item.data-v-6c0592d9:hover {
  transform: scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.color-item.active.data-v-6c0592d9 {
  transform: scale(1.1);
  border-color: #495057;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
.color-check.data-v-6c0592d9 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  border-radius: 50%;
}
.opacity-section.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.opacity-label.data-v-6c0592d9 {
  font-size: 24rpx;
  color: #6c757d;
}
.opacity-slider.data-v-6c0592d9 {
  width: 100%;
  margin-top: 16rpx;
}
.gradient-presets.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.gradient-grid.data-v-6c0592d9 {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.gradient-item.data-v-6c0592d9 {
  width: 56rpx;
  height: 56rpx;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  border: 2rpx solid #f0f0f0;
  position: relative;
}
.gradient-item.data-v-6c0592d9:hover {
  transform: scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.gradient-item.active.data-v-6c0592d9 {
  transform: scale(1.1);
  border-color: #495057;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
}
.gradient-check.data-v-6c0592d9 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 700;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  border-radius: 12rpx;
}
.text-settings.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.text-color-section.data-v-6c0592d9 {
  margin-bottom: 16rpx;
}
.color-grid.data-v-6c0592d9 {
  display: flex;
  gap: 16rpx;
}
.font-size-section.data-v-6c0592d9 {
  margin-bottom: 16rpx;
  padding: 0 24rpx;
}
.font-size-label.data-v-6c0592d9 {
  font-size: 24rpx;
  color: #6c757d;
}
.font-size-slider.data-v-6c0592d9 {
  width: 100%;
  max-width: 600rpx;
  margin: 24rpx auto 0 auto;
  display: block;
  box-sizing: border-box;
}
.creator-actions.data-v-6c0592d9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.create-btn.data-v-6c0592d9 {
  padding: 0 32rpx;
  height: 80rpx;
  background-color: #28a745;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.create-btn.data-v-6c0592d9:hover {
  background-color: #218838;
}
.create-btn.data-v-6c0592d9:active {
  transform: scale(0.98);
}
.create-btn.data-v-6c0592d9:disabled, .create-btn[disabled].data-v-6c0592d9 {
  background-color: #e3eaf5 !important;
  color: #b0b8c9 !important;
  opacity: 1 !important;
  cursor: not-allowed;
  box-shadow: none;
}
.create-btn-text.data-v-6c0592d9 {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
}
.reset-btn.data-v-6c0592d9 {
  padding: 0 32rpx;
  height: 80rpx;
  background-color: #f8f9fa;
  color: #495057;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.reset-btn.data-v-6c0592d9:hover {
  background-color: #e9ecef;
}
.reset-btn.data-v-6c0592d9:active {
  transform: scale(0.98);
}
.reset-btn-text.data-v-6c0592d9 {
  color: #495057;
  font-size: 28rpx;
  font-weight: 500;
}
.color-label.data-v-6c0592d9, .gradient-label.data-v-6c0592d9 {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
  margin-bottom: 16rpx;
  display: block;
}