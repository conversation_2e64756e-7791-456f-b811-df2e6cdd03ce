"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "ColorPickerModal",
  props: {
    value: { type: String, default: "#ffffff" }
  },
  data() {
    return {
      selectedColor: this.value,
      hexInput: this.value,
      rgb: this.hexToRgb(this.value),
      presets: [
        "#ffffff",
        "#f8f9fa",
        "#e9ecef",
        "#dee2e6",
        "#ced4da",
        "#adb5bd",
        "#6c757d",
        "#495057",
        "#3b82f6",
        "#ff6b6b",
        "#4ecdc4",
        "#ffbe76"
      ]
    };
  },
  watch: {
    value(val) {
      this.selectedColor = val;
      this.hexInput = val;
      this.rgb = this.hexToRgb(val);
    }
  },
  methods: {
    selectPreset(color) {
      this.selectedColor = color;
      this.hexInput = color;
      this.rgb = this.hexToRgb(color);
    },
    onHexInput(e) {
      let val = e.detail.value;
      if (!val.startsWith("#"))
        val = "#" + val;
      this.hexInput = val;
      if (/^#([A-Fa-f0-9]{6})$/.test(val)) {
        this.selectedColor = val;
        this.rgb = this.hexToRgb(val);
      }
    },
    onRgbInput() {
      const { r, g, b } = this.rgb;
      if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
        this.selectedColor = this.rgbToHex(this.rgb);
        this.hexInput = this.selectedColor;
      }
    },
    onConfirm() {
      this.$emit("confirm", this.selectedColor);
    },
    onCancel() {
      this.$emit("cancel");
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 255, g: 255, b: 255 };
    },
    rgbToHex({ r, g, b }) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(r)}${toHex(g)}${toHex(b)}`.toUpperCase();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.onCancel && $options.onCancel(...args)),
    b: common_vendor.f($data.presets, (color, k0, i0) => {
      return {
        a: color,
        b: color,
        c: $data.selectedColor === color ? "2rpx solid #007AFF" : "2rpx solid #e5e5e5",
        d: common_vendor.o(($event) => $options.selectPreset(color), color)
      };
    }),
    c: common_vendor.o([($event) => $data.hexInput = $event.detail.value, (...args) => $options.onHexInput && $options.onHexInput(...args)]),
    d: $data.hexInput,
    e: $data.selectedColor,
    f: common_vendor.o([($event) => $data.rgb.r = $event.detail.value, (...args) => $options.onRgbInput && $options.onRgbInput(...args)]),
    g: $data.rgb.r,
    h: common_vendor.o([($event) => $data.rgb.g = $event.detail.value, (...args) => $options.onRgbInput && $options.onRgbInput(...args)]),
    i: $data.rgb.g,
    j: common_vendor.o([($event) => $data.rgb.b = $event.detail.value, (...args) => $options.onRgbInput && $options.onRgbInput(...args)]),
    k: $data.rgb.b,
    l: common_vendor.o((...args) => $options.onCancel && $options.onCancel(...args)),
    m: common_vendor.o((...args) => $options.onConfirm && $options.onConfirm(...args)),
    n: common_vendor.o(() => {
    }),
    o: common_vendor.o((...args) => $options.onCancel && $options.onCancel(...args))
  };
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-19c31dd0"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/ColorPickerModal.js.map
