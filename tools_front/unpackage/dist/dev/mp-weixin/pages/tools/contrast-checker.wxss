
.contrast-checker.data-v-7ce08563 {
  min-height: 100vh;
  background-color: #ffffff;
}
.container.data-v-7ce08563 {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.header-section.data-v-7ce08563 {
  text-align: center;
  padding: 60rpx 40rpx;
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 40rpx;
}
.icon-wrapper.data-v-7ce08563 {
  width: 120rpx;
  height: 120rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}
.tool-icon.data-v-7ce08563 {
  font-size: 60rpx;
}
.tool-title.data-v-7ce08563 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 20rpx;
}
.tool-desc.data-v-7ce08563 {
  font-size: 28rpx;
  color: #6b7280;
}

/* 优化后的颜色选择器样式 */
.color-selection-section.data-v-7ce08563 {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border: 1rpx solid #e5e7eb;
  border-radius: 24rpx;
  box-shadow: 
    0 4rpx 20rpx rgba(0, 0, 0, 0.06),
    0 1rpx 3rpx rgba(0, 0, 0, 0.02);
  padding: 48rpx;
  margin-bottom: 40rpx;
  transition: box-shadow 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.color-selection-section.data-v-7ce08563:hover {
  box-shadow: 
    0 8rpx 32rpx rgba(0, 0, 0, 0.08),
    0 2rpx 6rpx rgba(0, 0, 0, 0.03);
}
.color-inputs.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 56rpx;
}
.color-input-group.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  position: relative;
}
.color-label.data-v-7ce08563 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: -0.02em;
  margin-bottom: 4rpx;
}
.color-input-row.data-v-7ce08563 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 8rpx;
  background-color: #fafafa;
  border-radius: 20rpx;
  border: 1rpx solid #e5e7eb;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.color-input-row.data-v-7ce08563:focus-within {
  background-color: #ffffff;
  border-color: #3b82f6;
  box-shadow: 
    0 0 0 4rpx rgba(59, 130, 246, 0.08),
    0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 优化颜色预览块 */
.color-preview.data-v-7ce08563 {
  width: 88rpx;
  height: 88rpx;
  border-radius: 16rpx;
  border: 2rpx solid rgba(0, 0, 0, 0.08);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.color-preview.data-v-7ce08563::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0),
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 8rpx 8rpx;
  z-index: -1;
  opacity: 0.5;
}
.color-preview.data-v-7ce08563:hover {
  transform: scale(1.05);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.16);
}

/* 优化文本输入框 */
.color-text-input.data-v-7ce08563 {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 14rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #1f2937;
  background-color: transparent;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Courier New', monospace;
  font-weight: 500;
  letter-spacing: 0.02em;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.color-text-input.data-v-7ce08563:focus {
  outline: none;
  background-color: rgba(59, 130, 246, 0.04);
}
.color-text-input.data-v-7ce08563::-webkit-input-placeholder {
  color: #9ca3af;
  font-weight: 400;
}
.color-text-input.data-v-7ce08563::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* 响应式设计优化 */
@media (max-width: 750rpx) {
.color-input-row.data-v-7ce08563 {
    flex-direction: column;
    align-items: stretch;
    gap: 20rpx;
    padding: 16rpx;
}
.color-preview.data-v-7ce08563 {
    align-self: center;
}
}
.contrast-results-section.data-v-7ce08563, .preview-section.data-v-7ce08563, .suggestions-section.data-v-7ce08563, .actions-section.data-v-7ce08563, .instructions-section.data-v-7ce08563 {
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.section-title.data-v-7ce08563 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 30rpx;
}
.contrast-display.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}
.contrast-ratio.data-v-7ce08563 {
  text-align: center;
  padding: 40rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
}
.ratio-label.data-v-7ce08563 {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 10rpx;
}
.ratio-value.data-v-7ce08563 {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  font-family: 'Courier New', monospace;
}
.compliance-checks.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.check-item.data-v-7ce08563 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f9fafb;
  border-radius: 12rpx;
}
.check-icon.data-v-7ce08563 {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ef4444;
  flex-shrink: 0;
}
.check-icon.pass.data-v-7ce08563 {
  background-color: #10b981;
}
.icon-text.data-v-7ce08563 {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: 700;
}
.check-label.data-v-7ce08563 {
  flex: 1;
  font-size: 26rpx;
  color: #1f2937;
  font-weight: 500;
}
.check-requirement.data-v-7ce08563 {
  font-size: 22rpx;
  color: #6b7280;
  font-family: 'Courier New', monospace;
}
.preview-samples.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.sample-item.data-v-7ce08563 {
  padding: 40rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.sample-title.data-v-7ce08563 {
  font-size: 36rpx;
  font-weight: 700;
  display: block;
  margin-bottom: 20rpx;
}
.sample-normal.data-v-7ce08563 {
  font-size: 28rpx;
  line-height: 1.6;
  display: block;
  margin-bottom: 15rpx;
}
.sample-small.data-v-7ce08563 {
  font-size: 22rpx;
  line-height: 1.5;
}
.suggestions-list.data-v-7ce08563 {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}
.suggestion-item.data-v-7ce08563 {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #fffbeb;
  border: 2rpx solid #fbbf24;
  border-radius: 16rpx;
}
.suggestion-icon.data-v-7ce08563 {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.suggestion-content.data-v-7ce08563 {
  flex: 1;
}
.suggestion-text.data-v-7ce08563 {
  font-size: 26rpx;
  color: #1f2937;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}
.suggestion-colors.data-v-7ce08563 {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}
.suggested-color.data-v-7ce08563 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.3s ease;
}
.suggested-color.data-v-7ce08563:active {
  transform: scale(1.05);
  border-color: #3b82f6;
}
.color-code.data-v-7ce08563 {
  font-size: 20rpx;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1f2937;
  text-shadow: 0 0 4rpx rgba(255, 255, 255, 0.8);
}
.actions-section.data-v-7ce08563 {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  flex-wrap: wrap;
}
.action-btn.data-v-7ce08563 {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.action-btn.primary.data-v-7ce08563 {
  background-color: #10b981;
  color: #ffffff;
}
.action-btn.primary.data-v-7ce08563:active {
  transform: translateY(2rpx);
  background-color: #059669;
}
.action-btn.secondary.data-v-7ce08563 {
  background-color: #3b82f6;
  color: #ffffff;
}
.action-btn.secondary.data-v-7ce08563:active {
  transform: translateY(2rpx);
  background-color: #2563eb;
}
.action-btn.tertiary.data-v-7ce08563 {
  background-color: #f3f4f6;
  color: #1f2937;
}
.action-btn.tertiary.data-v-7ce08563:active {
  transform: translateY(2rpx);
  background-color: #e5e7eb;
}
.action-icon.data-v-7ce08563 {
  font-size: 28rpx;
}
.action-text.data-v-7ce08563 {
  font-size: 26rpx;
  font-weight: 500;
}
.instructions-list.data-v-7ce08563 {
  margin-top: 30rpx;
}
.instruction-item.data-v-7ce08563 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 15rpx;
}
.instruction-item.data-v-7ce08563:last-child {
  margin-bottom: 0;
}
.highlight.data-v-7ce08563 {
  color: #3b82f6;
  font-weight: 600;
}

/* 颜色选择器弹窗样式 */
.color-picker-modal.data-v-7ce08563 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn-7ce08563 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
@keyframes fadeIn-7ce08563 {
from { opacity: 0;
}
to { opacity: 1;
}
}
.color-picker-container.data-v-7ce08563 {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 
    0 20rpx 60rpx rgba(0, 0, 0, 0.15),
    0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  animation: slideUp-7ce08563 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
@keyframes slideUp-7ce08563 {
from { 
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
}
to { 
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
.picker-header.data-v-7ce08563 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.picker-title.data-v-7ce08563 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}
.picker-close.data-v-7ce08563 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
}
.picker-close.data-v-7ce08563:hover {
  background-color: #e5e7eb;
  transform: scale(1.1);
}
.close-icon.data-v-7ce08563 {
  font-size: 36rpx;
  color: #6b7280;
  font-weight: 300;
}

/* 主颜色画布 */
.color-canvas-container.data-v-7ce08563 {
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.color-canvas.data-v-7ce08563 {
  width: 600rpx;
  height: 400rpx;
  display: block;
}
.canvas-cursor.data-v-7ce08563 {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

/* 色相滑块 */
.hue-slider-container.data-v-7ce08563 {
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.hue-slider.data-v-7ce08563 {
  width: 600rpx;
  height: 60rpx;
  display: block;
}
.hue-cursor.data-v-7ce08563 {
  position: absolute;
  top: 0;
  width: 6rpx;
  height: 60rpx;
  background-color: #ffffff;
  box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.5);
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 10;
}

/* RGB输入 */
.rgb-inputs.data-v-7ce08563 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.rgb-group.data-v-7ce08563 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.rgb-label.data-v-7ce08563 {
  font-size: 24rpx;
  font-weight: 600;
  color: #6b7280;
}
.rgb-input.data-v-7ce08563 {
  width: 100%;
  height: 60rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1f2937;
  text-align: center;
  font-family: 'SF Mono', 'Monaco', monospace;
  transition: border-color 0.2s ease;
}
.rgb-input.data-v-7ce08563:focus {
  border-color: #3b82f6;
  outline: none;
}

/* 颜色预览 */
.picker-preview.data-v-7ce08563 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}
.preview-color.data-v-7ce08563 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  position: relative;
  overflow: hidden;
}
.preview-color.data-v-7ce08563::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0),
    linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 8rpx 8rpx;
  z-index: -1;
}
.hex-value.data-v-7ce08563 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  font-family: 'SF Mono', 'Monaco', monospace;
  letter-spacing: 0.05em;
}

/* 操作按钮 */
.picker-actions.data-v-7ce08563 {
  display: flex;
  gap: 20rpx;
}
.picker-btn.data-v-7ce08563 {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.picker-btn.cancel.data-v-7ce08563 {
  background-color: #f3f4f6;
  color: #6b7280;
}
.picker-btn.cancel.data-v-7ce08563:hover {
  background-color: #e5e7eb;
  transform: translateY(-2rpx);
}
.picker-btn.confirm.data-v-7ce08563 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.picker-btn.confirm.data-v-7ce08563:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.4);
}
.picker-btn.data-v-7ce08563:active {
  transform: translateY(0);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
.color-picker-container.data-v-7ce08563 {
    margin: 20rpx;
    padding: 32rpx;
}
.color-canvas.data-v-7ce08563 {
    width: 100%;
    height: 300rpx;
}
.hue-slider.data-v-7ce08563 {
    width: 100%;
}
.rgb-inputs.data-v-7ce08563 {
    gap: 12rpx;
}
}
