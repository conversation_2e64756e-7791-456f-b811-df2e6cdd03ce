/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c91a4ea8 {
  display: flex;
}
.flex-1.data-v-c91a4ea8 {
  flex: 1;
}
.items-center.data-v-c91a4ea8 {
  align-items: center;
}
.justify-center.data-v-c91a4ea8 {
  justify-content: center;
}
.justify-between.data-v-c91a4ea8 {
  justify-content: space-between;
}
.text-center.data-v-c91a4ea8 {
  text-align: center;
}
.rounded.data-v-c91a4ea8 {
  border-radius: 3px;
}
.rounded-lg.data-v-c91a4ea8 {
  border-radius: 6px;
}
.shadow.data-v-c91a4ea8 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c91a4ea8 {
  padding: 16rpx;
}
.m-4.data-v-c91a4ea8 {
  margin: 16rpx;
}
.mb-4.data-v-c91a4ea8 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c91a4ea8 {
  margin-top: 16rpx;
}
.history-today.data-v-c91a4ea8 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.date-card.data-v-c91a4ea8,
.stats-card.data-v-c91a4ea8,
.filter-card.data-v-c91a4ea8,
.events-card.data-v-c91a4ea8,
.saved-card.data-v-c91a4ea8,
.tips-card.data-v-c91a4ea8 {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
}
.header-icon.data-v-c91a4ea8 {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.header-title.data-v-c91a4ea8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.current-date.data-v-c91a4ea8 {
  font-size: 28rpx;
  color: #2563eb;
  font-weight: 500;
}
.event-count.data-v-c91a4ea8 {
  font-size: 24rpx;
  color: #666;
  background: #f0f0f0;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}
.date-picker-container.data-v-c91a4ea8 {
  padding: 30rpx;
}
.picker-display.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
}
.picker-date.data-v-c91a4ea8 {
  font-size: 32rpx;
  font-weight: 500;
  color: #2563eb;
}
.picker-arrow.data-v-c91a4ea8 {
  font-size: 24rpx;
  color: #666;
}
.stats-card.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  padding: 30rpx;
}
.stat-item.data-v-c91a4ea8 {
  flex: 1;
  text-align: center;
}
.stat-number.data-v-c91a4ea8 {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #2563eb;
  margin-bottom: 8rpx;
}
.stat-label.data-v-c91a4ea8 {
  font-size: 24rpx;
  color: #666;
}
.stat-divider.data-v-c91a4ea8 {
  width: 1rpx;
  height: 60rpx;
  background: #e5e7eb;
  margin: 0 20rpx;
}
.filter-header.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}
.filter-title.data-v-c91a4ea8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.filter-reset-btn.data-v-c91a4ea8 {
  background: linear-gradient(to bottom, #fafafa, #f0f0f0);
  color: #444;
  padding: 14rpx;
  border-radius: 50%;
  font-size: 28rpx;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  min-width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}
.filter-reset-btn.data-v-c91a4ea8:active {
  transform: translateY(-50%) rotate(180deg);
  box-shadow: none;
}
.filter-options.data-v-c91a4ea8 {
  padding: 30rpx;
}
.category-scroll.data-v-c91a4ea8 {
  width: 100%;
}
.category-scroll.data-v-c91a4ea8::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 增强的滚动视图样式 */
.data-v-c91a4ea8::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
  display: none;
}

/* 适配微信小程序的滚动条样式 */
.data-v-c91a4ea8::-webkit-scrollbar-track {
  display: none;
}
.data-v-c91a4ea8::-webkit-scrollbar-thumb {
  display: none;
}
.category-filters.data-v-c91a4ea8 {
  display: flex;
  gap: 20rpx;
  padding-bottom: 20rpx;
}
.category-chip.data-v-c91a4ea8 {
  padding: 16rpx 24rpx;
  background: #f8f9ff;
  border: 2rpx solid #e5e7eb;
  border-radius: 50rpx;
  font-size: 26rpx;
  color: #666;
  white-space: nowrap;
}
.category-chip-active.data-v-c91a4ea8 {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}
.refresh-btn.data-v-c91a4ea8 {
  background: linear-gradient(to bottom, #fafafa, #f0f0f0);
  color: #444;
  padding: 14rpx;
  border-radius: 50%;
  font-size: 28rpx;
  border: 0.5px solid rgba(0, 0, 0, 0.1);
  min-width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.refresh-btn.data-v-c91a4ea8:active {
  transform: rotate(180deg);
  box-shadow: none;
}
.refresh-btn[disabled].data-v-c91a4ea8 {
  opacity: 0.5;
  pointer-events: none;
}
.loading-container.data-v-c91a4ea8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 30rpx;
}
.loading-spinner.data-v-c91a4ea8 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #2563eb;
  border-radius: 50%;
  animation: spin-c91a4ea8 1s linear infinite;
  margin-bottom: 30rpx;
}
.loading-text.data-v-c91a4ea8 {
  font-size: 28rpx;
  color: #666;
}
@keyframes spin-c91a4ea8 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.events-list.data-v-c91a4ea8 {
  padding: 30rpx;
}
.event-item.data-v-c91a4ea8 {
  background: #fafafa;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid #f0f0f0;
}
.event-item-last.data-v-c91a4ea8 {
  margin-bottom: 0;
}
.event-header.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.year-badge.data-v-c91a4ea8 {
  background: #2563eb;
  color: white;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(37, 99, 235, 0.3);
}
.year-text.data-v-c91a4ea8 {
  font-size: 32rpx;
  font-weight: 700;
}
.event-meta.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.category-tag.data-v-c91a4ea8 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}
.category-tag-political.data-v-c91a4ea8 {
  background: #fee2e2;
  color: #991b1b;
}
.category-tag-tech.data-v-c91a4ea8 {
  background: #dbeafe;
  color: #1e40af;
}
.category-tag-science.data-v-c91a4ea8 {
  background: #dcfce7;
  color: #166534;
}
.category-tag-culture.data-v-c91a4ea8 {
  background: #f3e8ff;
  color: #6b21a8;
}
.category-tag-sports.data-v-c91a4ea8 {
  background: #fef3c7;
  color: #92400e;
}
.category-tag-military.data-v-c91a4ea8 {
  background: #f3f4f6;
  color: #374151;
}
.category-tag-economy.data-v-c91a4ea8 {
  background: #ecfdf5;
  color: #065f46;
}
.category-tag-disaster.data-v-c91a4ea8 {
  background: #fed7aa;
  color: #9a3412;
}
.importance-stars.data-v-c91a4ea8 {
  display: flex;
  gap: 4rpx;
}
.star.data-v-c91a4ea8 {
  font-size: 24rpx;
}
.star-filled.data-v-c91a4ea8 {
  color: #fbbf24;
}
.star-empty.data-v-c91a4ea8 {
  color: #e5e7eb;
}
.event-content.data-v-c91a4ea8 {
  margin-bottom: 24rpx;
}
.event-description.data-v-c91a4ea8 {
  font-size: 30rpx;
  line-height: 1.6;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}
.event-details.data-v-c91a4ea8 {
  background: #f8f9ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #2563eb;
  margin-top: 20rpx;
  overflow: hidden;
}
.details-header.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: #f0f4ff;
  border-bottom: 1rpx solid #e5e7eb;
}
.details-title.data-v-c91a4ea8 {
  font-size: 26rpx;
  font-weight: 600;
  color: #2563eb;
}
.expand-btn.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background: #2563eb;
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
}
.expand-btn.data-v-c91a4ea8:active {
  transform: scale(0.95);
  background: #1d4ed8;
}
.expand-icon.data-v-c91a4ea8 {
  font-size: 18rpx;
  transition: transform 0.2s ease;
}
.details-content.data-v-c91a4ea8 {
  padding: 24rpx;
  animation: slideDown-c91a4ea8 0.3s ease-out;
}
.details-text.data-v-c91a4ea8 {
  font-size: 26rpx;
  line-height: 1.8;
  color: #4b5563;
  white-space: pre-wrap;
  /* 保持换行格式 */
  word-break: break-word;
  text-align: left;
  /* 左对齐 */
  text-indent: 0;
  /* 取消默认首行缩进，因为已在JS中处理 */
  /* 段落间距 */
}
.details-text p.data-v-c91a4ea8 {
  margin-bottom: 1em;
}
.details-preview.data-v-c91a4ea8 {
  padding: 20rpx 24rpx;
}
.details-preview-text.data-v-c91a4ea8 {
  font-size: 26rpx;
  line-height: 1.6;
  color: #6b7280;
}
@keyframes slideDown-c91a4ea8 {
from {
    opacity: 0;
    transform: translateY(-10rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
.event-details-text.data-v-c91a4ea8 {
  font-size: 26rpx;
  line-height: 1.5;
  color: #4b5563;
}
.event-actions.data-v-c91a4ea8 {
  display: flex;
  gap: 20rpx;
}
.action-btn.data-v-c91a4ea8 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f8f9ff;
  border: 1rpx solid #e5e7eb;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}
.action-btn-liked.data-v-c91a4ea8 {
  background: #fee2e2;
  border-color: #fca5a5;
  color: #dc2626;
}
.action-btn-saved.data-v-c91a4ea8 {
  background: #fef3c7;
  border-color: #fcd34d;
  color: #d97706;
}
.empty-state.data-v-c91a4ea8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 30rpx;
}
.empty-icon.data-v-c91a4ea8 {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}
.empty-text.data-v-c91a4ea8 {
  font-size: 30rpx;
  color: #666;
  margin-bottom: 40rpx;
}
.empty-btn.data-v-c91a4ea8 {
  background: linear-gradient(to bottom, #2870ef, #2563eb);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.empty-btn.data-v-c91a4ea8:active {
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(37, 99, 235, 0.1);
}
.saved-scroll.data-v-c91a4ea8 {
  width: 100%;
  white-space: nowrap;
}
.saved-scroll.data-v-c91a4ea8::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
.saved-events.data-v-c91a4ea8 {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
}
.saved-item.data-v-c91a4ea8 {
  flex-shrink: 0;
  width: 240rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e5e7eb;
}
.saved-year.data-v-c91a4ea8 {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 8rpx;
}
.saved-title.data-v-c91a4ea8 {
  font-size: 26rpx;
  line-height: 1.4;
  color: #374151;
}
.tips-list.data-v-c91a4ea8 {
  padding: 30rpx;
}
.tip-item.data-v-c91a4ea8 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tip-item.data-v-c91a4ea8:last-child {
  margin-bottom: 0;
}
.tip-bullet.data-v-c91a4ea8 {
  font-size: 28rpx;
  color: #2563eb;
  margin-right: 16rpx;
  margin-top: 4rpx;
}
.tip-text.data-v-c91a4ea8 {
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
  flex: 1;
}