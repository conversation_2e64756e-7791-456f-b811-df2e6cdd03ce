"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "<PERSON><PERSON><PERSON>oll<PERSON>",
  data() {
    return {
      currentMode: "classic",
      diceCount: 1,
      diceList: [],
      isRolling: false,
      showResult: false,
      soundEnabled: true,
      luckyIndex: 88,
      historyList: [],
      showingHistoryCount: 5,
      totalRolls: 0,
      todayRolls: 0,
      luckyStreak: 0,
      numberCounts: {
        1: 0,
        2: 0,
        3: 0,
        4: 0,
        5: 0,
        6: 0
      },
      gameModes: [
        { value: "classic", name: "经典", icon: "🎲" },
        { value: "even-odd", name: "单双", icon: "⚖️" },
        { value: "big-small", name: "大小", icon: "📏" },
        { value: "sum-guess", name: "猜总数", icon: "🎯" }
      ],
      rollAudioContext: null,
      resultAudioContext: null
    };
  },
  computed: {
    totalValue() {
      return this.diceList.reduce((sum, dice) => sum + dice.value, 0);
    },
    averageValue() {
      if (this.diceList.length === 0)
        return 0;
      return (this.totalValue / this.diceList.length).toFixed(1);
    },
    maxValue() {
      return Math.max(...this.diceList.map((dice) => dice.value));
    },
    gameResult() {
      if (!this.showResult)
        return "";
      switch (this.currentMode) {
        case "even-odd":
          return this.totalValue % 2 === 0 ? "双数 🎉" : "单数 🎉";
        case "big-small":
          const avg = this.totalValue / this.diceCount;
          return avg > 3.5 ? "大 🎉" : "小 🎉";
        case "sum-guess":
          if (this.diceCount === 2) {
            const sum = this.totalValue;
            if (sum === 7)
              return "幸运7 🍀";
            if (sum === 11)
              return "天使11 👼";
            if (sum === 2 || sum === 12)
              return "极值 ⭐";
          }
          return "";
        default:
          return "";
      }
    },
    favoriteNumber() {
      let maxCount = 0;
      let favorite = 1;
      for (let i = 1; i <= 6; i++) {
        if (this.numberCounts[i] > maxCount) {
          maxCount = this.numberCounts[i];
          favorite = i;
        }
      }
      return favorite;
    },
    probabilityData() {
      const total = Object.values(this.numberCounts).reduce((sum, count) => sum + count, 0);
      const data = {};
      for (let i = 1; i <= 6; i++) {
        data[i] = {
          count: this.numberCounts[i],
          percentage: total > 0 ? this.numberCounts[i] / total : 0
        };
      }
      return data;
    },
    displayHistory() {
      return this.historyList.slice(0, this.showingHistoryCount);
    }
  },
  onLoad() {
    this.loadData();
    this.initializeDice();
    this.updateLuckyIndex();
  },
  onUnload() {
    this.saveData();
  },
  methods: {
    initializeDice() {
      this.diceList = [];
      for (let i = 0; i < this.diceCount; i++) {
        this.diceList.push({
          id: Date.now() + i,
          value: 1,
          shouldBounce: false,
          isLucky: false
        });
      }
    },
    switchMode(mode) {
      this.currentMode = mode;
      this.resetDice();
    },
    setDiceCount(count) {
      this.diceCount = count;
      this.initializeDice();
      this.showResult = false;
    },
    async rollDice() {
      if (this.isRolling)
        return;
      this.isRolling = true;
      this.showResult = false;
      if (this.soundEnabled) {
        this.playRollSound();
      }
      await this.performRollAnimation();
      this.generateResult();
      this.updateStatistics();
      this.updateLuckyIndex();
      this.isRolling = false;
      this.showResult = true;
      if (this.soundEnabled) {
        setTimeout(() => {
          this.playResultSound();
        }, 500);
      }
      this.saveData();
    },
    async performRollAnimation() {
      const rollDuration = 1800;
      const intervalTime = 60;
      const iterations = rollDuration / intervalTime;
      this.diceList.forEach((dice, index) => {
        dice.rotationX = 0;
        dice.rotationY = 0;
        dice.rotationZ = 0;
      });
      for (let i = 0; i < iterations; i++) {
        this.diceList.forEach((dice, index) => {
          dice.value = Math.floor(Math.random() * 6) + 1;
          dice.shouldBounce = true;
          dice.rotationX = Math.random() * 360;
          dice.rotationY = Math.random() * 360;
          dice.rotationZ = Math.random() * 360;
          dice.shakeOffset = {
            x: (Math.random() - 0.5) * 10,
            y: (Math.random() - 0.5) * 10
          };
        });
        await new Promise((resolve) => setTimeout(resolve, intervalTime));
        const progress = i / iterations;
        const dampening = 1 - progress;
        this.diceList.forEach((dice) => {
          dice.shouldBounce = false;
          if (dice.shakeOffset) {
            dice.shakeOffset.x *= dampening;
            dice.shakeOffset.y *= dampening;
          }
        });
      }
      this.diceList.forEach((dice) => {
        dice.rotationX = 0;
        dice.rotationY = 0;
        dice.rotationZ = 0;
        dice.shakeOffset = { x: 0, y: 0 };
      });
    },
    generateResult() {
      this.diceList.forEach((dice) => {
        const value = Math.floor(Math.random() * 6) + 1;
        dice.value = value;
        dice.isLucky = value === 6;
        this.numberCounts[value]++;
      });
    },
    updateStatistics() {
      this.totalRolls++;
      this.todayRolls++;
      const hasLuckyDice = this.diceList.some((dice) => dice.isLucky);
      if (hasLuckyDice) {
        this.luckyStreak++;
      } else {
        this.luckyStreak = 0;
      }
    },
    updateLuckyIndex() {
      const recentRolls = this.historyList.slice(0, 10);
      let luckyCount = 0;
      recentRolls.forEach((record) => {
        if (record.isLucky)
          luckyCount++;
      });
      this.luckyIndex = Math.min(99, Math.max(
        1,
        50 + luckyCount * 5 + Math.floor(Math.random() * 20) - 10
      ));
    },
    resetDice() {
      this.initializeDice();
      this.showResult = false;
    },
    addToHistory() {
      if (!this.showResult)
        return;
      const record = {
        id: Date.now(),
        timestamp: /* @__PURE__ */ new Date(),
        mode: this.currentMode,
        values: this.diceList.map((dice) => dice.value),
        total: this.totalValue,
        result: this.gameResult,
        isLucky: this.diceList.some((dice) => dice.isLucky)
      };
      this.historyList.unshift(record);
      if (this.historyList.length > 100) {
        this.historyList = this.historyList.slice(0, 100);
      }
      common_vendor.index.showToast({
        title: "已保存到历史",
        icon: "success"
      });
      this.saveData();
    },
    clearHistory() {
      if (this.historyList.length === 0)
        return;
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有投掷历史吗？",
        success: (res) => {
          if (res.confirm) {
            this.historyList = [];
            this.showingHistoryCount = 5;
            this.saveData();
            common_vendor.index.showToast({
              title: "历史记录已清空",
              icon: "success"
            });
          }
        }
      });
    },
    showMoreHistory() {
      this.showingHistoryCount = Math.min(
        this.showingHistoryCount + 10,
        this.historyList.length
      );
    },
    toggleSound() {
      this.soundEnabled = !this.soundEnabled;
      this.saveData();
    },
    getModeLabel(mode) {
      const modeObj = this.gameModes.find((m) => m.value === mode);
      return modeObj ? modeObj.name : "经典";
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = /* @__PURE__ */ new Date();
      const diff = now - date;
      if (diff < 6e4)
        return "刚刚";
      if (diff < 36e5)
        return `${Math.floor(diff / 6e4)}分钟前`;
      if (diff < 864e5)
        return `${Math.floor(diff / 36e5)}小时前`;
      return `${date.getMonth() + 1}/${date.getDate()}`;
    },
    getDotPattern(number) {
      const patterns = {
        1: [5],
        // 中心
        2: [1, 9],
        // 对角
        3: [1, 5, 9],
        // 对角 + 中心
        4: [1, 3, 7, 9],
        // 四角
        5: [1, 3, 5, 7, 9],
        // 四角 + 中心
        6: [1, 3, 4, 6, 7, 9]
        // 两列
      };
      return patterns[number] || [5];
    },
    playRollSound() {
      if (!this.soundEnabled)
        return;
      try {
        const audioContext = common_vendor.index.createInnerAudioContext();
        this.generateDiceRollSound();
        common_vendor.index.vibrateShort({
          type: "medium"
        });
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:594", "播放摇骰子音效");
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:596", "音效播放失败", e);
      }
    },
    playResultSound() {
      if (!this.soundEnabled)
        return;
      try {
        const hasLucky = this.diceList.some((dice) => dice.isLucky);
        if (hasLucky) {
          this.generateLuckySound();
          common_vendor.index.vibrateShort({
            type: "heavy"
          });
        } else {
          this.generateNormalSound();
          common_vendor.index.vibrateShort({
            type: "light"
          });
        }
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:618", "播放结果音效");
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:620", "结果音效播放失败", e);
      }
    },
    generateDiceRollSound() {
      try {
        const duration = 2e3;
        const intervals = [];
        for (let i = 0; i < 10; i++) {
          setTimeout(() => {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }, i * 200);
        }
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:637", "生成滚动音效失败", e);
      }
    },
    generateLuckySound() {
      try {
        const notes = [262, 330, 392, 523];
        notes.forEach((freq, index) => {
          setTimeout(() => {
            common_vendor.index.vibrateShort({
              type: "medium"
            });
          }, index * 150);
        });
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:652", "生成幸运音效失败", e);
      }
    },
    generateNormalSound() {
      try {
        setTimeout(() => {
          common_vendor.index.vibrateShort({
            type: "light"
          });
        }, 100);
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:664", "生成普通音效失败", e);
      }
    },
    loadData() {
      try {
        const data = common_vendor.index.getStorageSync("dice-roller-data");
        if (data) {
          const parsed = JSON.parse(data);
          this.soundEnabled = parsed.soundEnabled !== false;
          this.historyList = parsed.historyList || [];
          this.totalRolls = parsed.totalRolls || 0;
          this.luckyStreak = parsed.luckyStreak || 0;
          this.numberCounts = parsed.numberCounts || { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0 };
          const today = (/* @__PURE__ */ new Date()).toDateString();
          const lastDate = parsed.lastSaveDate;
          if (lastDate === today) {
            this.todayRolls = parsed.todayRolls || 0;
          } else {
            this.todayRolls = 0;
          }
        }
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:689", "加载数据失败", e);
      }
    },
    saveData() {
      try {
        const data = {
          soundEnabled: this.soundEnabled,
          historyList: this.historyList,
          totalRolls: this.totalRolls,
          todayRolls: this.todayRolls,
          luckyStreak: this.luckyStreak,
          numberCounts: this.numberCounts,
          lastSaveDate: (/* @__PURE__ */ new Date()).toDateString()
        };
        common_vendor.index.setStorageSync("dice-roller-data", JSON.stringify(data));
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/dice-roller.vue:706", "保存数据失败", e);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.luckyIndex),
    b: common_vendor.f($data.gameModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: mode.value,
        d: $data.currentMode === mode.value ? 1 : "",
        e: common_vendor.o(($event) => $options.switchMode(mode.value), mode.value)
      };
    }),
    c: common_vendor.f([1, 2, 3, 4, 5, 6], (count, k0, i0) => {
      return {
        a: common_vendor.t(count),
        b: count,
        c: $data.diceCount === count ? 1 : "",
        d: common_vendor.o(($event) => $options.setDiceCount(count), count)
      };
    }),
    d: common_vendor.f($data.diceList, (dice, index, i0) => {
      return {
        a: common_vendor.f(6, (face, faceIndex, i1) => {
          return {
            a: common_vendor.f($options.getDotPattern(dice.value), (dot, k2, i2) => {
              return {
                a: dot,
                b: common_vendor.n(`dot-${dot}`)
              };
            }),
            b: faceIndex,
            c: common_vendor.n(`face-${faceIndex + 1}`),
            d: faceIndex + 1 === dice.value ? "flex" : "none"
          };
        }),
        b: dice.id,
        c: dice.shouldBounce ? 1 : "",
        d: dice.isLucky ? 1 : "",
        e: index * 100 + "ms"
      };
    }),
    e: $data.isRolling ? 1 : "",
    f: $data.showResult
  }, $data.showResult ? common_vendor.e({
    g: common_vendor.t($options.totalValue),
    h: common_vendor.t($options.averageValue),
    i: common_vendor.t($options.maxValue),
    j: $options.gameResult
  }, $options.gameResult ? {
    k: common_vendor.t($options.gameResult)
  } : {}) : {}, {
    l: common_vendor.t($data.isRolling ? "🔄" : "🎲"),
    m: common_vendor.t($data.isRolling ? "摇动中..." : "摇骰子"),
    n: $data.isRolling ? 1 : "",
    o: $data.isRolling ? 1 : "",
    p: common_vendor.o((...args) => $options.rollDice && $options.rollDice(...args)),
    q: common_vendor.o((...args) => $options.resetDice && $options.resetDice(...args)),
    r: common_vendor.o((...args) => $options.addToHistory && $options.addToHistory(...args)),
    s: !$data.showResult ? 1 : "",
    t: common_vendor.t($data.soundEnabled ? "🔊" : "🔇"),
    v: common_vendor.o((...args) => $options.toggleSound && $options.toggleSound(...args)),
    w: $data.soundEnabled ? 1 : "",
    x: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    y: $data.historyList.length === 0 ? 1 : "",
    z: $data.historyList.length === 0
  }, $data.historyList.length === 0 ? {} : {
    A: common_vendor.f($options.displayHistory, (record, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatTime(record.timestamp)),
        b: common_vendor.t($options.getModeLabel(record.mode)),
        c: common_vendor.f(record.values, (value, k1, i1) => {
          return {
            a: common_vendor.t(value),
            b: value
          };
        }),
        d: common_vendor.t(record.total),
        e: record.result
      }, record.result ? {
        f: common_vendor.t(record.result)
      } : {}, {
        g: record.id,
        h: record.isLucky ? 1 : ""
      });
    })
  }, {
    B: $data.historyList.length > 5
  }, $data.historyList.length > 5 ? {
    C: common_vendor.o((...args) => $options.showMoreHistory && $options.showMoreHistory(...args))
  } : {}, {
    D: common_vendor.t($data.totalRolls),
    E: common_vendor.t($options.favoriteNumber),
    F: common_vendor.t($data.luckyStreak),
    G: common_vendor.t($data.todayRolls),
    H: common_vendor.f($options.probabilityData, (prob, number, i0) => {
      return {
        a: prob.percentage * 100 + "%",
        b: common_vendor.t(number),
        c: common_vendor.t(prob.count),
        d: number
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c3fdaac2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/dice-roller.js.map
