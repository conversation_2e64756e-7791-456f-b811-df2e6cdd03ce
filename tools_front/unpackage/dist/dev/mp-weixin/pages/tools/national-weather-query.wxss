/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-34a20241 {
  display: flex;
}
.flex-1.data-v-34a20241 {
  flex: 1;
}
.items-center.data-v-34a20241 {
  align-items: center;
}
.justify-center.data-v-34a20241 {
  justify-content: center;
}
.justify-between.data-v-34a20241 {
  justify-content: space-between;
}
.text-center.data-v-34a20241 {
  text-align: center;
}
.rounded.data-v-34a20241 {
  border-radius: 3px;
}
.rounded-lg.data-v-34a20241 {
  border-radius: 6px;
}
.shadow.data-v-34a20241 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-34a20241 {
  padding: 16rpx;
}
.m-4.data-v-34a20241 {
  margin: 16rpx;
}
.mb-4.data-v-34a20241 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-34a20241 {
  margin-top: 16rpx;
}
.weather-query.data-v-34a20241 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-34a20241 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-34a20241 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.card-header.data-v-34a20241 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-header .header-icon.data-v-34a20241 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.card-header .header-title.data-v-34a20241 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-right: 16rpx;
}
.card-header .header-subtitle.data-v-34a20241 {
  font-size: 24rpx;
  color: #666;
}
.search-section .search-box.data-v-34a20241 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.search-section .search-box .search-input.data-v-34a20241 {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f9fafb;
  box-sizing: border-box;
  margin-right: 16rpx;
  line-height: 88rpx;
}
.search-section .search-box .search-input.data-v-34a20241:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}
.search-section .search-box .search-btn.data-v-34a20241 {
  height: 88rpx;
  padding: 0 32rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.search-section .search-box .search-btn .search-icon.data-v-34a20241 {
  color: white;
  font-size: 28rpx;
}
.search-section .search-box .search-btn.data-v-34a20241:active {
  transform: scale(0.98);
}
.search-section .hot-cities .section-label.data-v-34a20241 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.search-section .hot-cities .cities-grid.data-v-34a20241 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.search-section .hot-cities .cities-grid .city-btn.data-v-34a20241 {
  padding: 16rpx 24rpx;
  background: #f3f4f6;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #374151;
  border: 2rpx solid transparent;
}
.search-section .hot-cities .cities-grid .city-btn.active.data-v-34a20241 {
  background: #3b82f6;
  color: white;
  border-color: #2563eb;
}
.search-section .hot-cities .cities-grid .city-btn.data-v-34a20241:active {
  transform: scale(0.98);
}
.weather-card .current-weather .weather-main.data-v-34a20241 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.weather-card .current-weather .weather-main .weather-icon.data-v-34a20241 {
  font-size: 120rpx;
  margin-right: 40rpx;
}
.weather-card .current-weather .weather-main .weather-info.data-v-34a20241 {
  flex: 1;
}
.weather-card .current-weather .weather-main .weather-info .city-name.data-v-34a20241 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.weather-card .current-weather .weather-main .weather-info .temperature.data-v-34a20241 {
  display: block;
  font-size: 64rpx;
  font-weight: bold;
  color: #3b82f6;
  margin-bottom: 8rpx;
}
.weather-card .current-weather .weather-main .weather-info .weather-desc.data-v-34a20241 {
  font-size: 28rpx;
  color: #666;
}
.weather-card .current-weather .weather-details.data-v-34a20241 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.weather-card .current-weather .weather-details .detail-item.data-v-34a20241 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
}
.weather-card .current-weather .weather-details .detail-item .detail-icon.data-v-34a20241 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.weather-card .current-weather .weather-details .detail-item .detail-info .detail-label.data-v-34a20241 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.weather-card .current-weather .weather-details .detail-item .detail-info .detail-value.data-v-34a20241 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.aqi-card .aqi-content.data-v-34a20241 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.aqi-card .aqi-content .aqi-info .aqi-title.data-v-34a20241 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.aqi-card .aqi-content .aqi-info .aqi-subtitle.data-v-34a20241 {
  font-size: 24rpx;
  color: #666;
}
.aqi-card .aqi-content .aqi-display.data-v-34a20241 {
  text-align: right;
}
.aqi-card .aqi-content .aqi-display .aqi-value.data-v-34a20241 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.aqi-card .aqi-content .aqi-display .aqi-level.data-v-34a20241 {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 600;
}
.aqi-card .aqi-content .aqi-display .aqi-level.aqi-good.data-v-34a20241 {
  background: #d1fae5;
  color: #065f46;
}
.aqi-card .aqi-content .aqi-display .aqi-level.aqi-moderate.data-v-34a20241 {
  background: #dbeafe;
  color: #1e40af;
}
.aqi-card .aqi-content .aqi-display .aqi-level.aqi-unhealthy-sensitive.data-v-34a20241 {
  background: #fef3c7;
  color: #92400e;
}
.aqi-card .aqi-content .aqi-display .aqi-level.aqi-unhealthy.data-v-34a20241 {
  background: #fed7aa;
  color: #c2410c;
}
.aqi-card .aqi-content .aqi-display .aqi-level.aqi-hazardous.data-v-34a20241 {
  background: #fecaca;
  color: #dc2626;
}
.forecast-card .forecast-list .forecast-item.data-v-34a20241 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.forecast-card .forecast-list .forecast-item.data-v-34a20241:last-child {
  margin-bottom: 0;
}
.forecast-card .forecast-list .forecast-item .forecast-date.data-v-34a20241 {
  flex: 1;
}
.forecast-card .forecast-list .forecast-item .forecast-date .date-text.data-v-34a20241 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}
.forecast-card .forecast-list .forecast-item .forecast-date .weather-text.data-v-34a20241 {
  font-size: 24rpx;
  color: #666;
}
.forecast-card .forecast-list .forecast-item .forecast-icon.data-v-34a20241 {
  font-size: 48rpx;
  margin: 0 32rpx;
}
.forecast-card .forecast-list .forecast-item .forecast-temp.data-v-34a20241 {
  text-align: right;
}
.forecast-card .forecast-list .forecast-item .forecast-temp .temp-high.data-v-34a20241 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-right: 8rpx;
}
.forecast-card .forecast-list .forecast-item .forecast-temp .temp-low.data-v-34a20241 {
  font-size: 28rpx;
  color: #666;
}
.instructions .instruction-item.data-v-34a20241 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}