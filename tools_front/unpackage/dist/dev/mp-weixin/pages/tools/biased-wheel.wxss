/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-8fc283da {
  display: flex;
}
.flex-1.data-v-8fc283da {
  flex: 1;
}
.items-center.data-v-8fc283da {
  align-items: center;
}
.justify-center.data-v-8fc283da {
  justify-content: center;
}
.justify-between.data-v-8fc283da {
  justify-content: space-between;
}
.text-center.data-v-8fc283da {
  text-align: center;
}
.rounded.data-v-8fc283da {
  border-radius: 3px;
}
.rounded-lg.data-v-8fc283da {
  border-radius: 6px;
}
.shadow.data-v-8fc283da {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-8fc283da {
  padding: 16rpx;
}
.m-4.data-v-8fc283da {
  margin: 16rpx;
}
.mb-4.data-v-8fc283da {
  margin-bottom: 16rpx;
}
.mt-4.data-v-8fc283da {
  margin-top: 16rpx;
}
.biased-wheel.data-v-8fc283da {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-card.data-v-8fc283da,
.wheel-card.data-v-8fc283da,
.template-card.data-v-8fc283da,
.options-card.data-v-8fc283da,
.settings-card.data-v-8fc283da,
.tips-card.data-v-8fc283da {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-8fc283da {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
}
.card-title.data-v-8fc283da {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-8fc283da {
  padding: 30rpx;
}
.header-info.data-v-8fc283da {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.header-icon.data-v-8fc283da {
  font-size: 60rpx;
}
.header-text.data-v-8fc283da {
  flex: 1;
}
.header-title.data-v-8fc283da {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-desc.data-v-8fc283da {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.wheel-container.data-v-8fc283da {
  position: relative;
  width: 626rpx;
  height: 626rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: visible;
}
.wheel-card.data-v-8fc283da {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 700rpx;
  margin: 20rpx;
  padding: 20rpx;
}
.spin-controls.data-v-8fc283da {
  margin-top: 40rpx;
  width: 100%;
  text-align: center;
}
.spin-btn.data-v-8fc283da {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 24rpx 60rpx;
  background: #FF7B54;
  color: white;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(255, 123, 84, 0.2);
  transition: all 0.3s ease;
}
.spin-btn.data-v-8fc283da:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(255, 123, 84, 0.2);
}
.spin-btn.disabled.data-v-8fc283da {
  opacity: 0.5;
  cursor: not-allowed;
}
.spin-icon.data-v-8fc283da {
  font-size: 36rpx;
}
.spinning-icon.data-v-8fc283da {
  animation: spin-8fc283da 1s linear infinite;
}
@keyframes spin-8fc283da {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.result-display.data-v-8fc283da {
  margin-top: 40rpx;
  padding: 40rpx;
  background: linear-gradient(135deg, #FFF5F2 0%, #FFF9F7 100%);
  border-radius: 30rpx;
  text-align: center;
  border: 2rpx solid rgba(255, 123, 84, 0.1);
}
.result-display .result-title.data-v-8fc283da {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #FF7B54;
  margin-bottom: 16rpx;
}
.result-display .result-text.data-v-8fc283da {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #FF7B54;
  margin-bottom: 20rpx;
}
.result-display .result-info.data-v-8fc283da {
  font-size: 24rpx;
  color: rgba(255, 123, 84, 0.8);
}
.template-grid.data-v-8fc283da {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.template-item.data-v-8fc283da {
  padding: 30rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #fafafa;
}
.template-name.data-v-8fc283da {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.template-desc.data-v-8fc283da {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.header-actions.data-v-8fc283da {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-8fc283da {
  padding: 16rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #666;
  background: #ffffff;
}
.action-btn.primary.data-v-8fc283da {
  background: #2563eb;
  color: white;
  border-color: #2563eb;
}
.options-list.data-v-8fc283da {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.option-item.data-v-8fc283da {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx 24rpx;
  background: #fafafa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
}
.option-item.data-v-8fc283da:hover {
  background: #f5f5f5;
  border-color: #e5e7eb;
}
.color-picker.data-v-8fc283da {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 4rpx solid white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}
.color-picker.data-v-8fc283da:hover {
  transform: scale(1.1);
}
.option-input.data-v-8fc283da {
  flex: 1;
  padding: 12rpx 16rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 28rpx;
  background: white;
}
.option-input.data-v-8fc283da:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 2rpx rgba(37, 99, 235, 0.1);
}
.weight-control.data-v-8fc283da {
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 200rpx;
}
.weight-label.data-v-8fc283da {
  font-size: 24rpx;
  color: #666;
}
.weight-input.data-v-8fc283da {
  width: 70rpx;
  padding: 8rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 6rpx;
  text-align: center;
  font-size: 24rpx;
  background: white;
}
.probability-text.data-v-8fc283da {
  font-size: 22rpx;
  color: #2563eb;
  font-weight: 500;
}
.remove-btn.data-v-8fc283da {
  padding: 12rpx;
  min-width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #fee2e2;
  border-radius: 8rpx;
  background: #fff1f2;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.remove-btn text.data-v-8fc283da {
  font-size: 24rpx;
  color: #ef4444;
}
.remove-btn.data-v-8fc283da:hover:not(.disabled) {
  background: #fecaca;
  border-color: #fca5a5;
}
.remove-btn.disabled.data-v-8fc283da {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
  border-color: #e5e7eb;
}
.setting-item.data-v-8fc283da {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.setting-item.data-v-8fc283da:last-child {
  border-bottom: none;
}
.setting-info.data-v-8fc283da {
  flex: 1;
}
.setting-title.data-v-8fc283da {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}
.setting-desc.data-v-8fc283da {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.setting-switch.data-v-8fc283da {
  transform: scale(0.8);
}
.tips-list.data-v-8fc283da {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.tip-item.data-v-8fc283da {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.tip-bullet.data-v-8fc283da {
  font-size: 28rpx;
  color: #2563eb;
  margin-top: 4rpx;
}
.tip-text.data-v-8fc283da {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
}
.tip-bold.data-v-8fc283da {
  font-weight: 600;
  color: #1a1a1a;
}