/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-f4ba55c6 {
  display: flex;
}
.flex-1.data-v-f4ba55c6 {
  flex: 1;
}
.items-center.data-v-f4ba55c6 {
  align-items: center;
}
.justify-center.data-v-f4ba55c6 {
  justify-content: center;
}
.justify-between.data-v-f4ba55c6 {
  justify-content: space-between;
}
.text-center.data-v-f4ba55c6 {
  text-align: center;
}
.rounded.data-v-f4ba55c6 {
  border-radius: 3px;
}
.rounded-lg.data-v-f4ba55c6 {
  border-radius: 6px;
}
.shadow.data-v-f4ba55c6 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-f4ba55c6 {
  padding: 16rpx;
}
.m-4.data-v-f4ba55c6 {
  margin: 16rpx;
}
.mb-4.data-v-f4ba55c6 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-f4ba55c6 {
  margin-top: 16rpx;
}
.handheld-barrage.data-v-f4ba55c6 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-f4ba55c6 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-f4ba55c6 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.barrage-display.data-v-f4ba55c6 {
  padding: 0;
  overflow: hidden;
  position: relative;
}
.barrage-display .display-container.data-v-f4ba55c6 {
  position: relative;
  height: 500rpx;
  border-radius: 24rpx;
  overflow: hidden;
}
.barrage-display .display-container .barrage-text.data-v-f4ba55c6 {
  position: absolute;
  white-space: nowrap;
  transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: transform 0.3s ease;
}
.barrage-display .display-container .placeholder.data-v-f4ba55c6 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.barrage-display .display-container .placeholder .placeholder-text.data-v-f4ba55c6 {
  color: #9ca3af;
  font-size: 28rpx;
}
.barrage-display .fullscreen-btn.data-v-f4ba55c6 {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.barrage-display .fullscreen-btn .fullscreen-icon.data-v-f4ba55c6 {
  color: white;
  font-size: 28rpx;
}
.barrage-display .fullscreen-btn.data-v-f4ba55c6:active {
  background: rgba(0, 0, 0, 0.8);
}
.card-header.data-v-f4ba55c6 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-header .header-icon.data-v-f4ba55c6 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.card-header .header-title.data-v-f4ba55c6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card-content .input-section.data-v-f4ba55c6 {
  margin-bottom: 30rpx;
}
.card-content .input-section .text-input.data-v-f4ba55c6 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f9fafb;
  box-sizing: border-box;
  line-height: 88rpx;
}
.card-content .input-section .text-input.data-v-f4ba55c6:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}
.card-content .preset-section .section-label.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.card-content .preset-section .preset-grid.data-v-f4ba55c6 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.card-content .preset-section .preset-grid .preset-btn.data-v-f4ba55c6 {
  padding: 16rpx 24rpx;
  background: #f3f4f6;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #374151;
  border: 2rpx solid transparent;
}
.card-content .preset-section .preset-grid .preset-btn.data-v-f4ba55c6:active {
  background: #e5e7eb;
  transform: scale(0.98);
}
.card-content .instructions .instruction-item.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.control-buttons.data-v-f4ba55c6 {
  display: flex;
  gap: 20rpx;
}
.control-buttons .control-btn.data-v-f4ba55c6 {
  flex: 1;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.control-buttons .control-btn .btn-icon.data-v-f4ba55c6 {
  font-size: 32rpx;
}
.control-buttons .control-btn .btn-text.data-v-f4ba55c6 {
  font-size: 26rpx;
  font-weight: 600;
}
.control-buttons .control-btn.start-btn.data-v-f4ba55c6 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.3);
}
.control-buttons .control-btn.stop-btn.data-v-f4ba55c6 {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 8rpx 32rpx rgba(239, 68, 68, 0.3);
}
.control-buttons .control-btn.reset-btn.data-v-f4ba55c6 {
  background: #f3f4f6;
  color: #374151;
  border: 2rpx solid #e5e7eb;
}
.control-buttons .control-btn.data-v-f4ba55c6:active {
  transform: scale(0.98);
}
.setting-item.data-v-f4ba55c6 {
  margin-bottom: 40rpx;
}
.setting-item .setting-label.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.setting-item .setting-slider.data-v-f4ba55c6 {
  width: calc(100% - 60rpx);
  margin: 0 30rpx;
  padding: 0;
}
.setting-item .setting-slider.data-v-f4ba55c6 .uni-slider-track {
  height: 6rpx !important;
  background: #e5e7eb !important;
  border-radius: 3rpx !important;
}
.setting-item .setting-slider.data-v-f4ba55c6 .uni-slider-handle {
  width: 32rpx !important;
  height: 32rpx !important;
  background: #3b82f6 !important;
  border: 4rpx solid #ffffff !important;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3) !important;
}
.setting-item .setting-slider.data-v-f4ba55c6 .uni-slider-tap-area {
  padding: 20rpx 0 !important;
}
.setting-item .direction-buttons.data-v-f4ba55c6 {
  display: flex;
  gap: 16rpx;
}
.setting-item .direction-buttons .direction-btn.data-v-f4ba55c6 {
  flex: 1;
  padding: 20rpx;
  text-align: center;
  background: #f3f4f6;
  border-radius: 16rpx;
  font-size: 26rpx;
  color: #374151;
  border: 2rpx solid transparent;
}
.setting-item .direction-buttons .direction-btn.active.data-v-f4ba55c6 {
  background: #3b82f6;
  color: white;
  border-color: #2563eb;
}
.setting-item .direction-buttons .direction-btn.data-v-f4ba55c6:active {
  transform: scale(0.98);
}
.color-section.data-v-f4ba55c6 {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}
.color-section .color-item.data-v-f4ba55c6 {
  flex: 1;
}
.color-section .color-item .color-label.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.color-section .color-item .color-display.data-v-f4ba55c6 {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.color-section .color-item .color-display .color-value.data-v-f4ba55c6 {
  font-size: 24rpx;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.color-section .color-item .color-display.data-v-f4ba55c6:active {
  border-color: #3b82f6;
}
.preset-colors .setting-label.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.preset-colors .color-presets.data-v-f4ba55c6 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.preset-colors .color-presets .color-preset.data-v-f4ba55c6 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
}
.preset-colors .color-presets .color-preset .preset-bg.data-v-f4ba55c6,
.preset-colors .color-presets .color-preset .preset-text.data-v-f4ba55c6 {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  border: 1rpx solid #d1d5db;
}
.preset-colors .color-presets .color-preset .preset-name.data-v-f4ba55c6 {
  font-size: 24rpx;
  color: #374151;
}
.preset-colors .color-presets .color-preset.data-v-f4ba55c6:active {
  background: #f3f4f6;
  transform: scale(0.98);
}
.color-modal.data-v-f4ba55c6 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.color-modal .color-modal-content.data-v-f4ba55c6 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin: 30rpx;
  max-height: 80vh;
  overflow-y: auto;
}
.color-modal .color-modal-content .modal-header.data-v-f4ba55c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.color-modal .color-modal-content .modal-header .modal-title.data-v-f4ba55c6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.color-modal .color-modal-content .modal-header .modal-close.data-v-f4ba55c6 {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 50%;
  font-size: 28rpx;
  color: #666;
}
.color-modal .color-modal-content .color-grid.data-v-f4ba55c6 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.color-modal .color-modal-content .color-grid .color-option.data-v-f4ba55c6 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.color-modal .color-modal-content .color-grid .color-option .selected-mark.data-v-f4ba55c6 {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}
.color-modal .color-modal-content .color-grid .color-option.data-v-f4ba55c6:active {
  transform: scale(0.95);
}
.color-modal .color-modal-content .custom-color-section .custom-label.data-v-f4ba55c6 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.color-modal .color-modal-content .custom-color-section .custom-input.data-v-f4ba55c6 {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f9fafb;
  box-sizing: border-box;
}
.color-modal .color-modal-content .custom-color-section .custom-input.data-v-f4ba55c6:focus {
  border-color: #3b82f6;
  outline: none;
}
.fullscreen-barrage.data-v-f4ba55c6 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  overflow: hidden;
}
.fullscreen-barrage .fullscreen-text.data-v-f4ba55c6 {
  position: absolute;
  white-space: nowrap;
  transform: translateZ(0);
  will-change: transform, top, left;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: none;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}
.fullscreen-barrage .fullscreen-text.data-v-f4ba55c6::after {
  content: "";
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 5px;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s;
}
.fullscreen-barrage .fullscreen-text.data-v-f4ba55c6:active::after {
  opacity: 1;
}
.fullscreen-barrage .exit-fullscreen-btn.data-v-f4ba55c6 {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.fullscreen-barrage .exit-fullscreen-btn .exit-icon.data-v-f4ba55c6 {
  color: white;
  font-size: 40rpx;
  font-weight: bold;
}