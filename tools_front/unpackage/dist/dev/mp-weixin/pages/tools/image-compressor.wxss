/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-6f26eb81 {
  display: flex;
}
.flex-1.data-v-6f26eb81 {
  flex: 1;
}
.items-center.data-v-6f26eb81 {
  align-items: center;
}
.justify-center.data-v-6f26eb81 {
  justify-content: center;
}
.justify-between.data-v-6f26eb81 {
  justify-content: space-between;
}
.text-center.data-v-6f26eb81 {
  text-align: center;
}
.rounded.data-v-6f26eb81 {
  border-radius: 3px;
}
.rounded-lg.data-v-6f26eb81 {
  border-radius: 6px;
}
.shadow.data-v-6f26eb81 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-6f26eb81 {
  padding: 16rpx;
}
.m-4.data-v-6f26eb81 {
  margin: 16rpx;
}
.mb-4.data-v-6f26eb81 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-6f26eb81 {
  margin-top: 16rpx;
}
.image-compressor.data-v-6f26eb81 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-6f26eb81 {
  padding: 30rpx;
  max-width: 1000rpx;
  margin: 0 auto;
}
.upload-card.data-v-6f26eb81, .settings-card.data-v-6f26eb81, .result-card.data-v-6f26eb81 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header.data-v-6f26eb81 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-header .header-icon.data-v-6f26eb81 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.card-header .header-icon .icon.data-v-6f26eb81 {
  font-size: 32rpx;
  color: #3b82f6;
}
.card-header .header-title.data-v-6f26eb81 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.upload-area.data-v-6f26eb81 {
  border: 4rpx dashed #d1d5db;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}
.upload-area .upload-icon.data-v-6f26eb81 {
  margin-bottom: 30rpx;
}
.upload-area .upload-icon .icon.data-v-6f26eb81 {
  font-size: 96rpx;
  color: #9ca3af;
}
.upload-area .upload-desc.data-v-6f26eb81 {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 30rpx;
}
.upload-area .file-input-wrapper.data-v-6f26eb81 {
  margin-bottom: 30rpx;
}
.upload-area .file-input-btn.data-v-6f26eb81 {
  padding: 24rpx 48rpx;
  background: #3b82f6;
  color: white;
  border-radius: 12rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.upload-area .file-input-btn.data-v-6f26eb81:active {
  transform: scale(0.96);
  background: #2563eb;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.3);
}
.upload-area .file-info.data-v-6f26eb81 {
  background: #dbeafe;
  border-radius: 12rpx;
  padding: 24rpx;
}
.upload-area .file-info .file-name.data-v-6f26eb81, .upload-area .file-info .file-size.data-v-6f26eb81 {
  display: block;
  font-size: 26rpx;
  color: #1d4ed8;
  margin-bottom: 8rpx;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
}
.upload-area .file-info .file-size.data-v-6f26eb81 {
  color: #3b82f6;
  margin-bottom: 0;
}
.quality-settings.data-v-6f26eb81 {
  margin-bottom: 30rpx;
}
.quality-settings .settings-title.data-v-6f26eb81 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.quality-settings .quality-value.data-v-6f26eb81 {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
  margin-bottom: 16rpx;
}
.quality-settings .slider.data-v-6f26eb81 {
  margin-bottom: 24rpx;
}
.quality-settings .quality-presets.data-v-6f26eb81 {
  display: flex;
  gap: 16rpx;
}
.quality-settings .quality-presets .preset-btn.data-v-6f26eb81 {
  flex: 1;
  padding: 16rpx;
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #374151;
  text-align: center;
  transition: all 0.2s ease;
}
.quality-settings .quality-presets .preset-btn.data-v-6f26eb81:active {
  background: #f9fafb;
  transform: scale(0.98);
  border-color: #d1d5db;
}
.quality-settings .quality-presets .preset-btn.active.data-v-6f26eb81 {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #1d4ed8;
}
.compress-btn.data-v-6f26eb81 {
  width: 100%;
  padding: 48rpx 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}
.compress-btn.data-v-6f26eb81:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}
.compress-btn.disabled.data-v-6f26eb81 {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.compress-btn.disabled.data-v-6f26eb81:active {
  transform: none;
  box-shadow: none;
  background: #e5e7eb;
}
.compress-btn .btn-text.data-v-6f26eb81 {
  font-size: 32rpx;
  font-weight: 600;
}
.compression-info .info-title.data-v-6f26eb81 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.compression-info .info-grid.data-v-6f26eb81 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.compression-info .info-grid .info-item.data-v-6f26eb81 {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
}
.compression-info .info-grid .info-item .info-label.data-v-6f26eb81 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.compression-info .info-grid .info-item .info-value.data-v-6f26eb81 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.compression-info .info-grid .info-item .info-value.highlight.data-v-6f26eb81 {
  color: #3b82f6;
}
.compression-info .download-btn.data-v-6f26eb81 {
  width: 100%;
  padding: 32rpx 0;
  background: white;
  border: 2rpx solid #e5e7eb;
  color: #374151;
  border-radius: 16rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;
}
.compression-info .download-btn.data-v-6f26eb81:active {
  background: #f9fafb;
  transform: scale(0.98);
  border-color: #d1d5db;
}
.compression-info .download-btn .btn-icon.data-v-6f26eb81 {
  font-size: 28rpx;
}
.compression-info .download-btn .btn-text.data-v-6f26eb81 {
  font-size: 28rpx;
  font-weight: 500;
}
.info-section.data-v-6f26eb81 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.info-section .info-header.data-v-6f26eb81 {
  margin-bottom: 24rpx;
}
.info-section .info-header .info-title.data-v-6f26eb81 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.info-section .info-content .info-item.data-v-6f26eb81 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.info-section .info-content .info-item-last.data-v-6f26eb81 {
  margin-bottom: 0;
}
.settings-card .settings-content .quality-header.data-v-6f26eb81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.settings-card .settings-content .quality-header .setting-label.data-v-6f26eb81 {
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 600;
}
.settings-card .settings-content .quality-header .quality-value.data-v-6f26eb81 {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.1);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
}
.settings-card .settings-content .quality-slider.data-v-6f26eb81 {
  margin: 20rpx 0 40rpx;
}
.settings-card .settings-content .preset-buttons.data-v-6f26eb81 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.settings-card .settings-content .preset-buttons .preset-btn.data-v-6f26eb81 {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
  border: 2rpx solid #e2e8f0;
}
.settings-card .settings-content .preset-buttons .preset-btn.active.data-v-6f26eb81 {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}
.settings-card .settings-content .preset-buttons .preset-btn.data-v-6f26eb81:active {
  transform: scale(0.98);
}
.settings-card .settings-content .preset-buttons .preset-btn .preset-icon.data-v-6f26eb81 {
  display: block;
  font-size: 36rpx;
  margin-bottom: 12rpx;
}
.settings-card .settings-content .preset-buttons .preset-btn .preset-text.data-v-6f26eb81 {
  display: block;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.settings-card .settings-content .preset-buttons .preset-btn .preset-desc.data-v-6f26eb81 {
  display: block;
  font-size: 24rpx;
  color: #64748b;
}
.result-card .result-header.data-v-6f26eb81 {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}
.result-card .result-header .status-icon.data-v-6f26eb81 {
  font-size: 40rpx;
  margin-right: 16rpx;
}
.result-card .result-header .result-title.data-v-6f26eb81 {
  font-size: 34rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.result-card .result-stats.data-v-6f26eb81 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
}
.result-card .result-stats .stat-item.data-v-6f26eb81 {
  text-align: center;
}
.result-card .result-stats .stat-item.ratio.data-v-6f26eb81 {
  background: rgba(59, 130, 246, 0.1);
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
}
.result-card .result-stats .stat-item .stat-label.data-v-6f26eb81 {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}
.result-card .result-stats .stat-item .stat-value.data-v-6f26eb81 {
  display: block;
  font-size: 30rpx;
  color: #1a1a1a;
  font-weight: 600;
}
.result-card .result-stats .stat-item .stat-value.highlight.data-v-6f26eb81 {
  color: #3b82f6;
}
.result-card .result-stats .stat-item .stat-value.success.data-v-6f26eb81 {
  color: #10b981;
}
.result-card .result-stats .stat-arrow.data-v-6f26eb81 {
  font-size: 32rpx;
  color: #64748b;
  margin: 0 20rpx;
}
.result-card .download-btn.data-v-6f26eb81 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  margin: 0 auto;
  padding: 0;
}
.result-card .download-btn.data-v-6f26eb81:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}
.result-card .download-btn .download-icon.data-v-6f26eb81 {
  font-size: 32rpx;
}
.result-card .download-btn .download-text.data-v-6f26eb81 {
  font-size: 30rpx;
  color: white;
  font-weight: 600;
}