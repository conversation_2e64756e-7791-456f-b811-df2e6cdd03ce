/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-ffd35138 {
  display: flex;
}
.flex-1.data-v-ffd35138 {
  flex: 1;
}
.items-center.data-v-ffd35138 {
  align-items: center;
}
.justify-center.data-v-ffd35138 {
  justify-content: center;
}
.justify-between.data-v-ffd35138 {
  justify-content: space-between;
}
.text-center.data-v-ffd35138 {
  text-align: center;
}
.rounded.data-v-ffd35138 {
  border-radius: 3px;
}
.rounded-lg.data-v-ffd35138 {
  border-radius: 6px;
}
.shadow.data-v-ffd35138 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-ffd35138 {
  padding: 16rpx;
}
.m-4.data-v-ffd35138 {
  margin: 16rpx;
}
.mb-4.data-v-ffd35138 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-ffd35138 {
  margin-top: 16rpx;
}
.mock-call-container.data-v-ffd35138 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}
.mock-call-container.call-active.data-v-ffd35138 {
  background: #1a2634;
}
.safe-area.data-v-ffd35138 {
  padding-top: var(--status-bar-height);
}
.call-screen.data-v-ffd35138 {
  min-height: 100vh;
  color: white;
  position: relative;
  display: flex;
  flex-direction: column;
  background: #1a2634;
  padding-top: 60rpx;
}
.status-bar.data-v-ffd35138 {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx;
  background: #1a2634;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
}
.status-bar .signal.data-v-ffd35138 {
  font-size: 24rpx;
  opacity: 0.8;
}
.call-info.data-v-ffd35138 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 0 40rpx;
}
.call-info .caller-name.data-v-ffd35138 {
  font-size: 64rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.call-info .caller-location.data-v-ffd35138 {
  font-size: 36rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}
.call-info .caller-carrier.data-v-ffd35138 {
  font-size: 32rpx;
  opacity: 0.6;
}
.call-info .call-duration.data-v-ffd35138 {
  font-size: 36rpx;
  opacity: 0.8;
  margin-top: 20rpx;
}
.call-controls.data-v-ffd35138 {
  margin-top: auto;
  padding-bottom: 80rpx;
}
.call-controls .control-buttons.data-v-ffd35138 {
  margin-bottom: 30rpx;
}
.call-controls .control-buttons .button-row.data-v-ffd35138 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}
.call-controls .control-buttons .button-row .control-btn.data-v-ffd35138 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}
.call-controls .control-buttons .button-row .control-btn .btn-icon.data-v-ffd35138 {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
}
.call-controls .control-buttons .button-row .control-btn .btn-label.data-v-ffd35138 {
  font-size: 24rpx;
  color: white;
  opacity: 0.9;
}
.call-controls .control-buttons .button-row .control-btn.active .btn-icon.data-v-ffd35138 {
  background: rgba(255, 255, 255, 0.3);
}
.call-controls .end-call-container.data-v-ffd35138 {
  display: flex;
  justify-content: center;
  margin-top: 10rpx;
}
.call-controls .end-call-container .end-call-btn.data-v-ffd35138 {
  width: 120rpx;
  height: 120rpx;
  background: #FF3B30;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.call-controls .end-call-container .end-call-btn .end-call-icon.data-v-ffd35138 {
  font-size: 48rpx;
  color: white;
  transform: rotate(135deg);
}
.incoming-call.data-v-ffd35138 {
  margin-top: auto;
  padding: 0 40rpx;
}
.incoming-call .top-actions.data-v-ffd35138 {
  display: flex;
  justify-content: space-around;
  margin-bottom: 100rpx;
}
.incoming-call .top-actions .action-btn.data-v-ffd35138 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}
.incoming-call .top-actions .action-btn .action-icon.data-v-ffd35138 {
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}
.incoming-call .top-actions .action-btn .action-label.data-v-ffd35138 {
  font-size: 28rpx;
  color: white;
  opacity: 0.9;
}
.incoming-call .answer-controls.data-v-ffd35138 {
  display: flex;
  justify-content: space-between;
  padding: 0 40rpx;
  margin-bottom: 60rpx;
}
.incoming-call .answer-controls .reject-btn.data-v-ffd35138, .incoming-call .answer-controls .answer-btn.data-v-ffd35138 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}
.incoming-call .answer-controls .reject-btn .reject-icon.data-v-ffd35138, .incoming-call .answer-controls .reject-btn .answer-icon.data-v-ffd35138, .incoming-call .answer-controls .answer-btn .reject-icon.data-v-ffd35138, .incoming-call .answer-controls .answer-btn .answer-icon.data-v-ffd35138 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: white;
}
.incoming-call .answer-controls .reject-btn .reject-label.data-v-ffd35138, .incoming-call .answer-controls .reject-btn .answer-label.data-v-ffd35138, .incoming-call .answer-controls .answer-btn .reject-label.data-v-ffd35138, .incoming-call .answer-controls .answer-btn .answer-label.data-v-ffd35138 {
  font-size: 32rpx;
  color: white;
  opacity: 0.9;
}
.incoming-call .answer-controls .reject-btn .reject-icon.data-v-ffd35138 {
  background: #FF3B30;
  transform: rotate(135deg);
}
.incoming-call .answer-controls .answer-btn .answer-icon.data-v-ffd35138 {
  background: #34C759;
}
.settings-screen.data-v-ffd35138 {
  padding: 30rpx;
  padding-top: calc(var(--status-bar-height) + 18rpx);
}
.header-card.data-v-ffd35138 {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.header-card .header-content.data-v-ffd35138 {
  display: flex;
  align-items: center;
  color: white;
}
.header-card .header-content .header-icon.data-v-ffd35138 {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.header-card .header-content .header-info.data-v-ffd35138 {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-ffd35138 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-card .header-content .header-info .header-subtitle.data-v-ffd35138 {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.preview-card.data-v-ffd35138 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.preview-card .preview-header.data-v-ffd35138 {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e7eb;
}
.preview-card .preview-header .preview-title.data-v-ffd35138 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.preview-card .preview-content.data-v-ffd35138 {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.preview-card .preview-content .preview-avatar.data-v-ffd35138 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #007AFF, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}
.preview-card .preview-content .preview-avatar .preview-avatar-text.data-v-ffd35138 {
  font-size: 48rpx;
  font-weight: 600;
  color: white;
}
.preview-card .preview-content .preview-info.data-v-ffd35138 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.preview-card .preview-content .preview-info .preview-name.data-v-ffd35138 {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}
.preview-card .preview-content .preview-info .preview-number.data-v-ffd35138 {
  font-size: 32rpx;
  color: #6b7280;
}
.preview-card .preview-content .preview-info .preview-location.data-v-ffd35138 {
  font-size: 28rpx;
  color: #9ca3af;
}
.preview-card .preview-content .preview-info .preview-carrier.data-v-ffd35138 {
  font-size: 24rpx;
  color: #d1d5db;
}
.settings-form.data-v-ffd35138,
.form-section.data-v-ffd35138,
.form-group.data-v-ffd35138,
.form-input.data-v-ffd35138,
.picker-input.data-v-ffd35138 {
  box-sizing: border-box;
}
.form-input.data-v-ffd35138,
.picker-input.data-v-ffd35138 {
  width: 100%;
  min-width: 0;
  max-width: 100%;
  padding: 36rpx;
  min-height: 72rpx;
}
.settings-form.data-v-ffd35138 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.settings-form .form-section.data-v-ffd35138 {
  padding: 30rpx;
}
.settings-form .form-section .section-title.data-v-ffd35138 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.settings-form .form-section .form-group.data-v-ffd35138 {
  margin-bottom: 24rpx;
}
.settings-form .form-section .form-group.data-v-ffd35138:last-child {
  margin-bottom: 0;
}
.settings-form .form-section .form-group .form-label.data-v-ffd35138 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
}
.settings-form .form-section .form-group .form-input.data-v-ffd35138 {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
}
.settings-form .form-section .form-group .form-input.data-v-ffd35138:focus {
  border-color: #007AFF;
  background: white;
}
.settings-form .form-section .form-group .picker-input.data-v-ffd35138 {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9fafb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.settings-form .form-section .form-group .picker-input .picker-arrow.data-v-ffd35138 {
  color: #9ca3af;
  font-size: 24rpx;
}
.start-button.data-v-ffd35138 {
  background: linear-gradient(135deg, #007AFF, #0056b3);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.start-button .start-icon.data-v-ffd35138 {
  font-size: 40rpx;
  color: white;
}
.start-button .start-text.data-v-ffd35138 {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}
.info-card.data-v-ffd35138 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.info-card .info-title.data-v-ffd35138 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}
.info-card .info-content.data-v-ffd35138 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.info-card .info-content .info-item.data-v-ffd35138 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.custom-navbar.data-v-ffd35138 {
  display: flex;
  align-items: center;
  position: relative;
  height: calc(var(--status-bar-height) + 138rpx);
  padding-top: var(--status-bar-height);
  background: #fff;
  box-sizing: border-box;
  z-index: 101;
  box-shadow: 0 1rpx 0 #ededed;
}
.custom-navbar .nav-back.data-v-ffd35138,
.custom-navbar .nav-title-wrap.data-v-ffd35138,
.custom-navbar .nav-placeholder.data-v-ffd35138 {
  padding-top: 28rpx;
}
.custom-navbar .nav-back.data-v-ffd35138 {
  font-size: 48rpx;
  color: #222;
  margin-left: 32rpx;
  margin-right: 24rpx;
  font-weight: 600;
  height: 108rpx;
  line-height: 108rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.custom-navbar .nav-title-wrap.data-v-ffd35138 {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 108rpx;
}
.custom-navbar .nav-title.data-v-ffd35138 {
  font-size: 28rpx;
  font-weight: 400;
  color: #222;
  line-height: 108rpx;
  text-align: center;
  height: 108rpx;
}
.custom-navbar .nav-placeholder.data-v-ffd35138 {
  width: 132rpx;
  height: 108rpx;
}