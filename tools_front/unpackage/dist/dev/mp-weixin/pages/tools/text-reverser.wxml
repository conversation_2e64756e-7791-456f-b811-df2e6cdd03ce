<view class="text-reverser data-v-4c2051de"><view class="container data-v-4c2051de"><view class="header-section data-v-4c2051de"><view class="title-container data-v-4c2051de"><text class="title-icon data-v-4c2051de">🔄</text><text class="title-text data-v-4c2051de">文本反转器</text></view><text class="subtitle data-v-4c2051de">快速反转文本顺序，支持中英文、数字和特殊字符</text></view><view class="type-section data-v-4c2051de"><view class="section-header data-v-4c2051de"><text class="section-icon data-v-4c2051de">🔀</text><text class="section-title data-v-4c2051de">选择逆序类型</text></view><view class="type-options data-v-4c2051de"><view class="{{['type-option', 'data-v-4c2051de', a && 'active']}}" bindtap="{{b}}"><view class="option-title data-v-4c2051de">字符逆序</view><view class="option-desc data-v-4c2051de">按字符反转</view></view><view class="{{['type-option', 'data-v-4c2051de', c && 'active']}}" bindtap="{{d}}"><view class="option-title data-v-4c2051de">单词逆序</view><view class="option-desc data-v-4c2051de">按单词反转</view></view><view class="{{['type-option', 'data-v-4c2051de', e && 'active']}}" bindtap="{{f}}"><view class="option-title data-v-4c2051de">行逆序</view><view class="option-desc data-v-4c2051de">按行反转</view></view></view></view><view class="input-section data-v-4c2051de"><view class="section-header data-v-4c2051de"><text class="section-icon data-v-4c2051de">✏️</text><text class="section-title data-v-4c2051de">输入文本</text></view><view class="input-wrapper data-v-4c2051de"><block wx:if="{{r0}}"><textarea class="text-input data-v-4c2051de" placeholder="请输入要反转的文本..." maxlength="{{5000}}" adjust-position="{{false}}" auto-height="{{true}}" bindinput="{{g}}" value="{{h}}"/></block><view class="input-footer data-v-4c2051de"><view class="char-count data-v-4c2051de"><text class="count-text data-v-4c2051de">{{i}}/5000</text></view><view class="input-actions data-v-4c2051de"><view wx:if="{{j}}" class="clear-btn data-v-4c2051de" bindtap="{{k}}"><text class="btn-icon data-v-4c2051de">🗑️</text><text class="btn-text data-v-4c2051de">清空</text></view><view wx:if="{{l}}" class="reverse-btn data-v-4c2051de" bindtap="{{m}}"><text class="btn-icon data-v-4c2051de">🔄</text><text class="btn-text data-v-4c2051de">反转</text></view></view></view></view></view><view wx:if="{{n}}" class="output-section data-v-4c2051de"><view class="section-header data-v-4c2051de"><text class="section-icon data-v-4c2051de">✨</text><text class="section-title data-v-4c2051de">反转结果</text></view><view class="output-card data-v-4c2051de"><view class="result-text data-v-4c2051de">{{o}}</view><view class="output-actions data-v-4c2051de"><view class="action-btn primary data-v-4c2051de" bindtap="{{p}}"><text class="btn-icon data-v-4c2051de">📋</text><text class="btn-text data-v-4c2051de">复制结果</text></view><view class="action-btn secondary data-v-4c2051de" bindtap="{{q}}"><text class="btn-icon data-v-4c2051de">🔄</text><text class="btn-text data-v-4c2051de">再次反转</text></view></view></view></view><view class="help-section data-v-4c2051de"><view class="section-header data-v-4c2051de"><text class="section-icon data-v-4c2051de">💡</text><text class="section-title data-v-4c2051de">使用说明</text></view><view class="help-content data-v-4c2051de"><text class="help-item data-v-4c2051de">• 字符逆序：将整个文本按字符顺序进行反转</text><text class="help-item data-v-4c2051de">• 单词逆序：保持单词内部顺序，反转单词顺序</text><text class="help-item data-v-4c2051de">• 行逆序：保持每行内容顺序，反转行的顺序</text><text class="help-item data-v-4c2051de">• 支持中文、英文、数字和特殊字符</text><text class="help-item data-v-4c2051de">• 最多支持5000个字符</text></view></view></view></view>