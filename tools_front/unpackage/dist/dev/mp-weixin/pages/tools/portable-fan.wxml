<view class="portable-fan-tool data-v-f92699d7"><view class="header-card data-v-f92699d7"><view class="header-content data-v-f92699d7"><view class="header-icon data-v-f92699d7">🌀</view><view class="header-info data-v-f92699d7"><text class="header-title data-v-f92699d7">便携风扇</text><text class="header-subtitle data-v-f92699d7">清凉一夏 随身降温</text></view><view class="{{['status-badge', 'data-v-f92699d7', b && 'status-on']}}"><text class="status-text data-v-f92699d7">{{a}}</text></view></view></view><view class="fan-container data-v-f92699d7"><view class="fan-background data-v-f92699d7"><view wx:for="{{c}}" wx:for-item="wind" wx:key="a" class="wind-particle data-v-f92699d7" style="{{'left:' + wind.b + ';' + ('top:' + wind.c) + ';' + ('animation-delay:' + wind.d) + ';' + ('animation-duration:' + wind.e)}}"><text class="wind-emoji data-v-f92699d7">💨</text></view><view class="fan-unit data-v-f92699d7"><view class="fan-frame data-v-f92699d7"><view class="fan-circle data-v-f92699d7"><view class="fan-grid data-v-f92699d7"><view wx:for="{{d}}" wx:for-item="i" wx:key="a" class="grid-line horizontal data-v-f92699d7"></view><view wx:for="{{e}}" wx:for-item="i" wx:key="a" class="grid-line vertical data-v-f92699d7"></view></view></view></view><view class="{{['fan-blades-container', 'data-v-f92699d7', g]}}" style="{{'animation-duration:' + h + ';' + ('animation-play-state:' + i)}}"><view class="fan-blades data-v-f92699d7"><view wx:for="{{f}}" wx:for-item="i" wx:key="a" class="blade data-v-f92699d7" style="{{'transform:' + i.b}}"><view class="blade-body data-v-f92699d7"></view></view></view></view><view class="fan-center data-v-f92699d7"><view class="center-circle data-v-f92699d7"><view class="center-logo data-v-f92699d7">{{j}}</view></view></view></view><view class="{{['power-button', 'data-v-f92699d7', l && 'power-on']}}" bindtap="{{m}}"><text class="power-icon data-v-f92699d7">{{k}}</text></view><view wx:if="{{n}}" class="wind-indicators data-v-f92699d7"><view wx:for="{{o}}" wx:for-item="i" wx:key="a" class="wind-ring data-v-f92699d7" style="{{'animation-delay:' + i.b + ';' + ('animation-duration:' + p)}}"></view></view></view><view class="fan-info data-v-f92699d7"><view class="info-item data-v-f92699d7"><text class="info-label data-v-f92699d7">当前档位</text><text class="info-value data-v-f92699d7">{{q}}档</text></view><view class="info-item data-v-f92699d7"><text class="info-label data-v-f92699d7">风力等级</text><text class="info-value data-v-f92699d7">{{r}}</text></view><view class="info-item data-v-f92699d7"><text class="info-label data-v-f92699d7">运行时间</text><text class="info-value data-v-f92699d7">{{s}}</text></view></view></view><view class="control-panel data-v-f92699d7"><view class="panel-header data-v-f92699d7"><text class="panel-icon data-v-f92699d7">⚙️</text><text class="panel-title data-v-f92699d7">控制面板</text></view><view class="panel-content data-v-f92699d7"><view class="speed-control data-v-f92699d7"><text class="control-label data-v-f92699d7">风速档位</text><view class="speed-levels data-v-f92699d7"><view wx:for="{{t}}" wx:for-item="level" wx:key="c" class="{{['speed-btn', 'data-v-f92699d7', level.d && 'active', v && 'disabled']}}" bindtap="{{level.e}}"><text class="speed-number data-v-f92699d7">{{level.a}}</text><view class="speed-indicator data-v-f92699d7"><view wx:for="{{level.b}}" wx:for-item="dot" wx:key="a" class="indicator-dot data-v-f92699d7"></view></view></view></view></view><view class="mode-control data-v-f92699d7"><text class="control-label data-v-f92699d7">运行模式</text><view class="mode-options data-v-f92699d7"><view wx:for="{{w}}" wx:for-item="mode" wx:key="c" class="{{['mode-btn', 'data-v-f92699d7', mode.d && 'active']}}" bindtap="{{mode.e}}"><text class="mode-icon data-v-f92699d7">{{mode.a}}</text><text class="mode-name data-v-f92699d7">{{mode.b}}</text></view></view></view><view class="function-control data-v-f92699d7"><view class="function-item data-v-f92699d7"><view class="function-info data-v-f92699d7"><text class="function-icon data-v-f92699d7">🔊</text><text class="function-name data-v-f92699d7">风扇音效</text></view><view class="{{['toggle-switch', 'data-v-f92699d7', x && 'active']}}" bindtap="{{y}}"><view class="switch-thumb data-v-f92699d7"></view></view></view><view class="function-item data-v-f92699d7"><view class="function-info data-v-f92699d7"><text class="function-icon data-v-f92699d7">🌡️</text><text class="function-name data-v-f92699d7">温度显示</text></view><view class="{{['toggle-switch', 'data-v-f92699d7', z && 'active']}}" bindtap="{{A}}"><view class="switch-thumb data-v-f92699d7"></view></view></view></view></view></view><view wx:if="{{B}}" class="environment-card data-v-f92699d7"><view class="env-header data-v-f92699d7"><text class="env-icon data-v-f92699d7">🌡️</text><text class="env-title data-v-f92699d7">环境监测</text></view><view class="env-content data-v-f92699d7"><view class="env-grid data-v-f92699d7"><view class="env-item data-v-f92699d7"><text class="env-value data-v-f92699d7">{{C}}°C</text><text class="env-label data-v-f92699d7">当前温度</text></view><view class="env-item data-v-f92699d7"><text class="env-value data-v-f92699d7">{{D}}%</text><text class="env-label data-v-f92699d7">相对湿度</text></view><view class="env-item data-v-f92699d7"><text class="env-value data-v-f92699d7">{{E}}</text><text class="env-label data-v-f92699d7">舒适度</text></view></view></view></view><view class="stats-card data-v-f92699d7"><view class="stats-header data-v-f92699d7"><text class="stats-icon data-v-f92699d7">📊</text><text class="stats-title data-v-f92699d7">使用统计</text></view><view class="stats-content data-v-f92699d7"><view class="stats-grid data-v-f92699d7"><view class="stat-item data-v-f92699d7"><text class="stat-value data-v-f92699d7">{{F}}</text><text class="stat-label data-v-f92699d7">累计运行</text></view><view class="stat-item data-v-f92699d7"><text class="stat-value data-v-f92699d7">{{G}}</text><text class="stat-label data-v-f92699d7">今日使用</text></view><view class="stat-item data-v-f92699d7"><text class="stat-value data-v-f92699d7">{{H}}W</text><text class="stat-label data-v-f92699d7">节能效果</text></view></view></view></view><view class="timer-card data-v-f92699d7"><view class="timer-header data-v-f92699d7"><text class="timer-icon data-v-f92699d7">⏰</text><text class="timer-title data-v-f92699d7">定时关机</text><view wx:if="{{I}}" class="timer-remaining data-v-f92699d7"><text class="remaining-text data-v-f92699d7">{{J}}</text></view></view><view class="timer-content data-v-f92699d7"><view class="timer-options data-v-f92699d7"><view wx:for="{{K}}" wx:for-item="option" wx:key="b" class="{{['timer-btn', 'data-v-f92699d7', option.c && 'active']}}" bindtap="{{option.d}}"><text class="timer-text data-v-f92699d7">{{option.a}}</text></view></view><view class="timer-actions data-v-f92699d7"><view bindtap="{{L}}" class="{{['action-btn', 'start', 'data-v-f92699d7', M && 'disabled']}}"><text class="action-text data-v-f92699d7">开始定时</text></view><view bindtap="{{N}}" class="{{['action-btn', 'cancel', 'data-v-f92699d7', O && 'disabled']}}"><text class="action-text data-v-f92699d7">取消定时</text></view></view></view></view></view>