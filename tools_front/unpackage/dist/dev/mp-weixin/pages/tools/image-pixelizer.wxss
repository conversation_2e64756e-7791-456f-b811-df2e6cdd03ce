/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a46fb513 {
  display: flex;
}
.flex-1.data-v-a46fb513 {
  flex: 1;
}
.items-center.data-v-a46fb513 {
  align-items: center;
}
.justify-center.data-v-a46fb513 {
  justify-content: center;
}
.justify-between.data-v-a46fb513 {
  justify-content: space-between;
}
.text-center.data-v-a46fb513 {
  text-align: center;
}
.rounded.data-v-a46fb513 {
  border-radius: 3px;
}
.rounded-lg.data-v-a46fb513 {
  border-radius: 6px;
}
.shadow.data-v-a46fb513 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a46fb513 {
  padding: 16rpx;
}
.m-4.data-v-a46fb513 {
  margin: 16rpx;
}
.mb-4.data-v-a46fb513 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a46fb513 {
  margin-top: 16rpx;
}
.image-pixelizer-container.data-v-a46fb513 {
  min-height: 100vh;
  background: #ffffff;
  padding: 0 32rpx 40rpx;
}
.header.data-v-a46fb513 {
  padding: 80rpx 0 40rpx;
  text-align: center;
}
.header .header-content .title.data-v-a46fb513 {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}
.header .header-content .subtitle.data-v-a46fb513 {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 400;
}
.main-content .upload-section.data-v-a46fb513 {
  margin-bottom: 40rpx;
}
.main-content .upload-section .upload-card.data-v-a46fb513 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .upload-section .upload-card .upload-icon .icon.data-v-a46fb513 {
  font-size: 120rpx;
  display: block;
  margin-bottom: 24rpx;
}
.main-content .upload-section .upload-card .upload-title.data-v-a46fb513 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}
.main-content .upload-section .upload-card .upload-desc.data-v-a46fb513 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
  line-height: 1.5;
}
.main-content .upload-section .upload-card .upload-btn.data-v-a46fb513 {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: inline-block;
  box-shadow: 0 8rpx 25rpx rgba(245, 158, 11, 0.3);
  transition: all 0.3s ease;
}
.main-content .upload-section .upload-card .upload-btn.data-v-a46fb513:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(245, 158, 11, 0.4);
}
.main-content .upload-section .camera-option.data-v-a46fb513 {
  background: #f8fafc;
  border: 2rpx dashed #cbd5e0;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}
.main-content .upload-section .camera-option .camera-icon.data-v-a46fb513 {
  font-size: 32rpx;
}
.main-content .upload-section .camera-option text.data-v-a46fb513 {
  color: #4a5568;
  font-size: 28rpx;
  font-weight: 500;
}
.main-content .preview-section .preview-card.data-v-a46fb513,
.main-content .preview-section .result-card.data-v-a46fb513,
.main-content .result-section .preview-card.data-v-a46fb513,
.main-content .result-section .result-card.data-v-a46fb513 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .preview-section .preview-card .section-title.data-v-a46fb513,
.main-content .preview-section .result-card .section-title.data-v-a46fb513,
.main-content .result-section .preview-card .section-title.data-v-a46fb513,
.main-content .result-section .result-card .section-title.data-v-a46fb513 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
  text-align: center;
}
.main-content .image-preview.data-v-a46fb513 {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  background: #f8fafc;
}
.main-content .image-preview .preview-image.data-v-a46fb513 {
  width: 100%;
  height: 400rpx;
}
.main-content .pixel-settings.data-v-a46fb513 {
  margin-bottom: 32rpx;
}
.main-content .pixel-settings .setting-title.data-v-a46fb513 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 24rpx;
}
.main-content .pixel-size-control.data-v-a46fb513 {
  margin-bottom: 24rpx;
}
.main-content .pixel-size-control .control-label.data-v-a46fb513 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.main-content .preset-options.data-v-a46fb513 {
  margin-bottom: 24rpx;
}
.main-content .preset-options .preset-label.data-v-a46fb513 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 16rpx;
}
.main-content .preset-options .preset-grid.data-v-a46fb513 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.main-content .preset-options .preset-grid .preset-item.data-v-a46fb513 {
  padding: 20rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  text-align: center;
  transition: all 0.3s ease;
}
.main-content .preset-options .preset-grid .preset-item.active.data-v-a46fb513 {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}
.main-content .preset-options .preset-grid .preset-item .preset-icon.data-v-a46fb513 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.main-content .preset-options .preset-grid .preset-item .preset-name.data-v-a46fb513 {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 4rpx;
}
.main-content .preset-options .preset-grid .preset-item .preset-desc.data-v-a46fb513 {
  display: block;
  font-size: 20rpx;
  color: #6b7280;
}
.main-content .advanced-options.data-v-a46fb513 {
  margin-bottom: 32rpx;
}
.main-content .advanced-options .options-title.data-v-a46fb513 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}
.main-content .advanced-options .option-item.data-v-a46fb513 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}
.main-content .advanced-options .option-item.data-v-a46fb513:last-child {
  border-bottom: none;
}
.main-content .advanced-options .option-item .option-label.data-v-a46fb513 {
  font-size: 24rpx;
  font-weight: 500;
  color: #374151;
}
.main-content .process-btn.data-v-a46fb513 {
  width: 100%;
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  padding: 28rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}
.main-content .process-btn.data-v-a46fb513:active:not([disabled]) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(16, 185, 129, 0.4);
}
.main-content .process-btn[disabled].data-v-a46fb513 {
  opacity: 0.7;
  background: #9ca3af;
}
.main-content .comparison-view.data-v-a46fb513 {
  margin-bottom: 32rpx;
}
.main-content .comparison-view .comparison-item.data-v-a46fb513 {
  margin-bottom: 24rpx;
  text-align: center;
}
.main-content .comparison-view .comparison-item .comparison-label.data-v-a46fb513 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}
.main-content .comparison-view .comparison-item .image-container.data-v-a46fb513 {
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
}
.main-content .comparison-view .comparison-item .image-container.processed.data-v-a46fb513 {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.05);
}
.main-content .comparison-view .comparison-item .image-container .comparison-image.data-v-a46fb513 {
  width: 100%;
  height: 300rpx;
}
.main-content .comparison-view .comparison-arrow.data-v-a46fb513 {
  text-align: center;
  margin: 20rpx 0;
}
.main-content .comparison-view .comparison-arrow .arrow-icon.data-v-a46fb513 {
  display: block;
  font-size: 32rpx;
  color: #f59e0b;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.main-content .comparison-view .comparison-arrow .arrow-text.data-v-a46fb513 {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
}
.main-content .process-info.data-v-a46fb513 {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.main-content .process-info .info-item.data-v-a46fb513 {
  flex: 1;
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.main-content .process-info .info-item .info-icon.data-v-a46fb513 {
  font-size: 28rpx;
}
.main-content .process-info .info-item .info-content .info-label.data-v-a46fb513 {
  display: block;
  font-size: 20rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}
.main-content .process-info .info-item .info-content .info-value.data-v-a46fb513 {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
}
.main-content .action-buttons.data-v-a46fb513 {
  display: flex;
  gap: 12rpx;
  margin-top: 32rpx;
}
.main-content .action-buttons button.data-v-a46fb513 {
  flex: 1;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}
.main-content .action-buttons button.data-v-a46fb513:active {
  transform: translateY(1rpx);
}
.main-content .action-buttons .save-btn.data-v-a46fb513 {
  background: #10b981;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}
.main-content .action-buttons .share-btn.data-v-a46fb513 {
  background: #3b82f6;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}
.main-content .action-buttons .reset-btn.data-v-a46fb513 {
  background: #f3f4f6;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.main-content .instructions-section.data-v-a46fb513 {
  margin-top: 40rpx;
}
.main-content .instructions-section .instructions-card.data-v-a46fb513 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .instructions-section .instructions-card .instructions-title.data-v-a46fb513 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  text-align: center;
}
.main-content .instructions-section .instructions-card .instruction-list.data-v-a46fb513 {
  margin-bottom: 32rpx;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item.data-v-a46fb513 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item .instruction-number.data-v-a46fb513 {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item .instruction-text.data-v-a46fb513 {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
  flex: 1;
}
.main-content .instructions-section .instructions-card .effect-explanation .explanation-title.data-v-a46fb513 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}
.main-content .instructions-section .instructions-card .effect-explanation .explanation-list .explanation-item.data-v-a46fb513 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.8;
  margin-bottom: 8rpx;
}
.hidden-canvas.data-v-a46fb513 {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  opacity: 0;
  z-index: -1;
}