/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c5ef31c6 {
  display: flex;
}
.flex-1.data-v-c5ef31c6 {
  flex: 1;
}
.items-center.data-v-c5ef31c6 {
  align-items: center;
}
.justify-center.data-v-c5ef31c6 {
  justify-content: center;
}
.justify-between.data-v-c5ef31c6 {
  justify-content: space-between;
}
.text-center.data-v-c5ef31c6 {
  text-align: center;
}
.rounded.data-v-c5ef31c6 {
  border-radius: 3px;
}
.rounded-lg.data-v-c5ef31c6 {
  border-radius: 6px;
}
.shadow.data-v-c5ef31c6 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c5ef31c6 {
  padding: 16rpx;
}
.m-4.data-v-c5ef31c6 {
  margin: 16rpx;
}
.mb-4.data-v-c5ef31c6 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c5ef31c6 {
  margin-top: 16rpx;
}
.gradient-generator.data-v-c5ef31c6 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-c5ef31c6 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-c5ef31c6 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header.data-v-c5ef31c6 {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-title.data-v-c5ef31c6 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-content .preview-section.data-v-c5ef31c6 {
  margin-bottom: 40rpx;
}
.card-content .preview-section .preview-card.data-v-c5ef31c6 {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
  border: 4rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
}
.card-content .preview-section .preview-card .preview-text.data-v-c5ef31c6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.card-content .control-panel.data-v-c5ef31c6 {
  margin-bottom: 40rpx;
}
.card-content .control-item.data-v-c5ef31c6 {
  margin-bottom: 40rpx;
}
.card-content .control-item .label.data-v-c5ef31c6 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.card-content .type-selector.data-v-c5ef31c6 {
  display: flex;
  gap: 16rpx;
}
.card-content .type-selector .type-option.data-v-c5ef31c6 {
  flex: 1;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
}
.card-content .type-selector .type-option.active.data-v-c5ef31c6 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.card-content .type-selector .type-option.active .option-text.data-v-c5ef31c6 {
  color: white;
}
.card-content .type-selector .type-option .option-text.data-v-c5ef31c6 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.card-content .direction-grid.data-v-c5ef31c6 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.card-content .direction-grid .direction-option.data-v-c5ef31c6 {
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
}
.card-content .direction-grid .direction-option.active.data-v-c5ef31c6 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.card-content .direction-grid .direction-option.active .direction-text.data-v-c5ef31c6 {
  color: white;
}
.card-content .direction-grid .direction-option .direction-text.data-v-c5ef31c6 {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}
.card-content .color-header.data-v-c5ef31c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.card-content .color-header .add-color-btn.data-v-c5ef31c6 {
  padding: 16rpx 24rpx;
  background: #3b82f6;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.card-content .color-header .add-color-btn.disabled.data-v-c5ef31c6 {
  background: #9ca3af;
  opacity: 0.6;
}
.card-content .color-header .add-color-btn .btn-text.data-v-c5ef31c6 {
  font-size: 24rpx;
  color: white;
  font-weight: 500;
}
.card-content .color-list .color-item.data-v-c5ef31c6 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
  width: 100%;
}
.card-content .color-list .color-item .color-preview.data-v-c5ef31c6 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  cursor: pointer;
  flex-shrink: 0;
  /* 防止颜色预览被压缩 */
}
.card-content .color-list .color-item .color-controls.data-v-c5ef31c6 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  min-width: 0;
}
.card-content .color-list .color-item .color-controls .color-value.data-v-c5ef31c6 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.card-content .color-list .color-item .color-controls .color-position.data-v-c5ef31c6 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  width: 100%;
}
.card-content .color-list .color-item .color-controls .color-position .position-label.data-v-c5ef31c6 {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  min-width: 60rpx;
}
.card-content .color-list .color-item .color-controls .color-position .position-slider.data-v-c5ef31c6 {
  flex: 1;
  margin: 0 16rpx;
}
.card-content .color-list .color-item .color-controls .color-position .position-value.data-v-c5ef31c6 {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  min-width: 80rpx;
  text-align: right;
}
.card-content .color-list .color-item .color-actions.data-v-c5ef31c6 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex-shrink: 0;
  /* 防止按钮区域被压缩 */
}
.card-content .color-list .color-item .color-actions .action-btn.data-v-c5ef31c6 {
  padding: 16rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: white;
}
.card-content .color-list .color-item .color-actions .action-btn .delete-icon.data-v-c5ef31c6 {
  font-size: 24rpx;
  color: #dc2626;
  font-weight: 500;
}
.card-content .code-section.data-v-c5ef31c6 {
  margin-bottom: 40rpx;
}
.card-content .code-section .code-header.data-v-c5ef31c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.card-content .code-section .code-header .label.data-v-c5ef31c6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.card-content .code-section .code-header .copy-btn.data-v-c5ef31c6 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: white;
}
.card-content .code-section .code-header .copy-btn .copy-icon.data-v-c5ef31c6 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.card-content .code-section .code-header .copy-btn .copy-text.data-v-c5ef31c6 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.card-content .code-section .code-container.data-v-c5ef31c6 {
  background: #1a1a1a;
  border-radius: 16rpx;
  padding: 30rpx;
}
.card-content .code-section .code-container .code-text.data-v-c5ef31c6 {
  font-size: 24rpx;
  color: #4ade80;
  font-family: "Courier New", Consolas, monospace;
  line-height: 1.6;
}
.card-content .info-section .info-title.data-v-c5ef31c6 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.card-content .info-section .info-list .info-item.data-v-c5ef31c6 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

/* 颜色选择器样式 */
.color-picker-modal.data-v-c5ef31c6 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn-c5ef31c6 0.3s ease-out;
}
.color-picker-container.data-v-c5ef31c6 {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  width: 90%;
  max-width: 600rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: slideUp-c5ef31c6 0.3s ease-out;
}
.picker-header.data-v-c5ef31c6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.picker-title.data-v-c5ef31c6 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}
.picker-close.data-v-c5ef31c6 {
  width: 60rpx;
  height: 60rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.picker-close.data-v-c5ef31c6:hover {
  background-color: #e5e7eb;
  transform: scale(1.1);
}
.close-icon.data-v-c5ef31c6 {
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 600;
}
.color-canvas-container.data-v-c5ef31c6 {
  position: relative;
  margin-bottom: 30rpx;
}
.color-canvas.data-v-c5ef31c6 {
  width: 100%;
  height: 320rpx;
  border-radius: 16rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.canvas-cursor.data-v-c5ef31c6 {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.hue-slider-container.data-v-c5ef31c6 {
  position: relative;
  margin-bottom: 30rpx;
}
.hue-slider.data-v-c5ef31c6 {
  width: 100%;
  height: 40rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.hue-cursor.data-v-c5ef31c6 {
  position: absolute;
  top: 50%;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.rgb-inputs.data-v-c5ef31c6 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.rgb-group.data-v-c5ef31c6 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.rgb-label.data-v-c5ef31c6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
  text-align: center;
}
.rgb-input.data-v-c5ef31c6 {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #374151;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.2s ease;
}
.rgb-input.data-v-c5ef31c6:focus {
  border-color: #8B5CF6;
  background-color: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(139, 92, 246, 0.1);
}
.picker-preview.data-v-c5ef31c6 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: #f8fafc;
  border-radius: 16rpx;
}
.preview-color.data-v-c5ef31c6 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.hex-value.data-v-c5ef31c6 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  font-family: "SF Mono", "Monaco", "Consolas", monospace;
}
.picker-actions.data-v-c5ef31c6 {
  display: flex;
  gap: 20rpx;
}
.picker-btn.data-v-c5ef31c6 {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}
.picker-btn.cancel.data-v-c5ef31c6 {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}
.picker-btn.cancel.data-v-c5ef31c6:hover {
  background: #e5e7eb;
  transform: translateY(-2rpx);
}
.picker-btn.confirm.data-v-c5ef31c6 {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
}
.picker-btn.confirm.data-v-c5ef31c6:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(139, 92, 246, 0.4);
}
.picker-btn.data-v-c5ef31c6:active {
  transform: translateY(1rpx);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.color-picker-container.data-v-c5ef31c6 {
    width: 95%;
    padding: 30rpx;
}
.color-canvas.data-v-c5ef31c6 {
    height: 250rpx;
}
.rgb-inputs.data-v-c5ef31c6 {
    gap: 12rpx;
}
.rgb-input.data-v-c5ef31c6 {
    height: 70rpx;
    font-size: 26rpx;
}
}
/* 动画效果 */
@keyframes fadeIn-c5ef31c6 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-c5ef31c6 {
from {
    opacity: 0;
    transform: translateY(40rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}