
.container.data-v-b143980c {
  min-height: 100vh;
  background-color: #ffffff;
}
.color-picker-card.data-v-b143980c,
.scheme-card.data-v-b143980c,
.palette-card.data-v-b143980c,
.liked-card.data-v-b143980c,
.parameters-card.data-v-b143980c {
  margin: 20rpx;
  background-color: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}
.card-header.data-v-b143980c {
  padding: 30rpx 30rpx 20rpx;
}
.card-title.data-v-b143980c {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.color-picker-content.data-v-b143980c {
  padding: 0 30rpx 30rpx;
  display: flex;
  align-items: center;
  gap: 30rpx;
}
.color-preview.data-v-b143980c {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  border: 4rpx solid #e5e5e5;
  transition: all 0.3s ease;
}
.color-preview.data-v-b143980c:active {
  transform: scale(0.95);
}
.color-info.data-v-b143980c {
  flex: 1;
}
.color-value.data-v-b143980c {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}
.color-hint.data-v-b143980c {
  font-size: 24rpx;
  color: #6b7280;
}
.color-selection-panel.data-v-b143980c {
  padding: 0 30rpx 30rpx;
  border-top: 2rpx solid #f3f4f6;
  margin-top: 20rpx;
  padding-top: 30rpx;
}
.section-title.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 20rpx;
}
.preset-colors-section.data-v-b143980c {
  margin-bottom: 32rpx;
}
.preset-colors-grid.data-v-b143980c {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 16rpx;
}
.preset-color-item.data-v-b143980c {
  width: 60rpx;
  height: 60rpx;
  border-radius: 12rpx;
  border: 3rpx solid #e5e7eb;
  transition: all 0.3s ease;
}
.preset-color-item.active.data-v-b143980c {
  border-color: #8B5CF6;
  transform: scale(1.1);
}
.preset-color-item.data-v-b143980c:active {
  transform: scale(0.95);
}
.color-input-section.data-v-b143980c {
  margin-bottom: 32rpx;
}
.color-input-group.data-v-b143980c {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.color-input.data-v-b143980c {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #374151;
}
.color-input.data-v-b143980c:focus {
  border-color: #8B5CF6;
}
.color-input-preview.data-v-b143980c {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
}
.panel-actions.data-v-b143980c {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-b143980c {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.action-btn.secondary.data-v-b143980c {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}
.action-btn.primary.data-v-b143980c {
  background: #8B5CF6;
  color: #ffffff;
  box-shadow: 0 4rpx 15rpx rgba(139, 92, 246, 0.3);
}
.action-btn.data-v-b143980c:active {
  transform: translateY(2rpx);
}
.parameters-content.data-v-b143980c {
  padding: 0 30rpx 30rpx;
}
.parameter-item.data-v-b143980c {
  margin-bottom: 32rpx;
}
.parameter-header.data-v-b143980c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.parameter-label.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}
.parameter-value.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 600;
  color: #8B5CF6;
}
.slider-container.data-v-b143980c {
  padding: 0 16rpx;
}
.scheme-grid.data-v-b143980c {
  padding: 0 30rpx 30rpx;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.scheme-item.data-v-b143980c {
  padding: 25rpx;
  border: 3rpx solid #e5e5e5;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.scheme-item.active.data-v-b143980c {
  border-color: #8B5CF6;
  background-color: rgba(139, 92, 246, 0.1);
}
.scheme-name.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}
.scheme-desc.data-v-b143980c {
  font-size: 22rpx;
  color: #6b7280;
}
.generate-btn.data-v-b143980c {
  margin: 20rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #8B5CF6, #7C3AED);
  border-radius: 25rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
  box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
}
.generate-btn.data-v-b143980c:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(139, 92, 246, 0.4);
}
.generate-icon.data-v-b143980c {
  font-size: 36rpx;
  color: #ffffff;
}
.generate-text.data-v-b143980c {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
.palette-header.data-v-b143980c {
  padding: 30rpx 30rpx 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.palette-actions.data-v-b143980c {
  display: flex;
  gap: 15rpx;
}
.action-btn.data-v-b143980c {
  width: 60rpx;
  height: 60rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 15rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon.data-v-b143980c {
  font-size: 24rpx;
}
.palette-colors.data-v-b143980c {
  padding: 0 30rpx 30rpx;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20rpx;
}
.color-item.data-v-b143980c {
  text-align: center;
}
.color-block.data-v-b143980c {
  width: 100%;
  height: 120rpx;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  transition: transform 0.3s ease;
  border: 2rpx solid #e5e7eb;
}
.color-block.data-v-b143980c:active {
  transform: scale(0.95);
}
.color-code.data-v-b143980c {
  font-size: 20rpx;
  font-family: 'Courier New', monospace;
  color: #6b7280;
}
.preview-section.data-v-b143980c {
  padding: 0 30rpx 30rpx;
  border-top: 2rpx solid #f0f0f0;
  margin-top: 30rpx;
  padding-top: 30rpx;
}
.preview-title.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  display: block;
  margin-bottom: 20rpx;
}
.preview-grid.data-v-b143980c {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.preview-item.data-v-b143980c {
  height: 120rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-text.data-v-b143980c {
  font-size: 26rpx;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.liked-list.data-v-b143980c {
  padding: 0 30rpx 30rpx;
}
.liked-item.data-v-b143980c {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #f3f4f6;
}
.liked-colors.data-v-b143980c {
  display: flex;
  gap: 15rpx;
}
.liked-color.data-v-b143980c {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.liked-copy.data-v-b143980c {
  width: 50rpx;
  height: 50rpx;
  background-color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}
.copy-icon.data-v-b143980c {
  font-size: 20rpx;
}
.color-picker-modal.data-v-b143980c {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn-b143980c 0.3s ease-out;
}
.color-picker-container.data-v-b143980c {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  width: 90%;
  max-width: 600rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: slideUp-b143980c 0.3s ease-out;
}
.picker-header.data-v-b143980c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.picker-title.data-v-b143980c {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}
.picker-close.data-v-b143980c {
  width: 60rpx;
  height: 60rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.picker-close.data-v-b143980c:hover {
  background-color: #e5e7eb;
  transform: scale(1.1);
}
.close-icon.data-v-b143980c {
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 600;
}
.color-canvas-container.data-v-b143980c {
  position: relative;
  margin-bottom: 30rpx;
}
.color-canvas.data-v-b143980c {
  width: 100%;
  height: 320rpx;
  border-radius: 16rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.canvas-cursor.data-v-b143980c {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.hue-slider-container.data-v-b143980c {
  position: relative;
  margin-bottom: 30rpx;
}
.hue-slider.data-v-b143980c {
  width: 100%;
  height: 40rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.hue-cursor.data-v-b143980c {
  position: absolute;
  top: 50%;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.rgb-inputs.data-v-b143980c {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.rgb-group.data-v-b143980c {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.rgb-label.data-v-b143980c {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
  text-align: center;
}
.rgb-input.data-v-b143980c {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #374151;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.2s ease;
}
.rgb-input.data-v-b143980c:focus {
  border-color: #8B5CF6;
  background-color: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(139, 92, 246, 0.1);
}
.picker-preview.data-v-b143980c {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: #f8fafc;
  border-radius: 16rpx;
}
.preview-color.data-v-b143980c {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.hex-value.data-v-b143980c {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}
.picker-actions.data-v-b143980c {
  display: flex;
  gap: 20rpx;
}
.picker-btn.data-v-b143980c {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}
.picker-btn.cancel.data-v-b143980c {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}
.picker-btn.cancel.data-v-b143980c:hover {
  background: #e5e7eb;
  transform: translateY(-2rpx);
}
.picker-btn.confirm.data-v-b143980c {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
}
.picker-btn.confirm.data-v-b143980c:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(139, 92, 246, 0.4);
}
.picker-btn.data-v-b143980c:active {
  transform: translateY(1rpx);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.color-picker-container.data-v-b143980c {
    width: 95%;
    padding: 30rpx;
}
.color-canvas.data-v-b143980c {
    height: 250rpx;
}
.rgb-inputs.data-v-b143980c {
    gap: 12rpx;
}
.rgb-input.data-v-b143980c {
    height: 70rpx;
    font-size: 26rpx;
}
}

/* 动画效果 */
@keyframes fadeIn-b143980c {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-b143980c {
from {
    opacity: 0;
    transform: translateY(40rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
