<view class="beast-language data-v-6c2f8856"><view class="header-card data-v-6c2f8856"><view class="header-content data-v-6c2f8856"><text class="header-icon data-v-6c2f8856">🐾</text><view class="header-info data-v-6c2f8856"><text class="header-title data-v-6c2f8856">兽语转换器</text><text class="header-desc data-v-6c2f8856">将文字转换为可爱的兽语，支持多种动物语言</text></view></view></view><view class="mode-card data-v-6c2f8856"><view class="card-header data-v-6c2f8856"><text class="section-title data-v-6c2f8856">🐾 兽语模式</text></view><view class="mode-grid data-v-6c2f8856"><view wx:for="{{a}}" wx:for-item="mode" wx:key="c" class="{{['mode-item', 'data-v-6c2f8856', mode.d && 'active']}}" bindtap="{{mode.e}}"><text class="mode-name data-v-6c2f8856">{{mode.a}}</text><text class="mode-chars data-v-6c2f8856">{{mode.b}}</text></view></view></view><view class="convert-mode data-v-6c2f8856"><button class="{{['mode-btn', 'data-v-6c2f8856', b && 'active']}}" bindtap="{{c}}"> 加密模式 </button><button class="{{['mode-btn', 'data-v-6c2f8856', d && 'active']}}" bindtap="{{e}}"> 解密模式 </button></view><view class="input-card data-v-6c2f8856"><view class="card-header data-v-6c2f8856"><text class="section-title data-v-6c2f8856">{{f}}</text></view><view class="input-area data-v-6c2f8856"><block wx:if="{{r0}}"><textarea placeholder="{{g}}" class="input-textarea data-v-6c2f8856" value="{{h}}" bindinput="{{i}}"></textarea></block><view class="input-footer data-v-6c2f8856"><text class="char-count data-v-6c2f8856">字符数: {{j}}</text></view></view></view><view class="volume-control data-v-6c2f8856"><text class="volume-icon data-v-6c2f8856">{{k}}</text><slider value="{{l}}" bindchange="{{m}}" min="0" max="100" show-value class="volume-slider data-v-6c2f8856"/></view><view class="action-buttons data-v-6c2f8856"><button class="{{['convert-btn', 'data-v-6c2f8856', r && 'is-playing']}}" bindtap="{{s}}" disabled="{{t}}"><view class="btn-content data-v-6c2f8856"><text class="{{['btn-icon', 'data-v-6c2f8856', o && 'rotate']}}">{{n}}</text><text class="data-v-6c2f8856">{{p}}</text></view><view wx:if="{{q}}" class="playing-animation data-v-6c2f8856"><view class="bar data-v-6c2f8856"></view><view class="bar data-v-6c2f8856"></view><view class="bar data-v-6c2f8856"></view></view></button><button class="clear-btn data-v-6c2f8856" bindtap="{{v}}"><text class="btn-icon data-v-6c2f8856">🗑️</text></button></view><view wx:if="{{w}}" class="output-card data-v-6c2f8856"><view class="card-header data-v-6c2f8856"><text class="section-title data-v-6c2f8856">转换结果</text><view class="header-actions data-v-6c2f8856"><button class="action-btn data-v-6c2f8856" bindtap="{{x}}"><text class="btn-icon data-v-6c2f8856">📋</text><text class="data-v-6c2f8856">复制</text></button><button class="action-btn primary data-v-6c2f8856" bindtap="{{y}}"><text class="btn-icon data-v-6c2f8856">💾</text><text class="data-v-6c2f8856">保存</text></button></view></view><view class="output-content data-v-6c2f8856">{{z}}</view></view><view wx:if="{{A}}" class="history-card data-v-6c2f8856"><view class="card-header data-v-6c2f8856"><text class="section-title data-v-6c2f8856">转换历史</text><button class="clear-history-btn data-v-6c2f8856" bindtap="{{B}}">清空记录</button></view><view class="history-content data-v-6c2f8856"><view wx:for="{{C}}" wx:for-item="record" wx:key="i" class="history-item data-v-6c2f8856"><view class="history-item-header data-v-6c2f8856"><text class="history-item-mode data-v-6c2f8856">{{record.a}} - {{record.b}}</text><text class="history-item-time data-v-6c2f8856">{{record.c}}</text></view><view class="history-item-content data-v-6c2f8856"><text class="history-item-input data-v-6c2f8856">{{record.d}}{{record.e}}</text><text class="history-item-output data-v-6c2f8856">{{record.f}}{{record.g}}</text></view><button class="load-history-btn data-v-6c2f8856" bindtap="{{record.h}}">重新加载</button></view></view></view><view class="usage-card data-v-6c2f8856"><view class="card-header data-v-6c2f8856"><text class="section-title data-v-6c2f8856">使用说明</text></view><view class="usage-content data-v-6c2f8856"><view class="data-v-6c2f8856">• <view class="data-v-6c2f8856">选择兽语模式:</view> 支持汪汪语、喵喵语、哞哞语、哼哼语</view><view class="data-v-6c2f8856">• <view class="data-v-6c2f8856">加密模式:</view> 将普通文字转换为可爱的兽语表达</view><view class="data-v-6c2f8856">• <view class="data-v-6c2f8856">解密模式:</view> 将兽语还原为原始文字内容</view><view class="data-v-6c2f8856">• <view class="data-v-6c2f8856">历史记录:</view> 自动保存转换记录，支持重新加载</view><view class="data-v-6c2f8856">• <view class="data-v-6c2f8856">安全性:</view> 使用字符编码映射，支持中英文混合</view></view></view><view wx:if="{{D}}" class="loading-status data-v-6c2f8856"><text class="data-v-6c2f8856">正在加载音频资源...</text><text class="status-text data-v-6c2f8856">{{E}}/{{F}}</text></view></view>