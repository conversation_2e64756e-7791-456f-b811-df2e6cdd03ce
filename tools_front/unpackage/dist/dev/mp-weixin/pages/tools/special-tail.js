"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      selectedType: "cute",
      resultText: "",
      history: [],
      tailTypes: [
        { key: "cute", name: "可爱", icon: "🥰" },
        { key: "cool", name: "酷炫", icon: "😎" },
        { key: "special", name: "特殊", icon: "✨" },
        { key: "flower", name: "花朵", icon: "🌸" },
        { key: "star", name: "星星", icon: "⭐" },
        { key: "heart", name: "爱心", icon: "💕" }
      ],
      tailTemplates: {
        cute: [
          "(｡◕‿◕｡)",
          "(´∀｀)",
          "(◕‿◕)",
          "(｡♥‿♥｡)",
          "(◡‿◡)",
          "(´｡• ω •｡`)",
          "(◕ᴗ◕)",
          "(｡◕‿◕｡)♡",
          "(´∀｀)♡",
          "(◕‿◕)✿",
          "(´｡• ᵕ •｡`)",
          "(◕ω◕)",
          "(｡◕‿◕｡)💕",
          "(◡ ω ◡)",
          "(´∀｀)ﾉ"
        ],
        cool: [
          "(╯°□°）╯",
          "(⌐■_■)",
          "(╯°□°）╯︵ ┻━┻",
          "(∩_∩)",
          "(¬‿¬)",
          "(╯°□°）╯",
          "(°o°)",
          "(︶︿︶)",
          "(¬_¬)",
          "(╯°□°）╯︵ ┻━┻",
          "(∩_∩)",
          "(╯°□°）╯",
          "(¬‿¬)",
          "(°o°)",
          "(∩_∩)"
        ],
        special: [
          "◕‿◕",
          "★彡",
          "☆彡",
          "✧･ﾟ",
          "✦♡",
          "☆*:. o(≧▽≦)o .:*☆",
          "✨✨",
          "⭐⭐",
          "✧*｡",
          "☆ﾟ.*･｡ﾟ",
          "✦✧",
          "☆*:. o(≧▽≦)o .:*☆",
          "✨*⭐",
          "✧･ﾟ: *✧･ﾟ:*",
          "☆*:. o(≧▽≦)o .:*☆"
        ],
        flower: [
          "🌸",
          "🌺",
          "🌻",
          "🌷",
          "🌹",
          "🌸✨",
          "🌺💕",
          "🌻🌞",
          "🌷💖",
          "🌹❤️",
          "🌸🌸",
          "🌺🌺",
          "🌻🌻",
          "🌷🌷",
          "🌹🌹"
        ],
        star: [
          "⭐",
          "✨",
          "🌟",
          "💫",
          "⭐✨",
          "🌟💫",
          "✨⭐",
          "💫🌟",
          "⭐⭐",
          "✨✨",
          "🌟🌟",
          "💫💫",
          "⭐🌟",
          "✨💫",
          "🌟⭐"
        ],
        heart: [
          "💕",
          "💖",
          "💗",
          "💘",
          "💝",
          "💕💖",
          "💗💘",
          "💝💕",
          "💖💗",
          "💘💝",
          "💕💕",
          "💖💖",
          "💗💗",
          "💘💘",
          "💝💝"
        ]
      }
    };
  },
  computed: {
    currentTails() {
      return this.tailTemplates[this.selectedType] || [];
    }
  },
  onLoad() {
    this.loadHistory();
  },
  methods: {
    clearInput() {
      this.inputText = "";
      this.resultText = "";
    },
    applyTail(tail) {
      if (!this.inputText.trim()) {
        common_vendor.index.showToast({
          title: "请先输入文字",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      this.resultText = this.inputText + tail;
      this.addToHistory(this.resultText);
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort();
      }
    },
    copyResult() {
      if (!this.resultText)
        return;
      common_vendor.index.setClipboardData({
        data: this.resultText,
        success: () => {
          common_vendor.index.showToast({
            title: "结果已复制",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "文字已复制",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    shareResult() {
      if (!this.resultText) {
        common_vendor.index.showToast({
          title: "请先生成文字",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: this.resultText,
        success: () => {
          common_vendor.index.showToast({
            title: "文字已复制，可直接分享",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    addToHistory(text) {
      if (!this.history.includes(text)) {
        this.history.unshift(text);
        if (this.history.length > 10) {
          this.history.pop();
        }
        this.saveHistory();
      }
    },
    selectHistory(item) {
      this.resultText = item;
    },
    removeHistory(index) {
      this.history.splice(index, 1);
      this.saveHistory();
    },
    clearHistory() {
      this.history = [];
      this.saveHistory();
      common_vendor.index.showToast({
        title: "历史记录已清空",
        icon: "success",
        duration: 1500
      });
    },
    saveHistory() {
      common_vendor.index.setStorageSync("special-tail-history", this.history);
    },
    loadHistory() {
      const saved = common_vendor.index.getStorageSync("special-tail-history");
      if (saved) {
        this.history = saved;
      }
    }
  },
  // 配置页面的分享行为
  onShareAppMessage(res) {
    const sharedText = this.resultText || "为你的文字添加个性化的装饰符号";
    return {
      title: "特殊尾巴 - " + sharedText,
      path: "/pages/tools/special-tail",
      imageUrl: "/static/share-cover.png"
      // 可选：设置分享图片
    };
  },
  // 开启分享到朋友圈
  onShareTimeline() {
    return {
      title: "特殊尾巴 - 为你的文字添加个性化的装饰符号",
      path: "/pages/tools/special-tail",
      imageUrl: "/static/share-cover.png"
      // 可选：设置分享图片
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputText,
    b: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    c: common_vendor.t($data.inputText.length),
    d: $data.inputText.length > 0
  }, $data.inputText.length > 0 ? {
    e: common_vendor.o((...args) => $options.clearInput && $options.clearInput(...args))
  } : {}, {
    f: common_vendor.f($data.tailTypes, (type, k0, i0) => {
      return {
        a: common_vendor.t(type.icon),
        b: common_vendor.t(type.name),
        c: type.key,
        d: common_vendor.o(($event) => $data.selectedType = type.key, type.key),
        e: common_vendor.n({
          active: $data.selectedType === type.key
        })
      };
    }),
    g: common_vendor.f($options.currentTails, (tail, index, i0) => {
      return {
        a: common_vendor.t(tail),
        b: common_vendor.o(($event) => $options.applyTail(tail), index),
        c: index,
        d: common_vendor.o(($event) => $options.applyTail(tail), index)
      };
    }),
    h: common_vendor.t($data.inputText || "示例文字"),
    i: $data.resultText
  }, $data.resultText ? {
    j: common_vendor.t($data.resultText),
    k: common_vendor.o((...args) => $options.copyResult && $options.copyResult(...args)),
    l: common_vendor.o((...args) => $options.shareResult && $options.shareResult(...args))
  } : {}, {
    m: $data.history.length > 0
  }, $data.history.length > 0 ? {
    n: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    o: common_vendor.f($data.history, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: common_vendor.o(($event) => $options.copyText(item), index),
        c: common_vendor.o(($event) => $options.removeHistory(index), index),
        d: index,
        e: common_vendor.o(($event) => $options.selectHistory(item), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-557a13b8"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/special-tail.js.map
