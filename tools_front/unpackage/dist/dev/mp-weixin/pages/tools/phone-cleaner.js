"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "PhoneCleaner",
  data() {
    return {
      isPlaying: false,
      frequency: 2e4,
      duration: 30,
      remainingTime: 0,
      progressPercent: 0,
      audioContext: null,
      oscillator: null,
      gainNode: null,
      timer: null,
      innerAudioContext: null,
      presets: [
        { name: "标准清灰", frequency: 2e4, duration: 30, icon: "🔊", audioFile: "clean_20000.mp3" },
        { name: "深度清理", frequency: 15e3, duration: 60, icon: "🎵", audioFile: "clean_15000.mp3" },
        { name: "快速清理", frequency: 18e3, duration: 20, icon: "⚡", audioFile: "clean_18000.mp3" },
        { name: "超声清理", frequency: 22e3, duration: 90, icon: "🌊", audioFile: "clean_22000.mp3" }
      ]
    };
  },
  methods: {
    onFrequencyChange(e) {
      this.frequency = e.detail.value;
      if (this.isPlaying) {
        this.stopCleaning();
        this.startCleaning();
      }
    },
    onDurationChange(e) {
      this.duration = e.detail.value;
      if (this.isPlaying) {
        this.resetTimer();
      }
    },
    resetTimer() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      this.remainingTime = this.duration;
      this.startTimer();
    },
    startTimer() {
      this.timer = setInterval(() => {
        this.remainingTime--;
        this.progressPercent = (this.duration - this.remainingTime) / this.duration * 100;
        if (this.remainingTime <= 0) {
          this.stopCleaning();
          common_vendor.index.showToast({
            title: "清灰完成",
            icon: "success"
          });
        }
      }, 1e3);
    },
    usePreset(preset) {
      this.frequency = preset.frequency;
      this.duration = preset.duration;
      common_vendor.index.showToast({
        title: `已应用${preset.name}`,
        icon: "success"
      });
    },
    toggleCleaning() {
      if (this.isPlaying) {
        this.stopCleaning();
      } else {
        this.startCleaning();
      }
    },
    startCleaning() {
      if (!this.innerAudioContext) {
        this.innerAudioContext = common_vendor.index.createInnerAudioContext();
      }
      const currentPreset = this.presets.find((preset) => preset.frequency === this.frequency);
      const audioFile = currentPreset ? currentPreset.audioFile : "clean_20000.mp3";
      const audioPath = `/static/audio/${audioFile}`;
      this.innerAudioContext.src = audioPath;
      this.innerAudioContext.loop = true;
      this.innerAudioContext.onError((res) => {
        common_vendor.index.__f__("error", "at pages/tools/phone-cleaner.vue:249", "音频播放错误:", res);
        common_vendor.index.showToast({
          title: "音频加载失败",
          icon: "none"
        });
      });
      this.innerAudioContext.play();
      this.isPlaying = true;
      this.remainingTime = this.duration;
      this.progressPercent = 0;
      this.startTimer();
    },
    stopCleaning() {
      if (this.innerAudioContext) {
        this.innerAudioContext.stop();
        this.innerAudioContext.destroy();
        this.innerAudioContext = null;
      }
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.isPlaying = false;
      this.remainingTime = 0;
      this.progressPercent = 0;
    },
    beforeDestroy() {
      this.stopCleaning();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.isPlaying ? "🔊" : "🔇"),
    b: common_vendor.t($data.isPlaying ? "准备就绪" : "点击开始清灰"),
    c: $data.isPlaying ? 1 : "",
    d: common_vendor.t($data.frequency),
    e: $data.frequency,
    f: common_vendor.o((...args) => $options.onFrequencyChange && $options.onFrequencyChange(...args)),
    g: common_vendor.t($data.duration),
    h: $data.duration,
    i: common_vendor.o((...args) => $options.onDurationChange && $options.onDurationChange(...args)),
    j: common_vendor.t($data.isPlaying ? "⏸️" : "🔊"),
    k: common_vendor.t($data.isPlaying ? "停止清灰" : "开始清灰"),
    l: $data.isPlaying ? 1 : "",
    m: common_vendor.o((...args) => $options.toggleCleaning && $options.toggleCleaning(...args)),
    n: $data.isPlaying
  }, $data.isPlaying ? {
    o: common_vendor.t($data.remainingTime),
    p: $data.progressPercent + "%"
  } : {}, {
    q: common_vendor.f($data.presets, (preset, index, i0) => {
      return {
        a: common_vendor.t(preset.icon),
        b: common_vendor.t(preset.name),
        c: common_vendor.t(preset.frequency),
        d: common_vendor.t(preset.duration),
        e: index,
        f: common_vendor.o(($event) => $options.usePreset(preset), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-24b589b8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/phone-cleaner.js.map
