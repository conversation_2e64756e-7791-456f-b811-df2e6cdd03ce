"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "PxEmConverter",
  data() {
    return {
      inputValue: "16",
      fromUnitIndex: 0,
      toUnitIndex: 1,
      baseFontSize: 16,
      results: null,
      units: [
        { id: "px", name: "Pixels", desc: "绝对单位，固定像素大小，不受其他元素影响" },
        { id: "em", name: "Em", desc: "相对单位，相对于父元素的字体大小" },
        { id: "rem", name: "Rem", desc: "相对单位，相对于根元素(html)的字体大小" },
        { id: "%", name: "Percent", desc: "百分比单位，相对于父元素的大小" },
        { id: "vw", name: "Viewport Width", desc: "视口宽度的百分比，1vw = 视口宽度的1%" },
        { id: "vh", name: "Viewport Height", desc: "视口高度的百分比，1vh = 视口高度的1%" }
      ],
      commonSizes: [
        { name: "特大标题", px: 48, usage: "h1, 主标题" },
        { name: "大标题", px: 32, usage: "h2, 副标题" },
        { name: "中标题", px: 24, usage: "h3, 章节标题" },
        { name: "小标题", px: 20, usage: "h4, 小节标题" },
        { name: "正文大", px: 18, usage: "重要内容" },
        { name: "正文", px: 16, usage: "正常文本" },
        { name: "正文小", px: 14, usage: "辅助信息" },
        { name: "说明", px: 12, usage: "说明文字" },
        { name: "标注", px: 10, usage: "标注信息" }
      ]
    };
  },
  computed: {
    unitNames() {
      return this.units.map((unit) => unit.name);
    },
    resultsList() {
      if (!this.results)
        return [];
      return [
        { label: "PX", value: this.results.px },
        { label: "EM", value: this.results.em },
        { label: "REM", value: this.results.rem },
        { label: "百分比", value: this.results.percent },
        { label: "VW", value: this.results.vw },
        { label: "VH", value: this.results.vh }
      ].filter((item) => item.value);
    }
  },
  methods: {
    onFromUnitChange(e) {
      this.fromUnitIndex = e.detail.value;
      this.handleConvert();
    },
    onToUnitChange(e) {
      this.toUnitIndex = e.detail.value;
      this.handleConvert();
    },
    handleConvert() {
      const input = parseFloat(this.inputValue);
      const base = parseFloat(this.baseFontSize);
      if (isNaN(input) || isNaN(base) || base <= 0) {
        this.results = null;
        return;
      }
      const fromUnit = this.units[this.fromUnitIndex].id;
      const toUnit = this.units[this.toUnitIndex].id;
      let pxValue = input;
      switch (fromUnit) {
        case "em":
        case "rem":
          pxValue = input * base;
          break;
        case "%":
          pxValue = input / 100 * base;
          break;
        case "vw":
          pxValue = input / 100 * 375;
          break;
        case "vh":
          pxValue = input / 100 * 812;
          break;
      }
      let result = pxValue;
      switch (toUnit) {
        case "em":
        case "rem":
          result = pxValue / base;
          break;
        case "%":
          result = pxValue / base * 100;
          break;
        case "vw":
          result = pxValue / 375 * 100;
          break;
        case "vh":
          result = pxValue / 812 * 100;
          break;
      }
      this.results = {
        input: `${input}${fromUnit}`,
        output: `${result.toFixed(4)}${toUnit}`,
        px: `${pxValue.toFixed(2)}px`,
        em: `${(pxValue / base).toFixed(4)}em`,
        rem: `${(pxValue / base).toFixed(4)}rem`,
        percent: `${(pxValue / base * 100).toFixed(2)}%`,
        vw: `${(pxValue / 375 * 100).toFixed(4)}vw`,
        vh: `${(pxValue / 812 * 100).toFixed(4)}vh`
      };
    },
    swapUnits() {
      const temp = this.fromUnitIndex;
      this.fromUnitIndex = this.toUnitIndex;
      this.toUnitIndex = temp;
      this.handleConvert();
    },
    copyResult(value) {
      if (!value)
        return;
      common_vendor.index.setClipboardData({
        data: value.toString(),
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    copyAllResults() {
      if (!this.results)
        return;
      const allResults = this.resultsList.map((item) => `${item.label}: ${item.value}`).join("\n");
      common_vendor.index.setClipboardData({
        data: allResults,
        success: () => {
          common_vendor.index.showToast({
            title: "全部结果已复制",
            icon: "success"
          });
        }
      });
    },
    useCommonSize(px) {
      this.inputValue = px.toString();
      this.fromUnitIndex = 0;
      this.handleConvert();
    }
  },
  mounted() {
    this.handleConvert();
  },
  watch: {
    baseFontSize() {
      this.handleConvert();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: $data.baseFontSize,
    b: common_vendor.o(($event) => $data.baseFontSize = $event.detail.value),
    c: $data.inputValue,
    d: common_vendor.o(($event) => $data.inputValue = $event.detail.value),
    e: common_vendor.t($data.units[$data.fromUnitIndex].id),
    f: $data.fromUnitIndex,
    g: $options.unitNames,
    h: common_vendor.o((...args) => $options.onFromUnitChange && $options.onFromUnitChange(...args)),
    i: common_vendor.o((...args) => $options.swapUnits && $options.swapUnits(...args)),
    j: ((_a = $data.results) == null ? void 0 : _a.output) || "转换结果",
    k: common_vendor.t($data.units[$data.toUnitIndex].id),
    l: $data.toUnitIndex,
    m: $options.unitNames,
    n: common_vendor.o((...args) => $options.onToUnitChange && $options.onToUnitChange(...args)),
    o: common_vendor.o((...args) => $options.handleConvert && $options.handleConvert(...args)),
    p: $data.results
  }, $data.results ? {
    q: common_vendor.o((...args) => $options.copyAllResults && $options.copyAllResults(...args)),
    r: common_vendor.f($options.resultsList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: common_vendor.t(item.value),
        c: index,
        d: common_vendor.o(($event) => $options.copyResult(item.value), index)
      };
    })
  } : {}, {
    s: common_vendor.f($data.commonSizes, (size, index, i0) => {
      return {
        a: common_vendor.t(size.name),
        b: common_vendor.t(size.usage),
        c: common_vendor.t(size.px),
        d: common_vendor.t((size.px / $data.baseFontSize).toFixed(2)),
        e: index,
        f: common_vendor.o(($event) => $options.useCommonSize(size.px), index)
      };
    }),
    t: common_vendor.f($data.units, (unit, index, i0) => {
      return {
        a: common_vendor.t(unit.id.toUpperCase()),
        b: common_vendor.t(unit.name),
        c: common_vendor.t(unit.desc),
        d: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c6f9b57b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/px-em-converter.js.map
