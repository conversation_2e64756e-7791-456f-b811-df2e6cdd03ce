"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentText: "",
      isGenerating: false,
      kfcTexts: [
        "疯狂星期四，V我50！",
        "今天是疯狂星期四，你们知道这意味着什么吗？这意味着我要开始我的行为艺术了！",
        "疯狂星期四需要一个理由吗？需要吗？不需要吗？需要吗？不需要！",
        "KFC疯狂星期四，今天不吃炸鸡就是对不起这个节日！",
        "疯狂星期四文学：今天我想吃KFC了，但是我没有钱，所以我只能看着别人吃。",
        "疯狂星期四，我已经疯狂了，快给我一桶炸鸡冷静一下！",
        "星期四到了，是时候展现真正的疯狂了！KFC走起！",
        "疯狂星期四，不疯狂，不星期四！今天必须安排上KFC！",
        "听说今天是疯狂星期四？那我就疯狂地想吃炸鸡！",
        "疯狂星期四，让我们一起为了炸鸡而疯狂吧！",
        "今天疯狂星期四，我疯狂，我快乐，我想吃KFC！",
        "疯狂星期四到了，是时候来一份全家桶了！",
        "疯狂星期四文案：今天不吃KFC，明天就不是星期五了！",
        "疯狂星期四，唯一的治疗方法就是KFC原味鸡！",
        "疯狂星期四，如果你不疯狂，那就是在浪费这个星期四！"
      ]
    };
  },
  mounted() {
    this.generateText();
  },
  methods: {
    generateText() {
      this.isGenerating = true;
      setTimeout(() => {
        const randomText = this.kfcTexts[Math.floor(Math.random() * this.kfcTexts.length)];
        this.currentText = randomText;
        this.isGenerating = false;
      }, 500);
    },
    selectText(text) {
      this.currentText = text;
    },
    copyText() {
      if (!this.currentText)
        return;
      common_vendor.index.setClipboardData({
        data: this.currentText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.currentText || "点击生成按钮获取疯狂文案..."),
    b: common_vendor.t($data.isGenerating ? "⏳" : "🔄"),
    c: common_vendor.t($data.isGenerating ? "生成中..." : "重新生成"),
    d: $data.isGenerating ? 1 : "",
    e: common_vendor.o((...args) => $options.generateText && $options.generateText(...args)),
    f: !$data.currentText ? 1 : "",
    g: common_vendor.o((...args) => $options.copyText && $options.copyText(...args)),
    h: common_vendor.f($data.kfcTexts.slice(0, 5), (text, index, i0) => {
      return {
        a: common_vendor.t(text),
        b: index,
        c: common_vendor.o(($event) => $options.selectText(text), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-94675fb8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/crazy-thursday-text.js.map
