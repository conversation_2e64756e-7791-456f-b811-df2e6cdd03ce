"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      message: "",
      language: "javascript",
      recipientName: "",
      generatedCode: "",
      charCount: 0,
      isGenerating: false,
      languages: [
        { id: "javascript", name: "JavaScript", icon: "🟨" },
        { id: "python", name: "Python", icon: "🐍" },
        { id: "java", name: "Java", icon: "☕" },
        { id: "cpp", name: "C++", icon: "⚡" },
        { id: "html", name: "HTML", icon: "🌐" },
        { id: "css", name: "CSS", icon: "🎨" }
      ]
    };
  },
  computed: {
    canGenerate() {
      return this.message.trim().length > 0;
    }
  },
  methods: {
    onTextInput(e) {
      this.message = e.detail.value;
      this.charCount = this.message.length;
    },
    setLanguage(langId) {
      this.language = langId;
    },
    async generateCode() {
      if (!this.canGenerate || this.isGenerating)
        return;
      this.isGenerating = true;
      try {
        await new Promise((resolve) => setTimeout(resolve, 1500));
        const name = this.recipientName || "你";
        let code = "";
        switch (this.language) {
          case "javascript":
            code = `// 💝 写给${name}的表白代码
function confess() {
    const myHeart = "${this.message}";
    const recipient = "${name}";
    
    if (myHeart.includes("爱")) {
        uni.__f__('log','at pages/tools/confession-code-maker.vue:151',\`❤️ \${recipient}, \${myHeart}\`);
        return true; // 希望你能接受
    }
    
    // 无论如何，我都想让你知道
    alert("💕 " + myHeart);
}

// 执行表白
confess();
// 愿我们的爱情像代码一样，永远没有 bug 💖`;
            break;
          case "python":
            code = `# 💝 写给${name}的表白代码
def confess():
    my_heart = "${this.message}"
    recipient = "${name}"
    
    if "爱" in my_heart:
        print(f"❤️ {recipient}, {my_heart}")
        return True  # 希望你能接受
    
    # 无论如何，我都想让你知道
    print("💕 " + my_heart)

# 执行表白
if __name__ == "__main__":
    confess()
    print("愿我们的爱情像 Python 一样简洁优雅 💖")`;
            break;
          case "java":
            code = `// 💝 写给${name}的表白代码
public class Confession {
    public static void main(String[] args) {
        String myHeart = "${this.message}";
        String recipient = "${name}";
        
        System.out.println("❤️ " + recipient + ", " + myHeart);
        
        if (myHeart.contains("爱")) {
            System.out.println("💕 希望你能接受我的心意");
        }
        
        System.out.println("愿我们的爱情像 Java 一样稳定持久 💖");
    }
}`;
            break;
          case "cpp":
            code = `// 💝 写给${name}的表白代码
#include <iostream>
#include <string>
using namespace std;

int main() {
    string myHeart = "${this.message}";
    string recipient = "${name}";
    
    cout << "❤️ " << recipient << ", " << myHeart << endl;
    
    if (myHeart.find("爱") != string::npos) {
        cout << "💕 希望你能接受我的心意" << endl;
    }
    
    cout << "愿我们的爱情像 C++ 一样高效强大 💖" << endl;
    
    return 0;
}`;
            break;
          case "html":
            code = `<!-- 💝 写给${name}的表白页面 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>给${name}的表白</title>
    <style>
        body {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
        }
        .heart {
            color: #fff;
            font-size: 2em;
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="heart">
        <h1>💖 ${name} 💖</h1>
        <p>${this.message}</p>
        <p>愿我们的爱情像网页一样美丽动人 💕</p>
    </div>
</body>
</html>`;
            break;
          case "css":
            code = `/* 💝 写给${name}的表白样式 */
.confession {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    padding: 50px;
    text-align: center;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.confession::before {
    content: "💖 ${name} 💖";
    display: block;
    font-size: 2em;
    color: white;
    margin-bottom: 20px;
    animation: heartbeat 1s infinite;
}

.confession::after {
    content: "${this.message}";
    display: block;
    font-size: 1.2em;
    color: white;
    font-weight: bold;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.1); }
    50% { transform: scale(1); }
    75% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 愿我们的爱情像 CSS 一样美丽动人 💕 */`;
            break;
        }
        this.generatedCode = code;
        common_vendor.index.showToast({
          title: "代码生成成功！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "生成失败，请重试",
          icon: "error"
        });
      } finally {
        this.isGenerating = false;
      }
    },
    copyToClipboard() {
      common_vendor.index.setClipboardData({
        data: this.generatedCode,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    downloadCode() {
      const extensions = {
        javascript: "js",
        python: "py",
        java: "java",
        cpp: "cpp",
        html: "html",
        css: "css"
      };
      common_vendor.index.showModal({
        title: "下载提示",
        content: `请复制代码内容并保存为 confession.${extensions[this.language]} 文件`,
        showCancel: false
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.recipientName,
    b: common_vendor.o(($event) => $data.recipientName = $event.detail.value),
    c: common_vendor.o([($event) => $data.message = $event.detail.value, (...args) => $options.onTextInput && $options.onTextInput(...args)]),
    d: $data.message,
    e: common_vendor.t($data.charCount),
    f: common_vendor.f($data.languages, (lang, k0, i0) => {
      return {
        a: common_vendor.t(lang.icon),
        b: common_vendor.t(lang.name),
        c: lang.id,
        d: $data.language === lang.id ? 1 : "",
        e: common_vendor.o(($event) => $options.setLanguage(lang.id), lang.id)
      };
    }),
    g: $data.generatedCode
  }, $data.generatedCode ? {
    h: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    i: common_vendor.o((...args) => $options.downloadCode && $options.downloadCode(...args)),
    j: common_vendor.t($data.generatedCode)
  } : {}, {
    k: common_vendor.t($data.isGenerating ? "⏳" : "❤️"),
    l: common_vendor.t($data.isGenerating ? "生成中..." : "生成表白代码"),
    m: !$options.canGenerate ? 1 : "",
    n: $data.isGenerating ? 1 : "",
    o: common_vendor.o((...args) => $options.generateCode && $options.generateCode(...args)),
    p: !$options.canGenerate || $data.isGenerating
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-90a76c95"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/confession-code-maker.js.map
