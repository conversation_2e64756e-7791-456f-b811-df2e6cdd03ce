<view class="bordered-text data-v-4138af9f"><view class="content data-v-4138af9f"><view class="card data-v-4138af9f"><view class="card-header data-v-4138af9f"><text class="header-title data-v-4138af9f">📝 输入文字</text></view><view class="card-content data-v-4138af9f"><input class="text-input data-v-4138af9f" placeholder="输入要加框的文字..." bindinput="{{a}}" value="{{b}}"/></view></view><view wx:if="{{c}}" class="card data-v-4138af9f"><view class="card-header data-v-4138af9f"><text class="header-title data-v-4138af9f">边框效果</text></view><view class="card-content data-v-4138af9f"><view class="results-container data-v-4138af9f"><view wx:for="{{d}}" wx:for-item="result" wx:key="c" class="result-item data-v-4138af9f" bindtap="{{result.d}}"><view class="result-content data-v-4138af9f"><text class="result-text data-v-4138af9f">{{result.a}}</text><view class="copy-btn data-v-4138af9f" catchtap="{{result.b}}"><text class="copy-icon data-v-4138af9f">📋</text></view></view></view></view></view></view><view class="card data-v-4138af9f"><view class="card-header data-v-4138af9f"><text class="header-title data-v-4138af9f">快速示例</text></view><view class="card-content data-v-4138af9f"><view class="examples-grid data-v-4138af9f"><view wx:for="{{e}}" wx:for-item="example" wx:key="b" class="example-btn data-v-4138af9f" bindtap="{{example.c}}"><text class="example-text data-v-4138af9f">{{example.a}}</text></view></view></view></view><view class="card data-v-4138af9f"><view class="card-header data-v-4138af9f"><text class="header-title data-v-4138af9f">使用说明</text></view><view class="card-content data-v-4138af9f"><view class="instructions data-v-4138af9f"><text class="instruction-item data-v-4138af9f">• 为文字添加各种样式的边框</text><text class="instruction-item data-v-4138af9f">• 支持简单、双线、圆角等多种样式</text><text class="instruction-item data-v-4138af9f">• 适合制作标题和重要提示</text><text class="instruction-item data-v-4138af9f">• 点击任意边框样式即可复制</text><text class="instruction-item data-v-4138af9f">• 兼容各种聊天和文档应用</text></view></view></view></view></view>