"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      selectedPlan: "premium",
      isProcessing: false,
      plans: [
        {
          id: "basic",
          name: "基础会员",
          price: "￥99",
          originalPrice: "￥199",
          colorClass: "blue-bg",
          features: [
            "无广告体验",
            "5个工具同时使用",
            "基础客服支持",
            "数据备份",
            "30天有效期"
          ],
          popular: false
        },
        {
          id: "premium",
          name: "高级会员",
          price: "￥299",
          originalPrice: "￥599",
          colorClass: "purple-bg",
          features: [
            "全部功能解锁",
            "无限工具使用",
            "优先客服支持",
            "云端数据同步",
            "会员专属标识",
            "永久有效"
          ],
          popular: true
        },
        {
          id: "ultimate",
          name: "至尊会员",
          price: "￥599",
          originalPrice: "￥1199",
          colorClass: "yellow-bg",
          features: [
            "至尊会员权益",
            "新功能优先体验",
            "专属客服通道",
            "定制化服务",
            "会员专属活动",
            "终身免费更新"
          ],
          popular: false
        }
      ],
      benefits: [
        { icon: "🚫", title: "无广告体验", desc: "享受纯净的使用环境" },
        { icon: "⚡", title: "功能全解锁", desc: "使用所有高级功能" },
        { icon: "☁️", title: "云端同步", desc: "多设备数据同步" },
        { icon: "🎯", title: "优先支持", desc: "专属客服快速响应" },
        { icon: "🎁", title: "会员福利", desc: "定期专属活动和礼品" },
        { icon: "🔄", title: "免费更新", desc: "永久享受功能更新" }
      ]
    };
  },
  methods: {
    selectPlan(planId) {
      this.selectedPlan = planId;
    },
    async handlePurchase() {
      if (this.isProcessing)
        return;
      const selectedPlanData = this.plans.find((plan) => plan.id === this.selectedPlan);
      const confirmResult = await new Promise((resolve) => {
        common_vendor.index.showModal({
          title: "确认购买",
          content: `确定购买${selectedPlanData.name}（${selectedPlanData.price}）吗？`,
          success: resolve,
          fail: () => resolve({ confirm: false })
        });
      });
      if (!confirmResult.confirm)
        return;
      this.isProcessing = true;
      try {
        const params = {
          planId: this.selectedPlan,
          planName: selectedPlanData.name,
          price: selectedPlanData.price
        };
        const result = await utils_toolService.toolService.getPermanentMember(params);
        if (result.success) {
          utils_index.showSuccess("购买成功！会员权益已激活");
          setTimeout(() => {
            common_vendor.index.navigateBack();
          }, 1500);
        } else {
          utils_index.showError(result.message || "购买失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/permanent-membership.vue:214", "购买会员失败:", error);
        utils_index.showError(error.message || "购买失败，请重试");
        common_vendor.index.showToast({
          title: "跳转支付页面",
          icon: "success"
        });
      } finally {
        this.isProcessing = false;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.benefits, (benefit, index, i0) => {
      return {
        a: common_vendor.t(benefit.icon),
        b: common_vendor.t(benefit.title),
        c: common_vendor.t(benefit.desc),
        d: index
      };
    }),
    b: common_vendor.f($data.plans, (plan, k0, i0) => {
      return common_vendor.e({
        a: plan.popular
      }, plan.popular ? {} : {}, {
        b: common_vendor.n(plan.colorClass),
        c: common_vendor.t(plan.name),
        d: common_vendor.t(plan.price),
        e: common_vendor.t(plan.originalPrice),
        f: $data.selectedPlan === plan.id
      }, $data.selectedPlan === plan.id ? {} : {}, {
        g: common_vendor.f(plan.features, (feature, index, i1) => {
          return {
            a: common_vendor.t(feature),
            b: index
          };
        }),
        h: plan.id,
        i: $data.selectedPlan === plan.id ? 1 : "",
        j: common_vendor.o(($event) => $options.selectPlan(plan.id), plan.id)
      });
    }),
    c: common_vendor.t($data.isProcessing ? "⏳" : "⚡"),
    d: common_vendor.t($data.isProcessing ? "处理中..." : "立即购买会员"),
    e: common_vendor.o((...args) => $options.handlePurchase && $options.handlePurchase(...args)),
    f: $data.isProcessing ? 1 : ""
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f649a93e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/permanent-membership.js.map
