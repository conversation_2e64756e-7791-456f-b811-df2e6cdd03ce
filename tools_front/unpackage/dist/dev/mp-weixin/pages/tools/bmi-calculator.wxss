/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b7dc2734 {
  display: flex;
}
.flex-1.data-v-b7dc2734 {
  flex: 1;
}
.items-center.data-v-b7dc2734 {
  align-items: center;
}
.justify-center.data-v-b7dc2734 {
  justify-content: center;
}
.justify-between.data-v-b7dc2734 {
  justify-content: space-between;
}
.text-center.data-v-b7dc2734 {
  text-align: center;
}
.rounded.data-v-b7dc2734 {
  border-radius: 3px;
}
.rounded-lg.data-v-b7dc2734 {
  border-radius: 6px;
}
.shadow.data-v-b7dc2734 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b7dc2734 {
  padding: 16rpx;
}
.m-4.data-v-b7dc2734 {
  margin: 16rpx;
}
.mb-4.data-v-b7dc2734 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b7dc2734 {
  margin-top: 16rpx;
}
.bmi-calculator.data-v-b7dc2734 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-b7dc2734 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-b7dc2734 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.card-header.data-v-b7dc2734 {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-icon.data-v-b7dc2734 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-header .header-icon .calculator-icon.data-v-b7dc2734 {
  font-size: 60rpx;
}
.card-header .header-title.data-v-b7dc2734 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-header .header-subtitle.data-v-b7dc2734 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.main-content.data-v-b7dc2734 {
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}
.section-title.data-v-b7dc2734 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.input-section .input-grid.data-v-b7dc2734 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 30rpx;
}
.input-section .input-group .input-label.data-v-b7dc2734 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.input-section .input-group .input-field.data-v-b7dc2734 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #374151;
  background: #ffffff;
  transition: all 0.3s ease;
  box-sizing: border-box;
}
.input-section .input-group .input-field.data-v-b7dc2734:focus {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  outline: none;
}
.input-section .input-group .picker-field.data-v-b7dc2734 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #d1d5db;
  border-radius: 12rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  box-sizing: border-box;
}
.input-section .input-group .picker-field .picker-text.data-v-b7dc2734 {
  font-size: 30rpx;
  color: #374151;
}
.input-section .input-group .picker-field .picker-arrow.data-v-b7dc2734 {
  font-size: 24rpx;
  color: #9ca3af;
}
.input-section .input-group .picker-field.data-v-b7dc2734:active {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.button-group.data-v-b7dc2734 {
  display: flex;
  gap: 20rpx;
  margin-top: 20rpx;
}
.calculate-btn.data-v-b7dc2734 {
  flex: 2;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.calculate-btn .btn-icon.data-v-b7dc2734 {
  font-size: 32rpx;
  color: white;
}
.calculate-btn .btn-text.data-v-b7dc2734 {
  font-size: 30rpx;
  font-weight: 600;
  color: white;
}
.calculate-btn.data-v-b7dc2734:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}
.reset-btn.data-v-b7dc2734 {
  flex: 1;
  height: 96rpx;
  background: #f3f4f6;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.reset-btn .btn-text.data-v-b7dc2734 {
  font-size: 30rpx;
  font-weight: 600;
  color: #6b7280;
}
.reset-btn.data-v-b7dc2734:active {
  background: #e5e7eb;
  transform: translateY(2rpx);
}
.result-section .bmi-result.data-v-b7dc2734 {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
}
.result-section .bmi-result .bmi-value-container .bmi-value.data-v-b7dc2734 {
  display: block;
  font-size: 80rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
}
.result-section .bmi-result .bmi-value-container .bmi-label.data-v-b7dc2734 {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 20rpx;
}
.result-section .bmi-result .bmi-value-container .category-badge.data-v-b7dc2734 {
  display: inline-block;
  padding: 12rpx 24rpx;
  border-radius: 50rpx;
}
.result-section .bmi-result .bmi-value-container .category-badge .category-text.data-v-b7dc2734 {
  font-size: 28rpx;
  font-weight: 600;
}
.ideal-weight-card.data-v-b7dc2734, .advice-card.data-v-b7dc2734 {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.ideal-weight-card .card-title.data-v-b7dc2734, .advice-card .card-title.data-v-b7dc2734 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}
.ideal-weight-card .weight-range.data-v-b7dc2734, .advice-card .weight-range.data-v-b7dc2734 {
  text-align: center;
}
.ideal-weight-card .weight-range .weight-text.data-v-b7dc2734, .advice-card .weight-range .weight-text.data-v-b7dc2734 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #059669;
  margin-bottom: 12rpx;
}
.ideal-weight-card .weight-range .weight-advice .advice-lose.data-v-b7dc2734, .advice-card .weight-range .weight-advice .advice-lose.data-v-b7dc2734 {
  font-size: 26rpx;
  color: #dc2626;
}
.ideal-weight-card .weight-range .weight-advice .advice-gain.data-v-b7dc2734, .advice-card .weight-range .weight-advice .advice-gain.data-v-b7dc2734 {
  font-size: 26rpx;
  color: #2563eb;
}
.ideal-weight-card .advice-text.data-v-b7dc2734, .advice-card .advice-text.data-v-b7dc2734 {
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.6;
}
.category-section .category-list .category-item.data-v-b7dc2734 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  background: #f9fafb;
  transition: all 0.3s ease;
}
.category-section .category-list .category-item .category-info.data-v-b7dc2734 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.category-section .category-list .category-item .category-info .category-range.data-v-b7dc2734 {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
  min-width: 120rpx;
}
.category-section .category-list .category-item .category-info .category-name.data-v-b7dc2734 {
  font-size: 28rpx;
  color: #6b7280;
}
.category-section .category-list .category-item .active-indicator.data-v-b7dc2734 {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #667eea;
}
.category-section .category-list .category-item.active.data-v-b7dc2734 {
  background: #e0f2fe;
  border: 2rpx solid #0891b2;
}
.category-section .category-list .category-item.active .category-name.data-v-b7dc2734 {
  color: #0891b2;
  font-weight: 600;
}
.tips-section .tips-list .tip-item.data-v-b7dc2734 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
}
.tips-section .tips-list .tip-item .tip-icon.data-v-b7dc2734 {
  font-size: 32rpx;
  margin-top: 4rpx;
}
.tips-section .tips-list .tip-item .tip-text.data-v-b7dc2734 {
  flex: 1;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
}
.bmi-underweight.data-v-b7dc2734 {
  color: #2563eb;
}
.bmi-normal.data-v-b7dc2734 {
  color: #059669;
}
.bmi-overweight.data-v-b7dc2734 {
  color: #d97706;
}
.bmi-obese1.data-v-b7dc2734 {
  color: #dc2626;
}
.bmi-obese2.data-v-b7dc2734 {
  color: #991b1b;
}
.underweight .category-text.data-v-b7dc2734 {
  color: #2563eb;
}
.underweight.active.data-v-b7dc2734 {
  background: #dbeafe;
  border-color: #2563eb;
}
.normal .category-text.data-v-b7dc2734 {
  color: #059669;
}
.normal.active.data-v-b7dc2734 {
  background: #d1fae5;
  border-color: #059669;
}
.overweight .category-text.data-v-b7dc2734 {
  color: #d97706;
}
.overweight.active.data-v-b7dc2734 {
  background: #fef3c7;
  border-color: #d97706;
}
.obese1 .category-text.data-v-b7dc2734 {
  color: #dc2626;
}
.obese1.active.data-v-b7dc2734 {
  background: #fee2e2;
  border-color: #dc2626;
}
.obese2 .category-text.data-v-b7dc2734 {
  color: #991b1b;
}
.obese2.active.data-v-b7dc2734 {
  background: #fecaca;
  border-color: #991b1b;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.input-grid.data-v-b7dc2734 {
    grid-template-columns: 1fr !important;
    gap: 20rpx !important;
}
.button-group.data-v-b7dc2734 {
    flex-direction: column;
}
}