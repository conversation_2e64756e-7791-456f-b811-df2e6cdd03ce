/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-f7b94a4b {
  display: flex;
}
.flex-1.data-v-f7b94a4b {
  flex: 1;
}
.items-center.data-v-f7b94a4b {
  align-items: center;
}
.justify-center.data-v-f7b94a4b {
  justify-content: center;
}
.justify-between.data-v-f7b94a4b {
  justify-content: space-between;
}
.text-center.data-v-f7b94a4b {
  text-align: center;
}
.rounded.data-v-f7b94a4b {
  border-radius: 3px;
}
.rounded-lg.data-v-f7b94a4b {
  border-radius: 6px;
}
.shadow.data-v-f7b94a4b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-f7b94a4b {
  padding: 16rpx;
}
.m-4.data-v-f7b94a4b {
  margin: 16rpx;
}
.mb-4.data-v-f7b94a4b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-f7b94a4b {
  margin-top: 16rpx;
}
.sound-effects-page.data-v-f7b94a4b {
  background: #f8f9fa;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.search-section.data-v-f7b94a4b {
  background: white;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}
.search-box.data-v-f7b94a4b {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 20rpx;
  position: relative;
}
.search-icon.data-v-f7b94a4b {
  font-size: 28rpx;
  color: #666;
  margin-right: 12rpx;
}
.search-input.data-v-f7b94a4b {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.search-input.data-v-f7b94a4b::-webkit-input-placeholder {
  color: #999;
}
.search-input.data-v-f7b94a4b::placeholder {
  color: #999;
}
.search-clear.data-v-f7b94a4b {
  font-size: 24rpx;
  color: #999;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
}
.category-section.data-v-f7b94a4b {
  background: white;
  padding: 24rpx 20rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}
.category-title.data-v-f7b94a4b {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.expand-btn.data-v-f7b94a4b {
  font-size: 24rpx;
  color: #667eea;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(102, 126, 234, 0.1);
}
.category-tags.data-v-f7b94a4b {
  transition: all 0.3s ease;
  padding: 8rpx 0;
}
.category-tags.data-v-f7b94a4b::-webkit-scrollbar {
  display: none;
}
.category-tags-scroll.data-v-f7b94a4b {
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  /* 隐藏滚动条但保持滚动功能 */
  /* 确保在小程序中也能正常滚动 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.category-tags-scroll.data-v-f7b94a4b::-webkit-scrollbar {
  display: none;
}
.category-scroll-content.data-v-f7b94a4b {
  display: inline-flex;
  align-items: center;
  padding-right: 60rpx;
  /* 增加右边距，确保最后一个分类完全可见 */
  white-space: nowrap;
  min-width: -webkit-max-content;
  min-width: max-content;
  /* 确保内容不被压缩 */
}
.category-tags-wrap.data-v-f7b94a4b {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  padding: 0 4rpx;
}
.category-tag.data-v-f7b94a4b {
  display: inline-block;
  background: #f5f5f5;
  color: #666;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  margin-right: 12rpx;
  flex-shrink: 0;
  /* 防止分类标签被压缩 */
  white-space: nowrap;
  /* 确保文字不换行 */
}
.category-tag.active.data-v-f7b94a4b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: scale(1.05);
}
.category-tag.data-v-f7b94a4b:last-child {
  margin-right: 0;
}
.audio-list.data-v-f7b94a4b {
  padding: 20rpx;
  padding-top: 0;
}
.list-header.data-v-f7b94a4b {
  margin-bottom: 20rpx;
}
.result-count.data-v-f7b94a4b {
  font-size: 24rpx;
  color: #666;
}
.audio-item.data-v-f7b94a4b {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.audio-item.data-v-f7b94a4b:active {
  transform: scale(0.98);
}
.audio-item.playing.data-v-f7b94a4b {
  padding-bottom: 48rpx;
}
.audio-main.data-v-f7b94a4b {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24rpx;
}
.audio-info.data-v-f7b94a4b {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}
.play-icon.data-v-f7b94a4b {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}
.play-icon.data-v-f7b94a4b:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}
.play-icon.playing.data-v-f7b94a4b {
  background: rgba(102, 126, 234, 0.2);
  color: #764ba2;
}
.audio-title.data-v-f7b94a4b {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}
.download-btn.data-v-f7b94a4b {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}
.download-btn.data-v-f7b94a4b:active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.2);
}
.download-btn.downloading.data-v-f7b94a4b {
  animation: downloading-f7b94a4b 1s infinite linear;
  background: rgba(102, 126, 234, 0.2);
  color: #764ba2;
}
@keyframes downloading-f7b94a4b {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
.audio-progress.data-v-f7b94a4b {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 24rpx 16rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.progress-bar.data-v-f7b94a4b {
  width: 100%;
  height: 4rpx;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 2rpx;
  overflow: visible;
  position: relative;
}
.progress-fill.data-v-f7b94a4b {
  height: 100%;
  background: #667eea;
  border-radius: 2rpx;
  transition: width 0.1s linear;
}
.progress-handle.data-v-f7b94a4b {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 12rpx;
  height: 12rpx;
  background: #667eea;
  border: 4rpx solid white;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  z-index: 1;
}
.time-info.data-v-f7b94a4b {
  display: flex;
  justify-content: space-between;
  font-size: 20rpx;
  color: #999;
  padding: 0 2rpx;
}
.current-time.data-v-f7b94a4b, .duration.data-v-f7b94a4b {
  font-family: monospace;
}
.empty-state.data-v-f7b94a4b {
  text-align: center;
  padding: 80rpx 40rpx;
}
.empty-icon.data-v-f7b94a4b {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.empty-text.data-v-f7b94a4b {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
}
.empty-tip.data-v-f7b94a4b {
  font-size: 24rpx;
  color: #999;
}
.player-bar.data-v-f7b94a4b,
.player-info.data-v-f7b94a4b,
.player-title.data-v-f7b94a4b,
.player-progress.data-v-f7b94a4b,
.player-controls.data-v-f7b94a4b,
.control-btn.data-v-f7b94a4b {
  display: none;
}
@media (max-width: 750rpx) {
.category-tags.data-v-f7b94a4b {
    gap: 8rpx;
}
.category-tag.data-v-f7b94a4b {
    padding: 8rpx 16rpx;
    font-size: 22rpx;
}
.audio-item.data-v-f7b94a4b {
    padding: 20rpx;
}
.audio-title.data-v-f7b94a4b {
    font-size: 26rpx;
}
.play-btn.data-v-f7b94a4b, .download-btn.data-v-f7b94a4b {
    width: 56rpx;
    height: 56rpx;
    font-size: 20rpx;
}
}