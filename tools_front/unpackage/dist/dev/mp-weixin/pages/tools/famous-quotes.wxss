/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-abd25efd {
  display: flex;
}
.flex-1.data-v-abd25efd {
  flex: 1;
}
.items-center.data-v-abd25efd {
  align-items: center;
}
.justify-center.data-v-abd25efd {
  justify-content: center;
}
.justify-between.data-v-abd25efd {
  justify-content: space-between;
}
.text-center.data-v-abd25efd {
  text-align: center;
}
.rounded.data-v-abd25efd {
  border-radius: 3px;
}
.rounded-lg.data-v-abd25efd {
  border-radius: 6px;
}
.shadow.data-v-abd25efd {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-abd25efd {
  padding: 16rpx;
}
.m-4.data-v-abd25efd {
  margin: 16rpx;
}
.mb-4.data-v-abd25efd {
  margin-bottom: 16rpx;
}
.mt-4.data-v-abd25efd {
  margin-top: 16rpx;
}
.min-h-screen.data-v-abd25efd {
  min-height: 100vh;
  background: #f8f9fa;
}
.p-4.data-v-abd25efd {
  padding: 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.navbar.data-v-abd25efd {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-abd25efd {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-abd25efd {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-abd25efd {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-abd25efd {
  padding: 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.category-card.data-v-abd25efd, .quote-card.data-v-abd25efd, .recommend-card.data-v-abd25efd, .info-card.data-v-abd25efd {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.category-card .card-header.data-v-abd25efd, .quote-card .card-header.data-v-abd25efd, .recommend-card .card-header.data-v-abd25efd, .info-card .card-header.data-v-abd25efd {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}
.category-card .card-header .header-icon.data-v-abd25efd, .quote-card .card-header .header-icon.data-v-abd25efd, .recommend-card .card-header .header-icon.data-v-abd25efd, .info-card .card-header .header-icon.data-v-abd25efd {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.category-card .card-header .header-title.data-v-abd25efd, .quote-card .card-header .header-title.data-v-abd25efd, .recommend-card .card-header .header-title.data-v-abd25efd, .info-card .card-header .header-title.data-v-abd25efd {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.category-card .card-content.data-v-abd25efd, .quote-card .card-content.data-v-abd25efd, .recommend-card .card-content.data-v-abd25efd, .info-card .card-content.data-v-abd25efd {
  padding: 0 30rpx 30rpx;
}
.category-grid.data-v-abd25efd {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.category-grid .category-btn.data-v-abd25efd {
  padding: 20rpx 16rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  text-align: center;
  transition: all 0.3s ease;
}
.category-grid .category-btn.active.data-v-abd25efd {
  background: #6366f1;
  border-color: #6366f1;
}
.category-grid .category-btn.active .category-text.data-v-abd25efd {
  color: white;
}
.category-grid .category-btn .category-text.data-v-abd25efd {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}
.quote-display.data-v-abd25efd {
  background: #eef2ff;
  padding: 40rpx;
  border-radius: 16rpx;
  border-left: 8rpx solid #6366f1;
  margin-bottom: 30rpx;
}
.quote-display .quote-text.data-v-abd25efd {
  display: block;
  font-size: 32rpx;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 24rpx;
}
.quote-display .quote-author.data-v-abd25efd {
  display: block;
  font-size: 28rpx;
  color: #6366f1;
  font-weight: 600;
  text-align: right;
}
.quote-actions.data-v-abd25efd {
  display: flex;
  gap: 16rpx;
}
.quote-actions .action-btn.data-v-abd25efd {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.quote-actions .action-btn.primary-btn.data-v-abd25efd {
  flex: 1;
  background: #6366f1;
  color: white;
}
.quote-actions .action-btn.primary-btn .btn-text.data-v-abd25efd {
  color: white;
}
.quote-actions .action-btn.outline-btn.data-v-abd25efd {
  background: white;
  border: 2rpx solid #e5e7eb;
}
.quote-actions .action-btn.outline-btn .btn-icon.data-v-abd25efd, .quote-actions .action-btn.outline-btn .btn-text.data-v-abd25efd {
  color: #666;
}
.quote-actions .action-btn .btn-icon.data-v-abd25efd {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.quote-actions .action-btn .btn-text.data-v-abd25efd {
  font-size: 26rpx;
  font-weight: 500;
}
.recommend-list.data-v-abd25efd {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.recommend-list .recommend-item.data-v-abd25efd {
  padding: 24rpx;
  background: #f9fafb;
  border: 2rpx solid #f3f4f6;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.recommend-list .recommend-item .recommend-text.data-v-abd25efd {
  display: block;
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
  margin-bottom: 12rpx;
}
.recommend-list .recommend-item .recommend-author.data-v-abd25efd {
  display: block;
  font-size: 22rpx;
  color: #9ca3af;
  text-align: right;
}
.info-list .info-item.data-v-abd25efd {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}