<view class="hue-generator data-v-1bb15d32"><view class="content data-v-1bb15d32"><view class="card data-v-1bb15d32"><view class="card-header data-v-1bb15d32"><view class="header-icon data-v-1bb15d32"><view class="icon-gradient data-v-1bb15d32"></view></view><text class="header-title data-v-1bb15d32">色调变化生成器</text><text class="header-subtitle data-v-1bb15d32">基于单一颜色生成丰富的色调变化</text></view><view class="main-content data-v-1bb15d32"><view class="control-section data-v-1bb15d32"><text class="section-title data-v-1bb15d32">基础颜色</text><view class="color-input-group data-v-1bb15d32"><view class="color-preview-large data-v-1bb15d32" style="{{'background-color:' + a}}" bindtap="{{b}}"></view><input class="color-input-text data-v-1bb15d32" type="text" bindinput="{{c}}" placeholder="#3b82f6" value="{{d}}"/></view><view class="slider-group data-v-1bb15d32"><view class="slider-header data-v-1bb15d32"><text class="slider-label data-v-1bb15d32">色调数量</text><text class="slider-value data-v-1bb15d32">{{e}}</text></view><slider class="custom-slider data-v-1bb15d32" value="{{f}}" bindchange="{{g}}" min="3" max="20" activeColor="#8B5CF6"/></view><view class="slider-group data-v-1bb15d32"><view class="slider-header data-v-1bb15d32"><text class="slider-label data-v-1bb15d32">饱和度</text><text class="slider-value data-v-1bb15d32">{{h}}%</text></view><slider class="custom-slider data-v-1bb15d32" value="{{i}}" bindchange="{{j}}" min="0" max="100" activeColor="#8B5CF6"/></view><view class="slider-group data-v-1bb15d32"><view class="slider-header data-v-1bb15d32"><text class="slider-label data-v-1bb15d32">亮度</text><text class="slider-value data-v-1bb15d32">{{k}}%</text></view><slider class="custom-slider data-v-1bb15d32" value="{{l}}" bindchange="{{m}}" min="10" max="90" activeColor="#8B5CF6"/></view><view class="copy-all-btn data-v-1bb15d32" bindtap="{{n}}"><text class="copy-icon data-v-1bb15d32">📋</text><text class="copy-text data-v-1bb15d32">复制全部颜色</text></view></view><view class="result-section data-v-1bb15d32"><view class="result-header data-v-1bb15d32"><text class="section-title data-v-1bb15d32">色调变化</text><text class="color-count data-v-1bb15d32">{{o}} 个颜色</text></view><view class="color-grid data-v-1bb15d32"><view wx:for="{{p}}" wx:for-item="color" wx:key="c" class="color-item data-v-1bb15d32" bindtap="{{color.d}}"><view class="color-block data-v-1bb15d32" style="{{'background-color:' + color.a}}"></view><text class="color-code data-v-1bb15d32">{{color.b}}</text></view></view><view class="info-section data-v-1bb15d32"><text class="info-title data-v-1bb15d32">色调生成说明</text><view class="info-list data-v-1bb15d32"><text class="info-item data-v-1bb15d32">色调生成器通过改变基础颜色的色相（Hue）值，在色环上生成等间距的颜色变化。</text><text class="info-item data-v-1bb15d32">饱和度和亮度保持一致，确保颜色的和谐统一。</text><text class="info-item data-v-1bb15d32">适用于创建彩虹效果、渐变色带等设计。</text></view></view></view></view></view></view><view wx:if="{{q}}" class="color-picker-modal data-v-1bb15d32" bindtap="{{K}}"><view class="color-picker-container data-v-1bb15d32" catchtap="{{J}}"><view class="picker-header data-v-1bb15d32"><text class="picker-title data-v-1bb15d32">选择颜色</text><view class="picker-close data-v-1bb15d32" bindtap="{{r}}"><text class="close-icon data-v-1bb15d32">×</text></view></view><view class="color-canvas-container data-v-1bb15d32"><block wx:if="{{r0}}"><canvas class="color-canvas data-v-1bb15d32" canvas-id="colorCanvas" bindtouchstart="{{s}}" bindtouchmove="{{t}}"></canvas></block><view class="canvas-cursor data-v-1bb15d32" style="{{v}}"></view></view><view class="hue-slider-container data-v-1bb15d32"><block wx:if="{{r0}}"><canvas class="hue-slider data-v-1bb15d32" canvas-id="hueCanvas" bindtouchstart="{{w}}" bindtouchmove="{{x}}"></canvas></block><view class="hue-cursor data-v-1bb15d32" style="{{y}}"></view></view><view class="rgb-inputs data-v-1bb15d32"><view class="rgb-group data-v-1bb15d32"><text class="rgb-label data-v-1bb15d32">R</text><input class="rgb-input data-v-1bb15d32" type="number" bindinput="{{z}}" min="0" max="255" value="{{A}}"/></view><view class="rgb-group data-v-1bb15d32"><text class="rgb-label data-v-1bb15d32">G</text><input class="rgb-input data-v-1bb15d32" type="number" bindinput="{{B}}" min="0" max="255" value="{{C}}"/></view><view class="rgb-group data-v-1bb15d32"><text class="rgb-label data-v-1bb15d32">B</text><input class="rgb-input data-v-1bb15d32" type="number" bindinput="{{D}}" min="0" max="255" value="{{E}}"/></view></view><view class="picker-preview data-v-1bb15d32"><view class="preview-color data-v-1bb15d32" style="{{'background-color:' + F}}"></view><text class="hex-value data-v-1bb15d32">{{G}}</text></view><view class="picker-actions data-v-1bb15d32"><view class="picker-btn cancel data-v-1bb15d32" bindtap="{{H}}">取消</view><view class="picker-btn confirm data-v-1bb15d32" bindtap="{{I}}">确认</view></view></view></view></view>