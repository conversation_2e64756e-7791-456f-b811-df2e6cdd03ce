<view class="handheld-barrage data-v-f4ba55c6"><view class="content data-v-f4ba55c6"><view class="card barrage-display data-v-f4ba55c6"><view class="display-container data-v-f4ba55c6" style="{{'background-color:' + f}}"><view wx:for="{{a}}" wx:for-item="item" wx:key="b" class="barrage-text data-v-f4ba55c6" style="{{'left:' + item.c + ';' + ('top:' + item.d) + ';' + ('font-size:' + b) + ';' + ('color:' + c) + ';' + ('font-weight:' + 'bold') + ';' + ('text-shadow:' + '2px 2px 4px rgba(0,0,0,0.5)') + ';' + ('position:' + 'absolute') + ';' + ('white-space:' + 'nowrap')}}">{{item.a}}</view><view wx:if="{{d}}" class="placeholder data-v-f4ba55c6"><text class="placeholder-text data-v-f4ba55c6">输入文字后开始播放弹幕</text></view><view class="fullscreen-btn data-v-f4ba55c6" bindtap="{{e}}"><text class="fullscreen-icon data-v-f4ba55c6">⛶</text></view></view></view><view class="card data-v-f4ba55c6"><view class="card-header data-v-f4ba55c6"><view class="header-icon data-v-f4ba55c6">💬</view><text class="header-title data-v-f4ba55c6">弹幕内容</text></view><view class="card-content data-v-f4ba55c6"><view class="input-section data-v-f4ba55c6"><input type="text" placeholder="输入要显示的文字..." maxlength="50" class="text-input data-v-f4ba55c6" value="{{g}}" bindinput="{{h}}"/></view><view class="preset-section data-v-f4ba55c6"><text class="section-label data-v-f4ba55c6">快速输入</text><view class="preset-grid data-v-f4ba55c6"><view wx:for="{{i}}" wx:for-item="presetText" wx:key="b" class="preset-btn data-v-f4ba55c6" bindtap="{{presetText.c}}">{{presetText.a}}</view></view></view></view></view><view class="card data-v-f4ba55c6"><view class="card-content data-v-f4ba55c6"><view class="control-buttons data-v-f4ba55c6"><view wx:if="{{j}}" class="control-btn start-btn data-v-f4ba55c6" bindtap="{{k}}"><text class="btn-icon data-v-f4ba55c6">▶️</text><text class="btn-text data-v-f4ba55c6">开始播放</text></view><view wx:else class="control-btn stop-btn data-v-f4ba55c6" bindtap="{{l}}"><text class="btn-icon data-v-f4ba55c6">⏸️</text><text class="btn-text data-v-f4ba55c6">暂停播放</text></view><view class="control-btn reset-btn data-v-f4ba55c6" bindtap="{{m}}"><text class="btn-icon data-v-f4ba55c6">🔄</text><text class="btn-text data-v-f4ba55c6">重置</text></view></view></view></view><view class="card data-v-f4ba55c6"><view class="card-header data-v-f4ba55c6"><view class="header-icon data-v-f4ba55c6">🎨</view><text class="header-title data-v-f4ba55c6">样式设置</text></view><view class="card-content data-v-f4ba55c6"><view class="setting-item data-v-f4ba55c6"><text class="setting-label data-v-f4ba55c6">字体大小: {{n}}px</text><slider value="{{o}}" min="{{16}}" max="{{48}}" step="{{2}}" bindchange="{{p}}" class="setting-slider data-v-f4ba55c6"/></view><view class="setting-item data-v-f4ba55c6"><text class="setting-label data-v-f4ba55c6">滚动速度: {{q}}%</text><slider value="{{r}}" min="{{10}}" max="{{100}}" step="{{10}}" bindchange="{{s}}" class="setting-slider data-v-f4ba55c6"/></view><view class="setting-item data-v-f4ba55c6"><text class="setting-label data-v-f4ba55c6">弹幕数量: {{t}}条</text><slider value="{{v}}" min="{{1}}" max="{{5}}" step="{{1}}" bindchange="{{w}}" class="setting-slider data-v-f4ba55c6"/></view><view class="setting-item data-v-f4ba55c6"><text class="setting-label data-v-f4ba55c6">滚动方向</text><view class="direction-buttons data-v-f4ba55c6"><view class="{{['direction-btn', 'data-v-f4ba55c6', x && 'active']}}" bindtap="{{y}}"> 从左到右 </view><view class="{{['direction-btn', 'data-v-f4ba55c6', z && 'active']}}" bindtap="{{A}}"> 从右到左 </view></view></view><view class="color-section data-v-f4ba55c6"><view class="color-item data-v-f4ba55c6"><text class="color-label data-v-f4ba55c6">文字颜色</text><view class="color-display data-v-f4ba55c6" style="{{'background-color:' + C}}" bindtap="{{D}}"><text class="color-value data-v-f4ba55c6">{{B}}</text></view></view><view class="color-item data-v-f4ba55c6"><text class="color-label data-v-f4ba55c6">背景颜色</text><view class="color-display data-v-f4ba55c6" style="{{'background-color:' + F}}" bindtap="{{G}}"><text class="color-value data-v-f4ba55c6">{{E}}</text></view></view></view><view class="preset-colors data-v-f4ba55c6"><text class="setting-label data-v-f4ba55c6">配色方案</text><view class="color-presets data-v-f4ba55c6"><view wx:for="{{H}}" wx:for-item="preset" wx:key="d" class="color-preset data-v-f4ba55c6" bindtap="{{preset.e}}"><view class="preset-bg data-v-f4ba55c6" style="{{'background-color:' + preset.a}}"></view><view class="preset-text data-v-f4ba55c6" style="{{'background-color:' + preset.b}}"></view><text class="preset-name data-v-f4ba55c6">{{preset.c}}</text></view></view></view></view></view><view class="card data-v-f4ba55c6"><view class="card-header data-v-f4ba55c6"><view class="header-icon data-v-f4ba55c6">ℹ️</view><text class="header-title data-v-f4ba55c6">使用说明</text></view><view class="card-content data-v-f4ba55c6"><view class="instructions data-v-f4ba55c6"><text class="instruction-item data-v-f4ba55c6">• 输入要显示的文字内容</text><text class="instruction-item data-v-f4ba55c6">• 调整字体大小、滚动速度和弹幕数量</text><text class="instruction-item data-v-f4ba55c6">• 点击颜色区域选择自定义颜色</text><text class="instruction-item data-v-f4ba55c6">• 点击全屏按钮进入横屏播放模式</text><text class="instruction-item data-v-f4ba55c6">• 适用于演唱会、比赛等场合应援</text></view></view></view></view><view wx:if="{{I}}" class="color-modal data-v-f4ba55c6" bindtap="{{Q}}"><view class="color-modal-content data-v-f4ba55c6" catchtap="{{P}}"><view class="modal-header data-v-f4ba55c6"><text class="modal-title data-v-f4ba55c6">选择{{J}}颜色</text><view class="modal-close data-v-f4ba55c6" bindtap="{{K}}">✕</view></view><view class="color-grid data-v-f4ba55c6"><view wx:for="{{L}}" wx:for-item="color" wx:key="b" class="color-option data-v-f4ba55c6" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"><text wx:if="{{color.a}}" class="selected-mark data-v-f4ba55c6">✓</text></view></view><view class="custom-color-section data-v-f4ba55c6"><text class="custom-label data-v-f4ba55c6">自定义颜色</text><input type="text" placeholder="#ffffff" class="custom-input data-v-f4ba55c6" bindconfirm="{{M}}" value="{{N}}" bindinput="{{O}}"/></view></view></view><view wx:if="{{R}}" class="fullscreen-barrage data-v-f4ba55c6" style="{{'background-color:' + W}}"><view wx:for="{{S}}" wx:for-item="item" wx:key="b" class="fullscreen-text data-v-f4ba55c6" style="{{'left:' + item.c + ';' + ('top:' + item.d) + ';' + ('font-size:' + T) + ';' + ('color:' + U) + ';' + ('font-weight:' + 'bold') + ';' + ('text-shadow:' + '4px 4px 8px rgba(0,0,0,0.8)') + ';' + ('position:' + 'absolute') + ';' + ('white-space:' + 'nowrap') + ';' + ('text-align:' + 'center') + ';' + ('transform:' + 'translateX(-50%) rotate(90deg)') + ';' + ('transform-origin:' + 'center center')}}">{{item.a}}</view><view class="exit-fullscreen-btn data-v-f4ba55c6" bindtap="{{V}}"><text class="exit-icon data-v-f4ba55c6">✕</text></view></view></view>