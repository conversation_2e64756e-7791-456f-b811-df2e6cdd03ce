"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      screamResults: [],
      screamExamples: ["啊啊啊", "救命", "太棒了", "不可能", "惊喜", "震惊", "厉害", "完美"]
    };
  },
  methods: {
    generateScreamText() {
      if (!this.inputText.trim()) {
        this.screamResults = [];
        return;
      }
      const text = this.inputText.trim();
      const results = [];
      results.push(`${text.toUpperCase()}!!!`);
      results.push(`${text.toUpperCase()}!!!!!`);
      const stretched = text.split("").map((char) => {
        if (/[aeiouAEIOU啊哦额呃唉]/.test(char)) {
          return char.repeat(3);
        }
        return char;
      }).join("");
      results.push(`${stretched.toUpperCase()}!!!`);
      results.push(`😱${text.toUpperCase()}😱`);
      results.push(`🔥${text.toUpperCase()}🔥`);
      results.push(`⚡${text.toUpperCase()}⚡`);
      results.push(`💥${text.toUpperCase()}💥`);
      results.push(`～${text.toUpperCase()}～`);
      results.push(`〜〜${text.toUpperCase()}〜〜`);
      results.push(`【${text.toUpperCase()}】`);
      results.push(`《${text.toUpperCase()}》`);
      results.push(`『${text.toUpperCase()}』`);
      results.push(`━━━${text.toUpperCase()}━━━`);
      results.push(`▁▂▃▄▅▆▇█${text.toUpperCase()}█▇▆▅▄▃▂▁`);
      results.push(`◆◇◆${text.toUpperCase()}◆◇◆`);
      this.screamResults = results;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "尖叫文字已复制",
            icon: "success",
            duration: 1500
          });
          if (common_vendor.index.vibrateShort) {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }
        }
      });
    },
    setExample(example) {
      this.inputText = example;
      this.generateScreamText();
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort({
          type: "light"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.generateScreamText && $options.generateScreamText(...args)]),
    b: $data.inputText,
    c: $data.screamResults.length > 0
  }, $data.screamResults.length > 0 ? {
    d: common_vendor.o((...args) => $options.generateScreamText && $options.generateScreamText(...args)),
    e: common_vendor.f($data.screamResults, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    f: common_vendor.f($data.screamExamples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.setExample(example), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-41da7f38"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/scream-text.js.map
