/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a4f74c06 {
  display: flex;
}
.flex-1.data-v-a4f74c06 {
  flex: 1;
}
.items-center.data-v-a4f74c06 {
  align-items: center;
}
.justify-center.data-v-a4f74c06 {
  justify-content: center;
}
.justify-between.data-v-a4f74c06 {
  justify-content: space-between;
}
.text-center.data-v-a4f74c06 {
  text-align: center;
}
.rounded.data-v-a4f74c06 {
  border-radius: 3px;
}
.rounded-lg.data-v-a4f74c06 {
  border-radius: 6px;
}
.shadow.data-v-a4f74c06 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a4f74c06 {
  padding: 16rpx;
}
.m-4.data-v-a4f74c06 {
  margin: 16rpx;
}
.mb-4.data-v-a4f74c06 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a4f74c06 {
  margin-top: 16rpx;
}
.loading-image-generator.data-v-a4f74c06 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-a4f74c06 {
  padding: 32rpx;
}
.section.data-v-a4f74c06 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.section.data-v-a4f74c06:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-2rpx);
}
.section-header.data-v-a4f74c06 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-a4f74c06 {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.section-title.data-v-a4f74c06 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.result-count.data-v-a4f74c06 {
  font-size: 24rpx;
  color: #6c757d;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}
.settings-container.data-v-a4f74c06 {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.size-settings.data-v-a4f74c06 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.input-group.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.input-item.data-v-a4f74c06 {
  display: flex;
  flex-direction: column;
}
.input-label.data-v-a4f74c06 {
  font-size: 26rpx;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8rpx;
}
.input-field.data-v-a4f74c06 {
  background: #ffffff;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #1a1a1a;
}
.input-field.data-v-a4f74c06:focus {
  border-color: #6c757d;
  outline: none;
}
.text-setting.data-v-a4f74c06 {
  display: flex;
  flex-direction: column;
}
.color-settings.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.color-item.data-v-a4f74c06 {
  display: flex;
  flex-direction: column;
}
.color-selector.data-v-a4f74c06 {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.color-preview.data-v-a4f74c06 {
  width: 24rpx;
  height: 24rpx;
  border-radius: 4rpx;
}
.color-value.data-v-a4f74c06 {
  font-size: 24rpx;
  color: #495057;
}
.color-arrow.data-v-a4f74c06 {
  font-size: 20rpx;
  color: #6c757d;
}
.color-selector.data-v-a4f74c06 {
  background: #ffffff;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.color-selector.data-v-a4f74c06:hover {
  border-color: #6c757d;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.color-selector.data-v-a4f74c06:active {
  transform: scale(0.98);
}
.color-preview.data-v-a4f74c06 {
  width: 32rpx;
  height: 32rpx;
  border-radius: 6rpx;
  border: 1rpx solid #e9ecef;
  flex-shrink: 0;
}
.color-value.data-v-a4f74c06 {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
  flex: 1;
}
.color-arrow.data-v-a4f74c06 {
  font-size: 18rpx;
  color: #6c757d;
  transition: transform 0.15s;
}
.preset-grid.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}
.preset-card.data-v-a4f74c06 {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}
.preset-card.data-v-a4f74c06::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(52, 58, 64, 0.05) 0%, rgba(52, 58, 64, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.2s;
}
.preset-card.data-v-a4f74c06:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border-color: #6c757d;
}
.preset-card.data-v-a4f74c06:hover::before {
  opacity: 1;
}
.preset-card.data-v-a4f74c06:active {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.preset-name.data-v-a4f74c06 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
  position: relative;
  z-index: 1;
}
.preset-size.data-v-a4f74c06 {
  font-size: 22rpx;
  color: #6c757d;
  display: block;
  position: relative;
  z-index: 1;
  font-weight: 500;
}
.generate-section.data-v-a4f74c06 {
  margin-bottom: 24rpx;
}
.generate-buttons.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.generate-btn.data-v-a4f74c06 {
  min-height: 56rpx;
  height: 56rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  padding: 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  gap: 12rpx;
  font-weight: 600;
  width: 100%;
  box-sizing: border-box;
  transition: box-shadow 0.2s, background 0.2s, border 0.2s, color 0.2s, transform 0.2s;
  position: relative;
  overflow: hidden;
}
.generate-btn .btn-icon.data-v-a4f74c06 {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.generate-btn .btn-text.data-v-a4f74c06 {
  font-size: 28rpx;
  font-weight: 600;
}
.generate-btn.primary.data-v-a4f74c06 {
  background: #101828;
  color: #fff;
  border: none;
  box-shadow: 0 2rpx 8rpx rgba(16, 24, 40, 0.1);
}
.generate-btn.primary.data-v-a4f74c06:active {
  background: #23272b;
  color: #fff;
  box-shadow: 0 1rpx 4rpx rgba(16, 24, 40, 0.08);
  transform: scale(0.98);
}
.generate-btn.secondary.data-v-a4f74c06 {
  background: #fff;
  color: #101828;
  border: 1rpx solid #e0e7ef;
  box-shadow: none;
}
.generate-btn.secondary.data-v-a4f74c06:active {
  border: 1rpx solid #343a40;
  background: #f8f9fa;
  color: #101828;
  transform: scale(0.98);
}
.image-grid.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.image-item.data-v-a4f74c06 {
  position: relative;
  background: #ffffff;
  border: 1rpx solid #e9ecef;
  border-radius: 16rpx;
  overflow: hidden;
  aspect-ratio: 4/3;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.image-item.data-v-a4f74c06:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  border-color: #6c757d;
}
.image-item:hover .image-actions.data-v-a4f74c06 {
  opacity: 1;
  transform: translateY(0);
}
.image-item.data-v-a4f74c06:active {
  transform: translateY(-2rpx);
}
.generated-image.data-v-a4f74c06 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s;
}
.image-item:hover .generated-image.data-v-a4f74c06 {
  transform: scale(1.02);
}
.image-info.data-v-a4f74c06 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #ffffff;
  padding: 20rpx;
  font-size: 22rpx;
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
}
.image-size.data-v-a4f74c06 {
  display: block;
  font-weight: 600;
  margin-bottom: 6rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}
.image-text.data-v-a4f74c06 {
  display: block;
  opacity: 0.9;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}
.image-actions.data-v-a4f74c06 {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
  display: flex;
  gap: 8rpx;
  opacity: 0;
  transform: translateY(-8rpx);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.data-v-a4f74c06 {
  background: rgba(255, 255, 255, 0.9);
  color: #495057;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx;
  font-size: 20rpx;
  cursor: pointer;
  transition: all 0.15s;
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.action-btn.data-v-a4f74c06:hover {
  background: #ffffff;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.action-btn.data-v-a4f74c06:active {
  transform: scale(0.95);
}
.action-icon.data-v-a4f74c06 {
  font-size: 20rpx;
}

/* 预览弹窗 */
.preview-mask.data-v-a4f74c06 {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-dialog.data-v-a4f74c06 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 400rpx;
  max-width: 90vw;
  position: relative;
}
.preview-close.data-v-a4f74c06 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  color: #666666;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1;
  z-index: 2;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.15s, color 0.15s;
  background: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-close.data-v-a4f74c06:active {
  opacity: 1;
  color: #333333;
}
.preview-image.data-v-a4f74c06 {
  width: 320rpx;
  height: 240rpx;
  border-radius: 12rpx;
  object-fit: contain;
  margin-bottom: 16rpx;
  border: 1rpx solid #e9ecef;
}
.preview-info.data-v-a4f74c06 {
  text-align: center;
  margin-bottom: 24rpx;
}
.preview-size.data-v-a4f74c06 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  display: block;
  margin-bottom: 8rpx;
}
.preview-text.data-v-a4f74c06 {
  font-size: 24rpx;
  color: #6c757d;
  display: block;
}
.preview-actions.data-v-a4f74c06 {
  display: flex;
  gap: 16rpx;
}
.preview-btn.data-v-a4f74c06 {
  background: #f8f9fa;
  color: #495057;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.preview-btn.primary.data-v-a4f74c06 {
  background: #343a40;
  color: #ffffff;
  border-color: #343a40;
}
.preview-btn.primary.data-v-a4f74c06:active {
  background: #23272b;
}
.preview-btn.data-v-a4f74c06:active {
  background: #e9ecef;
}
.tips-section.data-v-a4f74c06 {
  background: #fafafa;
  border: 1rpx solid #e9ecef;
}
.tips-list.data-v-a4f74c06 {
  margin: 0;
}
.tip-item.data-v-a4f74c06 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.tip-item.data-v-a4f74c06:last-child {
  margin-bottom: 0;
}
.tip-bullet.data-v-a4f74c06 {
  color: #6c757d;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}
.tip-text.data-v-a4f74c06 {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.5;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.input-group.data-v-a4f74c06 {
    grid-template-columns: 1fr;
}
.color-settings.data-v-a4f74c06 {
    grid-template-columns: 1fr;
}
.preset-grid.data-v-a4f74c06 {
    grid-template-columns: repeat(2, 1fr);
}
.generate-buttons.data-v-a4f74c06 {
    grid-template-columns: 1fr;
}
.image-grid.data-v-a4f74c06 {
    grid-template-columns: 1fr;
}
.preview-dialog.data-v-a4f74c06 {
    min-width: 0;
    width: 90vw;
    padding: 24rpx;
}
.preview-image.data-v-a4f74c06 {
    width: 80vw;
    height: 60vw;
}
.preview-actions.data-v-a4f74c06 {
    flex-direction: column;
    width: 100%;
}
.preview-btn.data-v-a4f74c06 {
    width: 100%;
    justify-content: center;
}
}
/* 颜色选择器弹窗样式 */
.color-picker-mask.data-v-a4f74c06 {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.color-picker-dialog.data-v-a4f74c06 {
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  animation: colorPickerShow-a4f74c06 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
@keyframes colorPickerShow-a4f74c06 {
from {
    opacity: 0;
    transform: scale(0.9) translateY(20rpx);
}
to {
    opacity: 1;
    transform: scale(1) translateY(0);
}
}
.color-picker-header.data-v-a4f74c06 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
}
.color-picker-title.data-v-a4f74c06 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.color-picker-close.data-v-a4f74c06 {
  color: #666666;
  font-size: 36rpx;
  font-weight: 600;
  line-height: 1;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.15s, color 0.15s;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.color-picker-close.data-v-a4f74c06:hover {
  opacity: 1;
  color: #333333;
  background: rgba(0, 0, 0, 0.05);
}
.color-picker-content.data-v-a4f74c06 {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.preset-colors.data-v-a4f74c06 {
  margin-bottom: 32rpx;
}
.preset-colors-title.data-v-a4f74c06 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}
.preset-colors-grid.data-v-a4f74c06 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12rpx;
}
.preset-color-item.data-v-a4f74c06 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
  border: 2rpx solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.preset-color-item.data-v-a4f74c06:hover {
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}
.preset-color-item.data-v-a4f74c06:active {
  transform: scale(0.95);
}
.color-selected.data-v-a4f74c06 {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
}
.custom-color.data-v-a4f74c06 {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 32rpx;
}
.custom-color-title.data-v-a4f74c06 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}
.custom-color-input.data-v-a4f74c06 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.color-input-field.data-v-a4f74c06 {
  flex: 1;
  background: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #1a1a1a;
}
.color-input-field.data-v-a4f74c06:focus {
  border-color: #6c757d;
  background: #ffffff;
  outline: none;
}
.color-confirm-btn.data-v-a4f74c06 {
  background: #343a40;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.color-confirm-btn.data-v-a4f74c06:hover {
  background: #23272b;
  transform: translateY(-1rpx);
}
.color-confirm-btn.data-v-a4f74c06:active {
  transform: translateY(0);
}
.preset-card.selected.data-v-a4f74c06 {
  border: 2rpx solid #343a40;
  box-shadow: 0 4rpx 16rpx rgba(52, 58, 64, 0.12);
  background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
  position: relative;
}
.preset-check.data-v-a4f74c06 {
  position: absolute;
  top: 12rpx;
  right: 16rpx;
  color: #343a40;
  font-size: 28rpx;
  font-weight: 700;
}
.generate-btn.primary.data-v-a4f74c06 {
  min-height: 96rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  background: #101828;
  color: #fff;
  box-shadow: 0 8rpx 32rpx rgba(16, 24, 40, 0.18);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-weight: 700;
  transition: box-shadow 0.2s, transform 0.2s;
}
.generate-btn.primary.data-v-a4f74c06:active {
  box-shadow: 0 2rpx 8rpx rgba(16, 24, 40, 0.12);
  transform: scale(0.98);
}
.generate-btn.secondary.data-v-a4f74c06 {
  min-height: 96rpx;
  font-size: 32rpx;
  border-radius: 16rpx;
  background: #fff;
  color: #101828;
  border: 2rpx solid #e0e7ef;
  box-shadow: 0 2rpx 8rpx rgba(16, 24, 40, 0.04);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  font-weight: 700;
  transition: border 0.2s, box-shadow 0.2s, transform 0.2s;
}
.generate-btn.secondary.data-v-a4f74c06:active {
  border: 2rpx solid #343a40;
  box-shadow: 0 4rpx 16rpx rgba(16, 24, 40, 0.1);
  transform: scale(0.98);
}
.preview-image.large.data-v-a4f74c06 {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: 24rpx;
}
.preview-actions.single.data-v-a4f74c06 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 0;
  width: 100%;
}
.preview-btn.large.data-v-a4f74c06 {
  min-width: 180rpx;
  height: 44rpx;
  font-size: 28rpx;
  padding: 0 24rpx;
  border-radius: 8rpx;
  justify-content: center;
  align-items: center;
  display: flex;
}
.preview-btn .btn-text.data-v-a4f74c06 {
  font-size: 28rpx;
  font-weight: 600;
  margin: 0;
}