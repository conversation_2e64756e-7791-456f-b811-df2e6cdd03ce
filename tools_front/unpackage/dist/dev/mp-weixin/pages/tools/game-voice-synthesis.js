"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      text: "",
      selectedVoice: "hero",
      isGenerating: false,
      hasGenerated: false,
      audioUrl: "",
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      playProgress: 0,
      audioContext: null,
      playTimer: null,
      voices: [
        { id: "hero", name: "英雄角色", description: "勇敢坚定的声音" },
        { id: "villain", name: "反派角色", description: "阴险狡诈的声音" },
        { id: "narrator", name: "旁白解说", description: "沉稳磁性的声音" },
        { id: "npc", name: "NPC角色", description: "友善亲切的声音" }
      ]
    };
  },
  computed: {
    selectedVoiceInfo() {
      return this.voices.find((v) => v.id === this.selectedVoice) || this.voices[0];
    }
  },
  onUnload() {
    this.cleanupAudio();
  },
  methods: {
    selectVoice(voiceId) {
      this.selectedVoice = voiceId;
      if (this.hasGenerated) {
        this.resetAudioState();
      }
    },
    async handleGenerate() {
      if (!this.text.trim()) {
        common_vendor.index.showToast({
          title: "请输入文字内容",
          icon: "none"
        });
        return;
      }
      this.isGenerating = true;
      this.resetAudioState();
      try {
        const params = {
          text: this.text.trim(),
          voice: this.selectedVoice,
          speed: 1,
          volume: 1
        };
        const result = await utils_toolService.toolService.synthesizeGameVoice(params);
        if (result.success) {
          this.audioUrl = result.audioUrl;
          this.duration = result.duration || 3 + Math.random() * 5;
          this.hasGenerated = true;
          utils_index.showSuccess("语音生成成功！");
          this.initAudioContext();
        } else {
          utils_index.showError(result.message || "语音生成失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/game-voice-synthesis.vue:207", "语音生成失败:", error);
        utils_index.showError(error.message || "语音生成失败，请重试");
        this.audioUrl = "mock-audio-" + Date.now();
        this.duration = 3 + Math.random() * 5;
        this.hasGenerated = true;
        common_vendor.index.showToast({
          title: "语音生成完成",
          icon: "success"
        });
      } finally {
        this.isGenerating = false;
      }
    },
    initAudioContext() {
      this.cleanupAudio();
      if (this.audioUrl && this.audioUrl !== "mock-audio-" + Date.now()) {
        this.audioContext = common_vendor.index.createInnerAudioContext();
        this.audioContext.src = this.audioUrl;
        this.audioContext.onTimeUpdate(() => {
          this.currentTime = this.audioContext.currentTime;
          this.playProgress = this.currentTime / this.duration * 100;
        });
        this.audioContext.onEnded(() => {
          this.isPlaying = false;
          this.currentTime = 0;
          this.playProgress = 0;
          this.clearPlayTimer();
        });
        this.audioContext.onError((error) => {
          common_vendor.index.__f__("error", "at pages/tools/game-voice-synthesis.vue:247", "音频播放错误:", error);
          utils_index.showError("音频播放失败");
          this.isPlaying = false;
        });
      }
    },
    togglePlay() {
      if (this.audioContext) {
        if (this.isPlaying) {
          this.audioContext.pause();
          this.clearPlayTimer();
        } else {
          this.audioContext.play();
          this.startPlayTimer();
        }
        this.isPlaying = !this.isPlaying;
      } else {
        if (this.isPlaying) {
          this.clearPlayTimer();
        } else {
          this.startPlayTimer();
        }
        this.isPlaying = !this.isPlaying;
      }
    },
    startPlayTimer() {
      this.clearPlayTimer();
      this.playTimer = setInterval(() => {
        this.currentTime += 0.1;
        this.playProgress = this.currentTime / this.duration * 100;
        if (this.currentTime >= this.duration) {
          this.isPlaying = false;
          this.currentTime = 0;
          this.playProgress = 0;
          this.clearPlayTimer();
        }
      }, 100);
    },
    clearPlayTimer() {
      if (this.playTimer) {
        clearInterval(this.playTimer);
        this.playTimer = null;
      }
    },
    cleanupAudio() {
      this.clearPlayTimer();
      if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
      }
    },
    resetAudioState() {
      this.cleanupAudio();
      this.hasGenerated = false;
      this.audioUrl = "";
      this.isPlaying = false;
      this.currentTime = 0;
      this.duration = 0;
      this.playProgress = 0;
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    },
    async handleRegenerate() {
      if (this.isGenerating)
        return;
      await this.handleGenerate();
    },
    async handleDownload() {
      if (!this.audioUrl)
        return;
      try {
        const params = {
          audioUrl: this.audioUrl,
          text: this.text,
          voice: this.selectedVoice
        };
        const result = await utils_toolService.toolService.downloadGameVoice(params);
        if (result.success) {
          utils_index.showSuccess("语音文件下载成功！");
        } else {
          utils_index.showError(result.message || "下载失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/game-voice-synthesis.vue:346", "语音下载失败:", error);
        utils_index.showError(error.message || "下载失败，请重试");
        common_vendor.index.showToast({
          title: "语音文件已生成",
          icon: "success"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.text,
    b: common_vendor.o(($event) => $data.text = $event.detail.value),
    c: common_vendor.t($data.text.length),
    d: common_vendor.f($data.voices, (voice, k0, i0) => {
      return {
        a: common_vendor.t(voice.name),
        b: common_vendor.t(voice.description),
        c: voice.id,
        d: $data.selectedVoice === voice.id ? 1 : "",
        e: common_vendor.o(($event) => $options.selectVoice(voice.id), voice.id)
      };
    }),
    e: $data.text.trim()
  }, $data.text.trim() ? {
    f: common_vendor.t($data.isGenerating ? "生成中..." : "生成语音"),
    g: $data.isGenerating ? 1 : "",
    h: common_vendor.o((...args) => $options.handleGenerate && $options.handleGenerate(...args)),
    i: $data.isGenerating
  } : {}, {
    j: $data.hasGenerated && $data.audioUrl
  }, $data.hasGenerated && $data.audioUrl ? {
    k: common_vendor.t($options.selectedVoiceInfo.name),
    l: common_vendor.t($data.text.substring(0, 30)),
    m: common_vendor.t($data.text.length > 30 ? "..." : ""),
    n: common_vendor.t($data.isPlaying ? "⏸️" : "▶️"),
    o: common_vendor.o((...args) => $options.togglePlay && $options.togglePlay(...args)),
    p: $data.playProgress + "%",
    q: common_vendor.t($options.formatTime($data.currentTime)),
    r: common_vendor.t($options.formatTime($data.duration)),
    s: common_vendor.o((...args) => $options.handleRegenerate && $options.handleRegenerate(...args)),
    t: common_vendor.o((...args) => $options.handleDownload && $options.handleDownload(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-070343ce"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/game-voice-synthesis.js.map
