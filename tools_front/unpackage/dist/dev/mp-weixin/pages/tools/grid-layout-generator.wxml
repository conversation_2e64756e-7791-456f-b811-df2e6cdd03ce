<view class="grid-generator-container data-v-b660904b"><view class="header-card data-v-b660904b"><view class="header-content data-v-b660904b"><view class="header-icon data-v-b660904b">📐</view><view class="header-info data-v-b660904b"><text class="header-title data-v-b660904b">CSS网格布局生成器</text><text class="header-subtitle data-v-b660904b">快速生成专业的CSS Grid布局代码</text></view></view></view><view class="settings-card data-v-b660904b"><view class="card-header data-v-b660904b"><text class="card-title data-v-b660904b">⚙️ 网格设置</text></view><view class="settings-row data-v-b660904b"><view class="setting-group data-v-b660904b"><text class="setting-label data-v-b660904b">行数 ({{a}})</text><slider value="{{b}}" bindchange="{{c}}" min="1" max="8" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="setting-slider data-v-b660904b"/></view><view class="setting-group data-v-b660904b"><text class="setting-label data-v-b660904b">列数 ({{d}})</text><slider value="{{e}}" bindchange="{{f}}" min="1" max="8" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="setting-slider data-v-b660904b"/></view><view class="setting-group data-v-b660904b"><text class="setting-label data-v-b660904b">间距 ({{g}}px)</text><slider value="{{h}}" bindchange="{{i}}" min="0" max="25" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="setting-slider data-v-b660904b"/></view></view></view><view class="templates-card data-v-b660904b"><view class="card-header data-v-b660904b"><text class="card-title data-v-b660904b">🎨 布局模板</text></view><view class="template-grid data-v-b660904b"><view wx:for="{{j}}" wx:for-item="template" wx:key="c" class="template-item data-v-b660904b" bindtap="{{template.d}}"><view class="template-preview data-v-b660904b"><view wx:for="{{template.a}}" wx:for-item="item" wx:key="a" class="template-block data-v-b660904b" style="{{item.b}}"></view></view><text class="template-name data-v-b660904b">{{template.b}}</text></view></view></view><view class="preview-card data-v-b660904b"><view class="card-header preview-header data-v-b660904b"><text class="card-title data-v-b660904b">👀 布局预览</text><button class="action-btn right-btn data-v-b660904b" bindtap="{{k}}"><text class="btn-icon data-v-b660904b">🎲</text><text class="btn-text data-v-b660904b">随机填充</text></button></view><view class="preview-container data-v-b660904b"><view class="grid-preview data-v-b660904b" style="{{m}}"><view wx:for="{{l}}" wx:for-item="item" wx:key="b" class="grid-item data-v-b660904b" bindtap="{{item.c}}"><text class="item-text data-v-b660904b">{{item.a}}</text></view></view></view></view><view class="code-card data-v-b660904b"><view class="card-header code-header data-v-b660904b"><text class="card-title data-v-b660904b">📄 生成的CSS代码</text><button class="copy-btn right-btn data-v-b660904b" bindtap="{{n}}"><text class="copy-icon data-v-b660904b">📋</text><text class="copy-text data-v-b660904b">复制代码</text></button></view><view class="code-content data-v-b660904b"><view class="code-block data-v-b660904b"><text class="code-text data-v-b660904b">{{o}}</text></view></view></view><view class="tips-card data-v-b660904b"><view class="card-header data-v-b660904b"><text class="card-title data-v-b660904b">💡 使用说明</text></view><view class="tips-content data-v-b660904b"><view class="tip-item data-v-b660904b"><text class="tip-title data-v-b660904b">🔧 调整参数</text><text class="tip-desc data-v-b660904b">使用滑块调整网格的行数、列数和间距</text></view><view class="tip-item data-v-b660904b"><text class="tip-title data-v-b660904b">🎨 选择模板</text><text class="tip-desc data-v-b660904b">点击布局模板快速生成常见的网格布局</text></view><view class="tip-item data-v-b660904b"><text class="tip-title data-v-b660904b">✏️ 自定义区域</text><text class="tip-desc data-v-b660904b">点击网格区域自定义区域名称</text></view><view class="tip-item data-v-b660904b"><text class="tip-title data-v-b660904b">📱 响应式设计</text><text class="tip-desc data-v-b660904b">生成的代码支持响应式布局和现代浏览器</text></view></view></view><view wx:if="{{p}}" class="modal-overlay data-v-b660904b" bindtap="{{x}}"><view class="modal-content data-v-b660904b" catchtap="{{w}}"><view class="modal-header data-v-b660904b"><text class="modal-title data-v-b660904b">编辑区域名称</text></view><view class="modal-body data-v-b660904b"><input class="area-input data-v-b660904b" placeholder="输入区域名称" bindconfirm="{{q}}" value="{{r}}" bindinput="{{s}}"/></view><view class="modal-footer data-v-b660904b"><button class="modal-btn cancel data-v-b660904b" bindtap="{{t}}"><text class="data-v-b660904b">取消</text></button><button class="modal-btn confirm data-v-b660904b" bindtap="{{v}}"><text class="data-v-b660904b">确定</text></button></view></view></view></view>