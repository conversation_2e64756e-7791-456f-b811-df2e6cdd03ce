<view class="contrast-checker data-v-7ce08563"><view class="container data-v-7ce08563"><view class="header-section data-v-7ce08563"><view class="icon-wrapper data-v-7ce08563"><text class="tool-icon data-v-7ce08563">👁️</text></view><text class="tool-title data-v-7ce08563">颜色对比度检查器</text><text class="tool-desc data-v-7ce08563">检查颜色组合的可访问性，确保符合WCAG标准</text></view><view class="color-selection-section data-v-7ce08563"><text class="section-title data-v-7ce08563">颜色选择</text><view class="color-inputs data-v-7ce08563"><view class="color-input-group data-v-7ce08563"><text class="color-label data-v-7ce08563">前景色（文字颜色）</text><view class="color-input-row data-v-7ce08563"><view class="color-preview data-v-7ce08563" style="{{'background-color:' + a}}" bindtap="{{b}}"></view><input type="text" placeholder="#000000" class="color-text-input data-v-7ce08563" bindinput="{{c}}" value="{{d}}"/></view></view><view class="color-input-group data-v-7ce08563"><text class="color-label data-v-7ce08563">背景色</text><view class="color-input-row data-v-7ce08563"><view class="color-preview data-v-7ce08563" style="{{'background-color:' + e}}" bindtap="{{f}}"></view><input type="text" placeholder="#FFFFFF" class="color-text-input data-v-7ce08563" bindinput="{{g}}" value="{{h}}"/></view></view></view></view><view class="contrast-results-section data-v-7ce08563"><text class="section-title data-v-7ce08563">对比度检查结果</text><view class="contrast-display data-v-7ce08563"><view class="contrast-ratio data-v-7ce08563"><text class="ratio-label data-v-7ce08563">对比度比值</text><text class="ratio-value data-v-7ce08563">{{i}}</text></view><view class="compliance-checks data-v-7ce08563"><view class="check-item data-v-7ce08563"><view class="{{['check-icon', 'data-v-7ce08563', k && 'pass']}}"><text class="icon-text data-v-7ce08563">{{j}}</text></view><text class="check-label data-v-7ce08563">WCAG AA (正常文字)</text><text class="check-requirement data-v-7ce08563">≥ 4.5:1</text></view><view class="check-item data-v-7ce08563"><view class="{{['check-icon', 'data-v-7ce08563', m && 'pass']}}"><text class="icon-text data-v-7ce08563">{{l}}</text></view><text class="check-label data-v-7ce08563">WCAG AAA (正常文字)</text><text class="check-requirement data-v-7ce08563">≥ 7:1</text></view><view class="check-item data-v-7ce08563"><view class="{{['check-icon', 'data-v-7ce08563', o && 'pass']}}"><text class="icon-text data-v-7ce08563">{{n}}</text></view><text class="check-label data-v-7ce08563">WCAG AA (大文字)</text><text class="check-requirement data-v-7ce08563">≥ 3:1</text></view><view class="check-item data-v-7ce08563"><view class="{{['check-icon', 'data-v-7ce08563', q && 'pass']}}"><text class="icon-text data-v-7ce08563">{{p}}</text></view><text class="check-label data-v-7ce08563">WCAG AAA (大文字)</text><text class="check-requirement data-v-7ce08563">≥ 4.5:1</text></view></view></view></view><view class="preview-section data-v-7ce08563"><text class="section-title data-v-7ce08563">预览效果</text><view class="preview-samples data-v-7ce08563"><view class="sample-item data-v-7ce08563" style="{{r}}"><text class="sample-title data-v-7ce08563">标题文字 (24px)</text><text class="sample-normal data-v-7ce08563">正常文字内容 (16px) - 这是一段示例文字，用于测试颜色对比度效果。</text><text class="sample-small data-v-7ce08563">小文字 (12px) - 用于测试小号文字的可读性。</text></view><view class="sample-item reverse data-v-7ce08563" style="{{s}}"><text class="sample-title data-v-7ce08563">反转效果 (24px)</text><text class="sample-normal data-v-7ce08563">正常文字内容 (16px) - 这是反转颜色的示例效果。</text><text class="sample-small data-v-7ce08563">小文字 (12px) - 反转颜色的小号文字效果。</text></view></view></view><view wx:if="{{t}}" class="suggestions-section data-v-7ce08563"><text class="section-title data-v-7ce08563">优化建议</text><view class="suggestions-list data-v-7ce08563"><view wx:for="{{v}}" wx:for-item="suggestion" wx:key="d" class="suggestion-item data-v-7ce08563"><view class="suggestion-icon data-v-7ce08563"><text class="icon-text data-v-7ce08563">💡</text></view><view class="suggestion-content data-v-7ce08563"><text class="suggestion-text data-v-7ce08563">{{suggestion.a}}</text><view wx:if="{{suggestion.b}}" class="suggestion-colors data-v-7ce08563"><view wx:for="{{suggestion.c}}" wx:for-item="color" wx:key="b" class="suggested-color data-v-7ce08563" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"><text class="color-code data-v-7ce08563">{{color.a}}</text></view></view></view></view></view></view><view class="actions-section data-v-7ce08563"><view class="action-btn primary data-v-7ce08563" bindtap="{{w}}"><text class="action-icon data-v-7ce08563">💾</text><text class="action-text data-v-7ce08563">保存配色</text></view><view class="action-btn secondary data-v-7ce08563" bindtap="{{x}}"><text class="action-icon data-v-7ce08563">📤</text><text class="action-text data-v-7ce08563">分享结果</text></view><view class="action-btn tertiary data-v-7ce08563" bindtap="{{y}}"><text class="action-icon data-v-7ce08563">🔄</text><text class="action-text data-v-7ce08563">重置</text></view></view><view class="instructions-section data-v-7ce08563"><text class="section-title data-v-7ce08563">使用说明</text><view class="instructions-list data-v-7ce08563"><text class="instruction-item data-v-7ce08563">• <text class="highlight data-v-7ce08563">WCAG标准</text>：Web内容可访问性指南，确保网站对所有用户友好</text><text class="instruction-item data-v-7ce08563">• <text class="highlight data-v-7ce08563">AA级别</text>：基本可访问性要求，适用于大多数网站</text><text class="instruction-item data-v-7ce08563">• <text class="highlight data-v-7ce08563">AAA级别</text>：最高可访问性标准，适用于政府和重要服务</text><text class="instruction-item data-v-7ce08563">• <text class="highlight data-v-7ce08563">大文字</text>：18pt粗体或24pt常规文字被视为大文字</text><text class="instruction-item data-v-7ce08563">• <text class="highlight data-v-7ce08563">对比度比值</text>：数值越高，颜色对比越明显，可读性越好</text></view></view></view><view wx:if="{{z}}" class="color-picker-modal data-v-7ce08563" bindtap="{{T}}"><view class="color-picker-container data-v-7ce08563" catchtap="{{S}}"><view class="picker-header data-v-7ce08563"><text class="picker-title data-v-7ce08563">选择颜色</text><view class="picker-close data-v-7ce08563" bindtap="{{A}}"><text class="close-icon data-v-7ce08563">×</text></view></view><view class="color-canvas-container data-v-7ce08563"><block wx:if="{{r0}}"><canvas class="color-canvas data-v-7ce08563" canvas-id="colorCanvas" style="{{'background-color:' + B}}" bindtouchstart="{{C}}" bindtouchmove="{{D}}"></canvas></block><view class="canvas-cursor data-v-7ce08563" style="{{E}}"></view></view><view class="hue-slider-container data-v-7ce08563"><block wx:if="{{r0}}"><canvas class="hue-slider data-v-7ce08563" canvas-id="hueCanvas" bindtouchstart="{{F}}" bindtouchmove="{{G}}"></canvas></block><view class="hue-cursor data-v-7ce08563" style="{{H}}"></view></view><view class="rgb-inputs data-v-7ce08563"><view class="rgb-group data-v-7ce08563"><text class="rgb-label data-v-7ce08563">R</text><input class="rgb-input data-v-7ce08563" type="number" bindinput="{{I}}" min="0" max="255" value="{{J}}"/></view><view class="rgb-group data-v-7ce08563"><text class="rgb-label data-v-7ce08563">G</text><input class="rgb-input data-v-7ce08563" type="number" bindinput="{{K}}" min="0" max="255" value="{{L}}"/></view><view class="rgb-group data-v-7ce08563"><text class="rgb-label data-v-7ce08563">B</text><input class="rgb-input data-v-7ce08563" type="number" bindinput="{{M}}" min="0" max="255" value="{{N}}"/></view></view><view class="picker-preview data-v-7ce08563"><view class="preview-color data-v-7ce08563" style="{{'background-color:' + O}}"></view><text class="hex-value data-v-7ce08563">{{P}}</text></view><view class="picker-actions data-v-7ce08563"><view class="picker-btn cancel data-v-7ce08563" bindtap="{{Q}}">取消</view><view class="picker-btn confirm data-v-7ce08563" bindtap="{{R}}">确认</view></view></view></view></view>