"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  name: "NationalGasPrice",
  data() {
    return {
      selectedProvince: "北京",
      selectedIndex: 0,
      lastUpdate: /* @__PURE__ */ new Date(),
      loading: false,
      gasPrices: {},
      toolService: new utils_toolService.ToolService(),
      provinces: [
        "北京",
        "上海",
        "天津",
        "重庆",
        "河北",
        "山西",
        "辽宁",
        "吉林",
        "黑龙江",
        "江苏",
        "浙江",
        "安徽",
        "福建",
        "江西",
        "山东",
        "河南",
        "湖北",
        "湖南",
        "广东",
        "海南",
        "四川",
        "贵州",
        "云南",
        "陕西",
        "甘肃",
        "青海",
        "内蒙古",
        "广西",
        "西藏",
        "宁夏",
        "新疆",
        "台湾",
        "香港",
        "澳门"
      ]
    };
  },
  computed: {
    currentPrices() {
      return this.gasPrices[this.selectedProvince] || {};
    },
    formatUpdateTime() {
      return this.lastUpdate.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit"
      });
    },
    // 动态生成本周价格趋势
    weeklyTrend() {
      const baseValue = this.selectedProvince.charCodeAt(0) % 10;
      const dayOfWeek = (/* @__PURE__ */ new Date()).getDay();
      const variation = (baseValue + dayOfWeek) % 20 - 10;
      const change = (variation * 0.01).toFixed(2);
      if (variation > 2) {
        return {
          value: `+${change}元/升`,
          class: "trend-up"
        };
      } else if (variation < -2) {
        return {
          value: `${change}元/升`,
          class: "trend-down"
        };
      } else {
        return {
          value: `${change}元/升`,
          class: "trend-neutral"
        };
      }
    },
    // 动态生成本月累计趋势
    monthlyTrend() {
      const baseValue = this.selectedProvince.charCodeAt(1) % 15;
      const currentMonth = (/* @__PURE__ */ new Date()).getMonth();
      const variation = (baseValue + currentMonth) % 30 - 15;
      const change = (variation * 0.01).toFixed(2);
      if (variation > 5) {
        return {
          value: `+${change}元/升`,
          class: "trend-up"
        };
      } else if (variation < -5) {
        return {
          value: `${change}元/升`,
          class: "trend-down"
        };
      } else {
        return {
          value: `${change}元/升`,
          class: "trend-neutral"
        };
      }
    },
    // 动态生成下次调价预测日期
    nextAdjustmentDate() {
      const today = /* @__PURE__ */ new Date();
      const currentDay = today.getDate();
      let nextDate = new Date(today);
      if (currentDay <= 15) {
        nextDate.setDate(28);
      } else {
        nextDate.setMonth(nextDate.getMonth() + 1, 12);
      }
      return `${nextDate.getMonth() + 1}月${nextDate.getDate()}日`;
    }
  },
  mounted() {
    this.loadGasPrices();
  },
  methods: {
    async loadGasPrices(province = null) {
      this.loading = true;
      try {
        const params = {
          type: "all",
          province: province || this.selectedProvince
          // 传递当前选中的省份
        };
        common_vendor.index.__f__("log", "at pages/tools/national-gas-price.vue:239", "查询油价参数:", params);
        const result = await this.toolService.queryGasPrice(params);
        common_vendor.index.__f__("log", "at pages/tools/national-gas-price.vue:241", "油价查询API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.provinces) {
            this.gasPrices = {};
            apiData.provinces.forEach((provinceData) => {
              this.gasPrices[provinceData.province] = provinceData.prices;
            });
            this.lastUpdate = new Date(apiData.updateTime || Date.now());
            common_vendor.index.showToast({
              title: `${province || this.selectedProvince}油价数据已更新`,
              icon: "success"
            });
          } else {
            throw new Error(apiData.message || "获取油价数据失败");
          }
        } else {
          throw new Error(result.message || "获取油价数据失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/national-gas-price.vue:265", "获取油价数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取油价数据失败",
          icon: "error",
          duration: 2e3
        });
        this.gasPrices = this.getDefaultGasPrices();
      } finally {
        this.loading = false;
      }
    },
    getDefaultGasPrices() {
      return {
        "北京": { "92号汽油": 7.84, "95号汽油": 8.35, "98号汽油": 9.35, "0号柴油": 7.57 },
        "上海": { "92号汽油": 7.81, "95号汽油": 8.31, "98号汽油": 9.31, "0号柴油": 7.54 },
        "天津": { "92号汽油": 7.83, "95号汽油": 8.32, "98号汽油": 9.32, "0号柴油": 7.56 },
        "重庆": { "92号汽油": 7.91, "95号汽油": 8.43, "98号汽油": 9.43, "0号柴油": 7.64 },
        "河北": { "92号汽油": 7.83, "95号汽油": 8.32, "98号汽油": 9.32, "0号柴油": 7.56 },
        "山西": { "92号汽油": 7.89, "95号汽油": 8.44, "98号汽油": 9.14, "0号柴油": 7.62 },
        "辽宁": { "92号汽油": 7.82, "95号汽油": 8.33, "98号汽油": 9.03, "0号柴油": 7.54 },
        "吉林": { "92号汽油": 7.81, "95号汽油": 8.38, "98号汽油": 9.08, "0号柴油": 7.55 },
        "黑龙江": { "92号汽油": 7.81, "95号汽油": 8.33, "98号汽油": 9.33, "0号柴油": 7.54 },
        "江苏": { "92号汽油": 7.83, "95号汽油": 8.33, "98号汽油": 9.33, "0号柴油": 7.56 },
        "浙江": { "92号汽油": 7.83, "95号汽油": 8.33, "98号汽油": 9.33, "0号柴油": 7.56 },
        "安徽": { "92号汽油": 7.82, "95号汽油": 8.33, "98号汽油": 9.28, "0号柴油": 7.56 },
        "福建": { "92号汽油": 7.84, "95号汽油": 8.36, "98号汽油": 9.36, "0号柴油": 7.58 },
        "江西": { "92号汽油": 7.82, "95号汽油": 8.32, "98号汽油": 9.42, "0号柴油": 7.57 },
        "山东": { "92号汽油": 7.83, "95号汽油": 8.35, "98号汽油": 9.05, "0号柴油": 7.55 },
        "河南": { "92号汽油": 7.85, "95号汽油": 8.37, "98号汽油": 9.37, "0号柴油": 7.58 },
        "湖北": { "92号汽油": 7.85, "95号汽油": 8.37, "98号汽油": 9.37, "0号柴油": 7.58 },
        "湖南": { "92号汽油": 7.81, "95号汽油": 8.31, "98号汽油": 9.21, "0号柴油": 7.57 },
        "广东": { "92号汽油": 7.86, "95号汽油": 8.44, "98号汽油": 9.54, "0号柴油": 7.58 },
        "海南": { "92号汽油": 8.96, "95号汽油": 9.52, "98号汽油": 10.62, "0号柴油": 8.67 },
        "四川": { "92号汽油": 7.9, "95号汽油": 8.43, "98号汽油": 9.23, "0号柴油": 7.63 },
        "贵州": { "92号汽油": 7.95, "95号汽油": 8.35, "98号汽油": 9.45, "0号柴油": 7.67 },
        "云南": { "92号汽油": 7.97, "95号汽油": 8.5, "98号汽油": 9.2, "0号柴油": 7.69 },
        "陕西": { "92号汽油": 7.75, "95号汽油": 8.23, "98号汽油": 9.23, "0号柴油": 7.52 },
        "甘肃": { "92号汽油": 7.83, "95号汽油": 8.35, "98号汽油": 8.95, "0号柴油": 7.54 },
        "青海": { "92号汽油": 7.88, "95号汽油": 8.41, "98号汽油": 9.31, "0号柴油": 7.6 },
        "内蒙古": { "92号汽油": 7.79, "95号汽油": 8.31, "98号汽油": 9.11, "0号柴油": 7.51 },
        "广西": { "92号汽油": 7.91, "95号汽油": 8.45, "98号汽油": 9.55, "0号柴油": 7.62 },
        "西藏": { "92号汽油": 8.75, "95号汽油": 9.22, "98号汽油": 10.12, "0号柴油": 8.08 },
        "宁夏": { "92号汽油": 7.76, "95号汽油": 8.24, "98号汽油": 9.24, "0号柴油": 7.52 },
        "新疆": { "92号汽油": 7.7, "95号汽油": 8.25, "98号汽油": 9.25, "0号柴油": 7.47 },
        "台湾": { "92号汽油": 8.2, "95号汽油": 8.7, "98号汽油": 9.7, "0号柴油": 7.9 },
        "香港": { "92号汽油": 16.5, "95号汽油": 17.2, "98号汽油": 18.5, "0号柴油": 15.8 },
        "澳门": { "92号汽油": 15.8, "95号汽油": 16.5, "98号汽油": 17.8, "0号柴油": 15.2 }
      };
    },
    onProvinceChange(e) {
      this.selectedIndex = e.detail.value;
      this.selectedProvince = this.provinces[e.detail.value];
      this.loadGasPrices(this.selectedProvince);
      common_vendor.index.showToast({
        title: `正在查询${this.selectedProvince}油价`,
        icon: "loading",
        duration: 1e3
      });
    },
    async refreshPrices() {
      await this.loadGasPrices(this.selectedProvince);
      common_vendor.index.showToast({
        title: `${this.selectedProvince}价格已更新`,
        icon: "success"
      });
    },
    calculateFuelCost(price) {
      const tankCapacity = 50;
      return (price * tankCapacity).toFixed(2);
    },
    getFuelIcon(fuelType) {
      const icons = {
        "92号汽油": "🟢",
        "95号汽油": "🟡",
        "98号汽油": "🔴",
        "0号柴油": "🟤"
      };
      return icons[fuelType] || "⛽";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.selectedProvince),
    b: $data.provinces,
    c: $data.selectedIndex,
    d: common_vendor.o((...args) => $options.onProvinceChange && $options.onProvinceChange(...args)),
    e: common_vendor.t($data.selectedProvince),
    f: common_vendor.o((...args) => $options.refreshPrices && $options.refreshPrices(...args)),
    g: common_vendor.f($options.currentPrices, (price, fuelType, i0) => {
      return {
        a: common_vendor.t(fuelType),
        b: common_vendor.t(price.toFixed(2)),
        c: common_vendor.t($options.calculateFuelCost(price)),
        d: common_vendor.t($options.getFuelIcon(fuelType)),
        e: fuelType
      };
    }),
    h: common_vendor.n($options.weeklyTrend.class),
    i: common_vendor.t($options.weeklyTrend.value),
    j: common_vendor.n($options.weeklyTrend.class),
    k: common_vendor.n($options.weeklyTrend.class),
    l: common_vendor.n($options.monthlyTrend.class),
    m: common_vendor.t($options.monthlyTrend.value),
    n: common_vendor.n($options.monthlyTrend.class),
    o: common_vendor.n($options.monthlyTrend.class),
    p: common_vendor.t($options.nextAdjustmentDate),
    q: common_vendor.t($options.formatUpdateTime)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-74a8804b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/national-gas-price.js.map
