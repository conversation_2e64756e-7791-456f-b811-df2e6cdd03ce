"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  name: "VehiclePriceQuery",
  data() {
    return {
      searchTerm: "",
      selectedBrandIndex: 0,
      results: [],
      hasSearched: false,
      loading: false,
      toolService: new utils_toolService.ToolService(),
      brands: [
        { id: "all", name: "全部品牌" },
        { id: "xiaomi", name: "小米" },
        { id: "bmw", name: "宝马" },
        { id: "benz", name: "奔驰" },
        { id: "audi", name: "奥迪" },
        { id: "toyota", name: "丰田" },
        { id: "honda", name: "本田" },
        { id: "volkswagen", name: "大众" }
      ]
    };
  },
  computed: {
    brandNames() {
      return this.brands.map((brand) => brand.name);
    }
  },
  onLoad() {
    common_vendor.index.setNavigationBarTitle({
      title: "车辆价格查询"
    });
    this.loadDefaultVehicles();
  },
  methods: {
    // 判断是否为图片URL
    isImageUrl(imageStr) {
      if (!imageStr)
        return false;
      return imageStr.includes("http://") || imageStr.includes("https://") || imageStr.includes(".png") || imageStr.includes(".jpg") || imageStr.includes(".jpeg");
    },
    // 加载默认车辆信息（小米SU7和YU7）
    async loadDefaultVehicles() {
      this.loading = true;
      try {
        const su7Results = await this.searchSpecificVehicle("小米SU7");
        const yu7Results = await this.searchSpecificVehicle("小米YU7");
        this.results = [...su7Results, ...yu7Results];
        if (this.results.length > 0) {
          utils_index.showSuccess(`默认加载了${this.results.length}个小米车型`);
        } else {
          utils_index.showError("暂无小米车型数据");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/vehicle-price-query.vue:199", "加载默认车辆失败:", error);
        utils_index.showError("加载默认车辆失败");
      } finally {
        this.loading = false;
      }
    },
    // 搜索特定车辆
    async searchSpecificVehicle(keyword) {
      try {
        const params = {
          keyword,
          brand: "all",
          type: "search"
        };
        const result = await this.toolService.queryVehiclePrice(params);
        common_vendor.index.__f__("log", "at pages/tools/vehicle-price-query.vue:216", `搜索${keyword}结果:`, result);
        if (result.code === 200 && result.data && result.data.success && result.data.vehicles) {
          return result.data.vehicles;
        }
        return [];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/vehicle-price-query.vue:223", `搜索${keyword}失败:`, error);
        return [];
      }
    },
    async handleSearch() {
      if (!this.searchTerm.trim() && this.selectedBrandIndex === 0) {
        utils_index.showError("请输入车型名称或选择品牌");
        return;
      }
      this.loading = true;
      this.hasSearched = true;
      try {
        const searchType = this.getSearchType(this.searchTerm.trim());
        const params = {
          keyword: this.searchTerm.trim(),
          brand: this.brands[this.selectedBrandIndex].id,
          type: "search",
          searchType
          // 添加搜索类型参数
        };
        common_vendor.index.__f__("log", "at pages/tools/vehicle-price-query.vue:248", "车辆搜索参数:", params);
        const result = await this.toolService.queryVehiclePrice(params);
        common_vendor.index.__f__("log", "at pages/tools/vehicle-price-query.vue:250", "车辆价格查询API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.vehicles) {
            this.results = apiData.vehicles;
            if (this.results.length === 0) {
              utils_index.showError("未找到相关车辆信息");
            } else {
              utils_index.showSuccess(`找到 ${this.results.length} 个相关车型`);
            }
          } else {
            this.results = [];
            utils_index.showError(apiData.message || "未找到车辆信息");
          }
        } else {
          this.results = [];
          utils_index.showError(result.message || "查询失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/vehicle-price-query.vue:271", "车辆价格查询失败:", error);
        this.results = [];
        utils_index.showError(error.message || "查询失败，请重试");
      } finally {
        this.loading = false;
      }
    },
    // 判断搜索类型
    getSearchType(searchTerm) {
      const brands = ["宝马", "奔驰", "奥迪", "丰田", "本田", "大众", "小米", "比亚迪", "特斯拉", "BMW", "Benz", "Audi", "Toyota", "Honda", "Volkswagen", "Xiaomi", "BYD", "Tesla"];
      if (brands.some((brand) => searchTerm.toLowerCase().includes(brand.toLowerCase()) || brand.toLowerCase().includes(searchTerm.toLowerCase()))) {
        return "fuzzy";
      }
      return "exact";
    },
    onBrandChange(e) {
      this.selectedBrandIndex = e.detail.value;
      if (this.hasSearched) {
        this.handleSearch();
      }
    },
    formatPrice(price) {
      if (!price)
        return "暂无";
      return (price / 1e4).toFixed(1) + "万";
    },
    getTrendIcon(trend) {
      switch (trend) {
        case "up":
          return "📈";
        case "down":
          return "📉";
        case "stable":
          return "➡️";
        default:
          return "➡️";
      }
    },
    getTrendText(trend) {
      switch (trend) {
        case "up":
          return "价格上涨";
        case "down":
          return "价格下降";
        case "stable":
          return "价格稳定";
        default:
          return "价格稳定";
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchTerm,
    c: common_vendor.o(($event) => $data.searchTerm = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.t($data.brands[$data.selectedBrandIndex].name),
    f: $data.selectedBrandIndex,
    g: $options.brandNames,
    h: common_vendor.o((...args) => $options.onBrandChange && $options.onBrandChange(...args)),
    i: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    j: $data.results.length > 0
  }, $data.results.length > 0 ? {
    k: common_vendor.f($data.results, (vehicle, k0, i0) => {
      return common_vendor.e({
        a: $options.isImageUrl(vehicle.image)
      }, $options.isImageUrl(vehicle.image) ? {
        b: vehicle.image
      } : {
        c: common_vendor.t(vehicle.image)
      }, {
        d: common_vendor.t(vehicle.name),
        e: common_vendor.t(vehicle.model),
        f: common_vendor.t(vehicle.category),
        g: common_vendor.t($options.formatPrice(vehicle.price.min)),
        h: common_vendor.t($options.formatPrice(vehicle.price.max)),
        i: common_vendor.t($options.formatPrice(vehicle.marketPrice)),
        j: common_vendor.t($options.getTrendIcon(vehicle.trend)),
        k: common_vendor.t($options.getTrendText(vehicle.trend)),
        l: common_vendor.f(vehicle.specs, (spec, index, i1) => {
          return {
            a: common_vendor.t(spec),
            b: index
          };
        }),
        m: vehicle.id
      });
    })
  } : {}, {
    l: $data.results.length === 0 && $data.searchTerm && $data.hasSearched
  }, $data.results.length === 0 && $data.searchTerm && $data.hasSearched ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0ead2f46"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/vehicle-price-query.js.map
