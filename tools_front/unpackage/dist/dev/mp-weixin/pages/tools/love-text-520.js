"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      selectedResult: "",
      results: [],
      favorites: [],
      examples: [
        "我爱你",
        "生日快乐",
        "永远爱你",
        "520",
        "1314",
        "亲爱的",
        "宝贝",
        "想你了"
      ],
      stylePreview: [
        { text: "💕我爱你💕", name: "经典爱心" },
        { text: "❤️我爱你❤️", name: "红心装饰" },
        { text: "💖我爱你💖", name: "闪亮爱心" },
        { text: "💝我爱你💝", name: "礼物爱心" },
        { text: "🌹我爱你🌹", name: "玫瑰装饰" },
        { text: "💞我爱你💞", name: "旋转爱心" }
      ]
    };
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    generateLoveText() {
      if (!this.inputText.trim()) {
        common_vendor.index.showToast({
          title: "请输入文字",
          icon: "none",
          duration: 1500
        });
        return;
      }
      const text = this.inputText.trim();
      const loveStyles = [
        `💕${text}💕`,
        `❤️${text}❤️`,
        `💖${text}💖`,
        `💝${text}💝`,
        `💗${text}💗`,
        `💘${text}💘`,
        `💞${text}💞`,
        `💓${text}💓`,
        `🌹${text}🌹`,
        `💐${text}💐`,
        `💋${text}💋`,
        `😘${text}😘`,
        `🥰${text}🥰`,
        `😍${text}😍`,
        `💕💕${text}💕💕`,
        `❤️💛💚💙💜${text}💜💙💚💛❤️`,
        `🌹💕${text}💕🌹`,
        `💖✨${text}✨💖`,
        `💝🎀${text}🎀💝`,
        `💞🌸${text}🌸💞`
      ];
      this.results = loveStyles;
      common_vendor.index.showToast({
        title: "生成成功",
        icon: "success",
        duration: 1500
      });
    },
    selectResult(result) {
      this.selectedResult = result;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    shareText(text) {
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 1,
        summary: text,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success",
            duration: 1500
          });
        },
        fail: () => {
          this.copyText(text);
        }
      });
    },
    clearInput() {
      this.inputText = "";
      this.results = [];
      this.selectedResult = "";
    },
    useExample(example) {
      this.inputText = example;
      this.generateLoveText();
    },
    addToFavorites(text) {
      const exists = this.favorites.find((item) => item === text);
      if (!exists) {
        this.favorites.push(text);
        this.saveData();
        common_vendor.index.showToast({
          title: "已添加到收藏",
          icon: "success",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "已存在收藏中",
          icon: "none",
          duration: 1500
        });
      }
    },
    removeFromFavorites(index) {
      this.favorites.splice(index, 1);
      this.saveData();
      common_vendor.index.showToast({
        title: "已从收藏中移除",
        icon: "success",
        duration: 1500
      });
    },
    saveData() {
      common_vendor.index.setStorageSync("love-text-520-favorites", this.favorites);
    },
    loadData() {
      const favorites = common_vendor.index.getStorageSync("love-text-520-favorites");
      if (favorites) {
        this.favorites = favorites;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputText,
    b: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    c: common_vendor.t($data.inputText.length),
    d: common_vendor.o((...args) => $options.generateLoveText && $options.generateLoveText(...args)),
    e: $data.results.length > 0
  }, $data.results.length > 0 ? {
    f: common_vendor.f($data.results, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: common_vendor.o(($event) => $options.copyText(result), index),
        c: index,
        d: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    g: common_vendor.f($data.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.useExample(example), index)
      };
    }),
    h: common_vendor.f($data.stylePreview, (style, index, i0) => {
      return {
        a: common_vendor.t(style.text),
        b: common_vendor.t(style.name),
        c: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4a3237a1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/love-text-520.js.map
