"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedCategory: "heart",
      selectedEmoji: null,
      favorites: [],
      categories: [
        { key: "heart", name: "爱心", icon: "♥" },
        { key: "kiss", name: "亲吻", icon: "♡" },
        { key: "couple", name: "情侣", icon: "❣" },
        { key: "romantic", name: "浪漫", icon: "♪" },
        { key: "wedding", name: "婚礼", icon: "☆" }
      ],
      emojis: {
        heart: [
          { symbol: "❤️", name: "红心", description: "经典红色爱心，表达深情" },
          { symbol: "💛", name: "黄心", description: "温暖的黄色爱心，友谊之爱" },
          { symbol: "💚", name: "绿心", description: "清新的绿色爱心，自然之爱" },
          { symbol: "💙", name: "蓝心", description: "深情的蓝色爱心，忠诚之爱" },
          { symbol: "💜", name: "紫心", description: "神秘的紫色爱心，高贵之爱" },
          { symbol: "🖤", name: "黑心", description: "深沉的黑色爱心，独特之爱" },
          { symbol: "🤍", name: "白心", description: "纯洁的白色爱心，纯真之爱" },
          { symbol: "🤎", name: "棕心", description: "温暖的棕色爱心，稳重之爱" },
          { symbol: "💖", name: "闪亮心", description: "闪闪发光的爱心" },
          { symbol: "💕", name: "双心", description: "两颗粉色爱心" },
          { symbol: "💞", name: "旋转心", description: "旋转的爱心" },
          { symbol: "💓", name: "心跳", description: "跳动的爱心" },
          { symbol: "💗", name: "成长心", description: "成长的爱心" },
          { symbol: "💘", name: "丘比特", description: "被丘比特射中的心" },
          { symbol: "💝", name: "礼物心", description: "礼盒装的爱心" },
          { symbol: "💟", name: "心形装饰", description: "心形装饰符号" }
        ],
        kiss: [
          { symbol: "💋", name: "嘴唇", description: "红色嘴唇印记" },
          { symbol: "😘", name: "飞吻", description: "抛飞吻的表情" },
          { symbol: "😗", name: "亲吻", description: "亲吻的表情" },
          { symbol: "😙", name: "微笑吻", description: "微笑着亲吻" },
          { symbol: "😚", name: "闭眼吻", description: "闭眼亲吻" },
          { symbol: "🥰", name: "爱心眼", description: "眼中有爱心" },
          { symbol: "😍", name: "心形眼", description: "心形眼睛" },
          { symbol: "🤩", name: "星星眼", description: "星星眼睛" }
        ],
        couple: [
          { symbol: "👫", name: "情侣", description: "男女情侣" },
          { symbol: "👬", name: "男性情侣", description: "男性情侣" },
          { symbol: "👭", name: "女性情侣", description: "女性情侣" },
          { symbol: "💏", name: "亲吻情侣", description: "亲吻的情侣" },
          { symbol: "💑", name: "恋人", description: "恋爱中的情侣" },
          { symbol: "👩‍❤️‍👨", name: "爱心情侣", description: "有爱心的情侣" },
          { symbol: "👨‍❤️‍👨", name: "男性爱侣", description: "男性爱侣" },
          { symbol: "👩‍❤️‍👩", name: "女性爱侣", description: "女性爱侣" }
        ],
        romantic: [
          { symbol: "🌹", name: "玫瑰", description: "红色玫瑰花" },
          { symbol: "🌺", name: "花朵", description: "美丽的花朵" },
          { symbol: "🌸", name: "樱花", description: "粉色樱花" },
          { symbol: "🌼", name: "雏菊", description: "白色雏菊" },
          { symbol: "🌻", name: "向日葵", description: "向日葵" },
          { symbol: "🌷", name: "郁金香", description: "郁金香" },
          { symbol: "💐", name: "花束", description: "美丽的花束" },
          { symbol: "🎁", name: "礼物", description: "包装好的礼物" },
          { symbol: "🍫", name: "巧克力", description: "甜蜜的巧克力" },
          { symbol: "🍰", name: "蛋糕", description: "生日蛋糕" },
          { symbol: "🥂", name: "干杯", description: "香槟干杯" },
          { symbol: "🕯️", name: "蜡烛", description: "浪漫的蜡烛" }
        ],
        wedding: [
          { symbol: "💒", name: "教堂", description: "婚礼教堂" },
          { symbol: "👰", name: "新娘", description: "穿婚纱的新娘" },
          { symbol: "🤵", name: "新郎", description: "穿礼服的新郎" },
          { symbol: "💍", name: "戒指", description: "结婚戒指" },
          { symbol: "👑", name: "皇冠", description: "公主皇冠" },
          { symbol: "🎊", name: "彩带", description: "庆祝彩带" },
          { symbol: "🎉", name: "派对", description: "庆祝派对" },
          { symbol: "🥳", name: "庆祝", description: "庆祝表情" }
        ]
      },
      combinations: [
        { text: "💖💕💞", name: "三心连环" },
        { text: "❤️🧡💛💚💙💜", name: "彩虹爱心" },
        { text: "💋💖💋", name: "亲吻爱心" },
        { text: "🌹💕🌹", name: "玫瑰爱心" },
        { text: "💝🎁💝", name: "礼物爱心" },
        { text: "😘💋😘", name: "飞吻组合" },
        { text: "👫💕👫", name: "情侣爱心" },
        { text: "💒💍💒", name: "婚礼组合" }
      ]
    };
  },
  onLoad() {
    this.loadFavorites();
  },
  methods: {
    selectCategory(key) {
      this.selectedCategory = key;
    },
    getCurrentCategoryIcon() {
      const category = this.categories.find((c) => c.key === this.selectedCategory);
      return category ? category.icon : "♥";
    },
    getCurrentCategoryName() {
      const category = this.categories.find((c) => c.key === this.selectedCategory);
      return category ? category.name : "爱心";
    },
    getCurrentEmojis() {
      return this.emojis[this.selectedCategory] || [];
    },
    selectEmoji(emoji) {
      this.selectedEmoji = emoji;
    },
    copyEmoji(symbol) {
      common_vendor.index.setClipboardData({
        data: symbol,
        success: () => {
          common_vendor.index.showToast({
            title: "表情已复制",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    addToFavorites(emoji) {
      const exists = this.favorites.find((item) => item.symbol === emoji.symbol);
      if (!exists) {
        this.favorites.push(emoji);
        this.saveFavorites();
        common_vendor.index.showToast({
          title: "已添加到收藏",
          icon: "success",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "已存在收藏中",
          icon: "none",
          duration: 1500
        });
      }
    },
    removeFromFavorites(index) {
      this.favorites.splice(index, 1);
      this.saveFavorites();
      common_vendor.index.showToast({
        title: "已从收藏中移除",
        icon: "success",
        duration: 1500
      });
    },
    saveFavorites() {
      common_vendor.index.setStorageSync("love-emoji-favorites", this.favorites);
    },
    loadFavorites() {
      const saved = common_vendor.index.getStorageSync("love-emoji-favorites");
      if (saved) {
        this.favorites = saved;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.categories, (category, index, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: index,
        d: $data.selectedCategory === category.key ? 1 : "",
        e: common_vendor.o(($event) => $options.selectCategory(category.key), index)
      };
    }),
    b: common_vendor.t($options.getCurrentCategoryIcon()),
    c: common_vendor.t($options.getCurrentCategoryName()),
    d: common_vendor.t($options.getCurrentEmojis().length),
    e: common_vendor.f($options.getCurrentEmojis(), (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji.symbol),
        b: common_vendor.t(emoji.name),
        c: index,
        d: common_vendor.o(($event) => $options.selectEmoji(emoji), index)
      };
    }),
    f: $data.selectedEmoji
  }, $data.selectedEmoji ? {
    g: common_vendor.t($data.selectedEmoji.symbol),
    h: common_vendor.t($data.selectedEmoji.name),
    i: common_vendor.t($data.selectedEmoji.description),
    j: common_vendor.o(($event) => $options.copyEmoji($data.selectedEmoji.symbol)),
    k: common_vendor.o(($event) => $options.addToFavorites($data.selectedEmoji))
  } : {}, {
    l: $data.favorites.length > 0
  }, $data.favorites.length > 0 ? {
    m: common_vendor.t($data.favorites.length),
    n: common_vendor.f($data.favorites, (favorite, index, i0) => {
      return {
        a: common_vendor.t(favorite.symbol),
        b: common_vendor.t(favorite.name),
        c: common_vendor.o(($event) => $options.copyEmoji(favorite.symbol), index),
        d: common_vendor.o(($event) => $options.removeFromFavorites(index), index),
        e: index,
        f: common_vendor.o(($event) => $options.selectEmoji(favorite), index)
      };
    })
  } : {}, {
    o: common_vendor.f($data.combinations, (combo, index, i0) => {
      return {
        a: common_vendor.t(combo.text),
        b: common_vendor.t(combo.name),
        c: index,
        d: common_vendor.o(($event) => $options.copyEmoji(combo.text), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b56fba27"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/love-emoji.js.map
