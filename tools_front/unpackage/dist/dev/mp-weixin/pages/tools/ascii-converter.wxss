/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b72d3fdb {
  display: flex;
}
.flex-1.data-v-b72d3fdb {
  flex: 1;
}
.items-center.data-v-b72d3fdb {
  align-items: center;
}
.justify-center.data-v-b72d3fdb {
  justify-content: center;
}
.justify-between.data-v-b72d3fdb {
  justify-content: space-between;
}
.text-center.data-v-b72d3fdb {
  text-align: center;
}
.rounded.data-v-b72d3fdb {
  border-radius: 3px;
}
.rounded-lg.data-v-b72d3fdb {
  border-radius: 6px;
}
.shadow.data-v-b72d3fdb {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b72d3fdb {
  padding: 16rpx;
}
.m-4.data-v-b72d3fdb {
  margin: 16rpx;
}
.mb-4.data-v-b72d3fdb {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b72d3fdb {
  margin-top: 16rpx;
}
.ascii-converter.data-v-b72d3fdb {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
  box-sizing: border-box;
  width: 100%;
}
.header-card.data-v-b72d3fdb,
.mode-card.data-v-b72d3fdb,
.input-card.data-v-b72d3fdb,
.output-card.data-v-b72d3fdb,
.ascii-table-card.data-v-b72d3fdb,
.tools-card.data-v-b72d3fdb,
.tips-card.data-v-b72d3fdb {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-b72d3fdb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
}
.card-title.data-v-b72d3fdb {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-b72d3fdb {
  padding: 30rpx;
  box-sizing: border-box;
  width: 100%;
}
.header-info.data-v-b72d3fdb {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.header-icon.data-v-b72d3fdb {
  font-size: 60rpx;
}
.header-text.data-v-b72d3fdb {
  flex: 1;
}
.header-title.data-v-b72d3fdb {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-desc.data-v-b72d3fdb {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.mode-selector.data-v-b72d3fdb {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
}
.mode-btn.data-v-b72d3fdb {
  flex: 1;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  background: #ffffff;
  color: #374151;
  transition: all 0.2s;
}
.mode-btn.active.data-v-b72d3fdb {
  background: #111827;
  border-color: #111827;
  color: #ffffff;
}
.mode-switch.data-v-b72d3fdb {
  display: flex;
  align-items: center;
  justify-content: center;
}
.swap-btn.data-v-b72d3fdb {
  width: 60rpx;
  height: 60rpx;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  background: #ffffff;
  transition: all 0.2s;
  cursor: pointer;
}
.swap-btn.data-v-b72d3fdb:active {
  background: #f3f4f6;
  transform: scale(0.96);
}
.swap-icon.data-v-b72d3fdb {
  font-size: 32rpx;
  color: #666;
  font-weight: normal;
  transition: transform 0.2s;
}
.swap-btn:active .swap-icon.data-v-b72d3fdb {
  transform: rotate(180deg);
}
.clear-btn.data-v-b72d3fdb,
.toggle-btn.data-v-b72d3fdb {
  font-size: 24rpx;
  color: #ef4444;
  background: none;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}
.toggle-btn.data-v-b72d3fdb {
  color: #3b82f6;
}
.input-section.data-v-b72d3fdb,
.output-section.data-v-b72d3fdb {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}
.section-label.data-v-b72d3fdb {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.text-input.data-v-b72d3fdb {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #1a1a1a;
  background: #ffffff;
  min-height: 120rpx;
  box-sizing: border-box;
}
.convert-btn.data-v-b72d3fdb {
  margin-top: 24rpx;
  width: 100%;
  padding: 24rpx;
  background: #111827;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s;
}
.convert-btn.disabled.data-v-b72d3fdb {
  opacity: 0.5;
  background: #e5e7eb;
  color: #9ca3af;
}
.convert-btn.data-v-b72d3fdb:active:not(.disabled) {
  transform: scale(0.98);
  background: #000000;
}
.output-header.data-v-b72d3fdb {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  margin-bottom: 16rpx;
}
.copy-btn.data-v-b72d3fdb {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #3b82f6;
  background: none;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: all 0.2s;
}
.copy-btn.data-v-b72d3fdb:active {
  opacity: 0.8;
}
.output-display.data-v-b72d3fdb {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
}
.output-text.data-v-b72d3fdb {
  font-size: 28rpx;
  line-height: 1.5;
  color: #1a1a1a;
  word-break: break-all;
  font-family: monospace;
}
.table-container.data-v-b72d3fdb {
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}
.table-scroll.data-v-b72d3fdb {
  width: 100%;
}
.ascii-table.data-v-b72d3fdb {
  min-width: 600rpx;
}
.table-header.data-v-b72d3fdb {
  display: flex;
  background: #374151;
  color: white;
}
.table-row.data-v-b72d3fdb {
  display: flex;
  border-bottom: 1rpx solid #e5e7eb;
  background: white;
}
.table-row.highlight.data-v-b72d3fdb {
  background: #fef3c7;
}
.table-row.data-v-b72d3fdb:active {
  background: #f3f4f6;
}
.table-cell.data-v-b72d3fdb {
  flex: 1;
  padding: 16rpx 12rpx;
  font-size: 24rpx;
  text-align: center;
  min-width: 120rpx;
}
.table-cell.header.data-v-b72d3fdb {
  font-weight: 600;
  background: #374151;
  color: white;
}
.table-cell.char.data-v-b72d3fdb {
  font-family: "Courier New", monospace;
  font-weight: 600;
}
.table-cell.code.data-v-b72d3fdb {
  font-family: "Courier New", monospace;
  color: #3b82f6;
}
.table-cell.hex.data-v-b72d3fdb {
  font-family: "Courier New", monospace;
  color: #059669;
}
.table-cell.desc.data-v-b72d3fdb {
  color: #6b7280;
  font-size: 22rpx;
}
.tips-list.data-v-b72d3fdb {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.tip-item.data-v-b72d3fdb {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.tip-bullet.data-v-b72d3fdb {
  font-size: 28rpx;
  color: #667eea;
  margin-top: 4rpx;
}
.tip-text.data-v-b72d3fdb {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
}
.tip-bold.data-v-b72d3fdb {
  font-weight: 600;
  color: #1a1a1a;
}
.text-input.full-width.data-v-b72d3fdb {
  width: 100%;
  box-sizing: border-box;
}
.ascii-table-header.data-v-b72d3fdb {
  position: relative;
}
.right-btn.data-v-b72d3fdb {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
}
.input-header.data-v-b72d3fdb {
  position: relative;
}
.clear-btn.right-btn.data-v-b72d3fdb {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  color: #ef4444;
  background: none;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
}
.clear-btn.right-btn.data-v-b72d3fdb:hover, .clear-btn.right-btn.data-v-b72d3fdb:active {
  color: #fff;
  background: #ef4444;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}
.input-card.data-v-b72d3fdb,
.output-card.data-v-b72d3fdb {
  width: 100%;
  box-sizing: border-box;
}