/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-9ae54d75 {
  display: flex;
}
.flex-1.data-v-9ae54d75 {
  flex: 1;
}
.items-center.data-v-9ae54d75 {
  align-items: center;
}
.justify-center.data-v-9ae54d75 {
  justify-content: center;
}
.justify-between.data-v-9ae54d75 {
  justify-content: space-between;
}
.text-center.data-v-9ae54d75 {
  text-align: center;
}
.rounded.data-v-9ae54d75 {
  border-radius: 3px;
}
.rounded-lg.data-v-9ae54d75 {
  border-radius: 6px;
}
.shadow.data-v-9ae54d75 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-9ae54d75 {
  padding: 16rpx;
}
.m-4.data-v-9ae54d75 {
  margin: 16rpx;
}
.mb-4.data-v-9ae54d75 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-9ae54d75 {
  margin-top: 16rpx;
}
.case-converter.data-v-9ae54d75 {
  min-height: 100vh;
  background: #f9fafb;
}
.container.data-v-9ae54d75 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.input-section.data-v-9ae54d75, .result-section.data-v-9ae54d75, .help-section.data-v-9ae54d75 {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.input-section.data-v-9ae54d75:hover, .result-section.data-v-9ae54d75:hover, .help-section.data-v-9ae54d75:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.section-header.data-v-9ae54d75 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-9ae54d75 {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.section-title.data-v-9ae54d75 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-wrapper.data-v-9ae54d75 {
  position: relative;
}
.text-input.data-v-9ae54d75 {
  width: 100%;
  min-height: 160rpx;
  padding: 24rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 30rpx;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
  resize: none;
  text-align: left;
}
.text-input.data-v-9ae54d75:focus {
  border-color: #6366f1;
  background: #fff;
  box-shadow: 0 0 0 3rpx rgba(99, 102, 241, 0.15);
  outline: none;
}
.result-section.data-v-9ae54d75 {
  margin-bottom: 32rpx;
}
.result-card.data-v-9ae54d75 {
  margin-bottom: 20rpx;
  background: #f3f4fd;
  border-radius: 16rpx;
  border-left: 6rpx solid #6366f1;
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.06);
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.result-card.data-v-9ae54d75:hover {
  background: #ede9fe;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.1);
}
.result-card.data-v-9ae54d75:active {
  transform: translateY(0) scale(0.98);
}
.result-header.data-v-9ae54d75 {
  padding: 25rpx 30rpx 15rpx;
}
.result-title.data-v-9ae54d75 {
  font-size: 26rpx;
  font-weight: 500;
  color: #6366f1;
}
.result-content.data-v-9ae54d75 {
  padding: 0 30rpx 25rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.result-text.data-v-9ae54d75 {
  flex: 1;
  font-family: "Courier New", monospace;
  font-size: 28rpx;
  color: #1f2937;
  word-break: break-all;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  padding: 18rpx 20rpx;
  border: 1rpx solid #e5e7eb;
  min-height: 60rpx;
  display: flex;
  align-items: center;
}
.copy-btn.data-v-9ae54d75 {
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(99, 102, 241, 0.08);
  margin-left: 16rpx;
  cursor: pointer;
}
.copy-btn.data-v-9ae54d75:hover {
  background: #fff;
  transform: scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.12);
}
.copy-btn.disabled.data-v-9ae54d75 {
  background: #e5e7eb;
  color: #bdbdbd;
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}
.copy-icon.data-v-9ae54d75 {
  font-size: 24rpx;
  color: #6366f1;
}
.help-content.data-v-9ae54d75 {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1rpx solid #e5e7eb;
}
.help-item.data-v-9ae54d75 {
  display: block;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 28rpx;
}
.help-item.data-v-9ae54d75:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #6366f1;
  font-weight: bold;
}
.help-item.data-v-9ae54d75:last-child {
  margin-bottom: 0;
}