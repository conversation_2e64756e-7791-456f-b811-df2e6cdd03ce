"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  __name: "id-photo-maker",
  setup(__props) {
    const toolService = new utils_toolService.ToolService();
    const selectedImage = common_vendor.ref(null);
    const processedImage = common_vendor.ref(null);
    const showPreview = common_vendor.ref(false);
    const selectedSize = common_vendor.reactive({ id: 1, name: "一寸照", width: "2.5", height: "3.5" });
    const selectedBgColor = common_vendor.reactive({ id: 1, name: "白色", value: "#ffffff" });
    const photoSizes = [
      { id: 1, name: "一寸照", width: "2.5", height: "3.5" },
      { id: 2, name: "二寸照", width: "3.5", height: "5.3" },
      { id: 3, name: "护照照", width: "3.3", height: "4.8" },
      { id: 4, name: "驾照照", width: "2.2", height: "3.2" }
    ];
    const backgroundColors = [
      { id: 1, name: "白色", value: "#ffffff" },
      { id: 2, name: "蓝色", value: "#6366f1" },
      { id: 3, name: "红色", value: "#ef4444" }
    ];
    const instructions = [
      "选择或拍摄一张清晰的正面照片",
      "选择合适的证件照尺寸规格",
      "选择所需的背景颜色",
      "调整美颜和优化设置",
      "点击制作并保存证件照"
    ];
    const selectImage = async () => {
      try {
        const res = await common_vendor.index.chooseImage({
          count: 1,
          // 最多选择1张图片
          sizeType: ["original", "compressed"],
          // 可选择原图或压缩后的图片
          sourceType: ["album"]
          // 从相册选择
        });
        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          const tempFilePath = res.tempFilePaths[0];
          selectedImage.value = tempFilePath;
          await uploadImageToQiniu(tempFilePath);
          common_vendor.index.showToast({
            title: "选择成功",
            icon: "success",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:538", "选择图片失败:", error);
        common_vendor.index.showToast({
          title: "选择失败",
          icon: "error",
          duration: 2e3
        });
      }
    };
    const takePhoto = async () => {
      try {
        const res = await common_vendor.index.chooseImage({
          count: 1,
          sizeType: ["original", "compressed"],
          sourceType: ["camera"]
          // 使用相机
        });
        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          const tempFilePath = res.tempFilePaths[0];
          selectedImage.value = tempFilePath;
          await uploadImageToQiniu(tempFilePath);
          common_vendor.index.showToast({
            title: "拍照成功",
            icon: "success",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:570", "拍照失败:", error);
        common_vendor.index.showToast({
          title: "拍照失败",
          icon: "error",
          duration: 2e3
        });
      }
    };
    const uploadImageToQiniu = async (filePath) => {
      try {
        utils_index.showLoading("正在上传图片...");
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:584", "开始上传图片:", filePath);
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:585", "上传URL:", toolService.baseUrl + "/api/upload/idphoto");
        const uploadResult = await new Promise((resolve, reject) => {
          common_vendor.index.uploadFile({
            url: toolService.baseUrl + "/api/upload/idphoto",
            filePath,
            name: "file",
            timeout: 6e4,
            // 设置60秒超时
            header: {
              "Accept": "application/json"
            },
            success: (res) => {
              common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:598", "上传响应:", res);
              try {
                const result = JSON.parse(res.data);
                if (result.code === 200) {
                  resolve(result.data);
                } else {
                  reject(new Error(result.message || "上传失败"));
                }
              } catch (parseError) {
                common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:607", "解析响应失败:", parseError);
                reject(new Error("服务器响应格式错误"));
              }
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:612", "上传请求失败:", err);
              reject(err);
            }
          });
        });
        uploadedImageUrl.value = uploadResult.url;
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:621", "图片上传成功:", uploadResult);
        utils_index.hideLoading();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:625", "图片上传失败:", error);
        utils_index.hideLoading();
        let errorMessage = "图片上传失败";
        if (error.errMsg) {
          if (error.errMsg.includes("timeout")) {
            errorMessage = "上传超时，请检查网络连接";
          } else if (error.errMsg.includes("fail")) {
            errorMessage = "网络连接失败，请检查服务器状态";
          } else {
            errorMessage = `上传失败：${error.errMsg}`;
          }
        } else if (error.message) {
          errorMessage = `上传失败：${error.message}`;
        }
        utils_index.showError(errorMessage);
        throw error;
      }
    };
    const selectSize = (size) => {
      selectedSize.id = size.id;
      selectedSize.name = size.name;
      selectedSize.width = size.width;
      selectedSize.height = size.height;
      common_vendor.index.showToast({
        title: `已选择${size.name}`,
        icon: "none",
        duration: 1500
      });
    };
    const selectBgColor = (color) => {
      selectedBgColor.id = color.id;
      selectedBgColor.name = color.name;
      selectedBgColor.value = color.value;
      common_vendor.index.showToast({
        title: `已选择${color.name}背景`,
        icon: "none",
        duration: 1500
      });
    };
    const getColorName = (colorValue) => {
      const colorMap = {
        "#ffffff": "白色",
        "#6366f1": "蓝色",
        "#ef4444": "红色"
      };
      return colorMap[colorValue] || "白色";
    };
    const generatePhoto = async () => {
      var _a, _b, _c;
      if (!selectedImage.value) {
        utils_index.showError("请先选择照片");
        return;
      }
      if (!uploadedImageUrl.value) {
        utils_index.showError("图片还未上传完成，请稍后重试");
        return;
      }
      let loadingTimer = null;
      let tipIndex = 0;
      const loadingTips = [
        "AI智能抠图中，请耐心等待...",
        "正在添加背景色...",
        "正在调整尺寸，即将完成..."
      ];
      try {
        utils_index.showLoading(loadingTips[0]);
        loadingTimer = setInterval(() => {
          tipIndex = (tipIndex + 1) % loadingTips.length;
          utils_index.hideLoading();
          utils_index.showLoading(loadingTips[tipIndex]);
        }, 2e3);
        const requestParams = {
          imageUrl: uploadedImageUrl.value,
          // 使用七牛云URL
          photoType: selectedSize.name,
          backgroundColor: getColorName(selectedBgColor.value),
          // 使用颜色名称
          width: Math.round(parseFloat(selectedSize.width) * 118),
          // 转换为像素 (300DPI)
          height: Math.round(parseFloat(selectedSize.height) * 118),
          // 转换为像素 (300DPI)
          autoAdjust: true,
          beautify: false,
          beautifyLevel: 0,
          outputFormat: "jpg",
          dpi: 300,
          extra: null
        };
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:728", "发送证件照制作请求:", requestParams);
        const result = await toolService.callIdPhotoAPI(requestParams);
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:733", "证件照制作返回结果:", result);
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:734", "result.data:", result.data);
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:735", "result.data.processResult:", (_a = result.data) == null ? void 0 : _a.processResult);
        common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:736", "result.data.processResult.processedUrl:", (_c = (_b = result.data) == null ? void 0 : _b.processResult) == null ? void 0 : _c.processedUrl);
        if (result.code === 200 && result.data && result.data.success) {
          if (result.data.processResult && result.data.processResult.processedUrl) {
            common_vendor.index.__f__("log", "at pages/tools/id-photo-maker.vue:743", "设置processedImage为:", result.data.processResult.processedUrl);
            processedImage.value = result.data.processResult.processedUrl;
            showPreview.value = true;
            utils_index.showSuccess("证件照制作成功！");
          } else {
            common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:748", "返回结果中缺少processedUrl字段");
            common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:749", "完整的processResult:", result.data.processResult);
            common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:750", "完整的result.data:", result.data);
            throw new Error("返回结果中缺少处理后的图片URL");
          }
        } else {
          common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:754", "API调用失败，完整结果:", result);
          throw new Error(result.message || "制作失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:758", "证件照制作失败:", error);
        if (error.errMsg && error.errMsg.includes("timeout")) {
          utils_index.showError("处理时间较长，请稍后重试");
        } else {
          try {
            await simulatePhotoProcessing();
            utils_index.showSuccess("证件照制作成功！（本地处理）");
          } catch (localError) {
            utils_index.showError("证件照制作失败，请重试");
          }
        }
      } finally {
        if (loadingTimer) {
          clearInterval(loadingTimer);
        }
        utils_index.hideLoading();
      }
    };
    const simulatePhotoProcessing = async () => {
      return new Promise((resolve) => {
        setTimeout(() => {
          processedImage.value = selectedImage.value;
          showPreview.value = true;
          resolve();
        }, 2e3);
      });
    };
    const closePreview = () => {
      showPreview.value = false;
    };
    const savePhoto = async () => {
      if (!processedImage.value) {
        utils_index.showError("没有可保存的照片");
        return;
      }
      try {
        utils_index.showLoading("正在保存照片...");
        if (processedImage.value.startsWith("http")) {
          const downloadResult = await common_vendor.index.downloadFile({
            url: processedImage.value
          });
          if (downloadResult.statusCode === 200) {
            await common_vendor.index.saveImageToPhotosAlbum({
              filePath: downloadResult.tempFilePath
            });
            utils_index.hideLoading();
            utils_index.showSuccess("保存成功");
          } else {
            throw new Error("下载图片失败");
          }
        } else {
          await common_vendor.index.saveImageToPhotosAlbum({
            filePath: processedImage.value
          });
          utils_index.hideLoading();
          utils_index.showSuccess("保存成功");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/id-photo-maker.vue:836", "保存照片失败:", error);
        utils_index.hideLoading();
        utils_index.showError("保存失败，请重试");
      }
    };
    const resetProcess = () => {
      selectedImage.value = null;
      uploadedImageUrl.value = null;
      processedImage.value = null;
      showPreview.value = false;
      common_vendor.index.showToast({
        title: "已重置",
        icon: "none"
      });
    };
    const uploadedImageUrl = common_vendor.ref(null);
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !selectedImage.value
      }, !selectedImage.value ? {
        b: common_vendor.o(selectImage),
        c: common_vendor.o(takePhoto)
      } : {}, {
        d: selectedImage.value
      }, selectedImage.value ? {
        e: common_vendor.f(photoSizes, (size, k0, i0) => {
          return {
            a: common_vendor.t(size.name),
            b: common_vendor.t(size.width),
            c: common_vendor.t(size.height),
            d: size.id,
            e: selectedSize.id === size.id ? 1 : "",
            f: common_vendor.o(($event) => selectSize(size), size.id)
          };
        })
      } : {}, {
        f: selectedImage.value
      }, selectedImage.value ? {
        g: common_vendor.f(backgroundColors, (color, k0, i0) => {
          return {
            a: color.value,
            b: common_vendor.t(color.name),
            c: color.id,
            d: selectedBgColor.id === color.id ? 1 : "",
            e: common_vendor.o(($event) => selectBgColor(color), color.id)
          };
        })
      } : {}, {
        h: selectedImage.value
      }, selectedImage.value ? {
        i: common_vendor.o(generatePhoto)
      } : {}, {
        j: showPreview.value
      }, showPreview.value ? {
        k: common_vendor.o(closePreview),
        l: processedImage.value,
        m: common_vendor.t(selectedSize.name),
        n: common_vendor.t(selectedSize.width),
        o: common_vendor.t(selectedSize.height),
        p: common_vendor.t(selectedBgColor.name),
        q: common_vendor.o(savePhoto),
        r: common_vendor.o(resetProcess),
        s: common_vendor.o(() => {
        }),
        t: common_vendor.o(closePreview)
      } : {}, {
        v: !selectedImage.value
      }, !selectedImage.value ? {
        w: common_vendor.f(instructions, (item, index, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: common_vendor.t(item),
            c: index
          };
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a470eddc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/id-photo-maker.js.map
