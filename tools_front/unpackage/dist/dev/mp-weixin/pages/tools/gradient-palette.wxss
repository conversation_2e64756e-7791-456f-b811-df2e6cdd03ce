/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-e91c53a2 {
  display: flex;
}
.flex-1.data-v-e91c53a2 {
  flex: 1;
}
.items-center.data-v-e91c53a2 {
  align-items: center;
}
.justify-center.data-v-e91c53a2 {
  justify-content: center;
}
.justify-between.data-v-e91c53a2 {
  justify-content: space-between;
}
.text-center.data-v-e91c53a2 {
  text-align: center;
}
.rounded.data-v-e91c53a2 {
  border-radius: 3px;
}
.rounded-lg.data-v-e91c53a2 {
  border-radius: 6px;
}
.shadow.data-v-e91c53a2 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-e91c53a2 {
  padding: 16rpx;
}
.m-4.data-v-e91c53a2 {
  margin: 16rpx;
}
.mb-4.data-v-e91c53a2 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-e91c53a2 {
  margin-top: 16rpx;
}
.gradient-palette.data-v-e91c53a2 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-e91c53a2 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.header-card.data-v-e91c53a2 {
  background: white;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  text-align: center;
}
.header-card .icon-container.data-v-e91c53a2 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ec4899 0%, #3b82f6 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}
.header-card .icon-container .icon.data-v-e91c53a2 {
  font-size: 60rpx;
  color: white;
}
.header-card .title.data-v-e91c53a2 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.header-card .desc.data-v-e91c53a2 {
  font-size: 28rpx;
  color: #666;
}
.gradient-grid.data-v-e91c53a2 {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300rpx, 1fr));
  gap: 30rpx;
  margin-bottom: 30rpx;
}
.gradient-grid .gradient-card.data-v-e91c53a2 {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.gradient-grid .gradient-card .gradient-preview.data-v-e91c53a2 {
  height: 200rpx;
  width: 100%;
}
.gradient-grid .gradient-card .gradient-info.data-v-e91c53a2 {
  padding: 30rpx;
}
.gradient-grid .gradient-card .gradient-info .gradient-header.data-v-e91c53a2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}
.gradient-grid .gradient-card .gradient-info .gradient-header .gradient-name.data-v-e91c53a2 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.gradient-grid .gradient-card .gradient-info .gradient-header .copy-btn.data-v-e91c53a2 {
  width: 60rpx;
  height: 60rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
}
.gradient-grid .gradient-card .gradient-info .gradient-header .copy-btn .copy-icon.data-v-e91c53a2 {
  font-size: 28rpx;
  color: #666;
}
.gradient-grid .gradient-card .gradient-info .gradient-code.data-v-e91c53a2 {
  font-size: 22rpx;
  color: #9ca3af;
  font-family: monospace;
  word-break: break-all;
}
.selected-card.data-v-e91c53a2 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.selected-card .card-header.data-v-e91c53a2 {
  margin-bottom: 30rpx;
}
.selected-card .card-header .header-title.data-v-e91c53a2 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.selected-card .card-content .selected-preview.data-v-e91c53a2 {
  width: 100%;
  height: 150rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}
.selected-card .card-content .code-container.data-v-e91c53a2 {
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.selected-card .card-content .code-container .code-text.data-v-e91c53a2 {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  font-family: monospace;
  word-break: break-all;
  margin-right: 20rpx;
}
.selected-card .card-content .code-container .copy-code-btn.data-v-e91c53a2 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: #3b82f6;
  border-radius: 12rpx;
}
.selected-card .card-content .code-container .copy-code-btn .copy-icon.data-v-e91c53a2 {
  font-size: 24rpx;
  color: white;
  margin-right: 8rpx;
}
.selected-card .card-content .code-container .copy-code-btn .copy-text.data-v-e91c53a2 {
  font-size: 26rpx;
  color: white;
  font-weight: 500;
}
.info-card.data-v-e91c53a2 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.info-card .card-header.data-v-e91c53a2 {
  margin-bottom: 24rpx;
}
.info-card .card-header .header-title.data-v-e91c53a2 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.info-card .card-content .info-list .info-item.data-v-e91c53a2 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}