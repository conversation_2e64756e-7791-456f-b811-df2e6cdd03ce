"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchQuery: "",
      selectedCategory: "全部",
      categories: ["全部", "Chrome", "Firefox", "Safari", "Edge", "Mobile", "Bots"],
      userAgents: [
        // Chrome
        {
          category: "Chrome",
          name: "Chrome Windows",
          ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        },
        {
          category: "Chrome",
          name: "Chrome macOS",
          ua: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        },
        {
          category: "Chrome",
          name: "Chrome Linux",
          ua: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        },
        // Firefox
        {
          category: "Firefox",
          name: "Firefox Windows",
          ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        },
        {
          category: "Firefox",
          name: "Firefox macOS",
          ua: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0"
        },
        {
          category: "Firefox",
          name: "Firefox Linux",
          ua: "Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0"
        },
        // Safari
        {
          category: "Safari",
          name: "Safari macOS",
          ua: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        },
        {
          category: "Safari",
          name: "Safari iOS",
          ua: "Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
        },
        // Edge
        {
          category: "Edge",
          name: "Edge Windows",
          ua: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
        },
        // Mobile
        {
          category: "Mobile",
          name: "Android Chrome",
          ua: "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36"
        },
        {
          category: "Mobile",
          name: "iPad Safari",
          ua: "Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1"
        },
        // Bots
        {
          category: "Bots",
          name: "Googlebot",
          ua: "Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)"
        },
        {
          category: "Bots",
          name: "Bingbot",
          ua: "Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)"
        },
        {
          category: "Bots",
          name: "Baiduspider",
          ua: "Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)"
        }
      ]
    };
  },
  computed: {
    filteredUAs() {
      return this.userAgents.filter((ua) => {
        const matchesSearch = this.searchQuery === "" || ua.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || ua.ua.toLowerCase().includes(this.searchQuery.toLowerCase());
        const matchesCategory = this.selectedCategory === "全部" || ua.category === this.selectedCategory;
        return matchesSearch && matchesCategory;
      });
    }
  },
  methods: {
    setSelectedCategory(category) {
      this.selectedCategory = category;
    },
    copyToClipboard(ua) {
      common_vendor.index.setClipboardData({
        data: ua,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    },
    copyUserAgent(ua) {
      common_vendor.index.setClipboardData({
        data: ua,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    },
    getCategoryColorClass(category) {
      const colorMap = {
        "Chrome": "tag-chrome",
        "Firefox": "tag-firefox",
        "Safari": "tag-safari",
        "Edge": "tag-edge",
        "Mobile": "tag-mobile",
        "Bots": "tag-bots"
      };
      return colorMap[category] || "tag-default";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.searchQuery,
    b: common_vendor.o(($event) => $data.searchQuery = $event.detail.value),
    c: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category),
        b: category,
        c: $data.selectedCategory === category ? 1 : "",
        d: common_vendor.o(($event) => $options.setSelectedCategory(category), category)
      };
    }),
    d: common_vendor.f($options.filteredUAs, (ua, index, i0) => {
      return {
        a: common_vendor.t(ua.name),
        b: common_vendor.t(ua.category),
        c: common_vendor.n($options.getCategoryColorClass(ua.category)),
        d: common_vendor.o(($event) => $options.copyToClipboard(ua.ua), index),
        e: common_vendor.t(ua.ua),
        f: index
      };
    }),
    e: $options.filteredUAs.length === 0
  }, $options.filteredUAs.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-18c7a761"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/user-agent-list.js.map
