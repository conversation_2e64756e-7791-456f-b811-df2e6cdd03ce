"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  name: "HistoryToday",
  data() {
    return {
      selectedDate: (/* @__PURE__ */ new Date()).toISOString().split("T")[0],
      events: [],
      loading: false,
      selectedCategories: [],
      savedEvents: [],
      toolService: new utils_toolService.ToolService(),
      categories: ["政治", "科技", "文化", "体育", "军事", "经济", "其他"]
    };
  },
  computed: {
    filteredEvents() {
      if (this.selectedCategories.length === 0) {
        return this.events;
      }
      return this.events.filter(
        (event) => this.selectedCategories.includes(event.category)
      );
    }
  },
  mounted() {
    this.loadHistoryEvents();
    this.loadSavedEvents();
  },
  methods: {
    async loadHistoryEvents() {
      this.loading = true;
      try {
        const params = {
          type: 1,
          // 需要详情
          date: this.selectedDate
        };
        common_vendor.index.__f__("log", "at pages/tools/history-today.vue:243", "历史事件查询参数:", params);
        const result = await this.toolService.getHistoryToday(params);
        common_vendor.index.__f__("log", "at pages/tools/history-today.vue:245", "历史事件API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.events) {
            this.events = apiData.events.map((event) => {
              return {
                id: event.id,
                year: event.year,
                event: event.title,
                // 后端的title对应前端的event
                details: event.details,
                category: event.category,
                importance: this.convertImportanceToLevel(event.importance),
                // 直接调用转换函数
                picUrl: event.picUrl,
                date: event.date,
                liked: false,
                saved: this.savedEvents.some((saved) => saved.id === event.id),
                dataSource: event.dataSource,
                expanded: false
                // 默认收起详情
              };
            });
            if (this.events.length === 0) {
              common_vendor.index.showToast({
                title: "今日暂无历史事件",
                icon: "none",
                duration: 2e3
              });
            } else {
              common_vendor.index.showToast({
                title: `找到${this.events.length}个历史事件`,
                icon: "success",
                duration: 1500
              });
            }
          } else {
            throw new Error(apiData.message || "获取历史事件失败");
          }
        } else {
          throw new Error(result.message || "获取历史事件失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/history-today.vue:289", "获取历史事件失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取历史事件失败",
          icon: "error",
          duration: 2e3
        });
        this.events = [];
      } finally {
        this.loading = false;
      }
    },
    generateRandomEvents() {
      this.loadHistoryEvents();
    },
    onDateChange(e) {
      this.selectedDate = e.detail.value;
      this.loadHistoryEvents();
    },
    formatDate(dateString) {
      const date = new Date(dateString);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    },
    formatDisplayDate(dateString) {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },
    getAverageImportance() {
      if (this.events.length === 0)
        return 0;
      const total = this.events.reduce((sum, event) => sum + event.importance, 0);
      return (total / this.events.length).toFixed(1);
    },
    getMostCommonCategory() {
      if (this.events.length === 0)
        return "无";
      const categoryCount = {};
      this.events.forEach((event) => {
        categoryCount[event.category] = (categoryCount[event.category] || 0) + 1;
      });
      return Object.keys(categoryCount).reduce(
        (a, b) => categoryCount[a] > categoryCount[b] ? a : b
      );
    },
    toggleCategory(category) {
      const index = this.selectedCategories.indexOf(category);
      if (index > -1) {
        this.selectedCategories.splice(index, 1);
      } else {
        this.selectedCategories.push(category);
      }
    },
    resetFilters() {
      this.selectedCategories = [];
      common_vendor.index.showToast({
        title: "已重置筛选条件",
        icon: "success",
        duration: 1e3
      });
    },
    selectEvent(event) {
      common_vendor.index.showModal({
        title: `${event.year}年`,
        content: `${event.event}

${event.details || ""}`,
        showCancel: false,
        confirmText: "知道了"
      });
    },
    likeEvent(event) {
      event.liked = !event.liked;
      if (event.liked) {
        event.likes = (event.likes || 0) + 1;
        common_vendor.index.showToast({
          title: "已点赞",
          icon: "success",
          duration: 1e3
        });
      } else {
        event.likes = Math.max(0, (event.likes || 1) - 1);
        common_vendor.index.showToast({
          title: "已取消点赞",
          icon: "none",
          duration: 1e3
        });
      }
    },
    shareEvent(event) {
      const text = `历史上的今天（${this.formatDate(this.selectedDate)}）：${event.year}年 - ${event.event}`;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    saveEvent(event) {
      event.saved = !event.saved;
      if (event.saved) {
        if (!this.savedEvents.find((e) => e.id === event.id)) {
          this.savedEvents.push(event);
          this.saveSavedEvents();
        }
        common_vendor.index.showToast({
          title: "已收藏",
          icon: "success",
          duration: 1e3
        });
      } else {
        const index = this.savedEvents.findIndex((e) => e.id === event.id);
        if (index > -1) {
          this.savedEvents.splice(index, 1);
          this.saveSavedEvents();
        }
        common_vendor.index.showToast({
          title: "已取消收藏",
          icon: "none",
          duration: 1e3
        });
      }
    },
    loadSavedEvents() {
      try {
        const saved = common_vendor.index.getStorageSync("savedHistoryEvents");
        if (saved) {
          this.savedEvents = JSON.parse(saved);
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tools/history-today.vue:435", "加载收藏事件失败:", e);
      }
    },
    saveSavedEvents() {
      try {
        common_vendor.index.setStorageSync("savedHistoryEvents", JSON.stringify(this.savedEvents));
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tools/history-today.vue:443", "保存收藏事件失败:", e);
      }
    },
    getCategoryTagClass(category) {
      const categoryMap = {
        "政治": "tag-political",
        "科技": "tag-tech",
        "科学": "tag-science",
        "文化": "tag-culture",
        "体育": "tag-sports",
        "军事": "tag-military",
        "经济": "tag-economy",
        "灾难": "tag-disaster"
      };
      return `category-${categoryMap[category] || "default"}`;
    },
    // 转换重要性文字为数字等级
    convertImportanceToLevel(importance) {
      if (importance === "重大")
        return 5;
      if (importance === "重要")
        return 4;
      if (importance === "一般")
        return 3;
      return 2;
    },
    // 保持原有方法名以兼容其他地方的调用
    getImportanceLevel(importance) {
      return this.convertImportanceToLevel(importance);
    },
    // 切换事件详情的展开/收起状态
    toggleEventDetails(event) {
      event.expanded = !event.expanded;
    },
    // 格式化详情文本
    formatDetails(details) {
      if (!details)
        return "";
      let formatted = details.replace(/　　/g, "\n\n");
      formatted = formatted.replace(/[ \t]+/g, " ");
      formatted = formatted.replace(/\n{3,}/g, "\n\n");
      formatted = formatted.trim();
      const paragraphs = formatted.split("\n\n");
      formatted = paragraphs.map((paragraph) => {
        if (paragraph.trim()) {
          if (!paragraph.startsWith("　")) {
            return "　　" + paragraph.trim();
          }
          return paragraph.trim();
        }
        return paragraph;
      }).join("\n\n");
      return formatted;
    },
    // 获取详情预览文本
    getDetailsPreview(details) {
      if (!details)
        return "暂无详情";
      let preview = details.replace(/　　/g, " ").replace(/\s+/g, " ").trim();
      if (preview.length <= 120) {
        return preview;
      }
      const truncated = preview.substring(0, 120);
      const lastPeriod = truncated.lastIndexOf("。");
      if (lastPeriod > 50) {
        return truncated.substring(0, lastPeriod + 1) + "...";
      }
      return truncated + "...";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.formatDate($data.selectedDate)),
    b: common_vendor.t($options.formatDisplayDate($data.selectedDate)),
    c: $data.selectedDate,
    d: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    e: common_vendor.t($data.events.length),
    f: common_vendor.t($options.getAverageImportance()),
    g: common_vendor.t($options.getMostCommonCategory()),
    h: common_vendor.o((...args) => $options.resetFilters && $options.resetFilters(...args)),
    i: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category),
        b: category,
        c: common_vendor.o(($event) => $options.toggleCategory(category), category),
        d: common_vendor.n($data.selectedCategories.includes(category) ? "category-chip-active" : "")
      };
    }),
    j: !$data.loading
  }, !$data.loading ? {} : {}, {
    k: common_vendor.o((...args) => $options.generateRandomEvents && $options.generateRandomEvents(...args)),
    l: $data.loading,
    m: $data.loading
  }, $data.loading ? {} : {
    n: common_vendor.f($options.filteredEvents, (event, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(event.year),
        b: common_vendor.t(event.category),
        c: common_vendor.n($options.getCategoryTagClass(event.category)),
        d: common_vendor.f(5, (i, k1, i1) => {
          return {
            a: i,
            b: common_vendor.n(i <= event.importance ? "star-filled" : "star-empty")
          };
        }),
        e: common_vendor.t(event.event),
        f: event.details
      }, event.details ? {
        g: common_vendor.t(event.expanded ? "收起" : "展开"),
        h: common_vendor.t(event.expanded ? "▲" : "▼"),
        i: common_vendor.o(($event) => $options.toggleEventDetails(event), index),
        j: common_vendor.t($options.formatDetails(event.details)),
        k: event.expanded,
        l: common_vendor.t($options.getDetailsPreview(event.details)),
        m: !event.expanded
      } : {}, {
        n: index,
        o: index === $options.filteredEvents.length - 1 ? 1 : "",
        p: common_vendor.o(($event) => $options.selectEvent(event), index)
      });
    })
  }, {
    o: !$data.loading && $options.filteredEvents.length === 0
  }, !$data.loading && $options.filteredEvents.length === 0 ? {
    p: common_vendor.o((...args) => $options.resetFilters && $options.resetFilters(...args))
  } : {}, {
    q: $data.savedEvents.length > 0
  }, $data.savedEvents.length > 0 ? {
    r: common_vendor.t($data.savedEvents.length),
    s: common_vendor.f($data.savedEvents, (event, k0, i0) => {
      return {
        a: common_vendor.t(event.year),
        b: common_vendor.t(event.event.substring(0, 20)),
        c: event.id,
        d: common_vendor.o(($event) => $options.selectEvent(event), event.id)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c91a4ea8"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/history-today.js.map
