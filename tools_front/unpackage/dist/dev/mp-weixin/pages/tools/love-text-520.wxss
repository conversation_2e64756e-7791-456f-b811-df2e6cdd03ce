/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-4a3237a1 {
  display: flex;
}
.flex-1.data-v-4a3237a1 {
  flex: 1;
}
.items-center.data-v-4a3237a1 {
  align-items: center;
}
.justify-center.data-v-4a3237a1 {
  justify-content: center;
}
.justify-between.data-v-4a3237a1 {
  justify-content: space-between;
}
.text-center.data-v-4a3237a1 {
  text-align: center;
}
.rounded.data-v-4a3237a1 {
  border-radius: 3px;
}
.rounded-lg.data-v-4a3237a1 {
  border-radius: 6px;
}
.shadow.data-v-4a3237a1 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-4a3237a1 {
  padding: 16rpx;
}
.m-4.data-v-4a3237a1 {
  margin: 16rpx;
}
.mb-4.data-v-4a3237a1 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-4a3237a1 {
  margin-top: 16rpx;
}
.love-text-520.data-v-4a3237a1 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-4a3237a1 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-4a3237a1 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-4a3237a1 {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-4a3237a1 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-4a3237a1 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-4a3237a1, .results-section.data-v-4a3237a1, .selected-section.data-v-4a3237a1, .history-section.data-v-4a3237a1, .favorites-section.data-v-4a3237a1, .examples-section.data-v-4a3237a1, .styles-section.data-v-4a3237a1, .help-section.data-v-4a3237a1 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-4a3237a1 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-4a3237a1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-container.data-v-4a3237a1 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.text-input.data-v-4a3237a1 {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #1f2937;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}
.text-input.data-v-4a3237a1::-webkit-input-placeholder {
  color: #9ca3af;
}
.text-input.data-v-4a3237a1::placeholder {
  color: #9ca3af;
}
.input-footer.data-v-4a3237a1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.char-count.data-v-4a3237a1 {
  font-size: 24rpx;
  color: #6b7280;
}
.input-actions.data-v-4a3237a1 {
  display: flex;
  gap: 12rpx;
}
.action-btn.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.clear.data-v-4a3237a1 {
  background: #f3f4f6;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.action-btn.clear.data-v-4a3237a1:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.action-btn.generate.data-v-4a3237a1 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.25);
}
.action-btn.generate.data-v-4a3237a1:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(236, 72, 153, 0.35);
}
.action-btn.primary.data-v-4a3237a1 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.25);
}
.action-btn.primary.data-v-4a3237a1:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(236, 72, 153, 0.35);
}
.action-btn.secondary.data-v-4a3237a1 {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #e5e7eb;
}
.action-btn.secondary.data-v-4a3237a1:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.btn-icon.data-v-4a3237a1 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-4a3237a1 {
  font-size: 26rpx;
}
.refresh-btn.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx;
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.refresh-btn.data-v-4a3237a1:hover {
  transform: translateY(-1rpx);
}
.refresh-icon.data-v-4a3237a1 {
  font-size: 24rpx;
  color: white;
}
.results-grid.data-v-4a3237a1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.result-item.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  border: 1rpx solid #f9a8d4;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.result-item.data-v-4a3237a1:hover {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border-color: #f472b6;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.15);
}
.result-item.selected.data-v-4a3237a1 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  border-color: #be185d;
  color: white;
}
.result-item.selected .result-text.data-v-4a3237a1 {
  color: white;
}
.result-text.data-v-4a3237a1 {
  font-size: 28rpx;
  font-weight: 500;
  color: #be185d;
  flex: 1;
  word-break: break-all;
}
.result-actions.data-v-4a3237a1 {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.selected-card.data-v-4a3237a1 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #f9a8d4;
  text-align: center;
}
.selected-text.data-v-4a3237a1 {
  font-size: 36rpx;
  font-weight: 600;
  color: #be185d;
  margin-bottom: 24rpx;
  display: block;
  word-break: break-all;
  line-height: 1.4;
}
.selected-actions.data-v-4a3237a1 {
  display: flex;
  gap: 16rpx;
}
.count-badge.data-v-4a3237a1 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.history-list.data-v-4a3237a1 {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.history-item.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
}
.history-item.data-v-4a3237a1:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.history-text.data-v-4a3237a1 {
  font-size: 26rpx;
  color: #374151;
  flex: 1;
}
.history-actions.data-v-4a3237a1 {
  display: flex;
  gap: 8rpx;
}
.favorites-grid.data-v-4a3237a1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.favorite-item.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bbf7d0;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.favorite-item.data-v-4a3237a1:hover {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(5, 150, 105, 0.15);
}
.favorite-text.data-v-4a3237a1 {
  font-size: 28rpx;
  font-weight: 500;
  color: #059669;
  flex: 1;
  word-break: break-all;
}
.favorite-actions.data-v-4a3237a1 {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.mini-btn.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mini-btn.data-v-4a3237a1:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.mini-btn.remove.data-v-4a3237a1 {
  background: #fecaca;
}
.mini-btn.remove.data-v-4a3237a1:hover {
  background: #fca5a5;
}
.mini-icon.data-v-4a3237a1 {
  font-size: 20rpx;
}
.examples-grid.data-v-4a3237a1 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}
.example-item.data-v-4a3237a1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  border: 1rpx solid #bae6fd;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.example-item.data-v-4a3237a1:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #7dd3fc;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.15);
}
.example-text.data-v-4a3237a1 {
  font-size: 26rpx;
  font-weight: 500;
  color: #0369a1;
  text-align: center;
}
.styles-grid.data-v-4a3237a1 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.style-item.data-v-4a3237a1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border-radius: 16rpx;
  border: 1rpx solid #fcd34d;
  text-align: center;
}
.style-text.data-v-4a3237a1 {
  font-size: 28rpx;
  font-weight: 500;
  color: #d97706;
  margin-bottom: 8rpx;
  word-break: break-all;
}
.style-name.data-v-4a3237a1 {
  font-size: 24rpx;
  color: #92400e;
  font-weight: 500;
}
.help-content.data-v-4a3237a1 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-4a3237a1 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-4a3237a1:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-4a3237a1 {
    padding: 30rpx 20rpx;
}
.examples-grid.data-v-4a3237a1 {
    grid-template-columns: repeat(2, 1fr);
}
.styles-grid.data-v-4a3237a1 {
    grid-template-columns: 1fr;
}
.selected-actions.data-v-4a3237a1 {
    flex-direction: column;
    gap: 12rpx;
}
.input-actions.data-v-4a3237a1 {
    flex-direction: column;
    gap: 8rpx;
}
}