/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-1bb15d32 {
  display: flex;
}
.flex-1.data-v-1bb15d32 {
  flex: 1;
}
.items-center.data-v-1bb15d32 {
  align-items: center;
}
.justify-center.data-v-1bb15d32 {
  justify-content: center;
}
.justify-between.data-v-1bb15d32 {
  justify-content: space-between;
}
.text-center.data-v-1bb15d32 {
  text-align: center;
}
.rounded.data-v-1bb15d32 {
  border-radius: 3px;
}
.rounded-lg.data-v-1bb15d32 {
  border-radius: 6px;
}
.shadow.data-v-1bb15d32 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-1bb15d32 {
  padding: 16rpx;
}
.m-4.data-v-1bb15d32 {
  margin: 16rpx;
}
.mb-4.data-v-1bb15d32 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-1bb15d32 {
  margin-top: 16rpx;
}
.hue-generator.data-v-1bb15d32 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.content.data-v-1bb15d32 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-1bb15d32 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-header.data-v-1bb15d32 {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-icon.data-v-1bb15d32 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-header .header-icon .icon-gradient.data-v-1bb15d32 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #ff6b6b, #ffd93d, #6bcf7f, #4d96ff, #9c88ff);
}
.card-header .header-title.data-v-1bb15d32 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-header .header-subtitle.data-v-1bb15d32 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.main-content.data-v-1bb15d32 {
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}
@media (min-width: 750rpx) {
.main-content.data-v-1bb15d32 {
    flex-direction: row;
    gap: 60rpx;
}
.control-section.data-v-1bb15d32 {
    flex: 1;
    min-width: 300rpx;
}
.result-section.data-v-1bb15d32 {
    flex: 2;
}
}
.control-section .section-title.data-v-1bb15d32 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.color-input-group.data-v-1bb15d32 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 30rpx;
}
.color-preview-large.data-v-1bb15d32 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 6rpx solid #e5e7eb;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}
.color-preview-large.data-v-1bb15d32::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0), linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0);
  background-size: 24rpx 24rpx;
  background-position: 0 0, 12rpx 12rpx;
  z-index: -1;
  opacity: 0.3;
}
.color-preview-large.data-v-1bb15d32:active {
  transform: scale(0.95);
  border-color: #8B5CF6;
  box-shadow: 0 6rpx 20rpx rgba(139, 92, 246, 0.3);
}
.color-input-text.data-v-1bb15d32 {
  flex: 1;
  height: 120rpx;
  padding: 0 30rpx;
  border: 4rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #333;
  background: #f9fafb;
  transition: all 0.3s ease;
}
.color-input-text.data-v-1bb15d32:focus {
  border-color: #8B5CF6;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(139, 92, 246, 0.1);
}
.slider-group.data-v-1bb15d32 {
  margin-bottom: 40rpx;
}
.slider-header.data-v-1bb15d32 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.slider-label.data-v-1bb15d32 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.slider-value.data-v-1bb15d32 {
  font-size: 28rpx;
  font-weight: 700;
  color: #8B5CF6;
  min-width: 80rpx;
  text-align: right;
}
.custom-slider.data-v-1bb15d32 {
  width: 100%;
  margin: 10rpx 0;
}
.copy-all-btn.data-v-1bb15d32 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  border-radius: 16rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
}
.copy-all-btn.data-v-1bb15d32:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(139, 92, 246, 0.4);
}
.copy-icon.data-v-1bb15d32 {
  font-size: 32rpx;
  color: white;
}
.copy-text.data-v-1bb15d32 {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}
.result-section.data-v-1bb15d32 {
  margin-bottom: 50rpx;
}
.result-section .result-header.data-v-1bb15d32 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.result-section .result-header .section-title.data-v-1bb15d32 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 0;
}
.result-section .result-header .color-count.data-v-1bb15d32 {
  font-size: 24rpx;
  color: #666;
  background: #f3f4f6;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.color-grid.data-v-1bb15d32 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}
.color-item.data-v-1bb15d32 {
  text-align: center;
  transition: all 0.3s ease;
}
.color-item.data-v-1bb15d32:active {
  transform: scale(0.95);
}
.color-block.data-v-1bb15d32 {
  width: 100%;
  height: 120rpx;
  border-radius: 16rpx;
  border: 4rpx solid #e5e7eb;
  margin-bottom: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.color-item:active .color-block.data-v-1bb15d32 {
  border-color: #8B5CF6;
  box-shadow: 0 6rpx 16rpx rgba(139, 92, 246, 0.3);
}
.color-code.data-v-1bb15d32 {
  font-size: 20rpx;
  color: #666;
  font-family: "Courier New", monospace;
  font-weight: 500;
}
.info-section.data-v-1bb15d32 {
  margin-top: 40rpx;
}
.info-section .info-title.data-v-1bb15d32 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.info-section .info-list.data-v-1bb15d32 {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  padding: 30rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e0f2fe;
}
.info-section .info-list .info-item.data-v-1bb15d32 {
  display: block;
  font-size: 26rpx;
  color: #475569;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.info-section .info-list .info-item.data-v-1bb15d32:last-child {
  margin-bottom: 0;
}

/* 颜色选择器样式 - 复用渐变代码生成器的样式 */
.color-picker-modal.data-v-1bb15d32 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn-1bb15d32 0.3s ease-out;
}
.color-picker-container.data-v-1bb15d32 {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24rpx;
  padding: 40rpx;
  width: 90%;
  max-width: 600rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
  animation: slideUp-1bb15d32 0.3s ease-out;
}
.picker-header.data-v-1bb15d32 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.picker-title.data-v-1bb15d32 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}
.picker-close.data-v-1bb15d32 {
  width: 60rpx;
  height: 60rpx;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.picker-close.data-v-1bb15d32:hover {
  background-color: #e5e7eb;
  transform: scale(1.1);
}
.close-icon.data-v-1bb15d32 {
  font-size: 32rpx;
  color: #6b7280;
  font-weight: 600;
}
.color-canvas-container.data-v-1bb15d32 {
  position: relative;
  margin-bottom: 30rpx;
}
.color-canvas.data-v-1bb15d32 {
  width: 100%;
  height: 320rpx;
  border-radius: 16rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.canvas-cursor.data-v-1bb15d32 {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.hue-slider-container.data-v-1bb15d32 {
  position: relative;
  margin-bottom: 30rpx;
}
.hue-slider.data-v-1bb15d32 {
  width: 100%;
  height: 40rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
}
.hue-cursor.data-v-1bb15d32 {
  position: absolute;
  top: 50%;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  border: 3rpx solid #8B5CF6;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 2rpx 8rpx rgba(139, 92, 246, 0.4);
  transform: translate(-50%, -50%);
  pointer-events: none;
  transition: all 0.15s ease;
}
.rgb-inputs.data-v-1bb15d32 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.rgb-group.data-v-1bb15d32 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.rgb-label.data-v-1bb15d32 {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12rpx;
  text-align: center;
}
.rgb-input.data-v-1bb15d32 {
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #374151;
  text-align: center;
  background-color: #f9fafb;
  transition: all 0.2s ease;
}
.rgb-input.data-v-1bb15d32:focus {
  border-color: #8B5CF6;
  background-color: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(139, 92, 246, 0.1);
}
.picker-preview.data-v-1bb15d32 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: #f8fafc;
  border-radius: 16rpx;
}
.preview-color.data-v-1bb15d32 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  border: 3rpx solid #e5e7eb;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.hex-value.data-v-1bb15d32 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  font-family: "SF Mono", "Monaco", "Consolas", monospace;
}
.picker-actions.data-v-1bb15d32 {
  display: flex;
  gap: 20rpx;
}
.picker-btn.data-v-1bb15d32 {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}
.picker-btn.cancel.data-v-1bb15d32 {
  background: #f3f4f6;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
}
.picker-btn.cancel.data-v-1bb15d32:hover {
  background: #e5e7eb;
  transform: translateY(-2rpx);
}
.picker-btn.confirm.data-v-1bb15d32 {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
}
.picker-btn.confirm.data-v-1bb15d32:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(139, 92, 246, 0.4);
}
.picker-btn.data-v-1bb15d32:active {
  transform: translateY(1rpx);
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.color-grid.data-v-1bb15d32 {
    grid-template-columns: repeat(3, 1fr);
}
.color-input-group.data-v-1bb15d32 {
    flex-direction: column;
    align-items: stretch;
}
.color-preview-large.data-v-1bb15d32 {
    width: 100%;
    height: 100rpx;
}
}
/* 动画效果 */
@keyframes fadeIn-1bb15d32 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes slideUp-1bb15d32 {
from {
    opacity: 0;
    transform: translateY(40rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}