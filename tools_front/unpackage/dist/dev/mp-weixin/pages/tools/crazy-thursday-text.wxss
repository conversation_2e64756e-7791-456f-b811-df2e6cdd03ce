/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-94675fb8 {
  display: flex;
}
.flex-1.data-v-94675fb8 {
  flex: 1;
}
.items-center.data-v-94675fb8 {
  align-items: center;
}
.justify-center.data-v-94675fb8 {
  justify-content: center;
}
.justify-between.data-v-94675fb8 {
  justify-content: space-between;
}
.text-center.data-v-94675fb8 {
  text-align: center;
}
.rounded.data-v-94675fb8 {
  border-radius: 3px;
}
.rounded-lg.data-v-94675fb8 {
  border-radius: 6px;
}
.shadow.data-v-94675fb8 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-94675fb8 {
  padding: 16rpx;
}
.m-4.data-v-94675fb8 {
  margin: 16rpx;
}
.mb-4.data-v-94675fb8 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-94675fb8 {
  margin-top: 16rpx;
}
.crazy-thursday-text.data-v-94675fb8 {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-94675fb8 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-94675fb8 {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-94675fb8 {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-94675fb8 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-94675fb8 {
  padding: 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.current-card.data-v-94675fb8, .recommend-card.data-v-94675fb8, .info-card.data-v-94675fb8 {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.current-card .card-header.data-v-94675fb8, .recommend-card .card-header.data-v-94675fb8, .info-card .card-header.data-v-94675fb8 {
  padding: 30rpx 30rpx 20rpx;
}
.current-card .card-header .header-content.data-v-94675fb8, .recommend-card .card-header .header-content.data-v-94675fb8, .info-card .card-header .header-content.data-v-94675fb8 {
  display: flex;
  align-items: center;
}
.current-card .card-header .header-content .header-icon.data-v-94675fb8, .recommend-card .card-header .header-content .header-icon.data-v-94675fb8, .info-card .card-header .header-content .header-icon.data-v-94675fb8 {
  font-size: 28rpx;
  margin-right: 15rpx;
  color: #f97316;
}
.current-card .card-header .header-content .header-title.data-v-94675fb8, .recommend-card .card-header .header-content .header-title.data-v-94675fb8, .info-card .card-header .header-content .header-title.data-v-94675fb8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.current-card .card-header .card-title.data-v-94675fb8, .recommend-card .card-header .card-title.data-v-94675fb8, .info-card .card-header .card-title.data-v-94675fb8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.current-card .card-content.data-v-94675fb8, .recommend-card .card-content.data-v-94675fb8, .info-card .card-content.data-v-94675fb8 {
  padding: 0 30rpx 30rpx;
}
.text-display.data-v-94675fb8 {
  background: #fff7ed;
  padding: 48rpx;
  border-radius: 16rpx;
  border-left: 8rpx solid #f97316;
  margin-bottom: 30rpx;
}
.text-display .display-text.data-v-94675fb8 {
  display: block;
  font-size: 32rpx;
  color: #c2410c;
  line-height: 1.6;
  text-align: center;
}
.action-buttons.data-v-94675fb8 {
  display: flex;
  gap: 20rpx;
}
.action-buttons .generate-btn.data-v-94675fb8, .action-buttons .copy-btn.data-v-94675fb8 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.action-buttons .generate-btn.disabled.data-v-94675fb8, .action-buttons .copy-btn.disabled.data-v-94675fb8 {
  opacity: 0.5;
}
.action-buttons .generate-btn .btn-icon.data-v-94675fb8, .action-buttons .copy-btn .btn-icon.data-v-94675fb8 {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.action-buttons .generate-btn .btn-text.data-v-94675fb8, .action-buttons .copy-btn .btn-text.data-v-94675fb8 {
  font-size: 28rpx;
  font-weight: 500;
}
.action-buttons .generate-btn.data-v-94675fb8 {
  flex: 1;
  background: #3b82f6;
  color: white;
}
.action-buttons .copy-btn.data-v-94675fb8 {
  padding: 24rpx 40rpx;
  border: 2rpx solid #e0e0e0;
  background: white;
  color: #666;
}
.text-list .text-item.data-v-94675fb8 {
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}
.text-list .text-item .item-text.data-v-94675fb8 {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.5;
}
.info-list .info-item.data-v-94675fb8 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}