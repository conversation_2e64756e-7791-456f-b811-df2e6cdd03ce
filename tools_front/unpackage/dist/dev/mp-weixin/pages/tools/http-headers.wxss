
.container.data-v-b0e94c0c {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.main-content.data-v-b0e94c0c {
  padding: 20rpx;
}
.search-card.data-v-b0e94c0c,
.info-card.data-v-b0e94c0c {
  background-color: #fff;
  border-radius: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}
.card-header.data-v-b0e94c0c {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 30rpx 30rpx 20rpx;
}
.globe-icon.data-v-b0e94c0c,
.info-icon.data-v-b0e94c0c {
  font-size: 32rpx;
}
.card-title.data-v-b0e94c0c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.search-section.data-v-b0e94c0c {
  padding: 0 30rpx 20rpx;
}
.search-box.data-v-b0e94c0c {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 20rpx 25rpx;
}
.search-icon.data-v-b0e94c0c {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #666;
}
.search-input.data-v-b0e94c0c {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.filter-section.data-v-b0e94c0c {
  padding: 0 30rpx 30rpx;
}
.filter-buttons.data-v-b0e94c0c {
  display: flex;
  gap: 15rpx;
}
.filter-btn.data-v-b0e94c0c {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.filter-btn.active.data-v-b0e94c0c {
  background-color: #007AFF;
  border-color: #007AFF;
}
.filter-btn.active .filter-text.data-v-b0e94c0c {
  color: #fff;
}
.filter-text.data-v-b0e94c0c {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.headers-list.data-v-b0e94c0c {
  margin-bottom: 20rpx;
}
.header-card.data-v-b0e94c0c {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.06);
  overflow: hidden;
}
.header-info.data-v-b0e94c0c {
  padding: 30rpx;
}
.header-title-row.data-v-b0e94c0c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.header-name.data-v-b0e94c0c {
  font-size: 36rpx;
  font-weight: 700;
  color: #007AFF;
}
.type-badge.data-v-b0e94c0c {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
}
.type-request.data-v-b0e94c0c {
  background-color: #e3f2fd;
  color: #1976d2;
}
.type-response.data-v-b0e94c0c {
  background-color: #e8f5e8;
  color: #388e3c;
}
.type-text.data-v-b0e94c0c {
  font-size: 20rpx;
  font-weight: 500;
}
.header-description.data-v-b0e94c0c {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25rpx;
  display: block;
}
.example-section.data-v-b0e94c0c {
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 20rpx;
}
.example-label.data-v-b0e94c0c {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}
.example-code.data-v-b0e94c0c {
  background-color: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 15rpx;
  position: relative;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20rpx;
}
.example-code.data-v-b0e94c0c:active {
  background-color: #f8f9fa;
}
.code-text.data-v-b0e94c0c {
  font-size: 24rpx;
  font-family: 'Courier New', monospace;
  color: #d63384;
  word-break: break-all;
  flex: 1;
}
.copy-hint.data-v-b0e94c0c {
  font-size: 20rpx;
  color: #999;
  background-color: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  white-space: nowrap;
  flex-shrink: 0;
}
.empty-state.data-v-b0e94c0c {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 80rpx 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.06);
}
.empty-icon.data-v-b0e94c0c {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  color: #ccc;
}
.empty-text.data-v-b0e94c0c {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}
.empty-hint.data-v-b0e94c0c {
  font-size: 26rpx;
  color: #999;
}
.info-content.data-v-b0e94c0c {
  padding: 0 30rpx 30rpx;
}
.info-item.data-v-b0e94c0c {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  display: block;
  margin-bottom: 15rpx;
}
.info-bold.data-v-b0e94c0c {
  font-weight: 600;
  color: #333;
}
