<view class="arrow-text data-v-b77d809d"><view class="container data-v-b77d809d"><view class="header-section data-v-b77d809d"><view class="title-container data-v-b77d809d"><text class="title-icon data-v-b77d809d">➡️</text><text class="title-text data-v-b77d809d">箭头文字</text></view><text class="subtitle data-v-b77d809d">用各种箭头符号装饰文字，突出重点</text></view><view class="input-section data-v-b77d809d"><view class="section-header data-v-b77d809d"><text class="section-icon data-v-b77d809d">✍️</text><text class="section-title data-v-b77d809d">输入文字</text></view><view class="input-container data-v-b77d809d"><block wx:if="{{r0}}"><textarea class="text-input data-v-b77d809d" placeholder="请输入要装饰的文字..." maxlength="100" auto-height="{{true}}" show-confirm-bar="{{false}}" value="{{a}}" bindinput="{{b}}"/></block><view class="input-footer data-v-b77d809d"><text class="char-count data-v-b77d809d">{{c}}/100</text><view class="input-actions data-v-b77d809d"><view class="action-btn generate data-v-b77d809d" bindtap="{{d}}"><text class="btn-icon data-v-b77d809d">➡️</text><text class="btn-text data-v-b77d809d">生成</text></view></view></view></view></view><view wx:if="{{e}}" class="results-section data-v-b77d809d"><view class="section-header data-v-b77d809d"><text class="section-icon data-v-b77d809d">🎯</text><text class="section-title data-v-b77d809d">生成结果</text></view><view class="results-grid data-v-b77d809d"><view wx:for="{{f}}" wx:for-item="result" wx:key="c" class="result-item data-v-b77d809d" bindtap="{{result.d}}"><text class="result-text data-v-b77d809d">{{result.a}}</text><view class="result-actions data-v-b77d809d"><view class="mini-btn data-v-b77d809d" catchtap="{{result.b}}"><text class="mini-icon data-v-b77d809d">📋</text></view></view></view></view></view><view class="examples-section data-v-b77d809d"><view class="section-header data-v-b77d809d"><text class="section-icon data-v-b77d809d">💡</text><text class="section-title data-v-b77d809d">快速示例</text></view><view class="examples-grid data-v-b77d809d"><view wx:for="{{g}}" wx:for-item="example" wx:key="b" class="example-item data-v-b77d809d" bindtap="{{example.c}}"><text class="example-text data-v-b77d809d">{{example.a}}</text></view></view></view><view class="styles-section data-v-b77d809d"><view class="section-header data-v-b77d809d"><text class="section-icon data-v-b77d809d">🎨</text><text class="section-title data-v-b77d809d">箭头样式</text></view><view class="styles-grid data-v-b77d809d"><view wx:for="{{h}}" wx:for-item="style" wx:key="c" class="style-item data-v-b77d809d" bindtap="{{style.d}}"><text class="style-symbol data-v-b77d809d">{{style.a}}</text><text class="style-name data-v-b77d809d">{{style.b}}</text></view></view></view><view class="help-section data-v-b77d809d"><view class="section-header data-v-b77d809d"><text class="section-icon data-v-b77d809d">📖</text><text class="section-title data-v-b77d809d">使用说明</text></view><view class="help-content data-v-b77d809d"><text class="help-item data-v-b77d809d">• 输入任意文字，点击生成按钮</text><text class="help-item data-v-b77d809d">• 支持多种箭头装饰样式</text><text class="help-item data-v-b77d809d">• 可以收藏和分享生成的文字</text><text class="help-item data-v-b77d809d">• 自动保存历史记录</text><text class="help-item data-v-b77d809d">• 适用于突出重点和指示方向</text></view></view></view></view>