"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const toolService = new utils_toolService.ToolService();
const _sfc_main = {
  data() {
    return {
      imagePrompt: "",
      selectedStyle: "realistic",
      isGenerating: false,
      generatedImage: null,
      imageStyles: [
        { value: "realistic", name: "写实", icon: "📷" },
        { value: "cartoon", name: "卡通", icon: "🎨" },
        { value: "anime", name: "动漫", icon: "🌸" },
        { value: "abstract", name: "抽象", icon: "🌀" },
        { value: "sketch", name: "素描", icon: "✏️" },
        { value: "watercolor", name: "水彩", icon: "🎨" }
      ]
    };
  },
  onLoad() {
    common_vendor.index.setNavigationBarTitle({
      title: "趣味图片生成器"
    });
  },
  methods: {
    async generateImage() {
      if (!this.imagePrompt.trim()) {
        utils_index.showError("请输入图片描述");
        return;
      }
      this.isGenerating = true;
      this.generatedImage = null;
      try {
        const params = {
          type: "ai-generate",
          text: this.imagePrompt.trim(),
          style: this.selectedStyle,
          size: "1024x1024",
          format: "png",
          quality: "hd",
          provider: "laozhang"
          // 使用推荐的laozhang.ai提供商
        };
        const result = await toolService.generateFunImage(params);
        this.generatedImage = {
          imageUrl: result.imageUrl,
          style: this.getStyleName(this.selectedStyle),
          size: params.size,
          prompt: this.imagePrompt,
          provider: result.provider || "laozhang",
          cost: result.estimatedCost || 0.01
        };
        utils_index.showSuccess("图片生成成功！");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/fun-image-generator.vue:205", "图片生成失败:", error);
        utils_index.showError(error.message || "图片生成失败，请重试");
      } finally {
        this.isGenerating = false;
      }
    },
    getStyleName(styleValue) {
      const style = this.imageStyles.find((s) => s.value === styleValue);
      return style ? style.name : styleValue;
    },
    previewImage() {
      if (this.generatedImage) {
        common_vendor.index.previewImage({
          urls: [this.generatedImage.imageUrl],
          current: 0
        });
      }
    },
    async downloadImage() {
      if (!this.generatedImage)
        return;
      try {
        await toolService.downloadFile(
          this.generatedImage.imageUrl,
          `generated_image_${Date.now()}.jpg`
        );
        utils_index.showSuccess("图片已保存到相册");
      } catch (error) {
        utils_index.showError("保存失败：" + error.message);
      }
    },
    async shareImage() {
      if (!this.generatedImage)
        return;
      try {
        await toolService.shareContent(
          "我用AI生成了一张有趣的图片",
          this.generatedImage.imageUrl,
          this.generatedImage.imageUrl
        );
      } catch (error) {
        utils_index.showError("分享失败：" + error.message);
      }
    },
    regenerateImage() {
      if (this.imagePrompt.trim()) {
        this.generateImage();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.imagePrompt,
    b: common_vendor.o(($event) => $data.imagePrompt = $event.detail.value),
    c: common_vendor.t($data.imagePrompt.length),
    d: common_vendor.f($data.imageStyles, (style, k0, i0) => {
      return {
        a: common_vendor.t(style.icon),
        b: common_vendor.t(style.name),
        c: style.value,
        d: common_vendor.n({
          active: $data.selectedStyle === style.value
        }),
        e: common_vendor.o(($event) => $data.selectedStyle = style.value, style.value)
      };
    }),
    e: $data.isGenerating
  }, $data.isGenerating ? {} : {}, {
    f: !$data.imagePrompt.trim() || $data.isGenerating,
    g: common_vendor.o((...args) => $options.generateImage && $options.generateImage(...args)),
    h: $data.generatedImage || $data.isGenerating
  }, $data.generatedImage || $data.isGenerating ? common_vendor.e({
    i: $data.isGenerating
  }, $data.isGenerating ? {} : {}, {
    j: $data.generatedImage
  }, $data.generatedImage ? {
    k: $data.generatedImage.imageUrl,
    l: common_vendor.o((...args) => $options.previewImage && $options.previewImage(...args)),
    m: common_vendor.t($data.generatedImage.style),
    n: common_vendor.t($data.generatedImage.size),
    o: common_vendor.o((...args) => $options.downloadImage && $options.downloadImage(...args)),
    p: common_vendor.o((...args) => $options.shareImage && $options.shareImage(...args)),
    q: common_vendor.o((...args) => $options.regenerateImage && $options.regenerateImage(...args))
  } : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8a1386b7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/fun-image-generator.js.map
