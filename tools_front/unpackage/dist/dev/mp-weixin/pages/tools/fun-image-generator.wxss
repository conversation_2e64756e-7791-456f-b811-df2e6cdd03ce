/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-8a1386b7 {
  display: flex;
}
.flex-1.data-v-8a1386b7 {
  flex: 1;
}
.items-center.data-v-8a1386b7 {
  align-items: center;
}
.justify-center.data-v-8a1386b7 {
  justify-content: center;
}
.justify-between.data-v-8a1386b7 {
  justify-content: space-between;
}
.text-center.data-v-8a1386b7 {
  text-align: center;
}
.rounded.data-v-8a1386b7 {
  border-radius: 3px;
}
.rounded-lg.data-v-8a1386b7 {
  border-radius: 6px;
}
.shadow.data-v-8a1386b7 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-8a1386b7 {
  padding: 16rpx;
}
.m-4.data-v-8a1386b7 {
  margin: 16rpx;
}
.mb-4.data-v-8a1386b7 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-8a1386b7 {
  margin-top: 16rpx;
}
.fun-image-generator-page.data-v-8a1386b7 {
  background: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx;
}
.page-header.data-v-8a1386b7 {
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  color: white;
  margin-bottom: 20rpx;
}
.tool-icon.data-v-8a1386b7 {
  font-size: 60rpx;
  margin-bottom: 16rpx;
}
.tool-title.data-v-8a1386b7 {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.tool-subtitle.data-v-8a1386b7 {
  font-size: 24rpx;
  opacity: 0.9;
}
.function-card.data-v-8a1386b7, .result-card.data-v-8a1386b7, .tips-card.data-v-8a1386b7 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card-title.data-v-8a1386b7 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.title-icon.data-v-8a1386b7 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.title-text.data-v-8a1386b7 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}
.input-section.data-v-8a1386b7 {
  position: relative;
  margin-bottom: 30rpx;
}
.prompt-textarea.data-v-8a1386b7 {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
}
.char-count.data-v-8a1386b7 {
  position: absolute;
  bottom: 12rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: #999;
}
.style-section.data-v-8a1386b7 {
  margin-bottom: 30rpx;
}
.section-title.data-v-8a1386b7 {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}
.style-grid.data-v-8a1386b7 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.style-item.data-v-8a1386b7 {
  padding: 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s;
}
.style-item.active.data-v-8a1386b7 {
  border-color: #a855f7;
  background: #f3f4f6;
}
.style-icon.data-v-8a1386b7 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.style-name.data-v-8a1386b7 {
  font-size: 24rpx;
  color: #666;
}
.generate-button.data-v-8a1386b7 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.generate-button[disabled].data-v-8a1386b7 {
  background: #ccc;
}
.loading-container.data-v-8a1386b7 {
  text-align: center;
  padding: 60rpx 0;
}
.loading-animation.data-v-8a1386b7 {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-bottom: 20rpx;
}
.loading-dot.data-v-8a1386b7 {
  width: 12rpx;
  height: 12rpx;
  background: #a855f7;
  border-radius: 50%;
  animation: loading-8a1386b7 1.4s infinite ease-in-out both;
}
.loading-dot.data-v-8a1386b7:nth-child(1) {
  animation-delay: -0.32s;
}
.loading-dot.data-v-8a1386b7:nth-child(2) {
  animation-delay: -0.16s;
}
@keyframes loading-8a1386b7 {
0%, 80%, 100% {
    transform: scale(0);
}
40% {
    transform: scale(1);
}
}
.loading-text.data-v-8a1386b7 {
  font-size: 26rpx;
  color: #666;
}
.result-container.data-v-8a1386b7 {
  text-align: center;
}
.image-container.data-v-8a1386b7 {
  margin-bottom: 20rpx;
}
.generated-image.data-v-8a1386b7 {
  width: 100%;
  max-width: 400rpx;
  max-height: 400rpx;
  border-radius: 12rpx;
}
.result-info.data-v-8a1386b7 {
  margin-bottom: 30rpx;
}
.info-item.data-v-8a1386b7 {
  display: flex;
  justify-content: center;
  margin-bottom: 8rpx;
}
.info-label.data-v-8a1386b7 {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}
.info-value.data-v-8a1386b7 {
  font-size: 24rpx;
  color: #333;
}
.action-buttons.data-v-8a1386b7 {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}
.action-btn.data-v-8a1386b7 {
  flex: 1;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}
.action-btn.primary.data-v-8a1386b7 {
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  color: white;
}
.action-btn.secondary.data-v-8a1386b7 {
  background: #f3f4f6;
  color: #666;
}
.btn-icon.data-v-8a1386b7 {
  margin-right: 8rpx;
}
.tip-item.data-v-8a1386b7 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}
.tip-icon.data-v-8a1386b7 {
  color: #a855f7;
  margin-right: 12rpx;
  font-size: 24rpx;
}
.tip-text.data-v-8a1386b7 {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}