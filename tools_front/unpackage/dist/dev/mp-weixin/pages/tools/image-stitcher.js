"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImages: [],
      selectedMode: "horizontal",
      gridCols: 2,
      isStitching: false,
      stitchedImageUrl: "",
      canvasWidth: 800,
      canvasHeight: 600,
      stitchModes: [
        {
          id: "horizontal",
          name: "水平拼接",
          icon: "↔️",
          description: "图片从左到右排列"
        },
        {
          id: "vertical",
          name: "垂直拼接",
          icon: "↕️",
          description: "图片从上到下排列"
        },
        {
          id: "grid",
          name: "网格拼接",
          icon: "⚏",
          description: "图片按网格排列"
        }
      ]
    };
  },
  methods: {
    // 选择图片
    selectImages() {
      common_vendor.index.chooseImage({
        count: 9,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const newImages = res.tempFilePaths.map((path, index) => ({
            url: path,
            id: Date.now() + index
          }));
          this.selectedImages = [...this.selectedImages, ...newImages].slice(0, 9);
          common_vendor.index.showToast({
            title: `已选择${this.selectedImages.length}张图片`,
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-stitcher.vue:212", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 移除图片
    removeImage(index) {
      this.selectedImages.splice(index, 1);
      if (this.selectedImages.length === 0) {
        this.resetStitch();
      }
      common_vendor.index.showToast({
        title: "图片已移除",
        icon: "success"
      });
    },
    // 选择拼接模式
    selectMode(modeId) {
      this.selectedMode = modeId;
      if (this.stitchedImageUrl) {
        this.stitchedImageUrl = "";
      }
      const mode = this.stitchModes.find((m) => m.id === modeId);
      common_vendor.index.showToast({
        title: `已选择${mode.name}`,
        icon: "success"
      });
    },
    // 调整网格列数
    adjustGridCols(delta) {
      const newCols = this.gridCols + delta;
      if (newCols >= 1 && newCols <= Math.min(this.selectedImages.length, 5)) {
        this.gridCols = newCols;
        if (this.stitchedImageUrl) {
          this.stitchedImageUrl = "";
        }
      }
    },
    // 开始拼接
    async handleStitch() {
      if (this.selectedImages.length < 2) {
        common_vendor.index.showToast({
          title: "至少需要2张图片",
          icon: "none"
        });
        return;
      }
      this.isStitching = true;
      try {
        await this.stitchImages();
        common_vendor.index.showToast({
          title: "拼接完成！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-stitcher.vue:285", "拼接失败:", error);
        common_vendor.index.showToast({
          title: "拼接失败，请重试",
          icon: "none"
        });
      } finally {
        this.isStitching = false;
      }
    },
    // 拼接图片核心逻辑
    async stitchImages() {
      const ctx = common_vendor.index.createCanvasContext("stitchCanvas", this);
      const imageInfos = await Promise.all(
        this.selectedImages.map((img) => this.getImageInfo(img.url))
      );
      const layout = this.calculateLayout(imageInfos);
      this.canvasWidth = layout.canvasWidth;
      this.canvasHeight = layout.canvasHeight;
      await this.$nextTick();
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      for (let i = 0; i < imageInfos.length; i++) {
        const imageInfo = imageInfos[i];
        const position = layout.positions[i];
        ctx.drawImage(
          imageInfo.path,
          position.x,
          position.y,
          position.width,
          position.height
        );
      }
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "stitchCanvas",
            success: (res) => {
              this.stitchedImageUrl = res.tempFilePath;
              common_vendor.index.__f__("log", "at pages/tools/image-stitcher.vue:340", "拼接完成，图片路径:", res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/image-stitcher.vue:343", "导出图片失败:", err);
              throw new Error("导出图片失败");
            }
          }, this);
        }, 500);
      });
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 计算布局
    calculateLayout(imageInfos) {
      const maxWidth = 800;
      const maxHeight = 1200;
      const padding = 10;
      let canvasWidth, canvasHeight;
      const positions = [];
      if (this.selectedMode === "horizontal") {
        const targetHeight = Math.min(400, Math.max(...imageInfos.map((img) => img.height)));
        let totalWidth = 0;
        for (let i = 0; i < imageInfos.length; i++) {
          const img = imageInfos[i];
          const scale = targetHeight / img.height;
          const scaledWidth = img.width * scale;
          positions.push({
            x: totalWidth,
            y: 0,
            width: scaledWidth,
            height: targetHeight
          });
          totalWidth += scaledWidth;
        }
        canvasWidth = Math.min(totalWidth, maxWidth);
        canvasHeight = targetHeight;
      } else if (this.selectedMode === "vertical") {
        const targetWidth = Math.min(400, Math.max(...imageInfos.map((img) => img.width)));
        let totalHeight = 0;
        for (let i = 0; i < imageInfos.length; i++) {
          const img = imageInfos[i];
          const scale = targetWidth / img.width;
          const scaledHeight = img.height * scale;
          positions.push({
            x: 0,
            y: totalHeight,
            width: targetWidth,
            height: scaledHeight
          });
          totalHeight += scaledHeight;
        }
        canvasWidth = targetWidth;
        canvasHeight = Math.min(totalHeight, maxHeight);
      } else {
        const cols = this.gridCols;
        const rows = Math.ceil(imageInfos.length / cols);
        const cellWidth = (maxWidth - padding * (cols + 1)) / cols;
        const cellHeight = cellWidth;
        for (let i = 0; i < imageInfos.length; i++) {
          const row = Math.floor(i / cols);
          const col = i % cols;
          positions.push({
            x: padding + col * (cellWidth + padding),
            y: padding + row * (cellHeight + padding),
            width: cellWidth,
            height: cellHeight
          });
        }
        canvasWidth = maxWidth;
        canvasHeight = padding + rows * (cellHeight + padding);
      }
      return {
        canvasWidth,
        canvasHeight,
        positions
      };
    },
    // 重置拼接
    resetStitch() {
      this.stitchedImageUrl = "";
      this.isStitching = false;
    },
    // 保存图片
    saveImage() {
      if (!this.stitchedImageUrl) {
        common_vendor.index.showToast({
          title: "没有可保存的图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.stitchedImageUrl,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功！",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/image-stitcher.vue:478", "保存失败:", err);
          if (err.errMsg.includes("auth")) {
            common_vendor.index.showModal({
              title: "需要授权",
              content: "需要您授权保存图片到相册",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedImages.length > 0
  }, $data.selectedImages.length > 0 ? {
    b: common_vendor.t($data.selectedImages.length)
  } : {}, {
    c: common_vendor.o((...args) => $options.selectImages && $options.selectImages(...args)),
    d: $data.selectedImages.length > 0
  }, $data.selectedImages.length > 0 ? {
    e: common_vendor.f($data.selectedImages, (image, index, i0) => {
      return {
        a: image.url,
        b: common_vendor.o(($event) => $options.removeImage(index), index),
        c: common_vendor.t(index + 1),
        d: index
      };
    })
  } : {}, {
    f: $data.selectedImages.length > 1
  }, $data.selectedImages.length > 1 ? {
    g: common_vendor.f($data.stitchModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: common_vendor.t(mode.description),
        d: mode.id,
        e: $data.selectedMode === mode.id ? 1 : "",
        f: common_vendor.o(($event) => $options.selectMode(mode.id), mode.id)
      };
    })
  } : {}, {
    h: $data.selectedImages.length > 1 && $data.selectedMode === "grid"
  }, $data.selectedImages.length > 1 && $data.selectedMode === "grid" ? {
    i: common_vendor.o(($event) => $options.adjustGridCols(-1)),
    j: common_vendor.t($data.gridCols),
    k: common_vendor.o(($event) => $options.adjustGridCols(1))
  } : {}, {
    l: $data.selectedImages.length > 1
  }, $data.selectedImages.length > 1 ? {
    m: common_vendor.t($data.isStitching ? "拼接中..." : "开始拼接"),
    n: $data.isStitching ? 1 : "",
    o: common_vendor.o((...args) => $options.handleStitch && $options.handleStitch(...args)),
    p: $data.isStitching
  } : {}, {
    q: $data.stitchedImageUrl
  }, $data.stitchedImageUrl ? {
    r: $data.stitchedImageUrl,
    s: common_vendor.o((...args) => $options.resetStitch && $options.resetStitch(...args)),
    t: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args))
  } : {}, {
    v: $data.canvasWidth + "px",
    w: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a2cc706b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-stitcher.js.map
