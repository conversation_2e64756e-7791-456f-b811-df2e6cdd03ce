<view class="container data-v-90a76c95"><view class="main-content data-v-90a76c95"><view class="input-section data-v-90a76c95"><view class="section-title data-v-90a76c95"><text class="title-icon data-v-90a76c95">❤️</text><text class="title-text data-v-90a76c95">表白内容</text></view><view class="input-container data-v-90a76c95"><input class="name-input data-v-90a76c95" placeholder="TA的名字（可选）" value="{{a}}" bindinput="{{b}}"/><view class="textarea-container data-v-90a76c95"><block wx:if="{{r0}}"><textarea class="message-input data-v-90a76c95" placeholder="输入你想说的话，比如：我喜欢你很久了..." maxlength="500" bindinput="{{c}}" value="{{d}}"/></block><view class="char-count data-v-90a76c95">{{e}}/500</view></view></view></view><view class="language-section data-v-90a76c95"><view class="section-title data-v-90a76c95"><text class="title-icon data-v-90a76c95">💻</text><text class="title-text data-v-90a76c95">选择编程语言</text></view><view class="language-grid data-v-90a76c95"><view wx:for="{{f}}" wx:for-item="lang" wx:key="c" class="{{['language-item', 'data-v-90a76c95', lang.d && 'active']}}" bindtap="{{lang.e}}"><view class="lang-icon data-v-90a76c95">{{lang.a}}</view><view class="lang-name data-v-90a76c95">{{lang.b}}</view></view></view></view><view wx:if="{{g}}" class="result-section data-v-90a76c95"><view class="section-header data-v-90a76c95"><view class="section-title data-v-90a76c95"><text class="title-icon data-v-90a76c95">🎉</text><text class="title-text data-v-90a76c95">生成的表白代码</text></view><view class="action-buttons data-v-90a76c95"><button class="action-btn copy-btn data-v-90a76c95" bindtap="{{h}}"><text class="action-icon data-v-90a76c95">📋</text><text class="action-text data-v-90a76c95">复制</text></button><button class="action-btn download-btn data-v-90a76c95" bindtap="{{i}}"><text class="action-icon data-v-90a76c95">⬇️</text><text class="action-text data-v-90a76c95">下载</text></button></view></view><view class="result-container data-v-90a76c95"><view class="code-container data-v-90a76c95"><text class="code-text data-v-90a76c95">{{j}}</text></view><view class="result-tip data-v-90a76c95"><text class="tip-text data-v-90a76c95">💝 代码已生成！快去向TA表白吧～</text></view></view></view><view class="generate-section data-v-90a76c95"><button class="{{['generate-btn', 'data-v-90a76c95', m && 'disabled', n && 'loading']}}" bindtap="{{o}}" disabled="{{p}}"><text class="btn-icon data-v-90a76c95">{{k}}</text><text class="btn-text data-v-90a76c95">{{l}}</text></button></view></view></view>