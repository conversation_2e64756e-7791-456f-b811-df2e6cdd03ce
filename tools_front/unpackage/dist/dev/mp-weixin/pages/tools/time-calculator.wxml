<view class="time-calculator data-v-0b025161"><view class="header-card data-v-0b025161"><view class="header-content data-v-0b025161"><view class="header-icon data-v-0b025161">⏰</view><view class="header-info data-v-0b025161"><text class="header-title data-v-0b025161">时间差计算器</text><text class="header-subtitle data-v-0b025161">精确计算两个时间点之间的时间差</text></view></view></view><view class="preset-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="header-icon data-v-0b025161">⚡</text><text class="header-title data-v-0b025161">快速预设</text></view><view class="preset-grid data-v-0b025161"><view bindtap="{{a}}" class="preset-item data-v-0b025161"><view class="preset-icon data-v-0b025161">💼</view><text class="preset-title data-v-0b025161">工作时间</text><text class="preset-desc data-v-0b025161">9:00-18:00</text></view><view bindtap="{{b}}" class="preset-item data-v-0b025161"><view class="preset-icon data-v-0b025161">🌙</view><text class="preset-title data-v-0b025161">睡眠时间</text><text class="preset-desc data-v-0b025161">23:00-7:00</text></view><view bindtap="{{c}}" class="preset-item data-v-0b025161"><view class="preset-icon data-v-0b025161">📅</view><text class="preset-title data-v-0b025161">一整天</text><text class="preset-desc data-v-0b025161">24小时</text></view><view bindtap="{{d}}" class="preset-item data-v-0b025161"><view class="preset-icon data-v-0b025161">🎉</view><text class="preset-title data-v-0b025161">周末时光</text><text class="preset-desc data-v-0b025161">本周末48小时</text></view></view></view><view class="time-card start-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="status-dot start-dot data-v-0b025161">●</text><text class="header-title data-v-0b025161">开始时间</text></view><view class="time-inputs data-v-0b025161"><view class="input-group data-v-0b025161"><text class="input-label data-v-0b025161">日期</text><picker mode="date" value="{{f}}" bindchange="{{g}}" class="date-picker data-v-0b025161"><view class="picker-display data-v-0b025161"><text class="picker-value data-v-0b025161">{{e}}</text><text class="picker-arrow data-v-0b025161">📅</text></view></picker></view><view class="input-group data-v-0b025161"><text class="input-label data-v-0b025161">时间</text><picker mode="time" value="{{i}}" bindchange="{{j}}" class="time-picker data-v-0b025161"><view class="picker-display data-v-0b025161"><text class="picker-value data-v-0b025161">{{h}}</text><text class="picker-arrow data-v-0b025161">🕐</text></view></picker></view></view><view class="quick-actions data-v-0b025161"><button bindtap="{{k}}" class="quick-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">⚡</text><text class="btn-text data-v-0b025161">设为现在</text></button><button bindtap="{{l}}" class="quick-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">📆</text><text class="btn-text data-v-0b025161">昨天同时</text></button></view></view><view class="time-card end-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="status-dot end-dot data-v-0b025161">●</text><text class="header-title data-v-0b025161">结束时间</text></view><view class="time-inputs data-v-0b025161"><view class="input-group data-v-0b025161"><text class="input-label data-v-0b025161">日期</text><picker mode="date" value="{{n}}" bindchange="{{o}}" class="date-picker data-v-0b025161"><view class="picker-display data-v-0b025161"><text class="picker-value data-v-0b025161">{{m}}</text><text class="picker-arrow data-v-0b025161">📅</text></view></picker></view><view class="input-group data-v-0b025161"><text class="input-label data-v-0b025161">时间</text><picker mode="time" value="{{q}}" bindchange="{{r}}" class="time-picker data-v-0b025161"><view class="picker-display data-v-0b025161"><text class="picker-value data-v-0b025161">{{p}}</text><text class="picker-arrow data-v-0b025161">🕐</text></view></picker></view></view><view class="quick-actions data-v-0b025161"><button bindtap="{{s}}" class="quick-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">⚡</text><text class="btn-text data-v-0b025161">设为现在</text></button><button bindtap="{{t}}" class="quick-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">📆</text><text class="btn-text data-v-0b025161">明天同时</text></button></view></view><view class="action-buttons data-v-0b025161"><button bindtap="{{v}}" disabled="{{w}}" class="{{['data-v-0b025161', 'calc-btn', x]}}"><text class="btn-icon data-v-0b025161">🧮</text><text class="btn-text data-v-0b025161">开始计算</text></button><button bindtap="{{y}}" class="clear-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">🧹</text><text class="btn-text data-v-0b025161">清空</text></button></view><view wx:if="{{z}}" class="result-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="header-icon data-v-0b025161">📊</text><text class="header-title data-v-0b025161">计算结果</text><button bindtap="{{A}}" class="save-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">💾</text><text class="btn-text data-v-0b025161">保存</text></button></view><view class="primary-result data-v-0b025161"><view class="result-header data-v-0b025161"><text class="result-title data-v-0b025161">⏱️ 精确时间差</text></view><view class="result-main data-v-0b025161"><text class="result-text data-v-0b025161"><text wx:if="{{B}}" class="time-unit data-v-0b025161">{{C}}年 </text><text wx:if="{{D}}" class="time-unit data-v-0b025161">{{E}}个月 </text><text wx:if="{{F}}" class="time-unit data-v-0b025161">{{G}}天 </text><text wx:if="{{H}}" class="time-unit data-v-0b025161">{{I}}小时 </text><text wx:if="{{J}}" class="time-unit data-v-0b025161">{{K}}分钟 </text><text class="time-unit data-v-0b025161">{{L}}秒</text></text></view><view class="result-summary data-v-0b025161"><text class="data-v-0b025161">总计: {{M}} 秒 ({{N}} 分钟)</text></view></view><view class="units-grid data-v-0b025161"><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{O}}</text><text class="unit-label data-v-0b025161">年</text></view><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{P}}</text><text class="unit-label data-v-0b025161">个月</text></view><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{Q}}</text><text class="unit-label data-v-0b025161">天</text></view><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{R}}</text><text class="unit-label data-v-0b025161">小时</text></view><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{S}}</text><text class="unit-label data-v-0b025161">分钟</text></view><view class="unit-item data-v-0b025161"><text class="unit-number data-v-0b025161">{{T}}</text><text class="unit-label data-v-0b025161">秒</text></view></view><view class="comparison-section data-v-0b025161"><view class="comparison-header data-v-0b025161"><text class="comparison-title data-v-0b025161">🌱 生活化对比</text></view><view class="comparison-list data-v-0b025161"><view class="comparison-item data-v-0b025161"><text class="comparison-icon data-v-0b025161">🎬</text><text class="comparison-text data-v-0b025161">相当于看了 {{U}} 部电影</text></view><view class="comparison-item data-v-0b025161"><text class="comparison-icon data-v-0b025161">🎵</text><text class="comparison-text data-v-0b025161">相当于听了 {{V}} 首歌</text></view><view class="comparison-item data-v-0b025161"><text class="comparison-icon data-v-0b025161">🚶</text><text class="comparison-text data-v-0b025161">相当于走了 {{W}} 公里</text></view><view wx:if="{{X}}" class="comparison-item data-v-0b025161"><text class="comparison-icon data-v-0b025161">📈</text><text class="comparison-text data-v-0b025161">相当于 {{Y}}% 的一年时间</text></view></view></view><view class="result-actions data-v-0b025161"><button bindtap="{{Z}}" class="action-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">📋</text><text class="btn-text data-v-0b025161">复制结果</text></button><button bindtap="{{aa}}" class="action-btn data-v-0b025161"><text class="btn-icon data-v-0b025161">📤</text><text class="btn-text data-v-0b025161">分享结果</text></button></view></view><view wx:if="{{ab}}" class="history-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="header-icon data-v-0b025161">📚</text><text class="header-title data-v-0b025161">计算历史</text><button bindtap="{{ac}}" class="clear-history-btn data-v-0b025161"><text class="btn-text data-v-0b025161">清空历史</text></button></view><scroll-view scroll-y="true" class="history-scroll data-v-0b025161"><view class="history-list data-v-0b025161"><view wx:for="{{ad}}" wx:for-item="record" wx:key="d" class="history-item data-v-0b025161" bindtap="{{record.e}}"><view class="history-content data-v-0b025161"><view class="history-main data-v-0b025161"><text class="history-desc data-v-0b025161">{{record.a}}</text><text class="history-result data-v-0b025161">{{record.b}}</text></view><view class="history-time data-v-0b025161"><text class="time-text data-v-0b025161">{{record.c}}</text></view></view></view></view></scroll-view></view><view class="tips-card data-v-0b025161"><view class="card-header data-v-0b025161"><text class="header-icon data-v-0b025161">💡</text><text class="header-title data-v-0b025161">使用说明</text></view><view class="tips-list data-v-0b025161"><view class="tip-item data-v-0b025161"><text class="tip-bullet data-v-0b025161">•</text><text class="tip-text data-v-0b025161">精确计算: 支持精确到秒的时间差计算</text></view><view class="tip-item data-v-0b025161"><text class="tip-bullet data-v-0b025161">•</text><text class="tip-text data-v-0b025161">快速预设: 提供常用时间段的快速设置</text></view><view class="tip-item data-v-0b025161"><text class="tip-bullet data-v-0b025161">•</text><text class="tip-text data-v-0b025161">多种单位: 显示年、月、日、时、分、秒等多种单位</text></view><view class="tip-item data-v-0b025161"><text class="tip-bullet data-v-0b025161">•</text><text class="tip-text data-v-0b025161">生活化对比: 将时间差转换为易于理解的生活场景</text></view><view class="tip-item data-v-0b025161"><text class="tip-bullet data-v-0b025161">•</text><text class="tip-text data-v-0b025161">历史记录: 保存计算结果，支持重新加载</text></view></view></view></view>