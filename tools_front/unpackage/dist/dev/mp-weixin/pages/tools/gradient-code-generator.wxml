<view class="gradient-generator data-v-c5ef31c6"><view class="content data-v-c5ef31c6"><view class="card data-v-c5ef31c6"><view class="card-header data-v-c5ef31c6"><text class="header-title data-v-c5ef31c6">渐变代码生成器</text></view><view class="card-content data-v-c5ef31c6"><view class="preview-section data-v-c5ef31c6"><view class="preview-card data-v-c5ef31c6" style="{{a}}"></view></view><view class="control-panel data-v-c5ef31c6"><view class="control-item data-v-c5ef31c6"><text class="label data-v-c5ef31c6">渐变类型</text><view class="type-selector data-v-c5ef31c6"><view class="{{['type-option', 'data-v-c5ef31c6', b && 'active']}}" bindtap="{{c}}"><text class="option-text data-v-c5ef31c6">线性渐变</text></view><view class="{{['type-option', 'data-v-c5ef31c6', d && 'active']}}" bindtap="{{e}}"><text class="option-text data-v-c5ef31c6">径向渐变</text></view></view></view><view wx:if="{{f}}" class="control-item data-v-c5ef31c6"><text class="label data-v-c5ef31c6">方向/角度</text><view class="direction-grid data-v-c5ef31c6"><view wx:for="{{g}}" wx:for-item="dir" wx:key="b" class="{{['direction-option', 'data-v-c5ef31c6', dir.c && 'active']}}" bindtap="{{dir.d}}"><text class="direction-text data-v-c5ef31c6">{{dir.a}}</text></view></view></view><view class="control-item data-v-c5ef31c6"><view class="color-header data-v-c5ef31c6"><text class="label data-v-c5ef31c6">颜色设置</text><view class="{{['add-color-btn', 'data-v-c5ef31c6', h && 'disabled']}}" bindtap="{{i}}"><text class="btn-text data-v-c5ef31c6">添加颜色</text></view></view><view class="color-list data-v-c5ef31c6"><view wx:for="{{j}}" wx:for-item="color" wx:key="h" class="color-item data-v-c5ef31c6"><view class="color-item data-v-c5ef31c6"><view class="color-preview data-v-c5ef31c6" style="{{'background-color:' + color.a}}" bindtap="{{color.b}}"></view><view class="color-controls data-v-c5ef31c6"><text class="color-value data-v-c5ef31c6">{{color.c}}</text><view class="color-position data-v-c5ef31c6"><text class="position-label data-v-c5ef31c6">位置</text><slider class="position-slider data-v-c5ef31c6" value="{{color.d}}" bindchange="{{color.e}}" min="0" max="100" activeColor="#8B5CF6"/><text class="position-value data-v-c5ef31c6">{{color.f}}%</text></view></view><view class="color-actions data-v-c5ef31c6"><view wx:if="{{k}}" class="action-btn delete data-v-c5ef31c6" bindtap="{{color.g}}"><text class="delete-icon data-v-c5ef31c6">×</text></view></view></view></view></view></view></view><view class="code-section data-v-c5ef31c6"><view class="code-header data-v-c5ef31c6"><text class="label data-v-c5ef31c6">CSS代码</text><view class="copy-btn data-v-c5ef31c6" bindtap="{{l}}"><text class="copy-icon data-v-c5ef31c6">📋</text><text class="copy-text data-v-c5ef31c6">复制</text></view></view><view class="code-container data-v-c5ef31c6"><text class="code-text data-v-c5ef31c6">{{m}}</text></view></view><view class="info-section data-v-c5ef31c6"><text class="info-title data-v-c5ef31c6">使用说明</text><view class="info-list data-v-c5ef31c6"><text class="info-item data-v-c5ef31c6">• 选择渐变类型：线性或径向渐变</text><text class="info-item data-v-c5ef31c6">• 线性渐变可设置方向和角度</text><text class="info-item data-v-c5ef31c6">• 添加2-5个颜色创建复杂渐变</text><text class="info-item data-v-c5ef31c6">• 调整颜色位置控制渐变效果</text><text class="info-item data-v-c5ef31c6">• 复制生成的CSS代码直接使用</text></view></view></view></view></view><view wx:if="{{n}}" class="color-picker-modal data-v-c5ef31c6" bindtap="{{H}}"><view class="color-picker-container data-v-c5ef31c6" catchtap="{{G}}"><view class="picker-header data-v-c5ef31c6"><text class="picker-title data-v-c5ef31c6">选择颜色</text><view class="picker-close data-v-c5ef31c6" bindtap="{{o}}"><text class="close-icon data-v-c5ef31c6">×</text></view></view><view class="color-canvas-container data-v-c5ef31c6"><block wx:if="{{r0}}"><canvas class="color-canvas data-v-c5ef31c6" canvas-id="colorCanvas" bindtouchstart="{{p}}" bindtouchmove="{{q}}"></canvas></block><view class="canvas-cursor data-v-c5ef31c6" style="{{r}}"></view></view><view class="hue-slider-container data-v-c5ef31c6"><block wx:if="{{r0}}"><canvas class="hue-slider data-v-c5ef31c6" canvas-id="hueCanvas" bindtouchstart="{{s}}" bindtouchmove="{{t}}"></canvas></block><view class="hue-cursor data-v-c5ef31c6" style="{{v}}"></view></view><view class="rgb-inputs data-v-c5ef31c6"><view class="rgb-group data-v-c5ef31c6"><text class="rgb-label data-v-c5ef31c6">R</text><input class="rgb-input data-v-c5ef31c6" type="number" bindinput="{{w}}" min="0" max="255" value="{{x}}"/></view><view class="rgb-group data-v-c5ef31c6"><text class="rgb-label data-v-c5ef31c6">G</text><input class="rgb-input data-v-c5ef31c6" type="number" bindinput="{{y}}" min="0" max="255" value="{{z}}"/></view><view class="rgb-group data-v-c5ef31c6"><text class="rgb-label data-v-c5ef31c6">B</text><input class="rgb-input data-v-c5ef31c6" type="number" bindinput="{{A}}" min="0" max="255" value="{{B}}"/></view></view><view class="picker-preview data-v-c5ef31c6"><view class="preview-color data-v-c5ef31c6" style="{{'background-color:' + C}}"></view><text class="hex-value data-v-c5ef31c6">{{D}}</text></view><view class="picker-actions data-v-c5ef31c6"><view class="picker-btn cancel data-v-c5ef31c6" bindtap="{{E}}">取消</view><view class="picker-btn confirm data-v-c5ef31c6" bindtap="{{F}}">确认</view></view></view></view></view>