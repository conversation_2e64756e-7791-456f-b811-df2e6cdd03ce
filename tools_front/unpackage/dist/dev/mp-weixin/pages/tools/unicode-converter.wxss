/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-f5ba67cb {
  display: flex;
}
.flex-1.data-v-f5ba67cb {
  flex: 1;
}
.items-center.data-v-f5ba67cb {
  align-items: center;
}
.justify-center.data-v-f5ba67cb {
  justify-content: center;
}
.justify-between.data-v-f5ba67cb {
  justify-content: space-between;
}
.text-center.data-v-f5ba67cb {
  text-align: center;
}
.rounded.data-v-f5ba67cb {
  border-radius: 3px;
}
.rounded-lg.data-v-f5ba67cb {
  border-radius: 6px;
}
.shadow.data-v-f5ba67cb {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-f5ba67cb {
  padding: 16rpx;
}
.m-4.data-v-f5ba67cb {
  margin: 16rpx;
}
.mb-4.data-v-f5ba67cb {
  margin-bottom: 16rpx;
}
.mt-4.data-v-f5ba67cb {
  margin-top: 16rpx;
}
.unicode-converter.data-v-f5ba67cb {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-f5ba67cb {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-f5ba67cb {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-f5ba67cb {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-f5ba67cb {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-f5ba67cb {
  padding: 30rpx;
}
.card.data-v-f5ba67cb {
  border-radius: 20rpx;
  box-shadow: 0 6rpx 24rpx rgba(59, 130, 246, 0.06);
  margin-bottom: 32rpx;
  padding: 0;
  overflow: hidden;
}
.card-header.data-v-f5ba67cb {
  padding: 32rpx 40rpx 0 40rpx;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.card-content.data-v-f5ba67cb {
  padding: 32rpx 40rpx 40rpx 40rpx;
}
.mode-selector.data-v-f5ba67cb {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 8rpx;
}
.mode-btn.data-v-f5ba67cb {
  flex: 1;
  padding: 28rpx 0;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  background: #fff;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  transition: all 0.2s;
}
.mode-btn.active.data-v-f5ba67cb {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #fff;
  border-color: transparent;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.1);
}
.swap-btn.data-v-f5ba67cb {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  border: 2rpx solid #e5e7eb;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #3b82f6;
  transition: background 0.2s;
}
.swap-btn.data-v-f5ba67cb:active {
  background: #f3f4f6;
}
.text-area.data-v-f5ba67cb {
  width: 100%;
  min-height: 200rpx;
  padding: 30rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 14rpx;
  font-size: 28rpx;
  color: #333;
  background: #f8f9fa;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}
.text-area.readonly.data-v-f5ba67cb {
  background: #f3f4f6;
  color: #9ca3af;
}
.convert-btn.data-v-f5ba67cb {
  width: 100%;
  padding: 24rpx 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #fff;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 700;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.1);
  margin-top: 8rpx;
  transition: transform 0.1s;
}
.convert-btn.data-v-f5ba67cb:active {
  transform: scale(0.98);
}
.convert-btn.data-v-f5ba67cb:disabled {
  background: #e5e7eb;
  color: #bdbdbd;
  box-shadow: none;
}
.copy-btn.data-v-f5ba67cb {
  display: flex;
  align-items: center;
  padding: 10rpx 18rpx;
  background: #f3f4f6;
  border-radius: 10rpx;
  color: #3b82f6;
  font-size: 26rpx;
  cursor: pointer;
  transition: background 0.2s;
}
.copy-btn.data-v-f5ba67cb:active {
  background: #e0e7ff;
}
.unicode-grid.data-v-f5ba67cb {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.unicode-item.data-v-f5ba67cb {
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: #fff;
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.char.data-v-f5ba67cb {
  font-size: 36rpx;
  margin-right: 12rpx;
}
.char-info.data-v-f5ba67cb {
  flex: 1;
}
.char-desc.data-v-f5ba67cb {
  font-size: 24rpx;
  color: #6c757d;
  margin-bottom: 4rpx;
}
.char-code.data-v-f5ba67cb {
  font-family: monospace;
  font-size: 22rpx;
  color: #adb5bd;
}
.instructions .instruction-item.data-v-f5ba67cb {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 16rpx;
  line-height: 1.6;
}