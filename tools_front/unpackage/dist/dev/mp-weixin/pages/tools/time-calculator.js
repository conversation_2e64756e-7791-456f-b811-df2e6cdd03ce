"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "TimeCalculator",
  data() {
    return {
      startDate: "",
      startTime: "00:00",
      endDate: "",
      endTime: "00:00",
      result: null,
      history: []
    };
  },
  mounted() {
    this.loadHistory();
  },
  methods: {
    // 格式化数字
    formatNumber(num) {
      return num.toLocaleString();
    },
    // 格式化历史时间
    formatHistoryTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleDateString() + " " + date.toLocaleTimeString().slice(0, 5);
    },
    // 格式化显示日期
    formatDisplayDate(dateString) {
      if (!dateString)
        return "";
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },
    // 开始日期变化
    onStartDateChange(e) {
      this.startDate = e.detail.value;
    },
    // 开始时间变化
    onStartTimeChange(e) {
      this.startTime = e.detail.value;
    },
    // 结束日期变化
    onEndDateChange(e) {
      this.endDate = e.detail.value;
    },
    // 结束时间变化
    onEndTimeChange(e) {
      this.endTime = e.detail.value;
    },
    // 快速设置为当前时间
    quickSetToday() {
      const now = /* @__PURE__ */ new Date();
      this.startDate = now.toISOString().split("T")[0];
      this.startTime = now.toTimeString().split(" ")[0].slice(0, 5);
      common_vendor.index.showToast({
        title: "已设置为当前时间",
        icon: "success",
        duration: 1500
      });
    },
    // 快速设置结束时间为现在
    quickSetNow() {
      const now = /* @__PURE__ */ new Date();
      this.endDate = now.toISOString().split("T")[0];
      this.endTime = now.toTimeString().split(" ")[0].slice(0, 5);
      common_vendor.index.showToast({
        title: "已设置为当前时间",
        icon: "success",
        duration: 1500
      });
    },
    // 设置为昨天同一时间
    setYesterday() {
      const yesterday = /* @__PURE__ */ new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      this.startDate = yesterday.toISOString().split("T")[0];
      this.startTime = this.startTime || "00:00";
      common_vendor.index.showToast({
        title: "已设置为昨天",
        icon: "success",
        duration: 1500
      });
    },
    // 设置为明天同一时间
    quickSetTomorrow() {
      const tomorrow = /* @__PURE__ */ new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      this.endDate = tomorrow.toISOString().split("T")[0];
      this.endTime = this.endTime || "00:00";
      common_vendor.index.showToast({
        title: "已设置为明天",
        icon: "success",
        duration: 1500
      });
    },
    // 设置工作时间
    setWorkHours() {
      const today = /* @__PURE__ */ new Date();
      this.startDate = today.toISOString().split("T")[0];
      this.startTime = "09:00";
      this.endDate = today.toISOString().split("T")[0];
      this.endTime = "18:00";
      common_vendor.index.showToast({
        title: "已设置工作时间",
        icon: "success",
        duration: 1500
      });
    },
    // 设置睡眠时间
    setSleepTime() {
      const today = /* @__PURE__ */ new Date();
      const tomorrow = /* @__PURE__ */ new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      this.startDate = today.toISOString().split("T")[0];
      this.startTime = "23:00";
      this.endDate = tomorrow.toISOString().split("T")[0];
      this.endTime = "07:00";
      common_vendor.index.showToast({
        title: "已设置睡眠时间",
        icon: "success",
        duration: 1500
      });
    },
    // 设置一整天
    setFullDay() {
      const today = /* @__PURE__ */ new Date();
      const tomorrow = /* @__PURE__ */ new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      this.startDate = today.toISOString().split("T")[0];
      this.startTime = "00:00";
      this.endDate = tomorrow.toISOString().split("T")[0];
      this.endTime = "00:00";
      common_vendor.index.showToast({
        title: "已设置为一整天",
        icon: "success",
        duration: 1500
      });
    },
    // 设置周末时间
    setWeekend() {
      const now = /* @__PURE__ */ new Date();
      const currentDay = now.getDay();
      const daysToSaturday = (6 - currentDay) % 7;
      const saturday = /* @__PURE__ */ new Date();
      saturday.setDate(saturday.getDate() + daysToSaturday);
      const monday = new Date(saturday);
      monday.setDate(monday.getDate() + 2);
      this.startDate = saturday.toISOString().split("T")[0];
      this.startTime = "00:00";
      this.endDate = monday.toISOString().split("T")[0];
      this.endTime = "00:00";
      common_vendor.index.showToast({
        title: "已设置周末时间",
        icon: "success",
        duration: 1500
      });
    },
    // 清空所有设置
    clearAll() {
      this.startDate = "";
      this.startTime = "00:00";
      this.endDate = "";
      this.endTime = "00:00";
      this.result = null;
      common_vendor.index.showToast({
        title: "已清空所有设置",
        icon: "success",
        duration: 1500
      });
    },
    // 计算时间差
    calculateTimeDifference() {
      if (!this.startDate || !this.endDate) {
        common_vendor.index.showToast({
          title: "请设置开始和结束时间",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      const start = /* @__PURE__ */ new Date(`${this.startDate}T${this.startTime}`);
      const end = /* @__PURE__ */ new Date(`${this.endDate}T${this.endTime}`);
      if (end < start) {
        common_vendor.index.showModal({
          title: "时间设置错误",
          content: "结束时间不能早于开始时间",
          showCancel: false,
          confirmText: "知道了"
        });
        return;
      }
      const diffMs = end.getTime() - start.getTime();
      const diffSeconds = Math.floor(diffMs / 1e3);
      const diffMinutes = Math.floor(diffSeconds / 60);
      const diffHours = Math.floor(diffMinutes / 60);
      const diffDays = Math.floor(diffHours / 24);
      const diffMonths = Math.floor(diffDays / 30.44);
      const diffYears = Math.floor(diffDays / 365.25);
      this.result = {
        milliseconds: diffMs,
        seconds: diffSeconds,
        minutes: diffMinutes,
        hours: diffHours,
        days: diffDays,
        months: diffMonths,
        years: diffYears,
        detailed: {
          years: Math.floor(diffDays / 365.25),
          months: Math.floor(diffDays % 365.25 / 30.44),
          days: Math.floor(diffDays % 30.44),
          hours: Math.floor(diffMs / (1e3 * 60 * 60) % 24),
          minutes: Math.floor(diffMs / (1e3 * 60) % 60),
          seconds: Math.floor(diffMs / 1e3 % 60)
        }
      };
      common_vendor.index.showToast({
        title: "计算完成！",
        icon: "success",
        duration: 1500
      });
    },
    // 保存到历史记录
    saveToHistory() {
      if (!this.result)
        return;
      const record = {
        startDate: this.startDate,
        startTime: this.startTime,
        endDate: this.endDate,
        endTime: this.endTime,
        result: `${this.result.detailed.days}天 ${this.result.detailed.hours}小时 ${this.result.detailed.minutes}分钟`,
        description: `${this.formatDisplayDate(this.startDate)} ${this.startTime} - ${this.formatDisplayDate(this.endDate)} ${this.endTime}`,
        timestamp: /* @__PURE__ */ new Date()
      };
      this.history.unshift(record);
      if (this.history.length > 20) {
        this.history = this.history.slice(0, 20);
      }
      this.saveHistory();
      common_vendor.index.showToast({
        title: "已保存到历史记录",
        icon: "success",
        duration: 1500
      });
    },
    // 从历史记录加载
    loadFromHistory(record) {
      this.startDate = record.startDate;
      this.startTime = record.startTime;
      this.endDate = record.endDate;
      this.endTime = record.endTime;
      this.calculateTimeDifference();
      common_vendor.index.showToast({
        title: "已加载历史记录",
        icon: "success",
        duration: 1500
      });
    },
    // 清空历史记录
    clearHistory() {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有历史记录吗？",
        success: (res) => {
          if (res.confirm) {
            this.history = [];
            common_vendor.index.removeStorageSync("timeCalculatorHistory");
            common_vendor.index.showToast({
              title: "历史记录已清空",
              icon: "success",
              duration: 1500
            });
          }
        }
      });
    },
    // 复制结果
    copyResult() {
      if (!this.result)
        return;
      const text = `时间差计算结果:
${this.formatDisplayDate(this.startDate)} ${this.startTime} - ${this.formatDisplayDate(this.endDate)} ${this.endTime}
共计: ${this.result.detailed.days}天 ${this.result.detailed.hours}小时 ${this.result.detailed.minutes}分钟 ${this.result.detailed.seconds}秒`;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 分享结果
    shareResult() {
      if (!this.result)
        return;
      const text = `时间差计算结果：${this.result.detailed.days}天 ${this.result.detailed.hours}小时 ${this.result.detailed.minutes}分钟`;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "结果已复制，可分享给朋友",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    // 加载历史记录
    loadHistory() {
      try {
        const savedHistory = common_vendor.index.getStorageSync("timeCalculatorHistory");
        if (savedHistory) {
          this.history = JSON.parse(savedHistory).map((record) => ({
            ...record,
            timestamp: new Date(record.timestamp)
          }));
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/time-calculator.vue:664", "加载历史记录失败:", error);
      }
    },
    // 保存历史记录
    saveHistory() {
      try {
        common_vendor.index.setStorageSync("timeCalculatorHistory", JSON.stringify(this.history));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/time-calculator.vue:673", "保存历史记录失败:", error);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.setWorkHours && $options.setWorkHours(...args)),
    b: common_vendor.o((...args) => $options.setSleepTime && $options.setSleepTime(...args)),
    c: common_vendor.o((...args) => $options.setFullDay && $options.setFullDay(...args)),
    d: common_vendor.o((...args) => $options.setWeekend && $options.setWeekend(...args)),
    e: common_vendor.t($options.formatDisplayDate($data.startDate) || "选择日期"),
    f: $data.startDate,
    g: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    h: common_vendor.t($data.startTime || "选择时间"),
    i: $data.startTime,
    j: common_vendor.o((...args) => $options.onStartTimeChange && $options.onStartTimeChange(...args)),
    k: common_vendor.o((...args) => $options.quickSetToday && $options.quickSetToday(...args)),
    l: common_vendor.o((...args) => $options.setYesterday && $options.setYesterday(...args)),
    m: common_vendor.t($options.formatDisplayDate($data.endDate) || "选择日期"),
    n: $data.endDate,
    o: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args)),
    p: common_vendor.t($data.endTime || "选择时间"),
    q: $data.endTime,
    r: common_vendor.o((...args) => $options.onEndTimeChange && $options.onEndTimeChange(...args)),
    s: common_vendor.o((...args) => $options.quickSetNow && $options.quickSetNow(...args)),
    t: common_vendor.o((...args) => $options.quickSetTomorrow && $options.quickSetTomorrow(...args)),
    v: common_vendor.o((...args) => $options.calculateTimeDifference && $options.calculateTimeDifference(...args)),
    w: !$data.startDate || !$data.endDate,
    x: common_vendor.n(!$data.startDate || !$data.endDate ? "calc-btn-disabled" : ""),
    y: common_vendor.o((...args) => $options.clearAll && $options.clearAll(...args)),
    z: $data.result
  }, $data.result ? common_vendor.e({
    A: common_vendor.o((...args) => $options.saveToHistory && $options.saveToHistory(...args)),
    B: $data.result.detailed.years > 0
  }, $data.result.detailed.years > 0 ? {
    C: common_vendor.t($data.result.detailed.years)
  } : {}, {
    D: $data.result.detailed.months > 0
  }, $data.result.detailed.months > 0 ? {
    E: common_vendor.t(Math.floor($data.result.detailed.months))
  } : {}, {
    F: $data.result.detailed.days > 0
  }, $data.result.detailed.days > 0 ? {
    G: common_vendor.t(Math.floor($data.result.detailed.days))
  } : {}, {
    H: $data.result.detailed.hours > 0
  }, $data.result.detailed.hours > 0 ? {
    I: common_vendor.t($data.result.detailed.hours)
  } : {}, {
    J: $data.result.detailed.minutes > 0
  }, $data.result.detailed.minutes > 0 ? {
    K: common_vendor.t($data.result.detailed.minutes)
  } : {}, {
    L: common_vendor.t($data.result.detailed.seconds),
    M: common_vendor.t($options.formatNumber($data.result.seconds)),
    N: common_vendor.t($options.formatNumber($data.result.minutes)),
    O: common_vendor.t($options.formatNumber($data.result.years)),
    P: common_vendor.t($options.formatNumber($data.result.months)),
    Q: common_vendor.t($options.formatNumber($data.result.days)),
    R: common_vendor.t($options.formatNumber($data.result.hours)),
    S: common_vendor.t($options.formatNumber($data.result.minutes)),
    T: common_vendor.t($options.formatNumber($data.result.seconds)),
    U: common_vendor.t(Math.floor($data.result.hours / 2)),
    V: common_vendor.t(Math.floor($data.result.minutes / 3)),
    W: common_vendor.t(Math.floor($data.result.hours * 5)),
    X: $data.result.days > 0
  }, $data.result.days > 0 ? {
    Y: common_vendor.t(Math.floor($data.result.days / 365 * 100))
  } : {}, {
    Z: common_vendor.o((...args) => $options.copyResult && $options.copyResult(...args)),
    aa: common_vendor.o((...args) => $options.shareResult && $options.shareResult(...args))
  }) : {}, {
    ab: $data.history.length > 0
  }, $data.history.length > 0 ? {
    ac: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    ad: common_vendor.f($data.history.slice(0, 10), (record, index, i0) => {
      return {
        a: common_vendor.t(record.description),
        b: common_vendor.t(record.result),
        c: common_vendor.t($options.formatHistoryTime(record.timestamp)),
        d: index,
        e: common_vendor.o(($event) => $options.loadFromHistory(record), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0b025161"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/time-calculator.js.map
