<view class="avatar-generator data-v-b4dba492"><view class="content data-v-b4dba492"><view class="section data-v-b4dba492"><view class="section-header data-v-b4dba492"><text class="section-icon data-v-b4dba492">⚙️</text><text class="section-title data-v-b4dba492">选择风格</text></view><view class="style-grid data-v-b4dba492"><view wx:for="{{a}}" wx:for-item="style" wx:key="d" bindtap="{{style.e}}" class="{{['data-v-b4dba492', 'style-card', style.f]}}"><view class="style-content data-v-b4dba492"><view class="style-header data-v-b4dba492"><text class="style-icon data-v-b4dba492">{{style.a}}</text><text class="style-name data-v-b4dba492">{{style.b}}</text></view><text class="style-desc data-v-b4dba492">{{style.c}}</text></view></view></view></view><view class="section data-v-b4dba492"><view class="section-header data-v-b4dba492"><text class="section-title data-v-b4dba492">性别偏好</text></view><view class="gender-grid data-v-b4dba492"><view wx:for="{{b}}" wx:for-item="gender" wx:key="c" bindtap="{{gender.d}}" class="{{['data-v-b4dba492', 'gender-card', gender.e]}}"><text class="gender-icon data-v-b4dba492">{{gender.a}}</text><text class="gender-name data-v-b4dba492">{{gender.b}}</text></view></view></view><view wx:if="{{c}}" class="section data-v-b4dba492"><view class="section-header data-v-b4dba492"><text class="section-icon data-v-b4dba492">🎨</text><text class="section-title data-v-b4dba492">女生头像风格</text></view><view class="girl-style-grid data-v-b4dba492"><view wx:for="{{d}}" wx:for-item="style" wx:key="b" bindtap="{{style.c}}" class="{{['data-v-b4dba492', 'girl-style-card', style.d]}}"><text class="girl-style-name data-v-b4dba492">{{style.a}}</text></view></view></view><view class="generate-section data-v-b4dba492"><button bindtap="{{g}}" disabled="{{h}}" class="{{['data-v-b4dba492', 'generate-btn', i]}}"><text class="btn-icon data-v-b4dba492">{{e}}</text><text class="btn-text data-v-b4dba492">{{f}}</text></button></view><view wx:if="{{j}}" class="section data-v-b4dba492"><view class="section-header data-v-b4dba492"><text class="section-icon data-v-b4dba492">🖼️</text><text class="section-title data-v-b4dba492">生成结果</text><button bindtap="{{k}}" class="download-all-btn data-v-b4dba492"><text class="btn-icon data-v-b4dba492">📥</text><text class="btn-text data-v-b4dba492">全部下载</text></button></view><view class="avatar-grid data-v-b4dba492"><view wx:for="{{l}}" wx:for-item="avatar" wx:key="f" class="avatar-item data-v-b4dba492" bindtap="{{avatar.g}}"><image src="{{avatar.a}}" class="avatar-image data-v-b4dba492" mode="aspectFill" binderror="{{avatar.b}}" bindload="{{avatar.c}}" show-loading="{{true}}" lazy-load="{{false}}" data-index="{{avatar.d}}"/><view wx:if="{{avatar.e}}" class="loading-overlay data-v-b4dba492"><text class="loading-text data-v-b4dba492">加载中...</text></view></view></view></view><view wx:if="{{m}}" class="preview-mask data-v-b4dba492" bindtap="{{s}}"><view class="preview-dialog data-v-b4dba492"><text class="preview-close data-v-b4dba492" bindtap="{{n}}" aria-label="关闭预览">×</text><image src="{{o}}" class="preview-image data-v-b4dba492" mode="aspectFit"/><button class="preview-download data-v-b4dba492" catchtap="{{r}}"><svg wx:if="{{q}}" u-s="{{['d']}}" class="download-svg data-v-b4dba492" u-i="b4dba492-0" bind:__l="__l" u-p="{{q}}"><path wx:if="{{p}}" class="data-v-b4dba492" u-i="b4dba492-1,b4dba492-0" bind:__l="__l" u-p="{{p}}"/></svg><text class="download-text data-v-b4dba492">下载</text></button></view></view><view class="section tips-section data-v-b4dba492"><view class="section-header data-v-b4dba492"><text class="section-title data-v-b4dba492">使用建议</text></view><view class="tips-list data-v-b4dba492"><view wx:for="{{t}}" wx:for-item="tip" wx:key="b" class="tip-item data-v-b4dba492"><text class="tip-bullet data-v-b4dba492">•</text><text class="tip-text data-v-b4dba492">{{tip.a}}</text></view></view></view></view></view>