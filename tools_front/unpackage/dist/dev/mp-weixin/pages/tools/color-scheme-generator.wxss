
.container {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: env(safe-area-inset-bottom);
}
.main-content {
  padding: 32rpx;
}
.section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.title-icon {
  font-size: 40rpx;
  margin-right: 16rpx;
}
.title-text {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 颜色显示区域 */
.color-display {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}
.color-preview {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  margin-right: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.color-value {
  display: flex;
  flex-direction: column;
}
.value-label {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}
.value-text {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
  font-family: monospace;
}

/* 颜色选择器弹窗 */
.color-picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.color-picker-container {
  width: 600rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
}
.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.close-btn {
  font-size: 48rpx;
  color: #6b7280;
  line-height: 1;
}

/* 更新相关样式 */
.color-canvas-container,
.hue-slider-container {
  position: relative;
  width: 100%;
  margin-bottom: 24rpx;
}
.color-canvas {
  width: 100%;
  height: 300rpx;
  border-radius: 16rpx;
}
.hue-slider {
  width: 100%;
  height: 40rpx;
  border-radius: 8rpx;
}
.canvas-cursor {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  pointer-events: none;
}
.hue-cursor {
  position: absolute;
  width: 24rpx;
  height: 40rpx;
  background-color: #ffffff;
  border-radius: 4rpx;
  transform: translateX(-50%);
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  pointer-events: none;
}
.rgb-inputs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.input-label {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}
.rgb-input {
  height: 72rpx;
  background-color: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1f2937;
}
.preview-actions {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.color-preview-large {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.confirm-btn {
  flex: 1;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 配色方案 */
.scheme-types {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.scheme-type {
  flex: 1;
  min-width: 160rpx;
  height: 80rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
}
.scheme-type.active {
  background-color: #4f46e5;
}
.scheme-type.active .type-text {
  color: #ffffff;
}
.type-icon {
  font-size: 32rpx;
  margin-right: 8rpx;
}
.type-text {
  font-size: 28rpx;
  color: #4b5563;
}
.color-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.color-item {
  flex: 1;
  min-width: 160rpx;
  height: 120rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.color-hex {
  font-size: 24rpx;
  color: #ffffff;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
  font-family: monospace;
}

/* 使用说明 */
.instructions-section {
  background-color: #fffbeb;
  border: 2rpx solid #fbbf24;
}
.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.instruction-item {
  display: flex;
  align-items: center;
}
.item-number {
  width: 40rpx;
  height: 40rpx;
  background-color: #fbbf24;
  border-radius: 50%;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.item-text {
  font-size: 28rpx;
  color: #92400e;
  line-height: 1.5;
}
