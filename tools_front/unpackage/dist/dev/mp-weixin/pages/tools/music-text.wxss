/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a4931e09 {
  display: flex;
}
.flex-1.data-v-a4931e09 {
  flex: 1;
}
.items-center.data-v-a4931e09 {
  align-items: center;
}
.justify-center.data-v-a4931e09 {
  justify-content: center;
}
.justify-between.data-v-a4931e09 {
  justify-content: space-between;
}
.text-center.data-v-a4931e09 {
  text-align: center;
}
.rounded.data-v-a4931e09 {
  border-radius: 3px;
}
.rounded-lg.data-v-a4931e09 {
  border-radius: 6px;
}
.shadow.data-v-a4931e09 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a4931e09 {
  padding: 16rpx;
}
.m-4.data-v-a4931e09 {
  margin: 16rpx;
}
.mb-4.data-v-a4931e09 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a4931e09 {
  margin-top: 16rpx;
}
.music-text.data-v-a4931e09 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-a4931e09 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-a4931e09 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-a4931e09 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-a4931e09 {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-a4931e09 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-a4931e09 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.selected-section.data-v-a4931e09, .favorites-section.data-v-a4931e09, .notes-section.data-v-a4931e09, .decorations-section.data-v-a4931e09, .emoji-section.data-v-a4931e09, .styles-section.data-v-a4931e09, .random-section.data-v-a4931e09, .help-section.data-v-a4931e09 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-a4931e09 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-a4931e09 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-a4931e09 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.selected-card.data-v-a4931e09 {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #6366f1;
  text-align: center;
}
.music-display.data-v-a4931e09 {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #e5e7eb;
}
.music-text-content.data-v-a4931e09 {
  font-size: 32rpx;
  color: #374151;
  line-height: 1.5;
  display: block;
  text-align: center;
  font-weight: 500;
}
.selected-actions.data-v-a4931e09 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-a4931e09 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-a4931e09 {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.25);
}
.action-btn.primary.data-v-a4931e09:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(99, 102, 241, 0.35);
}
.action-btn.secondary.data-v-a4931e09 {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #e5e7eb;
}
.action-btn.secondary.data-v-a4931e09:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.action-btn.full-width.data-v-a4931e09 {
  width: 100%;
  margin-bottom: 24rpx;
}
.btn-icon.data-v-a4931e09 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-a4931e09 {
  font-size: 26rpx;
}
.count-badge.data-v-a4931e09 {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.favorites-list.data-v-a4931e09 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.favorite-item.data-v-a4931e09 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  border-radius: 16rpx;
  border: 1rpx solid #6366f1;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.favorite-item.data-v-a4931e09:hover {
  background: linear-gradient(135deg, #c7d2fe 0%, #a5b4fc 100%);
  border-color: #4f46e5;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.15);
}
.favorite-text.data-v-a4931e09 {
  font-size: 28rpx;
  color: #3730a3;
  font-weight: 500;
  flex: 1;
}
.favorite-actions.data-v-a4931e09 {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.notes-grid.data-v-a4931e09 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx;
}
.note-item.data-v-a4931e09 {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #0ea5e9;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.note-item.data-v-a4931e09:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #0284c7;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.15);
}
.note-symbol.data-v-a4931e09 {
  font-size: 32rpx;
  color: #0369a1;
  font-weight: 600;
  display: block;
}
.decorations-grid.data-v-a4931e09 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.decoration-item.data-v-a4931e09 {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #10b981;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.decoration-item.data-v-a4931e09:hover {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #059669;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.15);
}
.decoration-text.data-v-a4931e09 {
  font-size: 28rpx;
  color: #065f46;
  font-weight: 500;
  display: block;
}
.emoji-grid.data-v-a4931e09 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.emoji-item.data-v-a4931e09 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #ec4899;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.emoji-item.data-v-a4931e09:hover {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border-color: #db2777;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.15);
}
.emoji-text.data-v-a4931e09 {
  font-size: 36rpx;
  display: block;
}
.styles-grid.data-v-a4931e09 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.style-item.data-v-a4931e09 {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #f59e0b;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.style-item.data-v-a4931e09:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: #d97706;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}
.style-text.data-v-a4931e09 {
  font-size: 28rpx;
  color: #92400e;
  font-weight: 600;
  display: block;
}
.random-content.data-v-a4931e09 {
  text-align: center;
  padding: 0 20rpx;
}
.random-content .action-btn.data-v-a4931e09 {
  margin-bottom: 20rpx;
}
.random-display.data-v-a4931e09 {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #cbd5e1;
  text-align: center;
  margin-top: 20rpx;
}
.random-text.data-v-a4931e09 {
  font-size: 32rpx;
  color: #475569;
  font-weight: 500;
  display: block;
  margin-bottom: 24rpx;
}
.help-content.data-v-a4931e09 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-a4931e09 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-a4931e09:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-a4931e09 {
    padding: 30rpx 20rpx;
}
.notes-grid.data-v-a4931e09 {
    grid-template-columns: repeat(3, 1fr);
}
.emoji-grid.data-v-a4931e09 {
    grid-template-columns: repeat(2, 1fr);
}
.styles-grid.data-v-a4931e09 {
    grid-template-columns: 1fr;
}
.selected-actions.data-v-a4931e09, .random-actions.data-v-a4931e09 {
    flex-direction: column;
    gap: 12rpx;
}
}