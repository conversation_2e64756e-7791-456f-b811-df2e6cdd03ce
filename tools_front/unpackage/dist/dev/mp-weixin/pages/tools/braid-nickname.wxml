<view class="braid-nickname data-v-c7bbac36"><view class="content data-v-c7bbac36"><view class="card data-v-c7bbac36"><view class="card-header data-v-c7bbac36"><view class="header-title-container data-v-c7bbac36"><text class="header-icon data-v-c7bbac36">👤</text><text class="header-title data-v-c7bbac36">输入昵称</text></view></view><view class="card-content data-v-c7bbac36"><view class="input-section data-v-c7bbac36"><input class="name-input data-v-c7bbac36" placeholder="输入你的昵称..." maxlength="20" bindinput="{{a}}" focus value="{{b}}"/><view class="input-counter data-v-c7bbac36">{{c}}/20</view></view></view></view><view wx:if="{{d}}" class="card data-v-c7bbac36"><view class="card-header data-v-c7bbac36"><view class="header-title-container data-v-c7bbac36"><text class="header-icon data-v-c7bbac36">✨</text><text class="header-title data-v-c7bbac36">装饰效果预览</text></view><view class="refresh-btn data-v-c7bbac36" bindtap="{{e}}"><text class="refresh-icon data-v-c7bbac36">🔄</text><text class="refresh-text data-v-c7bbac36">刷新</text></view></view><view class="card-content data-v-c7bbac36"><view class="names-container data-v-c7bbac36"><view wx:for="{{f}}" wx:for-item="name" wx:key="c" class="name-item data-v-c7bbac36" bindtap="{{name.d}}"><text class="name-text data-v-c7bbac36">{{name.a}}</text><view class="copy-btn data-v-c7bbac36" catchtap="{{name.b}}"><text class="copy-icon data-v-c7bbac36">📋</text></view></view></view></view></view><view class="card data-v-c7bbac36"><view class="card-header data-v-c7bbac36"><view class="header-title-container data-v-c7bbac36"><text class="header-icon data-v-c7bbac36">🔣</text><text class="header-title data-v-c7bbac36">特殊符号库</text></view></view><view class="card-content data-v-c7bbac36"><view class="symbols-section data-v-c7bbac36"><view class="symbol-category data-v-c7bbac36"><text class="category-title data-v-c7bbac36">装饰符号</text><view class="symbols-grid data-v-c7bbac36"><view wx:for="{{g}}" wx:for-item="char" wx:key="b" class="symbol-btn data-v-c7bbac36" bindtap="{{char.c}}"><text class="symbol-text data-v-c7bbac36">{{char.a}}</text></view></view></view><view class="symbol-category data-v-c7bbac36"><text class="category-title data-v-c7bbac36">前后缀符号</text><view class="symbols-grid data-v-c7bbac36"><view wx:for="{{h}}" wx:for-item="char" wx:key="b" class="symbol-btn data-v-c7bbac36" bindtap="{{char.c}}"><text class="symbol-text data-v-c7bbac36">{{char.a}}</text></view></view></view></view></view></view><view class="card data-v-c7bbac36"><view class="card-header data-v-c7bbac36"><view class="header-title-container data-v-c7bbac36"><text class="header-icon data-v-c7bbac36">💡</text><text class="header-title data-v-c7bbac36">使用说明</text></view></view><view class="card-content data-v-c7bbac36"><view class="instructions data-v-c7bbac36"><text class="instruction-item data-v-c7bbac36">• 输入昵称自动生成多种装饰效果</text><text class="instruction-item data-v-c7bbac36">• 点击任意装饰昵称即可复制</text><text class="instruction-item data-v-c7bbac36">• 支持各种特殊符号组合</text><text class="instruction-item data-v-c7bbac36">• 适合游戏、社交等场景使用</text><text class="instruction-item data-v-c7bbac36">• 可单独复制特殊符号</text></view></view></view></view></view>