/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-4506e66d {
  display: flex;
}
.flex-1.data-v-4506e66d {
  flex: 1;
}
.items-center.data-v-4506e66d {
  align-items: center;
}
.justify-center.data-v-4506e66d {
  justify-content: center;
}
.justify-between.data-v-4506e66d {
  justify-content: space-between;
}
.text-center.data-v-4506e66d {
  text-align: center;
}
.rounded.data-v-4506e66d {
  border-radius: 3px;
}
.rounded-lg.data-v-4506e66d {
  border-radius: 6px;
}
.shadow.data-v-4506e66d {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-4506e66d {
  padding: 16rpx;
}
.m-4.data-v-4506e66d {
  margin: 16rpx;
}
.mb-4.data-v-4506e66d {
  margin-bottom: 16rpx;
}
.mt-4.data-v-4506e66d {
  margin-top: 16rpx;
}
.animation-generator-container.data-v-4506e66d {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-4506e66d, .type-card.data-v-4506e66d, .params-card.data-v-4506e66d, .preview-card.data-v-4506e66d, .code-card.data-v-4506e66d, .tips-card.data-v-4506e66d {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-4506e66d {
  display: flex;
  align-items: center;
}
.header-icon.data-v-4506e66d {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-info.data-v-4506e66d {
  flex: 1;
}
.header-title.data-v-4506e66d {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-4506e66d {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-4506e66d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-4506e66d {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.animation-types.data-v-4506e66d {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.type-item.data-v-4506e66d {
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.type-item.active.data-v-4506e66d {
  border-color: #007AFF;
  background: linear-gradient(135deg, #007AFF 0%, #0056D6 100%);
  color: white;
}
.type-item.active .type-name.data-v-4506e66d, .type-item.active .type-desc.data-v-4506e66d {
  color: white;
}
.type-item.data-v-4506e66d:active {
  transform: scale(0.98);
}
.type-icon.data-v-4506e66d {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.type-name.data-v-4506e66d {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}
.type-desc.data-v-4506e66d {
  font-size: 20rpx;
  color: #666;
}
.param-groups.data-v-4506e66d {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.param-group.data-v-4506e66d {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.param-label.data-v-4506e66d {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.param-slider.data-v-4506e66d {
  width: 100%;
  max-width: calc(100% - 40rpx);
  box-sizing: border-box;
  margin-right: 0;
  margin-left: 0;
  display: block;
}
.iteration-options.data-v-4506e66d {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}
.iteration-btn.data-v-4506e66d {
  padding: 16rpx 12rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.iteration-btn.active.data-v-4506e66d {
  border-color: #007AFF;
  background: #007AFF;
  color: white;
}
.option-text.data-v-4506e66d {
  font-size: 22rpx;
  color: #333;
}
.iteration-btn.active .option-text.data-v-4506e66d {
  color: white;
}
.preview-controls.data-v-4506e66d {
  display: flex;
  gap: 12rpx;
}
.control-btn.data-v-4506e66d {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.control-btn.data-v-4506e66d:active {
  background: #e9ecef;
}
.preview-stage.data-v-4506e66d {
  height: 300rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.preview-element.data-v-4506e66d {
  width: 120rpx;
  height: 120rpx;
  background: white;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}
.element-text.data-v-4506e66d {
  font-size: 20rpx;
  font-weight: 500;
  color: #333;
}
.code-header.data-v-4506e66d {
  position: relative;
}
.copy-btn.right-btn.data-v-4506e66d {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
  transition: background 0.2s, box-shadow 0.2s;
}
.copy-btn.right-btn.data-v-4506e66d:active {
  background: #e9ecef;
}
.code-block.data-v-4506e66d {
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx;
}
.code-text.data-v-4506e66d {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 22rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
}
.tips-content.data-v-4506e66d {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-4506e66d {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-4506e66d {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-4506e66d {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.tip-bold.data-v-4506e66d {
  font-weight: 600;
  color: #1a1a1a;
}
@keyframes fadeIn-4506e66d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeOut-4506e66d {
from {
    opacity: 1;
}
to {
    opacity: 0;
}
}
@keyframes slideInLeft-4506e66d {
from {
    transform: translateX(-100%);
    opacity: 0;
}
to {
    transform: translateX(0);
    opacity: 1;
}
}
@keyframes slideInRight-4506e66d {
from {
    transform: translateX(100%);
    opacity: 0;
}
to {
    transform: translateX(0);
    opacity: 1;
}
}
@keyframes slideInUp-4506e66d {
from {
    transform: translateY(-100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes slideInDown-4506e66d {
from {
    transform: translateY(100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes zoomIn-4506e66d {
from {
    transform: scale(0);
    opacity: 0;
}
to {
    transform: scale(1);
    opacity: 1;
}
}
@keyframes zoomOut-4506e66d {
from {
    transform: scale(1);
    opacity: 1;
}
to {
    transform: scale(0);
    opacity: 0;
}
}
@keyframes rotateIn-4506e66d {
from {
    transform: rotate(-360deg) scale(0);
    opacity: 0;
}
to {
    transform: rotate(0deg) scale(1);
    opacity: 1;
}
}
.preview-element.fadeIn.data-v-4506e66d {
  animation: fadeIn-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.fadeOut.data-v-4506e66d {
  animation: fadeOut-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.slideInLeft.data-v-4506e66d {
  animation: slideInLeft-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.slideInRight.data-v-4506e66d {
  animation: slideInRight-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.slideInUp.data-v-4506e66d {
  animation: slideInUp-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.slideInDown.data-v-4506e66d {
  animation: slideInDown-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.zoomIn.data-v-4506e66d {
  animation: zoomIn-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.zoomOut.data-v-4506e66d {
  animation: zoomOut-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.rotateIn.data-v-4506e66d {
  animation: rotateIn-4506e66d 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.bounce.data-v-4506e66d {
  animation: bounce 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.pulse.data-v-4506e66d {
  animation: pulse 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.preview-element.shake.data-v-4506e66d {
  animation: shake 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}