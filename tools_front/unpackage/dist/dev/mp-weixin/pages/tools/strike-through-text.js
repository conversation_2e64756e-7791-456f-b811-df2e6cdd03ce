"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      selectedResult: null,
      strikeResults: [],
      history: [],
      favorites: [],
      examples: ["删除线文字", "已完成任务", "特价商品", "限时优惠", "已售罄"]
    };
  },
  onLoad() {
    this.loadHistory();
    this.loadFavorites();
  },
  methods: {
    clearInput() {
      this.inputText = "";
      this.strikeResults = [];
      this.selectedResult = null;
    },
    generateStrikeText() {
      if (!this.inputText.trim()) {
        this.strikeResults = [];
        return;
      }
      const text = this.inputText.trim();
      const results = [];
      const unicodeStrike = text.split("").map((char) => char + "̶").join("");
      results.push({
        text: unicodeStrike,
        label: "Unicode删除线"
      });
      const doubleStrike = text.split("").map((char) => char + "̵").join("");
      results.push({
        text: doubleStrike,
        label: "双删除线"
      });
      const combinedStrike = text.split("").map((char) => char + "̶̵").join("");
      results.push({
        text: combinedStrike,
        label: "组合删除线"
      });
      const longStrike = text.split("").map((char) => char + "̶̶").join("");
      results.push({
        text: longStrike,
        label: "加强删除线"
      });
      const specialStrike = text.split("").map((char) => char + "̶").join("");
      results.push({
        text: specialStrike,
        label: "特殊删除线"
      });
      const htmlStrike = text.split("").map((char) => char + "̵").join("");
      results.push({
        text: htmlStrike,
        label: "实体删除线"
      });
      this.strikeResults = results;
    },
    selectResult(result) {
      this.selectedResult = result;
      this.addToHistory(result);
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "删除线文字已复制",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    shareResult() {
      if (!this.selectedResult)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        title: "删除线文字",
        summary: this.selectedResult.text,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: () => {
          this.copyText(this.selectedResult.text);
        }
      });
    },
    addToHistory(result) {
      const existingIndex = this.history.findIndex((item) => item.text === result.text);
      if (existingIndex > -1) {
        this.history.splice(existingIndex, 1);
      }
      this.history.unshift(result);
      if (this.history.length > 20) {
        this.history = this.history.slice(0, 20);
      }
      this.saveHistory();
    },
    removeFromHistory(index) {
      this.history.splice(index, 1);
      this.saveHistory();
      common_vendor.index.showToast({
        title: "已从历史记录中移除",
        icon: "success",
        duration: 1500
      });
    },
    addToFavorites(result) {
      const exists = this.favorites.find((item) => item.text === result.text);
      if (!exists) {
        this.favorites.push(result);
        this.saveFavorites();
        common_vendor.index.showToast({
          title: "已添加到收藏",
          icon: "success",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "已存在收藏中",
          icon: "none",
          duration: 1500
        });
      }
    },
    removeFromFavorites(index) {
      this.favorites.splice(index, 1);
      this.saveFavorites();
      common_vendor.index.showToast({
        title: "已从收藏中移除",
        icon: "success",
        duration: 1500
      });
    },
    setExample(example) {
      this.inputText = example;
      this.generateStrikeText();
    },
    saveHistory() {
      common_vendor.index.setStorageSync("strike-text-history", this.history);
    },
    loadHistory() {
      const saved = common_vendor.index.getStorageSync("strike-text-history");
      if (saved) {
        this.history = saved;
      }
    },
    saveFavorites() {
      common_vendor.index.setStorageSync("strike-text-favorites", this.favorites);
    },
    loadFavorites() {
      const saved = common_vendor.index.getStorageSync("strike-text-favorites");
      if (saved) {
        this.favorites = saved;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.generateStrikeText && $options.generateStrikeText(...args)]),
    b: $data.inputText,
    c: common_vendor.t($data.inputText.length),
    d: $data.inputText.trim()
  }, $data.inputText.trim() ? {
    e: common_vendor.f($data.strikeResults, (result, index, i0) => {
      return common_vendor.e({
        a: result.label === "Unicode删除线"
      }, result.label === "Unicode删除线" ? {
        b: common_vendor.t($data.inputText)
      } : result.label === "双删除线" ? {
        d: common_vendor.t($data.inputText)
      } : result.label === "组合删除线" ? {
        f: common_vendor.t($data.inputText)
      } : result.label === "加强删除线" ? {
        h: common_vendor.t($data.inputText)
      } : result.label === "特殊删除线" ? {
        j: common_vendor.t($data.inputText)
      } : result.label === "实体删除线" ? {
        l: common_vendor.t($data.inputText)
      } : {}, {
        c: result.label === "双删除线",
        e: result.label === "组合删除线",
        g: result.label === "加强删除线",
        i: result.label === "特殊删除线",
        k: result.label === "实体删除线",
        m: common_vendor.t(result.label),
        n: common_vendor.o(($event) => $options.copyText(result.text), index),
        o: index,
        p: common_vendor.o(($event) => $options.copyText(result.text), index)
      });
    })
  } : {}, {
    f: common_vendor.f($data.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.setExample(example), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3e5e6762"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/strike-through-text.js.map
