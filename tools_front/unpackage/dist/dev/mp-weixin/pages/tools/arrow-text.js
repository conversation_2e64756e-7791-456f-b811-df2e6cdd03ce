"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      results: [],
      examples: [
        "重要通知",
        "点击这里",
        "立即查看",
        "马上行动",
        "下一步",
        "继续阅读",
        "了解更多",
        "开始使用"
      ],
      arrowStyles: [
        { symbol: "→", name: "右箭头" },
        { symbol: "←", name: "左箭头" },
        { symbol: "↑", name: "上箭头" },
        { symbol: "↓", name: "下箭头" },
        { symbol: "↗", name: "右上箭头" },
        { symbol: "↘", name: "右下箭头" },
        { symbol: "↙", name: "左下箭头" },
        { symbol: "↖", name: "左上箭头" },
        { symbol: "⇒", name: "双线右箭头" },
        { symbol: "⇐", name: "双线左箭头" },
        { symbol: "⇑", name: "双线上箭头" },
        { symbol: "⇓", name: "双线下箭头" },
        { symbol: "➡️", name: "表情右箭头" },
        { symbol: "⬅️", name: "表情左箭头" },
        { symbol: "⬆️", name: "表情上箭头" },
        { symbol: "⬇️", name: "表情下箭头" },
        { symbol: "↩️", name: "左弯箭头" },
        { symbol: "↪️", name: "右弯箭头" },
        { symbol: "⤴️", name: "右上弯箭头" },
        { symbol: "⤵️", name: "右下弯箭头" },
        { symbol: "🔄", name: "循环箭头" },
        { symbol: "🔃", name: "顺时针箭头" },
        { symbol: "🔂", name: "逆时针箭头" },
        { symbol: "▶️", name: "播放箭头" }
      ]
    };
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    generateArrowText() {
      if (!this.inputText.trim()) {
        common_vendor.index.showToast({
          title: "请输入文字",
          icon: "none",
          duration: 1500
        });
        return;
      }
      const text = this.inputText.trim();
      const arrowPatterns = [
        `→ ${text}`,
        `${text} ←`,
        `➡️ ${text}`,
        `${text} ⬅️`,
        `↗ ${text} ↙`,
        `↖ ${text} ↘`,
        `⇒ ${text}`,
        `${text} ⇐`,
        `▶️ ${text}`,
        `${text} ◀️`,
        `🔄 ${text}`,
        `${text} 🔄`,
        `→→ ${text} ←←`,
        `⇒⇒ ${text} ⇐⇐`,
        `➡️➡️ ${text} ⬅️⬅️`,
        `↗↗ ${text} ↙↙`,
        `【→ ${text} ←】`,
        `《→ ${text} ←》`,
        `〖→ ${text} ←〗`,
        `〔→ ${text} ←〕`
      ];
      this.results = arrowPatterns;
      this.addToHistory(text);
      common_vendor.index.showToast({
        title: "生成成功",
        icon: "success",
        duration: 1500
      });
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    useExample(example) {
      this.inputText = example;
      this.generateArrowText();
    },
    addToHistory(text) {
      const exists = this.history.find((item) => item === text);
      if (!exists) {
        this.history.unshift(text);
        if (this.history.length > 20) {
          this.history = this.history.slice(0, 20);
        }
        this.saveData();
      }
    },
    saveData() {
      common_vendor.index.setStorageSync("arrow-text-history", this.history);
    },
    loadData() {
      const history = common_vendor.index.getStorageSync("arrow-text-history");
      if (history) {
        this.history = history;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputText,
    b: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    c: common_vendor.t($data.inputText.length),
    d: common_vendor.o((...args) => $options.generateArrowText && $options.generateArrowText(...args)),
    e: $data.results.length > 0
  }, $data.results.length > 0 ? {
    f: common_vendor.f($data.results, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: common_vendor.o(($event) => $options.copyText(result), index),
        c: index,
        d: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    g: common_vendor.f($data.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.useExample(example), index)
      };
    }),
    h: common_vendor.f($data.arrowStyles, (style, index, i0) => {
      return {
        a: common_vendor.t(style.symbol),
        b: common_vendor.t(style.name),
        c: index,
        d: common_vendor.o(($event) => $options.copyText(style.symbol), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b77d809d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/arrow-text.js.map
