"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  name: "CPULadder",
  data() {
    return {
      searchTerm: "",
      selectedTier: "all",
      selectedCPU: null,
      sortAsc: false,
      loading: false,
      tiers: ["all", "旗舰", "高端", "主流", "入门"],
      cpuData: [],
      toolService: new utils_toolService.ToolService()
    };
  },
  computed: {
    filteredCPUs() {
      let filtered = this.cpuData.filter((cpu) => {
        const matchesSearch = cpu.name.toLowerCase().includes(this.searchTerm.toLowerCase());
        const matchesTier = this.selectedTier === "all" || cpu.tier === this.selectedTier;
        return matchesSearch && matchesTier;
      });
      if (this.sortAsc) {
        filtered.sort((a, b) => a.score - b.score);
      } else {
        filtered.sort((a, b) => b.score - a.score);
      }
      return filtered;
    }
  },
  mounted() {
    this.loadCpuData();
  },
  methods: {
    async loadCpuData() {
      this.loading = true;
      try {
        common_vendor.index.__f__("log", "at pages/tools/cpu-ladder.vue:312", "CPU天梯图查询请求");
        const result = await this.toolService.getCpuLadder();
        common_vendor.index.__f__("log", "at pages/tools/cpu-ladder.vue:314", "CPU天梯图API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.cpus) {
            this.cpuData = apiData.cpus.map((cpu) => {
              return {
                rank: cpu.rank,
                name: cpu.name,
                score: cpu.score,
                ratio: cpu.ratio,
                price: cpu.price,
                cores: cpu.cores,
                threads: cpu.threads,
                baseClock: cpu.baseClock,
                tier: cpu.tier,
                brand: cpu.brand,
                tdp: this.estimateTdp(cpu.name),
                dataSource: cpu.dataSource
              };
            });
            if (this.cpuData.length === 0) {
              common_vendor.index.showToast({
                title: "暂无CPU数据",
                icon: "none",
                duration: 2e3
              });
            } else {
              common_vendor.index.showToast({
                title: `找到${this.cpuData.length}个CPU`,
                icon: "success",
                duration: 1500
              });
            }
          } else {
            throw new Error(apiData.message || "获取CPU数据失败");
          }
        } else {
          throw new Error(result.message || "获取CPU数据失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/cpu-ladder.vue:358", "获取CPU数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取CPU数据失败",
          icon: "error",
          duration: 2e3
        });
        this.cpuData = this.getBackupData();
      } finally {
        this.loading = false;
      }
    },
    getBackupData() {
      return [
        { rank: 1, name: "AMD Ryzen 9 9950X", score: 405.8, ratio: "99.3%", price: "¥4000+", cores: "16核", threads: "32线程", baseClock: "4.3GHz", tier: "旗舰", brand: "AMD", tdp: "170W" },
        { rank: 2, name: "Intel 酷睿 Ultra 9 285K", score: 389.2, ratio: "98.6%", price: "¥3800+", cores: "24核", threads: "24线程", baseClock: "3.7GHz", tier: "旗舰", brand: "Intel", tdp: "125W" },
        { rank: 3, name: "Intel 酷睿 i9 14900K", score: 382.8, ratio: "97.9%", price: "¥3500+", cores: "24核", threads: "32线程", baseClock: "3.2GHz", tier: "旗舰", brand: "Intel", tdp: "125W" },
        { rank: 4, name: "AMD Ryzen 9 9900X", score: 380.2, ratio: "97.2%", price: "¥3200+", cores: "12核", threads: "24线程", baseClock: "4.4GHz", tier: "旗舰", brand: "AMD", tdp: "170W" },
        { rank: 5, name: "AMD Ryzen 7 9800X3D", score: 379, ratio: "95.1%", price: "¥3000+", cores: "8核", threads: "16线程", baseClock: "4.7GHz", tier: "旗舰", brand: "AMD", tdp: "120W" },
        { rank: 6, name: "Intel 酷睿 i7 14700K", score: 267.8, ratio: "93.0%", price: "¥2500+", cores: "20核", threads: "28线程", baseClock: "3.4GHz", tier: "高端", brand: "Intel", tdp: "125W" },
        { rank: 7, name: "Intel 酷睿 i5 14600K", score: 171.8, ratio: "86.7%", price: "¥1800+", cores: "14核", threads: "20线程", baseClock: "3.5GHz", tier: "高端", brand: "Intel", tdp: "125W" },
        { rank: 8, name: "AMD Ryzen 5 7600X", score: 114.9, ratio: "83.2%", price: "¥1800+", cores: "6核", threads: "12线程", baseClock: "4.7GHz", tier: "主流", brand: "AMD", tdp: "105W" },
        { rank: 9, name: "Intel 酷睿 i5 12400", score: 98, ratio: "79%", price: "¥1500+", cores: "6核", threads: "12线程", baseClock: "2.5GHz", tier: "主流", brand: "Intel", tdp: "65W" },
        { rank: 10, name: "AMD Ryzen 3 4300G", score: 35.3, ratio: "30.1%", price: "¥700+", cores: "4核", threads: "8线程", baseClock: "3.8GHz", tier: "入门", brand: "AMD", tdp: "65W" }
      ];
    },
    estimateTdp(name) {
      if (!name)
        return "未知";
      const upperName = name.toUpperCase();
      if (upperName.includes("I9"))
        return "125W";
      if (upperName.includes("I7"))
        return "125W";
      if (upperName.includes("I5"))
        return "125W";
      if (upperName.includes("I3"))
        return "60W";
      if (upperName.includes("9950X") || upperName.includes("9900X"))
        return "170W";
      if (upperName.includes("9700X") || upperName.includes("9600X"))
        return "105W";
      if (upperName.includes("X3D"))
        return "120W";
      if (upperName.includes("THREADRIPPER"))
        return "280W";
      return "未知";
    },
    onSearch() {
    },
    clearSearch() {
      this.searchTerm = "";
    },
    selectTier(tier) {
      this.selectedTier = tier;
    },
    toggleSortOrder() {
      this.sortAsc = !this.sortAsc;
    },
    selectCPU(cpu) {
      var _a;
      this.selectedCPU = ((_a = this.selectedCPU) == null ? void 0 : _a.rank) === cpu.rank ? null : cpu;
    },
    closeDetail() {
      this.selectedCPU = null;
    },
    getTierCount(tier) {
      if (tier === "all")
        return this.cpuData.length;
      return this.cpuData.filter((cpu) => cpu.tier === tier).length;
    },
    getTierClass(tier) {
      const classes = {
        "旗舰": "tier-flagship",
        "高端": "tier-high",
        "主流": "tier-mainstream",
        "入门": "tier-entry"
      };
      return classes[tier] || "tier-default";
    },
    getScoreClass(score) {
      if (score >= 300)
        return "score-flagship";
      if (score >= 150)
        return "score-high";
      if (score >= 75)
        return "score-mainstream";
      return "score-entry";
    },
    getProgressClass(score) {
      if (score >= 300)
        return "progress-flagship";
      if (score >= 150)
        return "progress-high";
      if (score >= 75)
        return "progress-mainstream";
      return "progress-entry";
    },
    getRankBadgeClass(rank) {
      if (rank <= 3)
        return "rank-top";
      if (rank <= 10)
        return "rank-high";
      return "rank-normal";
    },
    getBrand(name) {
      if (!name)
        return "其他";
      const upperName = name.toUpperCase();
      if (upperName.includes("INTEL") || upperName.includes("酷睿") || upperName.includes("I3") || upperName.includes("I5") || upperName.includes("I7") || upperName.includes("I9")) {
        return "Intel";
      }
      if (upperName.includes("AMD") || upperName.includes("RYZEN")) {
        return "AMD";
      }
      return "其他";
    },
    getValueRating(cpu) {
      const priceValue = parseInt(cpu.price.replace(/[^\d]/g, "")) || 5e3;
      const ratio = cpu.score / (priceValue / 1e3);
      if (ratio >= 80)
        return 5;
      if (ratio >= 60)
        return 4;
      if (ratio >= 40)
        return 3;
      if (ratio >= 20)
        return 2;
      return 1;
    },
    getRecommendation(cpu) {
      const rating = this.getValueRating(cpu);
      const recommendations = ["不推荐", "一般", "推荐", "强烈推荐", "超值选择"];
      return recommendations[rating - 1] || "一般";
    },
    getUseCase(score) {
      if (score >= 300)
        return "专业工作站";
      if (score >= 150)
        return "高端游戏";
      if (score >= 75)
        return "日常办公";
      return "基础使用";
    },
    getRecommendedUse(score) {
      if (score >= 300)
        return "内容创作、渲染";
      if (score >= 150)
        return "游戏、多任务";
      if (score >= 75)
        return "办公、娱乐";
      return "轻度使用";
    },
    // 计算性能百分比（基于最高分数）
    getPerformancePercentage(score) {
      const maxScore = this.cpuData.length > 0 ? Math.max(...this.cpuData.map((cpu) => cpu.score)) : 405.8;
      return Math.round(score / maxScore * 100);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.filteredCPUs.length),
    b: common_vendor.o([($event) => $data.searchTerm = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    c: $data.searchTerm,
    d: $data.searchTerm
  }, $data.searchTerm ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    f: common_vendor.f($data.tiers, (tier, k0, i0) => {
      return {
        a: common_vendor.t(tier === "all" ? "全部" : tier),
        b: common_vendor.t($options.getTierCount(tier)),
        c: tier,
        d: $data.selectedTier === tier ? 1 : "",
        e: common_vendor.o(($event) => $options.selectTier(tier), tier)
      };
    }),
    g: common_vendor.t($data.sortAsc ? "↑" : "↓"),
    h: common_vendor.t($data.sortAsc ? "升序" : "降序"),
    i: common_vendor.o((...args) => $options.toggleSortOrder && $options.toggleSortOrder(...args)),
    j: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    k: common_vendor.f($options.filteredCPUs, (cpu, index, i0) => {
      return {
        a: common_vendor.t(cpu.rank),
        b: common_vendor.n($options.getRankBadgeClass(cpu.rank)),
        c: common_vendor.t(cpu.name),
        d: common_vendor.t(cpu.tier),
        e: common_vendor.n($options.getTierClass(cpu.tier)),
        f: common_vendor.t($options.getBrand(cpu.name)),
        g: common_vendor.t(cpu.score),
        h: common_vendor.n($options.getScoreClass(cpu.score)),
        i: common_vendor.t(cpu.price),
        j: common_vendor.t(cpu.tdp),
        k: common_vendor.t(cpu.cores),
        l: common_vendor.t(cpu.threads),
        m: common_vendor.t($options.getPerformancePercentage(cpu.score)),
        n: common_vendor.n($options.getProgressClass(cpu.score)),
        o: $options.getPerformancePercentage(cpu.score) + "%",
        p: common_vendor.f(5, (star, k1, i1) => {
          return {
            a: star,
            b: star <= $options.getValueRating(cpu) ? 1 : ""
          };
        }),
        q: common_vendor.t($options.getRecommendation(cpu)),
        r: cpu.rank,
        s: common_vendor.o(($event) => $options.selectCPU(cpu), cpu.rank),
        t: $data.selectedCPU && $data.selectedCPU.rank === cpu.rank ? 1 : ""
      };
    }),
    l: $options.filteredCPUs.length === 0
  }, $options.filteredCPUs.length === 0 ? {} : {}), {
    m: $data.selectedCPU
  }, $data.selectedCPU ? {
    n: common_vendor.t($data.selectedCPU.name),
    o: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    p: common_vendor.t($data.selectedCPU.rank),
    q: common_vendor.t($data.selectedCPU.tier),
    r: common_vendor.t($options.getUseCase($data.selectedCPU.score)),
    s: common_vendor.t($options.getRecommendedUse($data.selectedCPU.score))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-604f976a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/cpu-ladder.js.map
