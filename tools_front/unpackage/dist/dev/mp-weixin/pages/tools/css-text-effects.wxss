/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-dda9f4f9 {
  display: flex;
}
.flex-1.data-v-dda9f4f9 {
  flex: 1;
}
.items-center.data-v-dda9f4f9 {
  align-items: center;
}
.justify-center.data-v-dda9f4f9 {
  justify-content: center;
}
.justify-between.data-v-dda9f4f9 {
  justify-content: space-between;
}
.text-center.data-v-dda9f4f9 {
  text-align: center;
}
.rounded.data-v-dda9f4f9 {
  border-radius: 3px;
}
.rounded-lg.data-v-dda9f4f9 {
  border-radius: 6px;
}
.shadow.data-v-dda9f4f9 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-dda9f4f9 {
  padding: 16rpx;
}
.m-4.data-v-dda9f4f9 {
  margin: 16rpx;
}
.mb-4.data-v-dda9f4f9 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-dda9f4f9 {
  margin-top: 16rpx;
}
.text-effects-container.data-v-dda9f4f9 {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-dda9f4f9, .input-card.data-v-dda9f4f9, .effects-card.data-v-dda9f4f9, .params-card.data-v-dda9f4f9, .preview-card.data-v-dda9f4f9, .code-card.data-v-dda9f4f9, .tips-card.data-v-dda9f4f9 {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-dda9f4f9 {
  display: flex;
  align-items: center;
}
.header-icon.data-v-dda9f4f9 {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-title.data-v-dda9f4f9 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-dda9f4f9 {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-dda9f4f9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-dda9f4f9 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.input-content.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.text-input.data-v-dda9f4f9 {
  width: 100%;
  height: 56rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 30rpx;
  background: #fafafa;
  box-sizing: border-box;
  color: #222;
  line-height: 56rpx;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.text-input.data-v-dda9f4f9::-webkit-input-placeholder {
  color: #bbb;
  font-size: 28rpx;
}
.text-input.data-v-dda9f4f9::placeholder {
  color: #bbb;
  font-size: 28rpx;
}
.text-input.data-v-dda9f4f9:focus {
  border-color: #007AFF;
  box-shadow: 0 0 8rpx rgba(0, 122, 255, 0.08);
  background: #fff;
}
.input-actions.data-v-dda9f4f9 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-dda9f4f9 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
  flex: 1;
}
.effects-grid.data-v-dda9f4f9 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.effect-item.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s ease;
}
.effect-item.active.data-v-dda9f4f9 {
  border-color: #007AFF;
  background: #f0f8ff;
}
.effect-item.data-v-dda9f4f9:active {
  transform: scale(0.95);
}
.effect-preview.data-v-dda9f4f9 {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #333;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}
.effect-text.data-v-dda9f4f9 {
  font-size: 32rpx;
  font-weight: 600;
}
.effect-name.data-v-dda9f4f9 {
  font-size: 22rpx;
  color: #333;
  text-align: center;
}
.params-content.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.param-group.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.param-label.data-v-dda9f4f9 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.preview-actions.data-v-dda9f4f9 {
  display: flex;
  gap: 16rpx;
}
.preview-btn.data-v-dda9f4f9 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 0 24rpx;
  height: 56rpx;
  background: #fff;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #222;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.18s cubic-bezier(0.2, 0, 0.1, 1), transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
}
.preview-btn.data-v-dda9f4f9:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: scale(0.97);
}
.preview-container.data-v-dda9f4f9 {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.preview-text.data-v-dda9f4f9 {
  font-size: 60rpx;
  font-weight: 700;
  text-align: center;
  max-width: 90%;
  word-break: break-all;
  transition: all 0.3s ease;
}
.preview-text.animate-pulse.data-v-dda9f4f9 {
  animation: pulse-dda9f4f9 2s infinite;
}
.code-header.data-v-dda9f4f9 {
  position: relative;
}
.copy-btn.data-v-dda9f4f9 {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
  justify-content: center;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.18s cubic-bezier(0.2, 0, 0.1, 1), transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
}
.copy-btn.data-v-dda9f4f9:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: scale(0.97);
}
.code-block.data-v-dda9f4f9 {
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx;
}
.code-text.data-v-dda9f4f9 {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 22rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
}
.tips-content.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-dda9f4f9 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-dda9f4f9 {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
@keyframes pulse-dda9f4f9 {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.7;
}
}
@keyframes rainbow-dda9f4f9 {
0%, 100% {
    filter: hue-rotate(0deg);
}
50% {
    filter: hue-rotate(180deg);
}
}
.animation-type-card.data-v-dda9f4f9 {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.animation-type-grid.data-v-dda9f4f9 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.animation-type-item.data-v-dda9f4f9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.animation-type-item.active.data-v-dda9f4f9 {
  border-color: #007AFF;
  background: linear-gradient(135deg, #007AFF 0%, #0056D6 100%);
  color: white;
}
.animation-type-item.active .animation-type-name.data-v-dda9f4f9, .animation-type-item.active .animation-type-desc.data-v-dda9f4f9 {
  color: white;
}
.animation-type-item.data-v-dda9f4f9:active {
  transform: scale(0.98);
}
.animation-type-icon.data-v-dda9f4f9 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.animation-type-name.data-v-dda9f4f9 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}
.animation-type-desc.data-v-dda9f4f9 {
  font-size: 20rpx;
  color: #666;
}
.animated-text.data-v-dda9f4f9 {
  will-change: transform, opacity;
  animation-duration: 1s;
  animation-fill-mode: both;
}
.animated-text.fadeIn.data-v-dda9f4f9 {
  animation: fadeIn-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.fadeOut.data-v-dda9f4f9 {
  animation: fadeOut-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.slideInLeft.data-v-dda9f4f9 {
  animation: slideInLeft-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.slideInRight.data-v-dda9f4f9 {
  animation: slideInRight-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.slideInUp.data-v-dda9f4f9 {
  animation: slideInUp-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.slideInDown.data-v-dda9f4f9 {
  animation: slideInDown-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.zoomIn.data-v-dda9f4f9 {
  animation: zoomIn-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.zoomOut.data-v-dda9f4f9 {
  animation: zoomOut-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.rotateIn.data-v-dda9f4f9 {
  animation: rotateIn-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.bounce.data-v-dda9f4f9 {
  animation: bounce-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.pulse.data-v-dda9f4f9 {
  animation: pulse-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
.animated-text.shake.data-v-dda9f4f9 {
  animation: shake-dda9f4f9 1s cubic-bezier(0.2, 0, 0.1, 1) both;
}
@keyframes fadeIn-dda9f4f9 {
from {
    opacity: 0;
    transform: translateY(20px);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@keyframes fadeOut-dda9f4f9 {
from {
    opacity: 1;
    transform: translateY(0);
}
to {
    opacity: 0;
    transform: translateY(-20px);
}
}
@keyframes slideInLeft-dda9f4f9 {
from {
    transform: translateX(-100%);
    opacity: 0;
}
to {
    transform: translateX(0);
    opacity: 1;
}
}
@keyframes slideInRight-dda9f4f9 {
from {
    transform: translateX(100%);
    opacity: 0;
}
to {
    transform: translateX(0);
    opacity: 1;
}
}
@keyframes slideInUp-dda9f4f9 {
from {
    transform: translateY(100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes slideInDown-dda9f4f9 {
from {
    transform: translateY(-100%);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}
@keyframes zoomIn-dda9f4f9 {
from {
    transform: scale(0.3);
    opacity: 0;
}
to {
    transform: scale(1);
    opacity: 1;
}
}
@keyframes zoomOut-dda9f4f9 {
from {
    transform: scale(1);
    opacity: 1;
}
to {
    transform: scale(0.3);
    opacity: 0;
}
}
@keyframes rotateIn-dda9f4f9 {
from {
    transform: rotate(-180deg) scale(0.3);
    opacity: 0;
}
to {
    transform: rotate(0) scale(1);
    opacity: 1;
}
}
@keyframes bounce-dda9f4f9 {
from, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
40%, 43% {
    transform: translate3d(0, -30px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}
70% {
    transform: translate3d(0, -15px, 0);
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
}
90% {
    transform: translate3d(0, -4px, 0);
}
}
@keyframes pulse-dda9f4f9 {
from {
    transform: scale3d(1, 1, 1);
}
50% {
    transform: scale3d(1.1, 1.1, 1.1);
}
to {
    transform: scale3d(1, 1, 1);
}
}
@keyframes shake-dda9f4f9 {
from, to {
    transform: translate3d(0, 0, 0);
}
10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0);
}
20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0);
}
}