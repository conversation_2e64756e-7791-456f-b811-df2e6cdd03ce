"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedColor: "#4f46e5",
      showColorPicker: false,
      currentHue: 0,
      canvasPosition: { x: 0, y: 0 },
      huePosition: 0,
      rgbValues: { r: 79, g: 70, b: 229 },
      canvasSize: { width: 0, height: 0 },
      currentScheme: "analogous",
      schemeTypes: [
        { label: "类比色", value: "analogous", icon: "🎨" },
        { label: "互补色", value: "complementary", icon: "☯️" },
        { label: "三角色", value: "triadic", icon: "△" },
        { label: "分裂互补", value: "split", icon: "◇" },
        { label: "方形色", value: "square", icon: "□" }
      ],
      colorScheme: [],
      colorCanvasContext: null,
      hueCanvasContext: null,
      canvasInitialized: false
    };
  },
  computed: {
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "%",
        top: this.canvasPosition.y + "%"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "%"
      };
    }
  },
  methods: {
    async openColorPicker() {
      this.showColorPicker = true;
      await this.$nextTick();
      if (!this.canvasInitialized) {
        await this.initColorCanvas();
        await this.initHueSlider();
        this.canvasInitialized = true;
      }
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      this.closeColorPicker();
      this.generateColorScheme();
    },
    async initColorCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      const canvas = await new Promise((resolve) => {
        query.select("#colorCanvas").fields({ node: true, size: true }).exec((res) => {
          if (res[0] && res[0].node) {
            const canvas2 = res[0].node;
            const ctx = canvas2.getContext("2d");
            const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
            canvas2.width = res[0].width * dpr;
            canvas2.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);
            this.colorCanvasContext = ctx;
            resolve(canvas2);
          }
        });
      });
      if (this.colorCanvasContext) {
        const width = canvas.width / common_vendor.index.getSystemInfoSync().pixelRatio;
        const height = canvas.height / common_vendor.index.getSystemInfoSync().pixelRatio;
        this.colorCanvasContext.clearRect(0, 0, width, height);
        const gradientWhite = this.colorCanvasContext.createLinearGradient(0, 0, width, 0);
        gradientWhite.addColorStop(0, "#fff");
        gradientWhite.addColorStop(1, `hsl(${this.currentHue}, 100%, 50%)`);
        this.colorCanvasContext.fillStyle = gradientWhite;
        this.colorCanvasContext.fillRect(0, 0, width, height);
        const gradientBlack = this.colorCanvasContext.createLinearGradient(0, 0, 0, height);
        gradientBlack.addColorStop(0, "rgba(0, 0, 0, 0)");
        gradientBlack.addColorStop(1, "#000");
        this.colorCanvasContext.fillStyle = gradientBlack;
        this.colorCanvasContext.fillRect(0, 0, width, height);
      }
    },
    async initHueSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      const canvas = await new Promise((resolve) => {
        query.select("#hueCanvas").fields({ node: true, size: true }).exec((res) => {
          if (res[0] && res[0].node) {
            const canvas2 = res[0].node;
            const ctx = canvas2.getContext("2d");
            const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
            canvas2.width = res[0].width * dpr;
            canvas2.height = res[0].height * dpr;
            ctx.scale(dpr, dpr);
            this.hueCanvasContext = ctx;
            resolve(canvas2);
          }
        });
      });
      if (this.hueCanvasContext) {
        const width = canvas.width / common_vendor.index.getSystemInfoSync().pixelRatio;
        const height = canvas.height / common_vendor.index.getSystemInfoSync().pixelRatio;
        this.hueCanvasContext.clearRect(0, 0, width, height);
        const gradient = this.hueCanvasContext.createLinearGradient(0, 0, width, 0);
        const hueSteps = 360;
        for (let i = 0; i <= hueSteps; i++) {
          gradient.addColorStop(i / hueSteps, `hsl(${i}, 100%, 50%)`);
        }
        this.hueCanvasContext.fillStyle = gradient;
        this.hueCanvasContext.fillRect(0, 0, width, height);
      }
    },
    onCanvasTouch(event) {
      const touch = event.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas-container").boundingClientRect((rect) => {
        if (!rect)
          return;
        let x = (touch.clientX - rect.left) / rect.width * 100;
        let y = (touch.clientY - rect.top) / rect.height * 100;
        x = Math.max(0, Math.min(100, x));
        y = Math.max(0, Math.min(100, y));
        this.canvasPosition = { x, y };
        this.updateColorFromCanvas();
      }).exec();
    },
    onHueTouch(event) {
      const touch = event.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider-container").boundingClientRect((rect) => {
        if (!rect)
          return;
        let position = (touch.clientX - rect.left) / rect.width * 100;
        position = Math.max(0, Math.min(100, position));
        this.huePosition = position;
        this.currentHue = position * 3.6;
        this.updateHueFromSlider();
      }).exec();
    },
    updateColorFromCanvas() {
      const { x, y } = this.canvasPosition;
      const s = x / 100;
      const v = 1 - y / 100;
      const h = this.currentHue;
      const rgb = this.hsvToRgb(h, s, v);
      this.rgbValues = rgb;
      this.selectedColor = this.rgbToHex(rgb.r, rgb.g, rgb.b);
    },
    updateHueFromSlider() {
      const h = this.currentHue;
      const { x, y } = this.canvasPosition;
      const s = x / 100;
      const v = 1 - y / 100;
      const rgb = this.hsvToRgb(h, s, v);
      this.rgbValues = rgb;
      this.selectedColor = this.rgbToHex(rgb.r, rgb.g, rgb.b);
      this.initColorCanvas();
    },
    updateColorFromRgb() {
      const { r, g, b } = this.rgbValues;
      this.rgbValues = {
        r: Math.max(0, Math.min(255, parseInt(r) || 0)),
        g: Math.max(0, Math.min(255, parseInt(g) || 0)),
        b: Math.max(0, Math.min(255, parseInt(b) || 0))
      };
      const hsv = this.rgbToHsv(this.rgbValues.r, this.rgbValues.g, this.rgbValues.b);
      this.currentHue = hsv.h;
      this.huePosition = hsv.h / 360 * 100;
      this.canvasPosition = {
        x: hsv.s * 100,
        y: (1 - hsv.v) * 100
      };
      this.selectedColor = this.rgbToHex(this.rgbValues.r, this.rgbValues.g, this.rgbValues.b);
      this.initColorCanvas();
    },
    hsvToRgb(h, s, v) {
      let r, g, b;
      const i = Math.floor(h / 60);
      const f = h / 60 - i;
      const p = v * (1 - s);
      const q = v * (1 - f * s);
      const t = v * (1 - (1 - f) * s);
      switch (i % 6) {
        case 0:
          r = v;
          g = t;
          b = p;
          break;
        case 1:
          r = q;
          g = v;
          b = p;
          break;
        case 2:
          r = p;
          g = v;
          b = t;
          break;
        case 3:
          r = p;
          g = q;
          b = v;
          break;
        case 4:
          r = t;
          g = p;
          b = v;
          break;
        case 5:
          r = v;
          g = p;
          b = q;
          break;
      }
      return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
      };
    },
    rgbToHsv(r, g, b) {
      r /= 255;
      g /= 255;
      b /= 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const d = max - min;
      let h;
      const s = max === 0 ? 0 : d / max;
      const v = max;
      if (max === min) {
        h = 0;
      } else {
        switch (max) {
          case r:
            h = (g - b) / d + (g < b ? 6 : 0);
            break;
          case g:
            h = (b - r) / d + 2;
            break;
          case b:
            h = (r - g) / d + 4;
            break;
        }
        h /= 6;
      }
      return { h: h * 360, s, v };
    },
    rgbToHex(r, g, b) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    selectScheme(type) {
      this.currentScheme = type;
      this.generateColorScheme();
    },
    generateColorScheme() {
      const { h, s, v } = this.rgbToHsv(this.rgbValues.r, this.rgbValues.g, this.rgbValues.b);
      let colors = [];
      switch (this.currentScheme) {
        case "analogous":
          colors = [
            this.hsvToRgb(h - 40, s, v),
            this.hsvToRgb(h - 20, s, v),
            this.rgbValues,
            this.hsvToRgb(h + 20, s, v),
            this.hsvToRgb(h + 40, s, v)
          ];
          break;
        case "complementary":
          colors = [
            this.hsvToRgb(h, s * 0.8, v),
            this.rgbValues,
            this.hsvToRgb(h, s, Math.min(1, v * 1.2)),
            // 互补色及其变体
            this.hsvToRgb((h + 180) % 360, s * 0.8, v),
            this.hsvToRgb((h + 180) % 360, s, v),
            this.hsvToRgb((h + 180) % 360, s, Math.min(1, v * 1.2))
          ];
          break;
        case "triadic":
          colors = [
            // 主色及其变体
            this.hsvToRgb(h, s * 0.9, v),
            this.rgbValues,
            // 第二个三角色及其变体
            this.hsvToRgb((h + 120) % 360, s * 0.9, v),
            this.hsvToRgb((h + 120) % 360, s, v),
            // 第三个三角色及其变体
            this.hsvToRgb((h + 240) % 360, s * 0.9, v),
            this.hsvToRgb((h + 240) % 360, s, v)
          ];
          break;
        case "split":
          colors = [
            // 主色及其变体
            this.hsvToRgb(h, s * 0.9, v),
            this.rgbValues,
            this.hsvToRgb(h, s, Math.min(1, v * 1.1)),
            // 分裂互补色1
            this.hsvToRgb((h + 150) % 360, s * 0.9, v),
            this.hsvToRgb((h + 150) % 360, s, v),
            // 分裂互补色2
            this.hsvToRgb((h + 210) % 360, s * 0.9, v),
            this.hsvToRgb((h + 210) % 360, s, v)
          ];
          break;
        case "square":
          colors = [
            // 主色及其变体
            this.hsvToRgb(h, s * 0.9, v),
            this.rgbValues,
            // 90度色及其变体
            this.hsvToRgb((h + 90) % 360, s * 0.9, v),
            this.hsvToRgb((h + 90) % 360, s, v),
            // 180度色及其变体
            this.hsvToRgb((h + 180) % 360, s * 0.9, v),
            this.hsvToRgb((h + 180) % 360, s, v),
            // 270度色及其变体
            this.hsvToRgb((h + 270) % 360, s * 0.9, v),
            this.hsvToRgb((h + 270) % 360, s, v)
          ];
          break;
      }
      this.colorScheme = colors.map((color) => this.rgbToHex(color.r, color.g, color.b));
    },
    copyColor(color) {
      common_vendor.index.setClipboardData({
        data: color.toUpperCase(),
        success: () => {
          common_vendor.index.showToast({
            title: "颜色代码已复制",
            icon: "success"
          });
        }
      });
    }
  },
  mounted() {
    this.generateColorScheme();
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedColor,
    b: common_vendor.t($data.selectedColor.toUpperCase()),
    c: common_vendor.o((...args) => $options.openColorPicker && $options.openColorPicker(...args)),
    d: $data.showColorPicker
  }, $data.showColorPicker ? {
    e: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    f: common_vendor.s($options.canvasCursorStyle),
    g: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    h: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    i: common_vendor.s($options.hueCursorStyle),
    j: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    k: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    l: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.updateColorFromRgb && $options.updateColorFromRgb(...args)]),
    m: $data.rgbValues.r,
    n: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.updateColorFromRgb && $options.updateColorFromRgb(...args)]),
    o: $data.rgbValues.g,
    p: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.updateColorFromRgb && $options.updateColorFromRgb(...args)]),
    q: $data.rgbValues.b,
    r: $data.selectedColor,
    s: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    t: common_vendor.o(() => {
    }),
    v: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {}, {
    w: common_vendor.f($data.schemeTypes, (type, k0, i0) => {
      return {
        a: common_vendor.t(type.icon),
        b: common_vendor.t(type.label),
        c: type.value,
        d: $data.currentScheme === type.value ? 1 : "",
        e: common_vendor.o(($event) => $options.selectScheme(type.value), type.value)
      };
    }),
    x: common_vendor.f($data.colorScheme, (color, index, i0) => {
      return {
        a: common_vendor.t(color.toUpperCase()),
        b: index,
        c: color,
        d: common_vendor.o(($event) => $options.copyColor(color), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/color-scheme-generator.js.map
