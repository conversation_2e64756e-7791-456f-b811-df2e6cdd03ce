"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedFile: null,
      startTime: "0",
      endTime: "0",
      isProcessing: false,
      processedFile: null,
      isPlaying: false,
      audioContext: null,
      audioBuffer: null,
      audioSource: null,
      duration: 0,
      currentTime: 0,
      isLoading: false,
      audioElement: null,
      playbackPosition: 0,
      waveformData: [],
      canvasContext: null,
      isDragging: false,
      dragTarget: null,
      startMarkerPosition: 0,
      endMarkerPosition: 100,
      progressWidth: 0,
      canvasTimer: null,
      recorderManager: null
    };
  },
  methods: {
    async chooseAudioFile() {
      common_vendor.index.chooseMessageFile({
        count: 1,
        type: "file",
        extension: ["mp3", "wav", "aac", "ogg", "m4a"],
        success: async (res) => {
          if (res.tempFiles && res.tempFiles.length > 0) {
            this.isLoading = true;
            try {
              const file = res.tempFiles[0];
              this.selectedFile = {
                name: file.name || "音频文件.mp3",
                path: file.path
              };
              await this.loadAudio(file.path);
              this.endTime = this.duration.toFixed(1);
              common_vendor.index.showToast({
                title: "音频加载成功",
                icon: "success"
              });
            } catch (err) {
              common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:241", "加载音频失败:", err);
              common_vendor.index.showToast({
                title: "加载音频失败",
                icon: "none"
              });
            } finally {
              this.isLoading = false;
            }
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:252", "选择音频失败:", err);
          common_vendor.index.showToast({
            title: "选择音频失败",
            icon: "none"
          });
        }
      });
    },
    async loadAudio(url) {
      return new Promise((resolve, reject) => {
        const innerAudioContext = common_vendor.index.createInnerAudioContext();
        innerAudioContext.src = url;
        innerAudioContext.onCanplay(() => {
          this.duration = innerAudioContext.duration;
          this.audioElement = innerAudioContext;
          this.generateSimulatedWaveform();
          resolve(true);
        });
        innerAudioContext.onError((err) => {
          common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:349", "音频加载失败:", err);
          reject(err);
        });
      });
    },
    async generateWaveform() {
      this.generateSimulatedWaveform();
    },
    generateSimulatedWaveform() {
      const numberOfSamples = 100;
      this.waveformData = [];
      for (let i = 0; i < numberOfSamples; i++) {
        const value = 0.3 + Math.random() * 0.4;
        this.waveformData.push(value);
      }
      this.$nextTick(() => {
        this.drawWaveform();
      });
    },
    drawWaveform() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#waveformCanvas").fields({ node: true, size: true }).exec((res) => {
        if (!res[0]) {
          common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:408", "未找到Canvas元素");
          return;
        }
        const canvas = res[0].node;
        const ctx = canvas.getContext("2d");
        if (!ctx) {
          common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:416", "无法获取Canvas上下文");
          return;
        }
        const width = canvas.width;
        const height = canvas.height;
        const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);
        ctx.clearRect(0, 0, width, height);
        const barWidth = width / this.waveformData.length;
        const centerY = height / 2;
        this.waveformData.forEach((value, index) => {
          const x = index * barWidth;
          const barHeight = value * height;
          ctx.fillStyle = "#4f46e5";
          ctx.fillRect(x, centerY - barHeight / 2, barWidth - 1, barHeight);
        });
      });
    },
    startDragging(target) {
      this.isDragging = true;
      this.dragTarget = target;
    },
    handleTimelineTouch(event) {
      if (this.isDragging)
        return;
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".timeline").boundingClientRect((rect) => {
        if (!rect)
          return;
        const touch = event.touches[0];
        const percentage = (touch.clientX - rect.left) / rect.width * 100;
        this.handleTimelinePosition(percentage);
      }).exec();
    },
    handleTimelinePosition(percentage) {
      percentage = Math.max(0, Math.min(100, percentage));
      const clickTime = percentage / 100 * this.duration;
      const distToStart = Math.abs(clickTime - parseFloat(this.startTime));
      const distToEnd = Math.abs(clickTime - parseFloat(this.endTime));
      if (distToStart < distToEnd) {
        if (clickTime < parseFloat(this.endTime)) {
          this.startTime = clickTime.toFixed(1);
          this.startMarkerPosition = percentage;
        }
      } else {
        if (clickTime > parseFloat(this.startTime)) {
          this.endTime = clickTime.toFixed(1);
          this.endMarkerPosition = percentage;
        }
      }
      this.updateMarkerPositions();
    },
    handleMarkerDrag(event) {
      if (!this.isDragging)
        return;
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".timeline").boundingClientRect((rect) => {
        if (!rect)
          return;
        const touch = event.touches[0];
        const percentage = (touch.clientX - rect.left) / rect.width * 100;
        this.updateMarkerPosition(percentage);
      }).exec();
    },
    updateMarkerPosition(percentage) {
      percentage = Math.max(0, Math.min(100, percentage));
      const time = percentage / 100 * this.duration;
      if (this.dragTarget === "start") {
        if (time < parseFloat(this.endTime)) {
          this.startTime = time.toFixed(1);
          this.startMarkerPosition = percentage;
          this.progressWidth = this.endMarkerPosition - percentage;
        }
      } else {
        if (time > parseFloat(this.startTime)) {
          this.endTime = time.toFixed(1);
          this.endMarkerPosition = percentage;
          this.progressWidth = percentage - this.startMarkerPosition;
        }
      }
    },
    stopDragging() {
      this.isDragging = false;
      this.dragTarget = null;
    },
    updateMarkerPositions() {
      const startPercentage = parseFloat(this.startTime) / this.duration * 100;
      const endPercentage = parseFloat(this.endTime) / this.duration * 100;
      this.startMarkerPosition = Math.max(0, Math.min(100, startPercentage));
      this.endMarkerPosition = Math.max(0, Math.min(100, endPercentage));
    },
    // 开始时间变化处理
    onStartTimeChange(e) {
      const value = e.detail.value;
      const numValue = parseFloat(value) || 0;
      if (numValue < 0) {
        this.startTime = "0";
        common_vendor.index.showToast({
          title: "开始时间不能小于0",
          icon: "none"
        });
        return;
      }
      if (numValue >= this.duration) {
        this.startTime = Math.max(0, this.duration - 0.1).toFixed(1);
        common_vendor.index.showToast({
          title: "开始时间不能超过音频总时长",
          icon: "none"
        });
        return;
      }
      const endTime = parseFloat(this.endTime) || this.duration;
      if (numValue >= endTime) {
        this.startTime = Math.max(0, endTime - 0.1).toFixed(1);
        common_vendor.index.showToast({
          title: "开始时间不能大于等于结束时间",
          icon: "none"
        });
        return;
      }
      this.startTime = numValue.toFixed(1);
      this.updateMarkerPositions();
    },
    // 结束时间变化处理
    onEndTimeChange(e) {
      const value = e.detail.value;
      const numValue = parseFloat(value) || 0;
      if (numValue > this.duration) {
        this.endTime = this.duration.toFixed(1);
        common_vendor.index.showToast({
          title: "结束时间不能超过音频总时长",
          icon: "none"
        });
        return;
      }
      const startTime = parseFloat(this.startTime) || 0;
      if (numValue <= startTime) {
        this.endTime = Math.min(this.duration, startTime + 0.1).toFixed(1);
        common_vendor.index.showToast({
          title: "结束时间必须大于开始时间",
          icon: "none"
        });
        return;
      }
      this.endTime = numValue.toFixed(1);
      this.updateMarkerPositions();
    },
    // 格式化时间显示
    formatTime(seconds) {
      if (!seconds || isNaN(seconds))
        return "0:00";
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, "0")}`;
    },
    // 获取剪辑时长
    getClipDuration() {
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      return Math.max(0, endTime - startTime);
    },
    // 验证剪辑参数
    validateClipParameters() {
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      if (startTime < 0) {
        common_vendor.index.showToast({
          title: "开始时间不能小于0",
          icon: "none"
        });
        return false;
      }
      if (endTime > this.duration) {
        common_vendor.index.showToast({
          title: "结束时间不能超过音频总时长",
          icon: "none"
        });
        return false;
      }
      if (startTime >= endTime) {
        common_vendor.index.showToast({
          title: "开始时间必须小于结束时间",
          icon: "none"
        });
        return false;
      }
      if (endTime - startTime < 0.1) {
        common_vendor.index.showToast({
          title: "剪辑时长至少需要0.1秒",
          icon: "none"
        });
        return false;
      }
      return true;
    },
    togglePlay() {
      if (this.isPlaying) {
        this.stopAudio();
      } else {
        this.previewAudio();
      }
    },
    previewAudio() {
      if (!this.selectedFile || this.isProcessing || !this.audioElement)
        return;
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      if (startTime >= endTime) {
        common_vendor.index.showToast({
          title: "请设置正确的时间范围",
          icon: "none"
        });
        return;
      }
      this.isPlaying = true;
      this.audioElement.currentTime = startTime;
      this.audioElement.play();
      this.updateProgress();
    },
    updateProgress() {
      if (!this.isPlaying || !this.audioElement)
        return;
      const currentTime = this.audioElement.currentTime;
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      if (currentTime < startTime) {
        this.audioElement.currentTime = startTime;
        return;
      }
      const progress = (currentTime - startTime) / (endTime - startTime) * 100;
      this.progressWidth = Math.max(0, Math.min(100, progress));
      if (currentTime >= endTime) {
        this.stopAudio();
        return;
      }
      this.canvasTimer = setTimeout(() => {
        this.updateProgress();
      }, 16);
    },
    // 处理音频剪辑
    handleProcess() {
      if (!this.selectedFile) {
        common_vendor.index.showToast({
          title: "请先选择音频文件",
          icon: "none"
        });
        return;
      }
      if (this.isProcessing) {
        common_vendor.index.showToast({
          title: "正在处理中，请稍候",
          icon: "none"
        });
        return;
      }
      if (!this.validateClipParameters()) {
        return;
      }
      this.isProcessing = true;
      this.stopAudio();
      this.processedFile = {
        name: `audio_clip_${this.startTime}s_to_${this.endTime}s.mp3`,
        startTime: this.startTime,
        endTime: this.endTime,
        duration: this.getClipDuration(),
        isSimulated: true
      };
      this.isProcessing = false;
      common_vendor.index.showModal({
        title: "音频剪辑方案",
        content: `由于小程序环境限制，我们提供以下解决方案：

1. 使用微信内置录音功能重新录制指定片段
2. 将剪辑参数保存，在H5环境下完成处理
3. 使用云端音频处理服务`,
        confirmText: "查看详细方案",
        cancelText: "保存参数",
        success: (res) => {
          if (res.confirm) {
            this.showMiniProgramSolutions();
          } else {
            this.saveMiniProgramParameters();
          }
        }
      });
    },
    // H5环境音频处理
    processAudioH5() {
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      common_vendor.index.__f__("log", "at pages/tools/audio-editor.vue:858", "H5音频剪辑参数:", {
        startTime,
        endTime,
        duration: endTime - startTime,
        sampleRate: this.audioBuffer ? this.audioBuffer.sampleRate : 44100
      });
      try {
        if (!this.audioBuffer) {
          throw new Error("音频缓冲区未初始化");
        }
        const sampleRate = this.audioBuffer.sampleRate;
        const numberOfChannels = this.audioBuffer.numberOfChannels;
        const startSample = Math.floor(startTime * sampleRate);
        const endSample = Math.floor(endTime * sampleRate);
        const clipLength = endSample - startSample;
        common_vendor.index.__f__("log", "at pages/tools/audio-editor.vue:878", "采样点计算:", {
          startSample,
          endSample,
          clipLength,
          sampleRate,
          numberOfChannels
        });
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const newBuffer = audioContext.createBuffer(numberOfChannels, clipLength, sampleRate);
        for (let channel = 0; channel < numberOfChannels; channel++) {
          const originalData = this.audioBuffer.getChannelData(channel);
          const newData = newBuffer.getChannelData(channel);
          for (let i = 0; i < clipLength; i++) {
            const sourceIndex = startSample + i;
            if (sourceIndex < originalData.length) {
              newData[i] = originalData[sourceIndex];
            }
          }
        }
        const wavArrayBuffer = this.audioBufferToWav(newBuffer);
        const blob = new Blob([wavArrayBuffer], { type: "audio/wav" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `audio_clip_${startTime}s_to_${endTime}s.wav`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        this.processedFile = {
          name: `audio_clip_${startTime}s_to_${endTime}s.wav`,
          size: blob.size,
          type: "audio/wav",
          url
        };
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "音频剪辑完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:932", "H5音频处理失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "处理失败: " + error.message,
          icon: "none"
        });
      } finally {
        this.isProcessing = false;
      }
    },
    // 小程序环境音频处理
    processAudioMiniProgram() {
      const startTime = parseFloat(this.startTime) || 0;
      const endTime = parseFloat(this.endTime) || this.duration;
      common_vendor.index.__f__("log", "at pages/tools/audio-editor.vue:948", "小程序音频剪辑参数:", {
        startTime,
        endTime,
        duration: endTime - startTime
      });
      common_vendor.index.showModal({
        title: "音频剪辑方案",
        content: `由于小程序环境限制，我们提供以下解决方案：

1. 使用微信内置录音功能重新录制指定片段
2. 将剪辑参数保存，在H5环境下完成处理
3. 使用第三方音频处理小程序`,
        confirmText: "查看详细方案",
        cancelText: "保存参数",
        success: (res) => {
          if (res.confirm) {
            this.showMiniProgramSolutions();
          } else {
            this.saveMiniProgramParameters();
          }
          this.isProcessing = false;
          common_vendor.index.hideLoading();
        }
      });
    },
    // 显示小程序解决方案
    showMiniProgramSolutions() {
      common_vendor.index.showModal({
        title: "小程序音频剪辑方案",
        content: `方案一：手动录制
• 播放原音频的指定片段
• 使用微信录音功能重新录制
• 保存录音到相册

方案二：参数导出
• 保存剪辑参数到剪贴板
• 在电脑端或H5环境使用
• 获得高质量剪辑结果

方案三：云端处理
• 上传音频到云端服务
• 使用服务器端音频处理
• 生成下载链接`,
        confirmText: "开始手动录制",
        cancelText: "导出参数",
        success: (res) => {
          if (res.confirm) {
            this.startManualRecording();
          } else {
            this.exportParameters();
          }
        }
      });
    },
    // 开始手动录制
    startManualRecording() {
      common_vendor.index.showModal({
        title: "录制指导",
        content: `请按以下步骤操作：

1. 点击"开始录制"按钮
2. 立即点击"预览"播放音频
3. 音频播放完成后点击"停止录制"
4. 保存录音文件

注意：请在安静环境下录制以获得最佳效果`,
        confirmText: "开始录制",
        success: (res) => {
          if (res.confirm) {
            this.initRecording();
          }
        }
      });
    },
    // 初始化录制功能
    initRecording() {
      const recorderManager = common_vendor.index.getRecorderManager();
      recorderManager.onStart(() => {
        common_vendor.index.__f__("log", "at pages/tools/audio-editor.vue:1009", "录制开始");
        common_vendor.index.showToast({
          title: "录制开始，请播放音频",
          icon: "none",
          duration: 2e3
        });
        setTimeout(() => {
          this.previewAudio();
        }, 500);
      });
      recorderManager.onStop((res) => {
        common_vendor.index.__f__("log", "at pages/tools/audio-editor.vue:1023", "录制结束", res);
        this.handleRecordingComplete(res);
      });
      recorderManager.onError((err) => {
        common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:1028", "录制错误:", err);
        common_vendor.index.showToast({
          title: "录制失败",
          icon: "none"
        });
      });
      recorderManager.start({
        duration: Math.ceil(this.getClipDuration() * 1e3) + 1e3,
        // 剪辑时长 + 1秒缓冲
        sampleRate: 16e3,
        numberOfChannels: 1,
        encodeBitRate: 48e3,
        format: "mp3"
      });
      this.recorderManager = recorderManager;
    },
    // 处理录制完成
    handleRecordingComplete(res) {
      const { tempFilePath, duration } = res;
      common_vendor.index.showModal({
        title: "录制完成",
        content: `录制时长：${Math.round(duration / 1e3)}秒

是否保存这段录音？`,
        confirmText: "保存录音",
        cancelText: "重新录制",
        success: (modalRes) => {
          if (modalRes.confirm) {
            this.saveRecordingToAlbum(tempFilePath);
          } else {
            this.initRecording();
          }
        }
      });
    },
    // 保存录音到相册
    saveRecordingToAlbum(filePath) {
      common_vendor.index.showActionSheet({
        itemList: ["分享给朋友", "保存到收藏", "复制文件路径"],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.shareRecording(filePath);
              break;
            case 1:
              this.saveToFavorites(filePath);
              break;
            case 2:
              this.copyFilePath(filePath);
              break;
          }
        }
      });
    },
    // 分享录音
    shareRecording(filePath) {
      common_vendor.index.shareFileMessage({
        filePath,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/audio-editor.vue:1098", "分享失败:", err);
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "none"
          });
        }
      });
    },
    // 保存到收藏
    saveToFavorites(filePath) {
      common_vendor.index.showToast({
        title: "请手动转发保存",
        icon: "none"
      });
    },
    // 复制文件路径
    copyFilePath(filePath) {
      common_vendor.index.setClipboardData({
        data: filePath,
        success: () => {
          common_vendor.index.showToast({
            title: "路径已复制",
            icon: "success"
          });
        }
      });
    },
    // 保存小程序参数
    saveMiniProgramParameters() {
      const parameters = {
        fileName: this.selectedFile.name,
        startTime: this.startTime,
        endTime: this.endTime,
        duration: this.getClipDuration(),
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      const paramText = `音频剪辑参数：
文件名：${parameters.fileName}
开始时间：${parameters.startTime}秒
结束时间：${parameters.endTime}秒
剪辑时长：${parameters.duration.toFixed(1)}秒

请在H5环境下使用这些参数完成剪辑。`;
      common_vendor.index.setClipboardData({
        data: paramText,
        success: () => {
          common_vendor.index.showToast({
            title: "参数已复制到剪贴板",
            icon: "success"
          });
          common_vendor.index.setStorageSync("audioEditParams", parameters);
        }
      });
    },
    // 导出参数
    exportParameters() {
      this.saveMiniProgramParameters();
      common_vendor.index.showModal({
        title: "导出成功",
        content: "参数已复制到剪贴板并保存到本地。\n\n您可以：\n1. 在电脑浏览器中打开本工具\n2. 粘贴参数完成剪辑\n3. 或扫描下方二维码直接跳转",
        confirmText: "生成二维码",
        cancelText: "知道了",
        success: (res) => {
          if (res.confirm) {
            this.generateQRCode();
          }
        }
      });
    },
    // 生成二维码
    generateQRCode() {
      const h5Url = `${window.location.origin}${window.location.pathname}`;
      encodeURIComponent(JSON.stringify({
        url: h5Url,
        params: {
          startTime: this.startTime,
          endTime: this.endTime,
          fileName: this.selectedFile.name
        }
      }));
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    },
    // 音频缓冲区转WAV格式
    audioBufferToWav(buffer) {
      const numberOfChannels = buffer.numberOfChannels;
      const sampleRate = buffer.sampleRate;
      const length = buffer.length;
      const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
      const view = new DataView(arrayBuffer);
      const writeString = (offset2, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset2 + i, string.charCodeAt(i));
        }
      };
      const writeUint32 = (offset2, value) => {
        view.setUint32(offset2, value, true);
      };
      const writeUint16 = (offset2, value) => {
        view.setUint16(offset2, value, true);
      };
      writeString(0, "RIFF");
      writeUint32(4, 36 + length * numberOfChannels * 2);
      writeString(8, "WAVE");
      writeString(12, "fmt ");
      writeUint32(16, 16);
      writeUint16(20, 1);
      writeUint16(22, numberOfChannels);
      writeUint32(24, sampleRate);
      writeUint32(28, sampleRate * numberOfChannels * 2);
      writeUint16(32, numberOfChannels * 2);
      writeUint16(34, 16);
      writeString(36, "data");
      writeUint32(40, length * numberOfChannels * 2);
      let offset = 44;
      for (let i = 0; i < length; i++) {
        for (let channel = 0; channel < numberOfChannels; channel++) {
          const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
          const intSample = sample < 0 ? sample * 32768 : sample * 32767;
          view.setInt16(offset, intSample, true);
          offset += 2;
        }
      }
      return arrayBuffer;
    },
    downloadProcessedFile() {
      if (!this.selectedFile) {
        common_vendor.index.showToast({
          title: "请先选择音频文件",
          icon: "none"
        });
        return;
      }
      if (!this.processedFile) {
        common_vendor.index.showToast({
          title: "请先完成音频剪辑",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "小程序下载限制",
        content: `由于微信小程序安全限制，无法直接下载音频文件。

推荐解决方案：

1. 📱 使用录音功能重新录制
2. 💾 导出参数到H5环境处理
3. ☁️ 使用云端音频处理服务
4. 🔗 复制链接在浏览器中打开`,
        confirmText: "查看详细方案",
        cancelText: "导出参数",
        success: (res) => {
          if (res.confirm) {
            this.showDownloadAlternatives();
          } else {
            this.saveMiniProgramParameters();
          }
        }
      });
    },
    // 显示下载替代方案
    showDownloadAlternatives() {
      common_vendor.index.showActionSheet({
        itemList: [
          "🎙️ 开始录音重制",
          "📋 复制剪辑参数",
          "🌐 在浏览器中打开",
          "📱 分享到微信群",
          "💡 查看更多方案"
        ],
        success: (res) => {
          switch (res.tapIndex) {
            case 0:
              this.startManualRecording();
              break;
            case 1:
              this.saveMiniProgramParameters();
              break;
            case 2:
              this.openInBrowser();
              break;
            case 3:
              this.shareToWechat();
              break;
            case 4:
              this.showMoreSolutions();
              break;
          }
        }
      });
    },
    // 在浏览器中打开
    openInBrowser() {
      const currentUrl = window.location.href;
      common_vendor.index.setClipboardData({
        data: currentUrl,
        success: () => {
          common_vendor.index.showModal({
            title: "链接已复制",
            content: "链接已复制到剪贴板。\n\n请在手机浏览器中粘贴打开，即可使用完整的音频剪辑功能。",
            confirmText: "知道了",
            showCancel: false
          });
        }
      });
    },
    // 分享到微信
    shareToWechat() {
      ({
        title: "音频剪辑工具",
        desc: `帮我处理一段音频，剪辑时间：${this.startTime}秒 - ${this.endTime}秒`,
        path: `/pages/tools/audio-editor?startTime=${this.startTime}&endTime=${this.endTime}`
      });
      common_vendor.index.showModal({
        title: "分享功能",
        content: "您可以将此工具分享给朋友，让他们帮您在H5环境下完成音频剪辑。",
        confirmText: "分享",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "请使用右上角分享",
              icon: "none"
            });
          }
        }
      });
    },
    // 显示更多解决方案
    showMoreSolutions() {
      common_vendor.index.showModal({
        title: "更多解决方案",
        content: `🔧 专业方案：
• 使用专业音频编辑软件（如Audacity）
• 在线音频编辑网站
• 手机音频编辑APP

⚡ 快速方案：
• 使用微信语音转发功能
• 录制屏幕时播放音频
• 使用其他支持音频下载的小程序

💡 技术方案：
• 搭建个人云端音频处理服务
• 使用API接口处理音频
• 开发H5版本的音频工具`,
        confirmText: "了解更多",
        cancelText: "返回",
        success: (res) => {
          if (res.confirm) {
            this.showTechnicalDetails();
          }
        }
      });
    },
    // 显示技术详情
    showTechnicalDetails() {
      common_vendor.index.showModal({
        title: "技术说明",
        content: `小程序音频处理限制：

🚫 不支持 Web Audio API
🚫 无法直接保存文件到设备
🚫 文件系统访问受限
🚫 无法使用 FileReader API

✅ 支持的功能：
• 音频播放和暂停
• 录音功能
• 参数设置和保存
• 分享和转发

建议在H5环境下使用完整功能。`,
        confirmText: "知道了",
        showCancel: false
      });
    },
    clearSelectedFile() {
      this.stopAudio();
      this.selectedFile = null;
      this.processedFile = null;
      this.startTime = "0";
      this.endTime = "0";
      this.duration = 0;
      this.currentTime = 0;
      this.waveformData = [];
      this.startMarkerPosition = 0;
      this.endMarkerPosition = 100;
      this.progressWidth = 0;
      if (this.audioElement) {
        if (this.audioElement.destroy) {
          this.audioElement.destroy();
        }
        this.audioElement = null;
      }
      if (this.audioBuffer) {
        this.audioBuffer = null;
      }
      if (this.canvasTimer) {
        clearTimeout(this.canvasTimer);
        this.canvasTimer = null;
      }
    },
    stopAudio() {
      if (this.audioElement && this.isPlaying) {
        this.audioElement.pause();
        this.isPlaying = false;
        const startTime = parseFloat(this.startTime) || 0;
        this.audioElement.currentTime = startTime;
        if (this.canvasTimer) {
          clearTimeout(this.canvasTimer);
          this.canvasTimer = null;
        }
        this.progressWidth = 0;
      }
    },
    // 组件销毁时清理定时器
    beforeDestroy() {
      if (this.canvasTimer) {
        clearTimeout(this.canvasTimer);
        this.canvasTimer = null;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.chooseAudioFile && $options.chooseAudioFile(...args)),
    b: $data.selectedFile
  }, $data.selectedFile ? {
    c: common_vendor.t($data.selectedFile.name),
    d: common_vendor.o((...args) => $options.clearSelectedFile && $options.clearSelectedFile(...args))
  } : {}, {
    e: $data.selectedFile
  }, $data.selectedFile ? {
    f: $data.progressWidth + "%",
    g: $data.startMarkerPosition + "%",
    h: common_vendor.t($options.formatTime($data.startTime)),
    i: $data.startMarkerPosition + "%",
    j: common_vendor.o(($event) => $options.startDragging("start")),
    k: common_vendor.o((...args) => $options.handleMarkerDrag && $options.handleMarkerDrag(...args)),
    l: common_vendor.o((...args) => $options.stopDragging && $options.stopDragging(...args)),
    m: common_vendor.t($options.formatTime($data.endTime)),
    n: $data.endMarkerPosition + "%",
    o: common_vendor.o(($event) => $options.startDragging("end")),
    p: common_vendor.o((...args) => $options.handleMarkerDrag && $options.handleMarkerDrag(...args)),
    q: common_vendor.o((...args) => $options.stopDragging && $options.stopDragging(...args)),
    r: common_vendor.o((...args) => $options.handleTimelineTouch && $options.handleTimelineTouch(...args)),
    s: common_vendor.o((...args) => $options.handleTimelineTouch && $options.handleTimelineTouch(...args)),
    t: common_vendor.o([($event) => $data.startTime = $event.detail.value, (...args) => $options.onStartTimeChange && $options.onStartTimeChange(...args)]),
    v: $data.startTime,
    w: common_vendor.t($options.formatTime(parseFloat($data.startTime) || 0)),
    x: common_vendor.o([($event) => $data.endTime = $event.detail.value, (...args) => $options.onEndTimeChange && $options.onEndTimeChange(...args)]),
    y: $data.endTime,
    z: common_vendor.t($options.formatTime(parseFloat($data.endTime) || $data.duration)),
    A: common_vendor.t($options.formatTime($options.getClipDuration())),
    B: common_vendor.t($options.getClipDuration().toFixed(1)),
    C: common_vendor.t($data.isPlaying ? "⏸️" : "▶️"),
    D: common_vendor.t($data.isPlaying ? "暂停" : "预览"),
    E: $data.isPlaying ? 1 : "",
    F: common_vendor.o((...args) => $options.togglePlay && $options.togglePlay(...args)),
    G: common_vendor.o((...args) => $options.stopAudio && $options.stopAudio(...args))
  } : {}, {
    H: $data.selectedFile
  }, $data.selectedFile ? common_vendor.e({
    I: !$data.isProcessing
  }, !$data.isProcessing ? {} : {}, {
    J: common_vendor.t($data.isProcessing ? "处理中..." : "开始剪辑"),
    K: $data.isProcessing ? 1 : "",
    L: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    M: !$data.processedFile ? 1 : "",
    N: common_vendor.o((...args) => $options.downloadProcessedFile && $options.downloadProcessedFile(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-579a7357"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/audio-editor.js.map
