"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      question: "",
      answer: "",
      isAsking: false,
      answers: [
        "是的，现在就去做吧！",
        "不，等等更好的时机。",
        "也许，但需要更多考虑。",
        "绝对不行，这不适合你。",
        "当然可以，相信自己！",
        "时机未到，再等等。",
        "这是个好主意！",
        "听从你的内心。",
        "寻求朋友的建议。",
        "专注于当下。",
        "保持耐心，答案会出现。",
        "勇敢地迈出第一步。",
        "重新考虑你的选择。",
        "相信命运的安排。",
        "这需要更多的努力。",
        "答案就在你心中。",
        "现在还不是时候。",
        "去尝试吧，不要犹豫。",
        "三思而后行。",
        "跟随你的直觉。",
        "让时间来证明一切。",
        "寻找更多的信息。",
        "相信自己的能力。",
        "这是一个转折点。",
        "坚持你的选择。"
      ]
    };
  },
  methods: {
    getAnswer() {
      if (!this.question.trim() || this.isAsking)
        return;
      this.isAsking = true;
      setTimeout(() => {
        const randomAnswer = this.answers[Math.floor(Math.random() * this.answers.length)];
        this.answer = randomAnswer;
        this.isAsking = false;
        if (typeof common_vendor.index !== "undefined" && common_vendor.index.vibrateShort) {
          common_vendor.index.vibrateShort();
        }
      }, 1500);
    },
    resetQuestion() {
      this.question = "";
      this.answer = "";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.question,
    b: common_vendor.o(($event) => $data.question = $event.detail.value),
    c: common_vendor.t($data.question.length),
    d: common_vendor.t($data.isAsking ? "⏳" : "✨"),
    e: common_vendor.t($data.isAsking ? "答案之书正在思考..." : "获取答案"),
    f: common_vendor.o((...args) => $options.getAnswer && $options.getAnswer(...args)),
    g: !$data.question.trim() || $data.isAsking ? 1 : "",
    h: $data.answer
  }, $data.answer ? {
    i: common_vendor.t($data.answer),
    j: common_vendor.o((...args) => $options.resetQuestion && $options.resetQuestion(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-3482bacd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/answer-book.js.map
