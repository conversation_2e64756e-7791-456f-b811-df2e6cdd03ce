/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c479fc88 {
  display: flex;
}
.flex-1.data-v-c479fc88 {
  flex: 1;
}
.items-center.data-v-c479fc88 {
  align-items: center;
}
.justify-center.data-v-c479fc88 {
  justify-content: center;
}
.justify-between.data-v-c479fc88 {
  justify-content: space-between;
}
.text-center.data-v-c479fc88 {
  text-align: center;
}
.rounded.data-v-c479fc88 {
  border-radius: 3px;
}
.rounded-lg.data-v-c479fc88 {
  border-radius: 6px;
}
.shadow.data-v-c479fc88 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c479fc88 {
  padding: 16rpx;
}
.m-4.data-v-c479fc88 {
  margin: 16rpx;
}
.mb-4.data-v-c479fc88 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c479fc88 {
  margin-top: 16rpx;
}
.magic-watermark-remover.data-v-c479fc88 {
  min-height: 100vh;
  background: #f8fafc;
  padding: 32rpx;
  box-sizing: border-box;
}
.magic-watermark-remover .content.data-v-c479fc88 {
  max-width: 1200rpx;
  margin: 0 auto;
}
.magic-watermark-remover .section-card.data-v-c479fc88 {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  border: 2rpx solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
.magic-watermark-remover .section-card.data-v-c479fc88:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.magic-watermark-remover .section-card .card-header.data-v-c479fc88 {
  padding: 24rpx;
  border-bottom: 2rpx solid rgba(226, 232, 240, 0.6);
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.magic-watermark-remover .section-card .card-header .header-icon.data-v-c479fc88 {
  font-size: 32rpx;
}
.magic-watermark-remover .section-card .card-header .header-title.data-v-c479fc88 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
}
.magic-watermark-remover .section-card .card-content.data-v-c479fc88 {
  padding: 24rpx;
}
.magic-watermark-remover .section-card .card-content .upload-area.data-v-c479fc88 {
  border: 2rpx dashed #e2e8f0;
  border-radius: 16rpx;
  padding: 48rpx 24rpx;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8fafc;
}
.magic-watermark-remover .section-card .card-content .upload-area.data-v-c479fc88:active {
  background: #f1f5f9;
  border-color: #cbd5e0;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-icon.data-v-c479fc88 {
  margin-bottom: 16rpx;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-icon .icon-text.data-v-c479fc88 {
  font-size: 64rpx;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-title.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
  display: block;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-desc-container.data-v-c479fc88 {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 4rpx;
  padding: 0 32rpx;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-desc-container .upload-desc.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.6;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-desc-container .upload-format.data-v-c479fc88 {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
  line-height: 1.6;
}
.magic-watermark-remover .section-card .card-content .upload-area .upload-desc-container .upload-feature.data-v-c479fc88 {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 500;
  line-height: 1.6;
}
.magic-watermark-remover .section-card .card-content .image-preview.data-v-c479fc88 {
  margin-top: 24rpx;
}
.magic-watermark-remover .section-card .card-content .image-preview .preview-header.data-v-c479fc88 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.magic-watermark-remover .section-card .card-content .image-preview .preview-header .preview-title.data-v-c479fc88 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
}
.magic-watermark-remover .section-card .card-content .image-preview .preview-header .change-button.data-v-c479fc88 {
  padding: 6rpx 16rpx;
  background: #3b82f6;
  border-radius: 8rpx;
}
.magic-watermark-remover .section-card .card-content .image-preview .preview-header .change-button .change-text.data-v-c479fc88 {
  font-size: 22rpx;
  color: #ffffff;
}
.magic-watermark-remover .section-card .card-content .image-preview .preview-image.data-v-c479fc88 {
  width: 100%;
  height: 360rpx;
  border-radius: 12rpx;
  object-fit: contain;
  background: #f8fafc;
}
.magic-watermark-remover .mode-grid.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 8rpx;
}
@media screen and (max-width: 768rpx) {
.magic-watermark-remover .mode-grid.data-v-c479fc88 {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
}
.magic-watermark-remover .mode-grid .mode-item.data-v-c479fc88 {
  position: relative;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
  background: #ffffff;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 1px solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
}
.magic-watermark-remover .mode-grid .mode-item.data-v-c479fc88::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: transparent;
  transition: background 0.3s ease;
}
.magic-watermark-remover .mode-grid .mode-item.active.data-v-c479fc88 {
  background: #f8fafc;
  border-color: #3b82f6;
}
.magic-watermark-remover .mode-grid .mode-item.active.data-v-c479fc88::before {
  background: #3b82f6;
}
.magic-watermark-remover .mode-grid .mode-item.active .mode-icon.data-v-c479fc88 {
  transform: scale(1.05);
}
.magic-watermark-remover .mode-grid .mode-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.magic-watermark-remover .mode-grid .mode-item .mode-icon.data-v-c479fc88 {
  width: 72rpx;
  height: 72rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  transition: transform 0.3s ease;
}
.magic-watermark-remover .mode-grid .mode-item .mode-icon .icon.data-v-c479fc88 {
  font-size: 36rpx;
  color: #ffffff;
}
.magic-watermark-remover .mode-grid .mode-item .mode-info.data-v-c479fc88 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}
.magic-watermark-remover .mode-grid .mode-item .mode-info .mode-name.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}
.magic-watermark-remover .mode-grid .mode-item .mode-info .mode-time.data-v-c479fc88 {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
  padding: 4rpx 12rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20rpx;
}
.magic-watermark-remover .mode-grid .mode-item .mode-desc.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  display: block;
}
.magic-watermark-remover .setting-group.data-v-c479fc88 {
  padding: 32rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  margin-bottom: 24rpx;
}
.magic-watermark-remover .setting-group.data-v-c479fc88:last-child {
  margin-bottom: 0;
}
.magic-watermark-remover .setting-group .setting-header.data-v-c479fc88 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.magic-watermark-remover .setting-group .setting-header .setting-label.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}
.magic-watermark-remover .setting-group .setting-header .setting-value.data-v-c479fc88 {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
  padding: 4rpx 12rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20rpx;
}
.magic-watermark-remover .setting-group .slider-container.data-v-c479fc88 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.magic-watermark-remover .setting-group .slider-container .slider-value.data-v-c479fc88 {
  font-size: 22rpx;
  color: #3b82f6;
  min-width: 80rpx;
  text-align: right;
}
.magic-watermark-remover .setting-group .edge-options.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.magic-watermark-remover .setting-group .edge-options .edge-item.data-v-c479fc88 {
  padding: 20rpx 24rpx;
  border-radius: 12rpx;
  background: #ffffff;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.magic-watermark-remover .setting-group .edge-options .edge-item.active.data-v-c479fc88 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.magic-watermark-remover .setting-group .edge-options .edge-item.active .edge-name.data-v-c479fc88 {
  color: #ffffff;
}
.magic-watermark-remover .setting-group .edge-options .edge-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.magic-watermark-remover .setting-group .edge-options .edge-item .edge-name.data-v-c479fc88 {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
}
.magic-watermark-remover .setting-group .quality-toggles.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item.data-v-c479fc88 {
  position: relative;
  padding: 24rpx;
  border-radius: 12rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  gap: 12rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item.active.data-v-c479fc88 {
  background: rgba(16, 185, 129, 0.1);
  border-color: #10b981;
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item.active .toggle-icon.data-v-c479fc88 {
  color: #10b981;
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item.active .toggle-text.data-v-c479fc88 {
  color: #10b981;
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item .toggle-icon.data-v-c479fc88 {
  font-size: 28rpx;
  color: transparent;
  width: 28rpx;
}
.magic-watermark-remover .setting-group .quality-toggles .toggle-item .toggle-text.data-v-c479fc88 {
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
}
.magic-watermark-remover .process-button.data-v-c479fc88 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
}
.magic-watermark-remover .process-button.data-v-c479fc88:active:not(.disabled) {
  transform: translateY(2rpx);
  opacity: 0.9;
}
.magic-watermark-remover .process-button.disabled.data-v-c479fc88 {
  background: #94a3b8;
  cursor: not-allowed;
}
.magic-watermark-remover .progress-container.data-v-c479fc88 {
  padding: 32rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
}
.magic-watermark-remover .progress-container .progress-info.data-v-c479fc88 {
  width: 100%;
}
.magic-watermark-remover .progress-container .progress-status.data-v-c479fc88 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.magic-watermark-remover .progress-container .progress-status .progress-percentage.data-v-c479fc88 {
  font-size: 48rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  -webkit-background-clip: text;
  color: transparent;
}
.magic-watermark-remover .progress-container .progress-status .current-step.data-v-c479fc88 {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  padding: 8rpx 20rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 24rpx;
}
.magic-watermark-remover .progress-container .progress-track.data-v-c479fc88 {
  width: 100%;
  height: 12rpx;
  background: #e2e8f0;
  border-radius: 6rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
  position: relative;
}
.magic-watermark-remover .progress-container .progress-track .progress-fill.data-v-c479fc88 {
  height: 100%;
  border-radius: 6rpx;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
}
.magic-watermark-remover .progress-container .progress-track .progress-fill .progress-glow.data-v-c479fc88 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.15) 50%, transparent 100%);
  animation: glow-c479fc88 2s linear infinite;
}
.magic-watermark-remover .progress-container .progress-eta.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  display: block;
  text-align: right;
}
.magic-watermark-remover .result-stats.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  margin-bottom: 32rpx;
  padding: 24rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
}
@media screen and (max-width: 768rpx) {
.magic-watermark-remover .result-stats.data-v-c479fc88 {
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
}
}
.magic-watermark-remover .result-stats .stat-item.data-v-c479fc88 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  border: 0.5px solid rgba(226, 232, 240, 0.6);
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.magic-watermark-remover .result-stats .stat-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
}
.magic-watermark-remover .result-stats .stat-item .stat-value.data-v-c479fc88 {
  font-size: 40rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
  line-height: 1.2;
}
.magic-watermark-remover .result-stats .stat-item .stat-label.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  text-align: center;
}
.magic-watermark-remover .comparison-container.data-v-c479fc88 {
  margin-bottom: 32rpx;
  padding: 32rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
}
.magic-watermark-remover .comparison-container .comparison-group.data-v-c479fc88 {
  display: flex;
  gap: 24rpx;
  align-items: flex-start;
}
@media screen and (max-width: 768rpx) {
.magic-watermark-remover .comparison-container .comparison-group.data-v-c479fc88 {
    flex-direction: column;
    align-items: center;
}
.magic-watermark-remover .comparison-container .comparison-group .comparison-divider.data-v-c479fc88 {
    transform: rotate(90deg);
    margin: 16rpx 0;
}
}
.magic-watermark-remover .comparison-container .comparison-item.data-v-c479fc88 {
  flex: 1;
  width: 100%;
}
.magic-watermark-remover .comparison-container .comparison-item .comparison-label.data-v-c479fc88 {
  font-size: 26rpx;
  color: #64748b;
  text-align: center;
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}
.magic-watermark-remover .comparison-container .comparison-item .image-wrapper.data-v-c479fc88 {
  width: 100%;
  background: #f8fafc;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.magic-watermark-remover .comparison-container .comparison-item .comparison-image.data-v-c479fc88 {
  display: block;
  object-fit: contain;
  background: #f8fafc;
}
.magic-watermark-remover .comparison-container .comparison-divider.data-v-c479fc88 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  flex-shrink: 0;
}
.magic-watermark-remover .comparison-container .comparison-divider .divider-icon.data-v-c479fc88 {
  font-size: 32rpx;
}
.magic-watermark-remover .result-actions.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.magic-watermark-remover .result-actions .action-button.data-v-c479fc88 {
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: none;
}
.magic-watermark-remover .result-actions .action-button.primary.data-v-c479fc88 {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: #ffffff;
}
.magic-watermark-remover .result-actions .action-button.primary.data-v-c479fc88:active {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
}
.magic-watermark-remover .result-actions .action-button.secondary.data-v-c479fc88 {
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(59, 130, 246, 0.3);
  color: #3b82f6;
}
.magic-watermark-remover .result-actions .action-button.secondary.data-v-c479fc88:active {
  background: rgba(255, 255, 255, 0.9);
}
.magic-watermark-remover .result-actions .action-button.data-v-c479fc88:active {
  transform: translateY(2rpx);
}
.magic-watermark-remover .result-actions .action-button .button-icon.data-v-c479fc88 {
  font-size: 32rpx;
}
.magic-watermark-remover .result-actions .action-button .button-text.data-v-c479fc88 {
  font-size: 28rpx;
}
.magic-watermark-remover .tech-grid.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
@media screen and (max-width: 768rpx) {
.magic-watermark-remover .tech-grid.data-v-c479fc88 {
    grid-template-columns: repeat(2, 1fr);
}
}
.magic-watermark-remover .tech-grid .tech-item.data-v-c479fc88 {
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  text-align: center;
}
.magic-watermark-remover .tech-grid .tech-item .tech-icon.data-v-c479fc88 {
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12rpx;
  border: 2rpx solid #e2e8f0;
}
.magic-watermark-remover .tech-grid .tech-item .tech-icon .icon.data-v-c479fc88 {
  font-size: 32rpx;
}
.magic-watermark-remover .tech-grid .tech-item .tech-name.data-v-c479fc88 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}
.magic-watermark-remover .tech-grid .tech-item .tech-desc.data-v-c479fc88 {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.4;
}
.magic-watermark-remover .usage-list .usage-item.data-v-c479fc88 {
  font-size: 24rpx;
  color: #1f2937;
  line-height: 1.6;
  margin-bottom: 8rpx;
  padding-left: 24rpx;
  position: relative;
}
.magic-watermark-remover .usage-list .usage-item.data-v-c479fc88:before {
  content: "";
  position: absolute;
  left: 8rpx;
  top: 12rpx;
  width: 6rpx;
  height: 6rpx;
  border-radius: 50%;
  background: #3b82f6;
}
.magic-watermark-remover .hidden-canvas.data-v-c479fc88 {
  position: fixed;
  left: -9999rpx;
  top: -9999rpx;
  visibility: hidden;
}
@keyframes glow-c479fc88 {
0% {
    transform: translateX(-100%);
}
100% {
    transform: translateX(100%);
}
}
@media (prefers-color-scheme: dark) {
.progress-container.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
}
.progress-container .progress-status .progress-percentage.data-v-c479fc88 {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    -webkit-background-clip: text;
}
.progress-container .progress-status .current-step.data-v-c479fc88 {
    color: #9ca3af;
    background: rgba(59, 130, 246, 0.15);
}
.progress-container .progress-track.data-v-c479fc88 {
    background: rgba(55, 65, 81, 0.5);
}
.progress-container .progress-eta.data-v-c479fc88 {
    color: #9ca3af;
}
}
.instruction-card.data-v-c479fc88 {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 0.5px solid rgba(226, 232, 240, 0.6);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.instruction-card .card-header.data-v-c479fc88 {
  border-bottom: 0.5px solid rgba(226, 232, 240, 0.8);
  padding: 24rpx 32rpx;
}
.instruction-card .card-header .header-icon.data-v-c479fc88 {
  font-size: 36rpx;
  margin-right: 12rpx;
}
.instruction-card .card-header .header-title.data-v-c479fc88 {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #1a1a1a, #333333);
  -webkit-background-clip: text;
  color: transparent;
}
.instruction-card .usage-steps.data-v-c479fc88 {
  padding: 24rpx 0;
}
.instruction-card .usage-steps .step-item.data-v-c479fc88 {
  display: flex;
  align-items: center;
  padding: 20rpx 24rpx;
  margin: 0 24rpx 16rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.6);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  transition: transform 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.instruction-card .usage-steps .step-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
  background: rgba(255, 255, 255, 0.8);
}
.instruction-card .usage-steps .step-item .step-icon.data-v-c479fc88 {
  width: 64rpx;
  height: 64rpx;
  margin-right: 20rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.instruction-card .usage-steps .step-item .step-icon .icon.data-v-c479fc88 {
  font-size: 32rpx;
}
.instruction-card .usage-steps .step-item .step-content.data-v-c479fc88 {
  flex: 1;
}
.instruction-card .usage-steps .step-item .step-content .step-title.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
  display: block;
}
.instruction-card .usage-steps .step-item .step-content .step-desc.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.5;
}
@media (prefers-color-scheme: dark) {
.instruction-card.data-v-c479fc88 {
    background: linear-gradient(135deg, #1a1a1a 0%, #262626 100%);
    border-color: rgba(55, 65, 81, 0.6);
}
.instruction-card .card-header.data-v-c479fc88 {
    border-color: rgba(55, 65, 81, 0.8);
}
.instruction-card .card-header .header-title.data-v-c479fc88 {
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    -webkit-background-clip: text;
}
.instruction-card .usage-steps .step-item.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.6);
    border-color: rgba(55, 65, 81, 0.8);
}
.instruction-card .usage-steps .step-item.data-v-c479fc88:hover {
    background: rgba(31, 41, 55, 0.8);
}
.instruction-card .usage-steps .step-item .step-icon.data-v-c479fc88 {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: rgba(55, 65, 81, 0.8);
}
.instruction-card .usage-steps .step-item .step-content .step-title.data-v-c479fc88 {
    color: #f9fafb;
}
.instruction-card .usage-steps .step-item .step-content .step-desc.data-v-c479fc88 {
    color: #9ca3af;
}
}
.feature-grid.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  padding: 16rpx;
}
@media screen and (max-width: 768rpx) {
.feature-grid.data-v-c479fc88 {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
}
}
.feature-grid .feature-item.data-v-c479fc88 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.8));
  border-radius: 20rpx;
  border: 0.5px solid rgba(226, 232, 240, 0.6);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  width: 100%;
}
.feature-grid .feature-item.data-v-c479fc88:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.feature-grid .feature-item .feature-icon.data-v-c479fc88 {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  width: 72rpx;
  height: 72rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.feature-grid .feature-item .feature-name.data-v-c479fc88 {
  font-size: 24rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #1a1a1a, #333333);
  -webkit-background-clip: text;
  color: transparent;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  text-align: center;
}
@media (prefers-color-scheme: dark) {
.feature-grid .feature-item.data-v-c479fc88 {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.6), rgba(17, 24, 39, 0.6));
    border-color: rgba(55, 65, 81, 0.6);
}
.feature-grid .feature-item .feature-icon.data-v-c479fc88 {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border-color: rgba(55, 65, 81, 0.8);
}
.feature-grid .feature-item .feature-name.data-v-c479fc88 {
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    -webkit-background-clip: text;
}
.feature-grid .feature-item.data-v-c479fc88:hover {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}
}
.mode-selection-card.data-v-c479fc88 {
  margin: 32rpx 0;
  overflow: visible;
}
.mode-grid.data-v-c479fc88 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
  padding: 12rpx;
}
@media screen and (max-width: 768rpx) {
.mode-grid.data-v-c479fc88 {
    grid-template-columns: 1fr;
    gap: 20rpx;
}
}
.mode-item.data-v-c479fc88 {
  position: relative;
  padding: 32rpx 24rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.7);
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
  overflow: visible;
}
.mode-item.data-v-c479fc88:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
}
.mode-item.active.data-v-c479fc88 {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 24rpx 48rpx rgba(59, 130, 246, 0.15);
}
.mode-item.active .mode-icon-wrapper.data-v-c479fc88 {
  transform: translateY(-8rpx);
}
.mode-item.active .mode-icon-wrapper .mode-icon.data-v-c479fc88 {
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
}
.mode-item.active .mode-name.data-v-c479fc88 {
  background: linear-gradient(135deg, #1a1a1a, #333333);
  -webkit-background-clip: text;
  color: transparent;
}
.mode-icon-wrapper.data-v-c479fc88 {
  position: relative;
  margin-bottom: 24rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.mode-icon.data-v-c479fc88 {
  width: 88rpx;
  height: 88rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 12rpx 24rpx rgba(0, 0, 0, 0.1);
}
.mode-icon .icon.data-v-c479fc88 {
  font-size: 40rpx;
  color: #ffffff;
}
.mode-content.data-v-c479fc88 {
  text-align: left;
}
.mode-name.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
  display: block;
  transition: all 0.3s ease;
}
.mode-desc.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  display: block;
}
@media (prefers-color-scheme: dark) {
.mode-item.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.7);
    border-color: rgba(55, 65, 81, 0.3);
}
.mode-item.active.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.9);
    border-color: rgba(59, 130, 246, 0.3);
}
.mode-item.active .mode-name.data-v-c479fc88 {
    background: linear-gradient(135deg, #f9fafb, #f3f4f6);
    -webkit-background-clip: text;
}
.mode-name.data-v-c479fc88 {
    color: #f3f4f6;
}
.mode-desc.data-v-c479fc88 {
    color: #9ca3af;
}
}
.selected-file-container.data-v-c479fc88 {
  width: 100%;
}
.file-info.data-v-c479fc88 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.file-icon.data-v-c479fc88 {
  font-size: 40rpx;
  margin-right: 16rpx;
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.file-details.data-v-c479fc88 {
  flex: 1;
  margin-right: 16rpx;
}
.file-name.data-v-c479fc88 {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4rpx;
  display: block;
  word-break: break-all;
}
.file-size.data-v-c479fc88 {
  font-size: 24rpx;
  color: #64748b;
}
.change-button.data-v-c479fc88 {
  padding: 12rpx 24rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 24rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.change-button.data-v-c479fc88:hover {
  background: rgba(59, 130, 246, 0.15);
}
.change-button .change-text.data-v-c479fc88 {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
}
.preview-container.data-v-c479fc88 {
  padding: 24rpx;
  border-radius: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border: 0.5px solid rgba(226, 232, 240, 0.8);
}
.preview-label.data-v-c479fc88 {
  font-size: 26rpx;
  color: #64748b;
  margin-bottom: 16rpx;
  display: block;
}
.preview-image.data-v-c479fc88 {
  width: 100%;
  height: 480rpx;
  border-radius: 12rpx;
  object-fit: contain;
  background: #f8fafc;
}
.preview-header.data-v-c479fc88 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.preview-actions.data-v-c479fc88 {
  display: flex;
  gap: 12rpx;
}
.action-button.data-v-c479fc88 {
  padding: 8rpx 16rpx;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-button.data-v-c479fc88:hover {
  background: rgba(59, 130, 246, 0.15);
}
.action-button .action-text.data-v-c479fc88 {
  font-size: 24rpx;
  color: #3b82f6;
  font-weight: 500;
}
@media (prefers-color-scheme: dark) {
.file-info.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
}
.file-icon.data-v-c479fc88 {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}
.file-name.data-v-c479fc88 {
    color: #f3f4f6;
}
.file-size.data-v-c479fc88 {
    color: #9ca3af;
}
.change-button.data-v-c479fc88 {
    background: rgba(59, 130, 246, 0.15);
}
.change-button.data-v-c479fc88:hover {
    background: rgba(59, 130, 246, 0.2);
}
.preview-container.data-v-c479fc88 {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(55, 65, 81, 0.3);
}
.preview-label.data-v-c479fc88 {
    color: #9ca3af;
}
.preview-image.data-v-c479fc88 {
    background: rgba(17, 24, 39, 0.8);
}
}
/* 预览图片容器样式 */
.preview-image-container.data-v-c479fc88 {
  position: relative;
  cursor: pointer;
}
.preview-image-container .watermark-overlay.data-v-c479fc88 {
  position: absolute;
  border: 2px solid #ef4444;
  background-color: rgba(239, 68, 68, 0.2);
  pointer-events: none;
}
.preview-image-container .watermark-overlay .watermark-label.data-v-c479fc88 {
  position: absolute;
  top: -24px;
  left: 0;
  background-color: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}
.preview-image-container .click-hint.data-v-c479fc88 {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.preview-image-container .click-hint.data-v-c479fc88:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.preview-image-container .click-hint:hover .hint-text.data-v-c479fc88 {
  opacity: 1;
  transform: translateY(0);
}
.preview-image-container .click-hint .hint-text.data-v-c479fc88 {
  background-color: #3b82f6;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  opacity: 0;
  transform: translateY(8px);
  transition: all 0.3s ease;
}

/* 水印选择器弹窗样式 */
.watermark-selector-modal.data-v-c479fc88 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.watermark-selector-mask.data-v-c479fc88 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10000;
}
.watermark-selector-content.data-v-c479fc88 {
  position: relative;
  width: 90vw;
  height: 80vh;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 10001;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}
.selector-header.data-v-c479fc88 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background-color: #f8f9fa;
}
.selector-title.data-v-c479fc88 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}
.selector-actions.data-v-c479fc88 {
  display: flex;
  gap: 8px;
}
.action-btn.data-v-c479fc88 {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}
.action-btn.clear-btn.data-v-c479fc88 {
  background-color: #f56565;
  color: white;
}
.action-btn.confirm-btn.data-v-c479fc88 {
  background-color: #48bb78;
  color: white;
}
.action-btn.confirm-btn.data-v-c479fc88:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}
.action-btn.close-btn.data-v-c479fc88 {
  background-color: #a0aec0;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}
.image-container.data-v-c479fc88 {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  min-height: 500px;
  user-select: none;
  -webkit-user-select: none;
  width: 100%;
}
.image-wrapper.data-v-c479fc88 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.selector-image.data-v-c479fc88 {
  max-width: 95%;
  max-height: 95%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  pointer-events: none;
  background-color: transparent;
}
.image-fallback.data-v-c479fc88 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bg-image.data-v-c479fc88 {
  border: 1px solid #ddd;
}
.selection-box.data-v-c479fc88 {
  position: absolute;
  border: 2px solid #ff4757;
  background-color: rgba(255, 71, 87, 0.2);
  pointer-events: none;
}
.selection-border.data-v-c479fc88 {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #ff4757;
  animation: dash-c479fc88 1s linear infinite;
}
@keyframes dash-c479fc88 {
to {
    stroke-dashoffset: -20;
}
}
.selection-label.data-v-c479fc88 {
  position: absolute;
  top: -24px;
  left: 0;
  background-color: #ff4757;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}
.selector-tips.data-v-c479fc88 {
  padding: 12px 16px;
  background-color: #e3f2fd;
  border-top: 1px solid #e5e5e5;
}
.tip-text.data-v-c479fc88 {
  font-size: 14px;
  color: #1976d2;
  text-align: center;
}
.debug-info-always.data-v-c479fc88 {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  max-width: 200px;
}
.debug-info-always text.data-v-c479fc88 {
  display: block;
  margin-bottom: 4px;
  word-break: break-all;
}
.no-image-tip.data-v-c479fc88 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ccc;
  text-align: center;
}
.no-image-tip text.data-v-c479fc88 {
  font-size: 16px;
  color: #666;
}

/* 预览操作按钮样式优化 */
.preview-actions.data-v-c479fc88 {
  display: flex;
  gap: 8px;
}
.preview-actions .action-button.data-v-c479fc88 {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  background-color: #3b82f6;
  color: white;
  transition: all 0.2s ease;
}
.preview-actions .action-button.data-v-c479fc88:hover {
  background-color: #2563eb;
}
.preview-actions .action-button.secondary.data-v-c479fc88 {
  background-color: #6b7280;
}
.preview-actions .action-button.secondary.data-v-c479fc88:hover {
  background-color: #4b5563;
}