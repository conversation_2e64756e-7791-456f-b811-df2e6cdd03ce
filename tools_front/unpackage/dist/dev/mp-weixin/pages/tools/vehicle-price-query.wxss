/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-0ead2f46 {
  display: flex;
}
.flex-1.data-v-0ead2f46 {
  flex: 1;
}
.items-center.data-v-0ead2f46 {
  align-items: center;
}
.justify-center.data-v-0ead2f46 {
  justify-content: center;
}
.justify-between.data-v-0ead2f46 {
  justify-content: space-between;
}
.text-center.data-v-0ead2f46 {
  text-align: center;
}
.rounded.data-v-0ead2f46 {
  border-radius: 3px;
}
.rounded-lg.data-v-0ead2f46 {
  border-radius: 6px;
}
.shadow.data-v-0ead2f46 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-0ead2f46 {
  padding: 16rpx;
}
.m-4.data-v-0ead2f46 {
  margin: 16rpx;
}
.mb-4.data-v-0ead2f46 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-0ead2f46 {
  margin-top: 16rpx;
}
.vehicle-price-query.data-v-0ead2f46 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-0ead2f46 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-0ead2f46 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.card-header.data-v-0ead2f46 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card-header .header-icon.data-v-0ead2f46 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.card-header .header-title.data-v-0ead2f46 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card-content .search-section.data-v-0ead2f46 {
  margin-bottom: 30rpx;
}
.card-content .search-section .search-input-wrapper.data-v-0ead2f46 {
  position: relative;
}
.card-content .search-section .search-input-wrapper .search-input.data-v-0ead2f46 {
  width: 100%;
  height: 88rpx;
  padding: 0 80rpx 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #333;
  background: #f9fafb;
  box-sizing: border-box;
  line-height: 88rpx;
}
.card-content .search-section .search-input-wrapper .search-input.data-v-0ead2f46:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
}
.card-content .search-section .search-input-wrapper .search-icon.data-v-0ead2f46 {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #6b7280;
}
.card-content .brand-section.data-v-0ead2f46 {
  margin-bottom: 30rpx;
}
.card-content .brand-section .section-label.data-v-0ead2f46 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.card-content .brand-section .brand-picker.data-v-0ead2f46 {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f9fafb;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
.card-content .brand-section .brand-picker .picker-text.data-v-0ead2f46 {
  font-size: 28rpx;
  color: #333;
  line-height: 1;
}
.card-content .instructions .instruction-item.data-v-0ead2f46 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.search-btn.data-v-0ead2f46 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  height: 88rpx;
  padding: 0 48rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
}
.search-btn .btn-icon.data-v-0ead2f46 {
  font-size: 32rpx;
}
.search-btn .btn-text.data-v-0ead2f46 {
  font-size: 32rpx;
  font-weight: 600;
}
.search-btn.data-v-0ead2f46:active {
  transform: scale(0.98);
}
.results-section.data-v-0ead2f46 {
  margin-bottom: 30rpx;
}
.result-card.data-v-0ead2f46 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.vehicle-info .vehicle-header.data-v-0ead2f46 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}
.vehicle-info .vehicle-header .vehicle-icon.data-v-0ead2f46 {
  width: 120rpx;
  height: 80rpx;
  margin-right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.vehicle-info .vehicle-header .vehicle-icon .vehicle-image.data-v-0ead2f46 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.vehicle-info .vehicle-header .vehicle-icon .vehicle-emoji.data-v-0ead2f46 {
  font-size: 60rpx;
}
.vehicle-info .vehicle-header .vehicle-details.data-v-0ead2f46 {
  flex: 1;
}
.vehicle-info .vehicle-header .vehicle-details .vehicle-name.data-v-0ead2f46 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.vehicle-info .vehicle-header .vehicle-details .vehicle-model.data-v-0ead2f46 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}
.vehicle-info .vehicle-header .vehicle-details .vehicle-category.data-v-0ead2f46 {
  display: inline-block;
  background: #dbeafe;
  color: #1e40af;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}
.vehicle-info .price-section.data-v-0ead2f46 {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.vehicle-info .price-section .price-item.data-v-0ead2f46 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.vehicle-info .price-section .price-item.data-v-0ead2f46:last-child {
  margin-bottom: 0;
}
.vehicle-info .price-section .price-item .price-label.data-v-0ead2f46 {
  font-size: 26rpx;
  color: #6b7280;
}
.vehicle-info .price-section .price-item .price-value.data-v-0ead2f46 {
  font-size: 28rpx;
  font-weight: 600;
}
.vehicle-info .price-section .price-item .price-value.official.data-v-0ead2f46 {
  color: #3b82f6;
}
.vehicle-info .price-section .price-item .price-value.market.data-v-0ead2f46 {
  color: #10b981;
}
.vehicle-info .price-section .price-item .trend-wrapper.data-v-0ead2f46 {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.vehicle-info .price-section .price-item .trend-wrapper .trend-icon.data-v-0ead2f46 {
  font-size: 24rpx;
}
.vehicle-info .price-section .price-item .trend-wrapper .trend-text.data-v-0ead2f46 {
  font-size: 26rpx;
  color: #374151;
}
.vehicle-info .specs-section .specs-title.data-v-0ead2f46 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.vehicle-info .specs-section .specs-grid.data-v-0ead2f46 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.vehicle-info .specs-section .specs-grid .spec-tag.data-v-0ead2f46 {
  background: #f3f4f6;
  color: #374151;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.no-results.data-v-0ead2f46 {
  background: white;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.no-results .no-results-icon.data-v-0ead2f46 {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}
.no-results .no-results-text.data-v-0ead2f46 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.no-results .no-results-hint.data-v-0ead2f46 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
}