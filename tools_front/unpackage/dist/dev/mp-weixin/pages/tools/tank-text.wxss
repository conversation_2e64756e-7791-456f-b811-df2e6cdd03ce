/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-8af08aa3 {
  display: flex;
}
.flex-1.data-v-8af08aa3 {
  flex: 1;
}
.items-center.data-v-8af08aa3 {
  align-items: center;
}
.justify-center.data-v-8af08aa3 {
  justify-content: center;
}
.justify-between.data-v-8af08aa3 {
  justify-content: space-between;
}
.text-center.data-v-8af08aa3 {
  text-align: center;
}
.rounded.data-v-8af08aa3 {
  border-radius: 3px;
}
.rounded-lg.data-v-8af08aa3 {
  border-radius: 6px;
}
.shadow.data-v-8af08aa3 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-8af08aa3 {
  padding: 16rpx;
}
.m-4.data-v-8af08aa3 {
  margin: 16rpx;
}
.mb-4.data-v-8af08aa3 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-8af08aa3 {
  margin-top: 16rpx;
}
.tank-text.data-v-8af08aa3 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-8af08aa3 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-8af08aa3 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-8af08aa3 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-8af08aa3 {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-8af08aa3 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-8af08aa3 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.selected-section.data-v-8af08aa3, .favorites-section.data-v-8af08aa3, .ascii-section.data-v-8af08aa3, .military-section.data-v-8af08aa3, .emoji-section.data-v-8af08aa3, .random-section.data-v-8af08aa3, .help-section.data-v-8af08aa3 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-8af08aa3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-8af08aa3 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-8af08aa3 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.selected-card.data-v-8af08aa3 {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #f59e0b;
  text-align: center;
}
.tank-display.data-v-8af08aa3 {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #e5e7eb;
}
.tank-text-content.data-v-8af08aa3 {
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  color: #374151;
  line-height: 1.2;
  white-space: pre;
  display: block;
  text-align: center;
}
.selected-actions.data-v-8af08aa3 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-8af08aa3 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-8af08aa3 {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.25);
}
.action-btn.primary.data-v-8af08aa3:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(245, 158, 11, 0.35);
}
.action-btn.secondary.data-v-8af08aa3 {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #e5e7eb;
}
.action-btn.secondary.data-v-8af08aa3:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.action-btn.full-width.data-v-8af08aa3 {
  width: 100%;
  margin-bottom: 24rpx;
}
.btn-icon.data-v-8af08aa3 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-8af08aa3 {
  font-size: 26rpx;
}
.count-badge.data-v-8af08aa3 {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.favorites-list.data-v-8af08aa3 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.favorite-item.data-v-8af08aa3 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-radius: 16rpx;
  border: 1rpx solid #f59e0b;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.favorite-item.data-v-8af08aa3:hover {
  background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
  border-color: #d97706;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}
.favorite-text.data-v-8af08aa3 {
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  color: #92400e;
  line-height: 1.2;
  white-space: pre;
  flex: 1;
}
.favorite-actions.data-v-8af08aa3 {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.ascii-grid.data-v-8af08aa3 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.ascii-item.data-v-8af08aa3 {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #0ea5e9;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.ascii-item.data-v-8af08aa3:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #0284c7;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.15);
}
.ascii-text.data-v-8af08aa3 {
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  color: #0369a1;
  line-height: 1.2;
  white-space: pre;
  display: block;
  text-align: center;
}
.ascii-actions.data-v-8af08aa3 {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-top: 16rpx;
}
.military-grid.data-v-8af08aa3 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.military-item.data-v-8af08aa3 {
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #10b981;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.military-item.data-v-8af08aa3:hover {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #059669;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.15);
}
.military-symbol.data-v-8af08aa3 {
  font-family: "Courier New", monospace;
  font-size: 28rpx;
  color: #065f46;
  font-weight: 600;
}
.military-actions.data-v-8af08aa3 {
  display: flex;
  justify-content: center;
  gap: 8rpx;
  margin-top: 16rpx;
}
.emoji-grid.data-v-8af08aa3 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.emoji-item.data-v-8af08aa3 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #ec4899;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.emoji-item.data-v-8af08aa3:hover {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border-color: #db2777;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.15);
}
.emoji-text.data-v-8af08aa3 {
  font-size: 32rpx;
  display: block;
  margin-bottom: 8rpx;
}
.emoji-actions.data-v-8af08aa3 {
  display: flex;
  justify-content: center;
  gap: 8rpx;
}
.random-content.data-v-8af08aa3 {
  padding-left: 0;
  padding-right: 0;
}
.random-display.data-v-8af08aa3 {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #cbd5e1;
  text-align: center;
}
.random-text.data-v-8af08aa3 {
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  color: #475569;
  line-height: 1.2;
  white-space: pre;
  display: block;
  margin-bottom: 24rpx;
}
.random-actions.data-v-8af08aa3 {
  display: flex;
  gap: 16rpx;
}
.mini-btn.data-v-8af08aa3 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mini-btn.data-v-8af08aa3:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.mini-btn.remove.data-v-8af08aa3 {
  background: #fecaca;
}
.mini-btn.remove.data-v-8af08aa3:hover {
  background: #fca5a5;
}
.mini-icon.data-v-8af08aa3 {
  font-size: 20rpx;
}
.help-content.data-v-8af08aa3 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-8af08aa3 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-8af08aa3:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-8af08aa3 {
    padding: 30rpx 20rpx;
}
.military-grid.data-v-8af08aa3 {
    grid-template-columns: 1fr;
}
.emoji-grid.data-v-8af08aa3 {
    grid-template-columns: repeat(2, 1fr);
}
.selected-actions.data-v-8af08aa3, .random-actions.data-v-8af08aa3 {
    flex-direction: column;
    gap: 12rpx;
}
}
.random-section .random-content.data-v-8af08aa3 {
  padding-left: 0;
  padding-right: 0;
}
.random-section .action-btn.primary.full-width.data-v-8af08aa3 {
  width: 100%;
  box-sizing: border-box;
  margin: 0 16rpx 24rpx 16rpx;
  background: #f3f4f6;
  color: #222;
  border-radius: 12rpx;
  font-weight: 600;
  font-size: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.18s cubic-bezier(0.2, 0, 0.1, 1);
  border: 1rpx solid #ececec;
  position: relative;
  overflow: hidden;
}
.random-section .action-btn.primary.full-width.data-v-8af08aa3:hover {
  background: #ececec;
  transform: scale(1.02);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}
.random-section .action-btn.primary.full-width.data-v-8af08aa3:active {
  transform: scale(0.98);
}
.random-section .action-btn.primary.full-width .btn-icon.data-v-8af08aa3 {
  font-size: 28rpx;
  margin-right: 12rpx;
  vertical-align: middle;
}
.random-section .action-btn.primary.full-width .btn-text.data-v-8af08aa3 {
  font-size: 28rpx;
  font-weight: 600;
  vertical-align: middle;
}