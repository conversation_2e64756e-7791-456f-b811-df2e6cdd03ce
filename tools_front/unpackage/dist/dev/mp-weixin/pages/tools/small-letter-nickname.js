"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      selectedStyle: "subscript",
      results: [],
      examples: ["hello", "world", "love", "dream", "star", "moon", "cute", "sweet"],
      styles: [
        { key: "subscript", name: "下标", icon: "₁" },
        { key: "superscript", name: "上标", icon: "¹" },
        { key: "small", name: "小字母", icon: "ᵃ" },
        { key: "circle", name: "圆圈", icon: "ⓐ" },
        { key: "square", name: "方框", icon: "🅰" },
        { key: "bold", name: "粗体", icon: "𝐚" }
      ],
      transforms: {
        subscript: {
          "a": "ₐ",
          "b": "ᵦ",
          "c": "ᶜ",
          "d": "ᵈ",
          "e": "ₑ",
          "f": "ᶠ",
          "g": "ᵍ",
          "h": "ₕ",
          "i": "ᵢ",
          "j": "ⱼ",
          "k": "ₖ",
          "l": "ₗ",
          "m": "ₘ",
          "n": "ₙ",
          "o": "ₒ",
          "p": "ₚ",
          "q": "ᵩ",
          "r": "ᵣ",
          "s": "ₛ",
          "t": "ₜ",
          "u": "ᵤ",
          "v": "ᵥ",
          "w": "ᵨ",
          "x": "ₓ",
          "y": "ᵧ",
          "z": "ᵤ",
          "0": "₀",
          "1": "₁",
          "2": "₂",
          "3": "₃",
          "4": "₄",
          "5": "₅",
          "6": "₆",
          "7": "₇",
          "8": "₈",
          "9": "₉"
        },
        superscript: {
          "a": "ᵃ",
          "b": "ᵇ",
          "c": "ᶜ",
          "d": "ᵈ",
          "e": "ᵉ",
          "f": "ᶠ",
          "g": "ᵍ",
          "h": "ʰ",
          "i": "ⁱ",
          "j": "ʲ",
          "k": "ᵏ",
          "l": "ˡ",
          "m": "ᵐ",
          "n": "ⁿ",
          "o": "ᵒ",
          "p": "ᵖ",
          "q": "ᵠ",
          "r": "ʳ",
          "s": "ˢ",
          "t": "ᵗ",
          "u": "ᵘ",
          "v": "ᵛ",
          "w": "ʷ",
          "x": "ˣ",
          "y": "ʸ",
          "z": "ᶻ",
          "0": "⁰",
          "1": "¹",
          "2": "²",
          "3": "³",
          "4": "⁴",
          "5": "⁵",
          "6": "⁶",
          "7": "⁷",
          "8": "⁸",
          "9": "⁹"
        },
        small: {
          "a": "ᵃ",
          "b": "ᵇ",
          "c": "ᶜ",
          "d": "ᵈ",
          "e": "ᵉ",
          "f": "ᶠ",
          "g": "ᵍ",
          "h": "ʰ",
          "i": "ⁱ",
          "j": "ʲ",
          "k": "ᵏ",
          "l": "ˡ",
          "m": "ᵐ",
          "n": "ⁿ",
          "o": "ᵒ",
          "p": "ᵖ",
          "q": "ᵠ",
          "r": "ʳ",
          "s": "ˢ",
          "t": "ᵗ",
          "u": "ᵘ",
          "v": "ᵛ",
          "w": "ʷ",
          "x": "ˣ",
          "y": "ʸ",
          "z": "ᶻ",
          "A": "ᴬ",
          "B": "ᴮ",
          "C": "ᶜ",
          "D": "ᴰ",
          "E": "ᴱ",
          "F": "ᶠ",
          "G": "ᴳ",
          "H": "ᴴ",
          "I": "ᴵ",
          "J": "ᴶ",
          "K": "ᴷ",
          "L": "ᴸ",
          "M": "ᴹ",
          "N": "ᴺ",
          "O": "ᴼ",
          "P": "ᴾ",
          "Q": "ᵠ",
          "R": "ᴿ",
          "S": "ˢ",
          "T": "ᵀ",
          "U": "ᵁ",
          "V": "ⱽ",
          "W": "ᵂ",
          "X": "ˣ",
          "Y": "ʸ",
          "Z": "ᶻ"
        },
        circle: {
          "a": "ⓐ",
          "b": "ⓑ",
          "c": "ⓒ",
          "d": "ⓓ",
          "e": "ⓔ",
          "f": "ⓕ",
          "g": "ⓖ",
          "h": "ⓗ",
          "i": "ⓘ",
          "j": "ⓙ",
          "k": "ⓚ",
          "l": "ⓛ",
          "m": "ⓜ",
          "n": "ⓝ",
          "o": "ⓞ",
          "p": "ⓟ",
          "q": "ⓠ",
          "r": "ⓡ",
          "s": "ⓢ",
          "t": "ⓣ",
          "u": "ⓤ",
          "v": "ⓥ",
          "w": "ⓦ",
          "x": "ⓧ",
          "y": "ⓨ",
          "z": "ⓩ",
          "A": "Ⓐ",
          "B": "Ⓑ",
          "C": "Ⓒ",
          "D": "Ⓓ",
          "E": "Ⓔ",
          "F": "Ⓕ",
          "G": "Ⓖ",
          "H": "Ⓗ",
          "I": "Ⓘ",
          "J": "Ⓙ",
          "K": "Ⓚ",
          "L": "Ⓛ",
          "M": "Ⓜ",
          "N": "Ⓝ",
          "O": "Ⓞ",
          "P": "Ⓟ",
          "Q": "Ⓠ",
          "R": "Ⓡ",
          "S": "Ⓢ",
          "T": "Ⓣ",
          "U": "Ⓤ",
          "V": "Ⓥ",
          "W": "Ⓦ",
          "X": "Ⓧ",
          "Y": "Ⓨ",
          "Z": "Ⓩ",
          "0": "⓪",
          "1": "①",
          "2": "②",
          "3": "③",
          "4": "④",
          "5": "⑤",
          "6": "⑥",
          "7": "⑦",
          "8": "⑧",
          "9": "⑨"
        },
        square: {
          "a": "🅰",
          "b": "🅱",
          "c": "🅲",
          "d": "🅳",
          "e": "🅴",
          "f": "🅵",
          "g": "🅶",
          "h": "🅷",
          "i": "🅸",
          "j": "🅹",
          "k": "🅺",
          "l": "🅻",
          "m": "🅼",
          "n": "🅽",
          "o": "🅾",
          "p": "🅿",
          "q": "🆀",
          "r": "🆁",
          "s": "🆂",
          "t": "🆃",
          "u": "🆄",
          "v": "🆅",
          "w": "🆆",
          "x": "🆇",
          "y": "🆈",
          "z": "🆉",
          "A": "🅰",
          "B": "🅱",
          "C": "🅲",
          "D": "🅳",
          "E": "🅴",
          "F": "🅵",
          "G": "🅶",
          "H": "🅷",
          "I": "🅸",
          "J": "🅹",
          "K": "🅺",
          "L": "🅻",
          "M": "🅼",
          "N": "🅽",
          "O": "🅾",
          "P": "🅿",
          "Q": "🆀",
          "R": "🆁",
          "S": "🆂",
          "T": "🆃",
          "U": "🆄",
          "V": "🆅",
          "W": "🆆",
          "X": "🆇",
          "Y": "🆈",
          "Z": "🆉"
        },
        bold: {
          "a": "𝐚",
          "b": "𝐛",
          "c": "𝐜",
          "d": "𝐝",
          "e": "𝐞",
          "f": "𝐟",
          "g": "𝐠",
          "h": "𝐡",
          "i": "𝐢",
          "j": "𝐣",
          "k": "𝐤",
          "l": "𝐥",
          "m": "𝐦",
          "n": "𝐧",
          "o": "𝐨",
          "p": "𝐩",
          "q": "𝐪",
          "r": "𝐫",
          "s": "𝐬",
          "t": "𝐭",
          "u": "𝐮",
          "v": "𝐯",
          "w": "𝐰",
          "x": "𝐱",
          "y": "𝐲",
          "z": "𝐳",
          "A": "𝐀",
          "B": "𝐁",
          "C": "𝐂",
          "D": "𝐃",
          "E": "𝐄",
          "F": "𝐅",
          "G": "𝐆",
          "H": "𝐇",
          "I": "𝐈",
          "J": "𝐉",
          "K": "𝐊",
          "L": "𝐋",
          "M": "𝐌",
          "N": "𝐍",
          "O": "𝐎",
          "P": "𝐏",
          "Q": "𝐐",
          "R": "𝐑",
          "S": "𝐒",
          "T": "𝐓",
          "U": "𝐔",
          "V": "𝐕",
          "W": "𝐖",
          "X": "𝐗",
          "Y": "𝐘",
          "Z": "𝐙"
        }
      }
    };
  },
  methods: {
    generateNicknames() {
      if (!this.inputText.trim()) {
        this.results = [];
        return;
      }
      const input = this.inputText.trim();
      const transforms = this.transforms[this.selectedStyle];
      this.results = [];
      let basicTransform = "";
      for (let char of input) {
        basicTransform += transforms[char] || char;
      }
      if (basicTransform !== input) {
        this.results.push(basicTransform);
      }
      if (basicTransform) {
        this.results.push("✨" + basicTransform + "✨");
        this.results.push("🌟" + basicTransform + "🌟");
        this.results.push("💫" + basicTransform + "💫");
        this.results.push("⭐" + basicTransform + "⭐");
        this.results.push("🌙" + basicTransform + "🌙");
      }
      this.results = this.results.slice(0, 8);
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "昵称已复制",
            icon: "success",
            duration: 1500
          });
          if (common_vendor.index.vibrateShort) {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }
        }
      });
    },
    setExample(example) {
      this.inputText = example;
      this.generateNicknames();
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort({
          type: "light"
        });
      }
    }
  },
  watch: {
    selectedStyle() {
      this.generateNicknames();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.generateNicknames && $options.generateNicknames(...args)]),
    b: $data.inputText,
    c: common_vendor.f($data.styles, (style, k0, i0) => {
      return {
        a: common_vendor.t(style.icon),
        b: common_vendor.t(style.name),
        c: style.key,
        d: common_vendor.o(($event) => $data.selectedStyle = style.key, style.key),
        e: common_vendor.n({
          active: $data.selectedStyle === style.key
        })
      };
    }),
    d: $data.results.length > 0
  }, $data.results.length > 0 ? {
    e: common_vendor.f($data.results, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    f: common_vendor.f($data.examples, (example, k0, i0) => {
      return {
        a: common_vendor.t(example),
        b: example,
        c: common_vendor.o(($event) => $options.setExample(example), example)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-da2d62e1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/small-letter-nickname.js.map
