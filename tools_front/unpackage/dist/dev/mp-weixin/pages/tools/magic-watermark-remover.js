"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: null,
      selectedMode: "",
      processingStrength: 70,
      edgeMode: "smooth",
      enableDenoising: true,
      enableSharpening: false,
      isProcessing: false,
      progress: 0,
      currentStep: "",
      estimatedTime: 0,
      processedImageUrl: "",
      canvasWidth: 800,
      canvasHeight: 600,
      detectedWatermarks: 0,
      removedWatermarks: 0,
      processingTime: 0,
      qualityScore: 0,
      toolService: new utils_toolService.ToolService(),
      // 水印区域选择相关
      showWatermarkSelector: false,
      // 确保初始为false
      watermarkArea: null,
      // 存储选择的水印区域
      selectionBox: {
        visible: false,
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0
      },
      isDragging: false,
      pageReady: false,
      // 添加页面准备状态
      imageInfo: {
        width: 0,
        height: 0,
        displayWidth: 0,
        displayHeight: 0
      },
      processingModes: [
        {
          id: "quick",
          name: "快速模式",
          desc: "快速处理，适合简单水印",
          icon: "⚡",
          gradient: "linear-gradient(135deg, #10b981, #059669)"
        },
        {
          id: "precise",
          name: "精确模式",
          desc: "精确识别，适合复杂水印",
          icon: "🎯",
          gradient: "linear-gradient(135deg, #3b82f6, #2563eb)"
        },
        {
          id: "deep",
          name: "深度模式",
          desc: "深度学习，最佳效果",
          icon: "🧠",
          gradient: "linear-gradient(135deg, #8b5cf6, #7c3aed)"
        }
      ],
      strengthLabels: ["轻度", "中度", "重度", "极强", "最强"],
      edgeOptions: [
        { id: "smooth", name: "平滑" },
        { id: "sharp", name: "锐化" },
        { id: "natural", name: "自然" }
      ]
    };
  },
  onLoad() {
    common_vendor.index.setNavigationBarTitle({
      title: "魔法抹除水印"
    });
    common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:553", "页面加载完成");
    common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:554", "初始状态 - showWatermarkSelector:", this.showWatermarkSelector);
    common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:555", "初始状态 - selectedImage:", this.selectedImage);
    this.showWatermarkSelector = false;
  },
  onReady() {
    common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:562", "页面渲染完成");
    common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:563", "渲染完成 - showWatermarkSelector:", this.showWatermarkSelector);
    this.pageReady = true;
    this.showWatermarkSelector = false;
  },
  computed: {
    selectionBoxStyle() {
      if (!this.selectionBox.visible)
        return {};
      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX);
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY);
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX);
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY);
      return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
      };
    }
  },
  methods: {
    chooseImage() {
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:590", "开始选择图片");
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:597", "选择的图片路径:", tempFilePath);
          common_vendor.index.getImageInfo({
            src: tempFilePath,
            success: (info) => {
              common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:603", "获取到的图片信息:", info);
              this.selectedImage = {
                url: tempFilePath,
                filePath: tempFilePath,
                // 微信小程序使用filePath
                name: `image_${Date.now()}.jpg`,
                size: info.width * info.height * 3,
                // 估算文件大小
                width: info.width,
                height: info.height
              };
              common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:613", "设置的selectedImage:", this.selectedImage);
              this.canvasWidth = info.width;
              this.canvasHeight = info.height;
              utils_index.showSuccess("图片上传成功");
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:622", "获取图片信息失败:", err);
              utils_index.showError("图片信息获取失败");
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:628", "选择图片失败:", err);
          utils_index.showError("图片选择失败");
        }
      });
    },
    selectMode(mode) {
      this.selectedMode = mode;
      utils_index.showSuccess(`已选择${this.processingModes.find((m) => m.id === mode).name}`);
    },
    onStrengthChange(e) {
      this.processingStrength = e.detail.value;
    },
    selectEdge(edge) {
      this.edgeMode = edge;
    },
    async handleProcess() {
      if (!this.selectedImage || !this.selectedMode) {
        utils_index.showError("请先选择图片和处理模式");
        return;
      }
      this.isProcessing = true;
      this.progress = 0;
      this.currentStep = "准备处理...";
      this.estimatedTime = 30;
      try {
        this.simulateProgress();
        const params = {
          mode: this.selectedMode,
          strength: this.processingStrength,
          edgeMode: this.edgeMode,
          enableDenoising: this.enableDenoising,
          enableSharpening: this.enableSharpening
        };
        if (this.watermarkArea) {
          params.watermarkAreas = [{
            imageIndex: 0,
            x: this.watermarkArea.x,
            y: this.watermarkArea.y,
            width: this.watermarkArea.width,
            height: this.watermarkArea.height
          }];
          common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:680", "传递水印区域到后端:", params.watermarkAreas);
        }
        const imageSource = this.selectedImage.file || this.selectedImage.filePath || this.selectedImage.url;
        const result = await this.toolService.removeMagicWatermark(imageSource, params);
        if (result.success) {
          this.processedImageUrl = result.data.processedImageUrl;
          this.detectedWatermarks = result.data.detectedWatermarks || 3;
          this.removedWatermarks = result.data.removedWatermarks || 3;
          this.processingTime = result.data.processingTime || 15;
          this.qualityScore = result.data.qualityScore || 95;
          this.progress = 100;
          this.currentStep = "处理完成";
          this.estimatedTime = 0;
          utils_index.showSuccess("水印去除成功！");
        } else {
          throw new Error(result.message || "处理失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:704", "魔法去水印失败:", error);
        utils_index.showError(error.message || "处理失败，请重试");
        await this.simulateLocalProcessing();
      } finally {
        this.isProcessing = false;
      }
    },
    async simulateLocalProcessing() {
      this.processedImageUrl = this.selectedImage.url;
      this.detectedWatermarks = 2;
      this.removedWatermarks = 2;
      this.processingTime = 12;
      this.qualityScore = 88;
      this.progress = 100;
      this.currentStep = "本地处理完成";
      this.estimatedTime = 0;
      utils_index.showSuccess("本地处理完成（模拟）");
    },
    simulateProgress() {
      const steps = [
        { progress: 20, step: "分析图片内容...", time: 25 },
        { progress: 40, step: "检测水印位置...", time: 20 },
        { progress: 60, step: "智能去除水印...", time: 15 },
        { progress: 80, step: "修复背景区域...", time: 10 },
        { progress: 95, step: "优化图片质量...", time: 5 }
      ];
      let currentStepIndex = 0;
      const updateProgress = () => {
        if (currentStepIndex < steps.length && this.isProcessing) {
          const currentStepData = steps[currentStepIndex];
          this.progress = currentStepData.progress;
          this.currentStep = currentStepData.step;
          this.estimatedTime = currentStepData.time;
          currentStepIndex++;
          setTimeout(updateProgress, 2e3);
        }
      };
      updateProgress();
    },
    saveImage() {
      if (!this.processedImageUrl) {
        utils_index.showError("没有可保存的图片");
        return;
      }
      common_vendor.index.downloadFile({
        url: this.processedImageUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                utils_index.showSuccess("图片已保存到相册");
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:770", "保存图片失败:", err);
                utils_index.showError("保存图片失败");
              }
            });
          } else {
            utils_index.showError("下载图片失败");
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:779", "下载图片失败:", err);
          utils_index.showError("下载图片失败");
        }
      });
    },
    resetProcess() {
      this.selectedImage = null;
      this.selectedMode = "";
      this.processingStrength = 70;
      this.edgeMode = "smooth";
      this.enableDenoising = true;
      this.enableSharpening = false;
      this.isProcessing = false;
      this.progress = 0;
      this.currentStep = "";
      this.estimatedTime = 0;
      this.processedImageUrl = "";
      this.detectedWatermarks = 0;
      this.removedWatermarks = 0;
      this.processingTime = 0;
      this.qualityScore = 0;
      this.watermarkArea = null;
      this.showWatermarkSelector = false;
      this.selectionBox.visible = false;
      utils_index.showSuccess("已重置，可重新处理");
    },
    formatFileSize(bytes) {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    // 处理水印按钮点击
    handleWatermarkButtonTap() {
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:820", "水印按钮被点击");
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:821", "页面准备状态:", this.pageReady);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:822", "图片状态:", !!this.selectedImage);
      if (!this.pageReady) {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:825", "页面未准备好，忽略按钮点击");
        return;
      }
      this.openWatermarkSelector();
    },
    // 处理预览图片点击
    handlePreviewImageTap() {
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:834", "预览图片被点击");
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:835", "页面准备状态:", this.pageReady);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:836", "图片状态:", !!this.selectedImage);
      if (!this.pageReady) {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:839", "页面未准备好，忽略点击");
        return;
      }
      if (!this.selectedImage) {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:844", "没有图片，忽略点击");
        return;
      }
      this.openWatermarkSelector();
    },
    // 水印区域选择相关方法
    openWatermarkSelector() {
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:853", "=== openWatermarkSelector 被调用 ===");
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:854", "页面准备状态:", this.pageReady);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:855", "调用堆栈:", new Error().stack);
      if (!this.pageReady) {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:859", "页面还未准备好，忽略调用");
        return;
      }
      if (!this.selectedImage) {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:864", "没有选择图片，显示错误提示");
        utils_index.showError("请先选择图片");
        return;
      }
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:869", "打开水印区域选择器");
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:870", "图片对象:", this.selectedImage);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:871", "图片URL:", this.selectedImage.url);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:872", "图片尺寸:", this.selectedImage.width, "x", this.selectedImage.height);
      this.showWatermarkSelector = true;
      this.selectionBox.visible = false;
      this.isDragging = false;
      this.$nextTick(() => {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:880", "nextTick: 准备获取图片信息");
        this.getImageInfo();
      });
    },
    closeWatermarkSelector() {
      this.showWatermarkSelector = false;
      this.selectionBox.visible = false;
      this.isDragging = false;
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:889", "关闭水印区域选择器");
    },
    onImageLoad(e) {
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:893", "弹窗中图片加载成功!");
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:894", "图片加载事件详情:", e);
      if (e.detail) {
        const { width, height } = e.detail;
        this.imageInfo.width = width;
        this.imageInfo.height = height;
        this.imageInfo.displayWidth = width;
        this.imageInfo.displayHeight = height;
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:901", "弹窗图片尺寸:", { width, height });
        utils_index.showSuccess("图片加载成功");
      } else {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:904", "图片加载完成，但没有尺寸信息");
        this.getImageInfo();
      }
    },
    onImageError(e) {
      var _a;
      common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:911", "弹窗中图片加载失败:", e);
      common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:912", "图片URL:", (_a = this.selectedImage) == null ? void 0 : _a.url);
      utils_index.showError("图片加载失败，请重新选择图片");
    },
    getImageInfo() {
      if (this.selectedImage && this.selectedImage.url) {
        common_vendor.index.getImageInfo({
          src: this.selectedImage.url,
          success: (info) => {
            this.imageInfo.width = info.width;
            this.imageInfo.height = info.height;
            this.imageInfo.displayWidth = info.width;
            this.imageInfo.displayHeight = info.height;
            common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:925", "重新获取图片信息成功:", info);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:928", "获取图片信息失败:", err);
          }
        });
      }
    },
    getImageSrc() {
      if (this.selectedImage && this.selectedImage.url) {
        const url = this.selectedImage.url;
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:938", "获取图片源:", url);
        return url;
      }
      return "";
    },
    onTouchStart(e) {
      if (!this.showWatermarkSelector)
        return;
      e.preventDefault();
      e.stopPropagation();
      const touch = e.touches[0];
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:951", "触摸开始:", touch);
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".image-container").boundingClientRect((rect) => {
        if (rect) {
          common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:956", "容器信息:", rect);
          const relativeX = touch.clientX - rect.left;
          const relativeY = touch.clientY - rect.top;
          const boundedX = Math.max(0, Math.min(relativeX, rect.width));
          const boundedY = Math.max(0, Math.min(relativeY, rect.height));
          this.isDragging = true;
          this.selectionBox.startX = boundedX;
          this.selectionBox.startY = boundedY;
          this.selectionBox.endX = boundedX;
          this.selectionBox.endY = boundedY;
          this.selectionBox.visible = true;
          common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:970", "开始选择水印区域:", {
            x: boundedX,
            y: boundedY,
            containerWidth: rect.width,
            containerHeight: rect.height
          });
        } else {
          common_vendor.index.__f__("error", "at pages/tools/magic-watermark-remover.vue:977", "无法获取容器信息");
        }
      }).exec();
    },
    onTouchMove(e) {
      if (!this.isDragging || !this.showWatermarkSelector)
        return;
      e.preventDefault();
      e.stopPropagation();
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".image-container").boundingClientRect((rect) => {
        if (rect) {
          const relativeX = touch.clientX - rect.left;
          const relativeY = touch.clientY - rect.top;
          const boundedX = Math.max(0, Math.min(relativeX, rect.width));
          const boundedY = Math.max(0, Math.min(relativeY, rect.height));
          this.selectionBox.endX = boundedX;
          this.selectionBox.endY = boundedY;
          common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:1001", "拖拽中:", { endX: boundedX, endY: boundedY });
        }
      }).exec();
    },
    onTouchEnd(e) {
      if (!this.isDragging || !this.showWatermarkSelector)
        return;
      e.preventDefault();
      e.stopPropagation();
      this.isDragging = false;
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX);
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY);
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:1017", "触摸结束，选择区域尺寸:", { width, height });
      if (width < 20 || height < 20) {
        this.selectionBox.visible = false;
        utils_index.showError("选择区域太小，请重新选择");
      } else {
        common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:1023", "水印区域选择完成:", {
          width,
          height,
          startX: this.selectionBox.startX,
          startY: this.selectionBox.startY,
          endX: this.selectionBox.endX,
          endY: this.selectionBox.endY
        });
        utils_index.showSuccess(`已选择 ${Math.round(width)}×${Math.round(height)} 区域`);
      }
    },
    confirmWatermarkArea() {
      if (!this.selectionBox.visible) {
        utils_index.showError("请先选择水印区域");
        return;
      }
      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX);
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY);
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX);
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY);
      this.watermarkArea = {
        x: Math.round(left),
        y: Math.round(top),
        width: Math.round(width),
        height: Math.round(height)
      };
      common_vendor.index.__f__("log", "at pages/tools/magic-watermark-remover.vue:1054", "确认水印区域:", this.watermarkArea);
      utils_index.showSuccess("水印区域已保存");
      this.closeWatermarkSelector();
    },
    clearWatermarkArea() {
      this.selectionBox.visible = false;
      this.watermarkArea = null;
      utils_index.showSuccess("已清除选择");
    },
    getWatermarkOverlayStyle() {
      if (!this.watermarkArea)
        return {};
      return {
        left: `${this.watermarkArea.x}px`,
        top: `${this.watermarkArea.y}px`,
        width: `${this.watermarkArea.width}px`,
        height: `${this.watermarkArea.height}px`
      };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedImage
  }, !$data.selectedImage ? {} : common_vendor.e({
    b: common_vendor.t($data.selectedImage.name),
    c: common_vendor.t($options.formatFileSize($data.selectedImage.size)),
    d: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    e: common_vendor.o((...args) => $options.handleWatermarkButtonTap && $options.handleWatermarkButtonTap(...args)),
    f: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    g: $data.selectedImage.url,
    h: $data.watermarkArea
  }, $data.watermarkArea ? {
    i: common_vendor.s($options.getWatermarkOverlayStyle())
  } : {}, {
    j: common_vendor.o((...args) => $options.handlePreviewImageTap && $options.handlePreviewImageTap(...args))
  }), {
    k: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    l: $data.selectedImage
  }, $data.selectedImage ? {
    m: common_vendor.f($data.processingModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: mode.gradient,
        c: common_vendor.t(mode.name),
        d: common_vendor.t(mode.desc),
        e: mode.id,
        f: $data.selectedMode === mode.id ? 1 : "",
        g: common_vendor.o(($event) => $options.selectMode(mode.id), mode.id)
      };
    })
  } : {}, {
    n: $data.selectedImage && $data.selectedMode
  }, $data.selectedImage && $data.selectedMode ? {
    o: common_vendor.t($data.strengthLabels[Math.floor(($data.processingStrength - 10) / 18)]),
    p: $data.processingStrength,
    q: common_vendor.o((...args) => $options.onStrengthChange && $options.onStrengthChange(...args)),
    r: common_vendor.f($data.edgeOptions, (edge, k0, i0) => {
      return {
        a: common_vendor.t(edge.name),
        b: edge.id,
        c: $data.edgeMode === edge.id ? 1 : "",
        d: common_vendor.o(($event) => $options.selectEdge(edge.id), edge.id)
      };
    }),
    s: common_vendor.t($data.enableDenoising ? "✓" : ""),
    t: $data.enableDenoising ? 1 : "",
    v: common_vendor.o(($event) => $data.enableDenoising = !$data.enableDenoising),
    w: common_vendor.t($data.enableSharpening ? "✓" : ""),
    x: $data.enableSharpening ? 1 : "",
    y: common_vendor.o(($event) => $data.enableSharpening = !$data.enableSharpening)
  } : {}, {
    z: $data.selectedImage && $data.selectedMode
  }, $data.selectedImage && $data.selectedMode ? {
    A: common_vendor.t($data.isProcessing ? "处理中..." : "开始魔法去水印"),
    B: $data.isProcessing ? 1 : "",
    C: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    D: $data.isProcessing
  } : {}, {
    E: $data.isProcessing
  }, $data.isProcessing ? {
    F: common_vendor.t($data.progress),
    G: common_vendor.t($data.currentStep),
    H: $data.progress + "%",
    I: `linear-gradient(135deg, #3b82f6 0%, #2563eb ${$data.progress}%)`,
    J: common_vendor.t($data.estimatedTime)
  } : {}, {
    K: $data.processedImageUrl
  }, $data.processedImageUrl ? {
    L: common_vendor.t($data.detectedWatermarks),
    M: common_vendor.t($data.removedWatermarks),
    N: common_vendor.t($data.processingTime),
    O: common_vendor.t($data.qualityScore),
    P: $data.selectedImage.url,
    Q: $data.processedImageUrl,
    R: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args)),
    S: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args))
  } : {}, {
    T: $data.canvasWidth + "px",
    U: $data.canvasHeight + "px",
    V: common_vendor.o((...args) => $options.closeWatermarkSelector && $options.closeWatermarkSelector(...args)),
    W: common_vendor.o((...args) => $options.clearWatermarkArea && $options.clearWatermarkArea(...args)),
    X: common_vendor.o((...args) => $options.confirmWatermarkArea && $options.confirmWatermarkArea(...args)),
    Y: !$data.selectionBox.visible,
    Z: common_vendor.o((...args) => $options.closeWatermarkSelector && $options.closeWatermarkSelector(...args)),
    aa: common_vendor.t($data.selectedImage ? "存在" : "不存在"),
    ab: $data.selectedImage
  }, $data.selectedImage ? {
    ac: common_vendor.t($data.selectedImage.url || "空")
  } : {}, {
    ad: $data.selectedImage
  }, $data.selectedImage ? {
    ae: common_vendor.t($data.selectedImage.width),
    af: common_vendor.t($data.selectedImage.height)
  } : {}, {
    ag: $data.selectedImage && $data.selectedImage.url
  }, $data.selectedImage && $data.selectedImage.url ? {
    ah: $data.selectedImage.url,
    ai: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    aj: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args))
  } : $data.selectedImage && $data.selectedImage.url ? {
    al: `url(${$data.selectedImage.url})`
  } : {
    am: common_vendor.t($data.selectedImage ? "图片URL为空" : "没有选择图片")
  }, {
    ak: $data.selectedImage && $data.selectedImage.url,
    an: $data.selectionBox.visible
  }, $data.selectionBox.visible ? {
    ao: common_vendor.s($options.selectionBoxStyle)
  } : {}, {
    ap: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    aq: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    ar: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args)),
    as: common_vendor.o(() => {
    }),
    at: common_vendor.t($data.selectionBox.visible ? '已选择水印区域，点击"确认"完成选择' : "在图片上拖拽绘制矩形框来选择水印区域"),
    av: common_vendor.o(() => {
    }),
    aw: $data.showWatermarkSelector && $data.selectedImage && $data.pageReady
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c479fc88"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/magic-watermark-remover.js.map
