"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      results: {
        uppercase: "",
        lowercase: "",
        capitalize: "",
        camelCase: "",
        pascalCase: "",
        snakeCase: "",
        kebabCase: ""
      },
      conversions: [
        { key: "uppercase", name: "全大写 (UPPERCASE)", example: "HELLO WORLD" },
        { key: "lowercase", name: "全小写 (lowercase)", example: "hello world" },
        { key: "capitalize", name: "首字母大写 (Capitalize)", example: "Hello World" },
        { key: "camelCase", name: "驼峰命名 (camelCase)", example: "helloWorld" },
        { key: "pascalCase", name: "帕斯卡命名 (PascalCase)", example: "HelloWorld" },
        { key: "snakeCase", name: "蛇形命名 (snake_case)", example: "hello_world" },
        { key: "kebabCase", name: "短横线命名 (kebab-case)", example: "hello-world" }
      ]
    };
  },
  methods: {
    convertText() {
      const text = this.inputText;
      if (!text) {
        this.results = {
          uppercase: "",
          lowercase: "",
          capitalize: "",
          camelCase: "",
          pascalCase: "",
          snakeCase: "",
          kebabCase: ""
        };
        return;
      }
      const uppercase = text.toUpperCase();
      const lowercase = text.toLowerCase();
      const capitalize = text.replace(/\b\w/g, (l) => l.toUpperCase());
      const camelCase = text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
        return index === 0 ? word.toLowerCase() : word.toUpperCase();
      }).replace(/\s+/g, "");
      const pascalCase = text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase()).replace(/\s+/g, "");
      const snakeCase = text.replace(/\W+/g, " ").split(/ |\B(?=[A-Z])/).map((word) => word.toLowerCase()).join("_");
      const kebabCase = text.replace(/\W+/g, " ").split(/ |\B(?=[A-Z])/).map((word) => word.toLowerCase()).join("-");
      this.results = {
        uppercase,
        lowercase,
        capitalize,
        camelCase,
        pascalCase,
        snakeCase,
        kebabCase
      };
    },
    copyResult(text, type) {
      if (!text)
        return;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: `${type}已复制`,
            icon: "success",
            duration: 2e3
          });
          if (common_vendor.index.vibrateShort) {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.convertText && $options.convertText(...args)]),
    b: $data.inputText,
    c: common_vendor.f($data.conversions, (conversion, k0, i0) => {
      return {
        a: common_vendor.t(conversion.name),
        b: common_vendor.t($data.results[conversion.key] || conversion.example),
        c: !$data.results[conversion.key] ? 1 : "",
        d: common_vendor.o(($event) => $options.copyResult($data.results[conversion.key], conversion.name), conversion.key),
        e: conversion.key
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9ae54d75"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/case-converter.js.map
