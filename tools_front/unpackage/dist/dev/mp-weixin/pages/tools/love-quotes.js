"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentQuote: "",
      quotes: [
        "你是我今生最美的相遇，是我最深的思念。",
        "你是我心中最美的风景，无论走到哪里，都想与你一起欣赏。",
        "遇见你是我这辈子最幸运的事，从此以后，我的世界因你而精彩。",
        "愿我们的爱情像星空一样永恒，像海洋一样深沉。",
        "你的笑容是我见过最美的花朵，你的声音是我听过最动听的音乐。",
        "我想和你一起看日出日落，一起数星星，一起变老。",
        "在千万人中遇见你，在时间的荒野里，没有早一步，也没有晚一步。",
        "你的笑容是我见过最美的花朵，你的声音是我听过最动听的音乐。",
        "愿我们的爱情像星空一样永恒，像海洋一样深沉。",
        "遇见你是我这辈子最幸运的事，从此以后，我的世界因你而精彩。"
      ],
      activeIndex: -1
    };
  },
  mounted() {
    this.getRandomQuote();
  },
  methods: {
    getRandomQuote() {
      const randomIndex = Math.floor(Math.random() * this.quotes.length);
      this.currentQuote = this.quotes[randomIndex];
    },
    copyQuote() {
      this.copyText(this.currentQuote);
    },
    handleTouchStart(index) {
      this.activeIndex = index;
    },
    handleTouchEnd() {
      this.activeIndex = -1;
    },
    copyText(text, index) {
      this.currentQuote = text;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.t($data.currentQuote),
    b: common_vendor.o((...args) => $options.getRandomQuote && $options.getRandomQuote(...args)),
    c: common_vendor.o((...args) => $options.copyQuote && $options.copyQuote(...args)),
    d: common_vendor.f($data.quotes, (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: index,
        c: $data.activeIndex === index ? 1 : "",
        d: common_vendor.o(($event) => $options.handleTouchStart(index), index),
        e: common_vendor.o((...args) => $options.handleTouchEnd && $options.handleTouchEnd(...args), index),
        f: common_vendor.o(($event) => $options.copyText(quote, index), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-56231a0c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/love-quotes.js.map
