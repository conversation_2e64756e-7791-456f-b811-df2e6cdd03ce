"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      length: 12,
      includeUppercase: true,
      includeLowercase: true,
      includeNumbers: true,
      includeSymbols: true,
      excludeSimilar: false,
      passwords: [],
      batchCount: 5,
      savedPasswords: [],
      showSaveDialog: false,
      passwordPurpose: "",
      selectedPassword: ""
    };
  },
  onLoad() {
    try {
      const saved = common_vendor.index.getStorageSync("savedPasswords");
      if (saved) {
        this.savedPasswords = JSON.parse(saved);
      }
    } catch (e) {
      common_vendor.index.__f__("error", "at pages/tools/password-generator.vue:244", "加载密码本失败:", e);
    }
  },
  methods: {
    onLengthChange(e) {
      this.length = e.detail.value;
    },
    toggleUppercase() {
      this.includeUppercase = !this.includeUppercase;
    },
    toggleLowercase() {
      this.includeLowercase = !this.includeLowercase;
    },
    toggleNumbers() {
      this.includeNumbers = !this.includeNumbers;
    },
    toggleSymbols() {
      this.includeSymbols = !this.includeSymbols;
    },
    toggleExcludeSimilar() {
      this.excludeSimilar = !this.excludeSimilar;
    },
    generatePassword() {
      if (!this.includeUppercase && !this.includeLowercase && !this.includeNumbers && !this.includeSymbols) {
        common_vendor.index.showToast({
          title: "请至少选择一种字符类型",
          icon: "none"
        });
        return "";
      }
      let charset = "";
      if (this.includeUppercase)
        charset += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
      if (this.includeLowercase)
        charset += "abcdefghijklmnopqrstuvwxyz";
      if (this.includeNumbers)
        charset += "0123456789";
      if (this.includeSymbols)
        charset += "!@#$%^&*()_+-=[]{}|;:,.<>?";
      if (this.excludeSimilar) {
        charset = charset.replace(/[0O1lI]/g, "");
      }
      let password = "";
      for (let i = 0; i < this.length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      return password;
    },
    calculateStrength(password) {
      let score = 0;
      if (password.length >= 8)
        score += 1;
      if (password.length >= 12)
        score += 1;
      if (/[a-z]/.test(password))
        score += 1;
      if (/[A-Z]/.test(password))
        score += 1;
      if (/[0-9]/.test(password))
        score += 1;
      if (/[^A-Za-z0-9]/.test(password))
        score += 1;
      if (score <= 2)
        return { level: "弱", color: "#ef4444" };
      if (score <= 4)
        return { level: "中", color: "#f59e0b" };
      return { level: "强", color: "#10b981" };
    },
    copyPassword() {
      if (!this.generatedPassword) {
        common_vendor.index.showToast({
          title: "请先生成密码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: this.generatedPassword,
        success: () => {
          common_vendor.index.showToast({
            title: "密码已复制",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    generateMultiplePasswords() {
      const newPasswords = [];
      for (let i = 0; i < this.batchCount; i++) {
        const pwd = this.generatePassword();
        if (pwd)
          newPasswords.push(pwd);
      }
      this.passwords = newPasswords;
    },
    getPasswordStrength(password) {
      let score = 0;
      if (password.length >= 8)
        score += 1;
      if (password.length >= 12)
        score += 1;
      if (/[a-z]/.test(password))
        score += 1;
      if (/[A-Z]/.test(password))
        score += 1;
      if (/[0-9]/.test(password))
        score += 1;
      if (/[^A-Za-z0-9]/.test(password))
        score += 1;
      if (score < 3)
        return { level: "弱", color: "weak-strength", score };
      if (score < 5)
        return { level: "中", color: "medium-strength", score };
      return { level: "强", color: "strong-strength", score };
    },
    async copyToClipboard(text) {
      try {
        await common_vendor.index.setClipboardData({
          data: text
        });
        common_vendor.index.showToast({
          title: "密码已复制",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "error"
        });
      }
    },
    showSaveModal(password) {
      this.selectedPassword = password;
      this.passwordPurpose = "";
      this.showSaveDialog = true;
    },
    cancelSave() {
      this.showSaveDialog = false;
      this.passwordPurpose = "";
      this.selectedPassword = "";
    },
    confirmSave() {
      if (!this.passwordPurpose.trim()) {
        common_vendor.index.showToast({
          title: "请输入密码用途",
          icon: "none"
        });
        return;
      }
      const newPasswordEntry = {
        purpose: this.passwordPurpose,
        password: this.selectedPassword,
        isVisible: false,
        saveDate: (/* @__PURE__ */ new Date()).toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit"
        })
      };
      this.savedPasswords.unshift(newPasswordEntry);
      try {
        common_vendor.index.setStorageSync("savedPasswords", JSON.stringify(this.savedPasswords));
        common_vendor.index.showToast({
          title: "保存成功",
          icon: "success"
        });
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tools/password-generator.vue:424", "保存失败:", e);
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "error"
        });
      }
      this.showSaveDialog = false;
      this.passwordPurpose = "";
      this.selectedPassword = "";
    },
    deleteSavedPassword(index) {
      common_vendor.index.showModal({
        title: "确认删除",
        content: "确定要删除这个保存的密码吗？",
        success: (res) => {
          if (res.confirm) {
            this.savedPasswords.splice(index, 1);
            try {
              common_vendor.index.setStorageSync("savedPasswords", JSON.stringify(this.savedPasswords));
              common_vendor.index.showToast({
                title: "删除成功",
                icon: "success"
              });
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/tools/password-generator.vue:451", "删除失败:", e);
              common_vendor.index.showToast({
                title: "删除失败",
                icon: "error"
              });
            }
          }
        }
      });
    },
    togglePasswordVisibility(index) {
      this.$set(this.savedPasswords[index], "isVisible", !this.savedPasswords[index].isVisible);
      try {
        common_vendor.index.setStorageSync("savedPasswords", JSON.stringify(this.savedPasswords));
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tools/password-generator.vue:469", "更新密码可见性失败:", e);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.length),
    b: $data.length,
    c: common_vendor.o((...args) => $options.onLengthChange && $options.onLengthChange(...args)),
    d: $data.includeUppercase,
    e: common_vendor.o((...args) => $options.toggleUppercase && $options.toggleUppercase(...args)),
    f: $data.includeLowercase,
    g: common_vendor.o((...args) => $options.toggleLowercase && $options.toggleLowercase(...args)),
    h: $data.includeNumbers,
    i: common_vendor.o((...args) => $options.toggleNumbers && $options.toggleNumbers(...args)),
    j: $data.includeSymbols,
    k: common_vendor.o((...args) => $options.toggleSymbols && $options.toggleSymbols(...args)),
    l: $data.excludeSimilar,
    m: common_vendor.o((...args) => $options.toggleExcludeSimilar && $options.toggleExcludeSimilar(...args)),
    n: $data.batchCount,
    o: common_vendor.o(common_vendor.m(($event) => $data.batchCount = $event.detail.value, {
      number: true
    })),
    p: common_vendor.o((...args) => $options.generateMultiplePasswords && $options.generateMultiplePasswords(...args)),
    q: $data.passwords.length > 0
  }, $data.passwords.length > 0 ? {
    r: common_vendor.f($data.passwords, (password, index, i0) => {
      return {
        a: common_vendor.t(password),
        b: common_vendor.o(($event) => $options.showSaveModal(password), index),
        c: common_vendor.o(($event) => $options.copyToClipboard(password), index),
        d: common_vendor.t($options.getPasswordStrength(password).level),
        e: common_vendor.n($options.getPasswordStrength(password).color),
        f: common_vendor.t(password.length),
        g: index
      };
    })
  } : {}, {
    s: $data.savedPasswords.length > 0
  }, $data.savedPasswords.length > 0 ? {
    t: common_vendor.t($data.savedPasswords.length),
    v: common_vendor.f($data.savedPasswords, (item, index, i0) => {
      return {
        a: common_vendor.t(item.purpose),
        b: common_vendor.o(($event) => $options.copyToClipboard(item.password), index),
        c: common_vendor.o(($event) => $options.deleteSavedPassword(index), index),
        d: common_vendor.t(item.isVisible ? item.password : "••••••••••••"),
        e: common_vendor.t(item.isVisible ? "👁️" : "👁️‍🗨️"),
        f: common_vendor.o(($event) => $options.togglePasswordVisibility(index), index),
        g: common_vendor.t(item.saveDate),
        h: index
      };
    })
  } : {}, {
    w: $data.showSaveDialog
  }, $data.showSaveDialog ? {
    x: $data.passwordPurpose,
    y: common_vendor.o(($event) => $data.passwordPurpose = $event.detail.value),
    z: common_vendor.t($data.selectedPassword),
    A: common_vendor.o((...args) => $options.cancelSave && $options.cancelSave(...args)),
    B: common_vendor.o((...args) => $options.confirmSave && $options.confirmSave(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-94dcd830"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/password-generator.js.map
