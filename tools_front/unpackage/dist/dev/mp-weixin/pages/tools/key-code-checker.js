"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "Key<PERSON>ode<PERSON>he<PERSON>",
  components: {
    Keyboard: common_vendor.Keyboard
  },
  data() {
    return {
      commonKeys: [
        { key: "Enter", keyCode: 13, code: "Enter" },
        { key: "Space", keyCode: 32, code: "Space" },
        { key: "Escape", keyCode: 27, code: "Escape" },
        { key: "Tab", keyCode: 9, code: "Tab" },
        { key: "Backspace", keyCode: 8, code: "Backspace" },
        { key: "Delete", keyCode: 46, code: "Delete" },
        { key: "ArrowUp", keyCode: 38, code: "ArrowUp" },
        { key: "ArrowDown", keyCode: 40, code: "ArrowDown" },
        { key: "ArrowLeft", keyCode: 37, code: "ArrowLeft" },
        { key: "ArrowRight", keyCode: 39, code: "ArrowRight" },
        { key: "Shift", keyCode: 16, code: "ShiftLeft" },
        { key: "Control", keyCode: 17, code: "ControlLeft" },
        { key: "Alt", keyCode: 18, code: "AltLeft" }
      ]
    };
  },
  computed: {
    numberKeys() {
      return Array.from({ length: 10 }, (_, i) => ({
        key: i.toString(),
        keyCode: 48 + i,
        code: `Digit${i}`
      }));
    },
    letterKeys() {
      return Array.from({ length: 26 }, (_, i) => ({
        key: String.fromCharCode(65 + i),
        keyCode: 65 + i,
        code: `Key${String.fromCharCode(65 + i)}`
      }));
    }
  }
};
if (!Array) {
  const _component_Keyboard = common_vendor.resolveComponent("Keyboard");
  _component_Keyboard();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.commonKeys, (key, k0, i0) => {
      return {
        a: common_vendor.t(key.keyCode),
        b: common_vendor.t(key.key),
        c: key.keyCode
      };
    }),
    b: common_vendor.f($options.numberKeys, (key, k0, i0) => {
      return {
        a: common_vendor.t(key.keyCode),
        b: common_vendor.t(key.key),
        c: key.keyCode
      };
    }),
    c: common_vendor.f($options.letterKeys, (key, k0, i0) => {
      return {
        a: common_vendor.t(key.keyCode),
        b: common_vendor.t(key.key),
        c: key.keyCode
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0ecd2679"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/key-code-checker.js.map
