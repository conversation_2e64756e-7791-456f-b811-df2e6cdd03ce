/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-061ecc7a {
  display: flex;
}
.flex-1.data-v-061ecc7a {
  flex: 1;
}
.items-center.data-v-061ecc7a {
  align-items: center;
}
.justify-center.data-v-061ecc7a {
  justify-content: center;
}
.justify-between.data-v-061ecc7a {
  justify-content: space-between;
}
.text-center.data-v-061ecc7a {
  text-align: center;
}
.rounded.data-v-061ecc7a {
  border-radius: 3px;
}
.rounded-lg.data-v-061ecc7a {
  border-radius: 6px;
}
.shadow.data-v-061ecc7a {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-061ecc7a {
  padding: 16rpx;
}
.m-4.data-v-061ecc7a {
  margin: 16rpx;
}
.mb-4.data-v-061ecc7a {
  margin-bottom: 16rpx;
}
.mt-4.data-v-061ecc7a {
  margin-top: 16rpx;
}
.text-nine-grid.data-v-061ecc7a {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-061ecc7a {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-061ecc7a {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-061ecc7a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-061ecc7a {
  font-size: 48rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}
.title-text.data-v-061ecc7a {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.subtitle.data-v-061ecc7a {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-061ecc7a, .grid-section.data-v-061ecc7a, .examples-section.data-v-061ecc7a, .help-section.data-v-061ecc7a {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-061ecc7a {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-061ecc7a {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-061ecc7a {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.input-wrapper.data-v-061ecc7a {
  position: relative;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
}
.text-input.data-v-061ecc7a {
  flex: 1;
  height: 80rpx;
  padding: 0 80rpx 0 24rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #374151;
  background: #f9fafb;
  text-align: left;
  transition: all 0.3s ease;
}
.text-input.data-v-061ecc7a::-webkit-input-placeholder {
  color: #9ca3af;
  text-align: center;
}
.text-input.data-v-061ecc7a::placeholder {
  color: #9ca3af;
  text-align: center;
}
.text-input.data-v-061ecc7a:focus {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 2rpx rgba(59, 130, 246, 0.1);
}
.input-counter.data-v-061ecc7a {
  position: absolute;
  right: 12rpx;
  top: 50%;
  transform: translateY(-50%);
  min-width: 60rpx;
  font-size: 24rpx;
  color: #94a3b8;
  background: #f9fafb;
  padding: 2rpx 8rpx;
  border-radius: 6rpx;
  text-align: center;
  pointer-events: none;
  z-index: 1;
}
.input-tip.data-v-061ecc7a {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
  display: block;
}
.grid-display.data-v-061ecc7a {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #e2e8f0;
}
.grid-text.data-v-061ecc7a {
  display: block;
  font-family: monospace;
  white-space: pre;
  font-size: 28rpx;
  color: #1f2937;
  text-align: center;
  margin-bottom: 24rpx;
}
.grid-actions.data-v-061ecc7a {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-061ecc7a {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 16rpx;
  font-weight: 600;
}
.action-btn.primary.data-v-061ecc7a {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.action-btn.secondary.data-v-061ecc7a {
  background: white;
  color: #3b82f6;
  border: 1rpx solid #3b82f6;
}
.btn-icon.data-v-061ecc7a {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-061ecc7a {
  font-size: 28rpx;
}
.examples-grid.data-v-061ecc7a {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.example-item.data-v-061ecc7a {
  padding: 20rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 16rpx;
  text-align: center;
}
.example-item.data-v-061ecc7a:active {
  background: #f1f5f9;
}
.example-text.data-v-061ecc7a {
  font-size: 28rpx;
  color: #1f2937;
}
.help-content.data-v-061ecc7a {
  background: #f8fafc;
  border-radius: 16rpx;
  padding: 24rpx;
}
.help-item.data-v-061ecc7a {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-061ecc7a:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 375px) {
.container.data-v-061ecc7a {
    padding: 30rpx 20rpx;
}
.grid-actions.data-v-061ecc7a {
    flex-direction: column;
}
.action-btn.data-v-061ecc7a {
    width: 100%;
}
}