"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  __name: "beast-language",
  setup(__props) {
    const inputText = common_vendor.ref("");
    const outputText = common_vendor.ref("");
    const isEncrypt = common_vendor.ref(true);
    const history = common_vendor.ref([]);
    const toolService = new utils_toolService.ToolService();
    const audioContext = common_vendor.ref(null);
    const isPlaying = common_vendor.ref(false);
    const audioQueue = common_vendor.ref([]);
    const currentAudioIndex = common_vendor.ref(0);
    const volume = common_vendor.ref(0.7);
    const isLoading = common_vendor.ref(false);
    const preloadedAudio = common_vendor.ref({});
    const audioResources = {
      wang: {
        short: "/static/audio/wang/short.mp3",
        medium: "/static/audio/wang/medium.mp3",
        long: "/static/audio/wang/long.mp3"
      },
      miao: {
        short: "/static/audio/miao/short.mp3",
        medium: "/static/audio/miao/medium.mp3",
        long: "/static/audio/miao/long.mp3"
      },
      moo: {
        short: "/static/audio/moo/short.mp3",
        medium: "/static/audio/moo/medium.mp3",
        long: "/static/audio/moo/long.mp3"
      },
      zhu: {
        short: "/static/audio/zhu/short.mp3",
        medium: "/static/audio/zhu/medium.mp3",
        long: "/static/audio/zhu/long.mp3"
      }
    };
    const audioLoadStatus = common_vendor.ref({
      total: 0,
      loaded: 0,
      failed: 0
    });
    const beastModes = [
      {
        id: "wang",
        name: "汪汪语",
        chars: ["汪", "汪汪", "汪汪汪"],
        separator: "~",
        description: "小狗语言"
      },
      {
        id: "miao",
        name: "喵喵语",
        chars: ["喵", "喵喵", "喵喵喵"],
        separator: "~",
        description: "小猫语言"
      },
      {
        id: "moo",
        name: "哞哞语",
        chars: ["哞", "哞哞", "哞哞哞"],
        separator: "~",
        description: "奶牛语言"
      },
      {
        id: "zhu",
        name: "哼哼语",
        chars: ["哼", "哼哼", "哼哼哼"],
        separator: "~",
        description: "小猪语言"
      }
    ];
    const selectedMode = common_vendor.ref(beastModes[0]);
    const convertToBeast = async (text) => {
      if (!text)
        return "";
      try {
        const params = {
          text,
          mode: selectedMode.value.id,
          action: "encrypt"
        };
        const result = await toolService.convertBeastLanguage(params);
        if (result.success) {
          return result.data.convertedText;
        } else {
          throw new Error(result.message || "加密失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:278", "兽语加密失败:", error);
        common_vendor.index.showToast({
          title: error.message || "加密失败",
          icon: "error",
          duration: 2e3
        });
        return convertToBeastLocal(text);
      }
    };
    const convertToBeastLocal = (text) => {
      if (!text)
        return "";
      const result = [];
      const chars = text.split("");
      chars.forEach((char) => {
        const charCode = char.charCodeAt(0);
        const index = charCode % selectedMode.value.chars.length;
        result.push(selectedMode.value.chars[index]);
      });
      return result.join(selectedMode.value.separator);
    };
    const convertFromBeast = async (text) => {
      if (!text)
        return "";
      try {
        const params = {
          text,
          mode: selectedMode.value.id,
          action: "decrypt"
        };
        const result = await toolService.convertBeastLanguage(params);
        if (result.success) {
          return result.data.convertedText;
        } else {
          throw new Error(result.message || "解密失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:324", "兽语解密失败:", error);
        common_vendor.index.showToast({
          title: error.message || "解密失败",
          icon: "error",
          duration: 2e3
        });
        return convertFromBeastLocal(text);
      }
    };
    const convertFromBeastLocal = (text) => {
      if (!text)
        return "";
      try {
        const parts = text.split(selectedMode.value.separator);
        const result = [];
        parts.forEach((part) => {
          const index = selectedMode.value.chars.indexOf(part);
          if (index !== -1) {
            const charCode = 65 + index;
            result.push(String.fromCharCode(charCode));
          } else {
            result.push("?");
          }
        });
        return result.join("");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:356", "本地解密失败:", error);
        return "解密失败";
      }
    };
    const convert = async () => {
      if (!inputText.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入要转换的文本",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "转换中..."
      });
      try {
        let result;
        if (isEncrypt.value) {
          result = await convertToBeast(inputText.value);
        } else {
          result = await convertFromBeast(inputText.value);
        }
        outputText.value = result;
        const historyItem = {
          id: Date.now(),
          input: inputText.value,
          output: result,
          mode: selectedMode.value.name,
          isEncrypt: isEncrypt.value,
          timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
        };
        history.value.unshift(historyItem);
        if (history.value.length > 10) {
          history.value = history.value.slice(0, 10);
        }
        saveHistory();
        common_vendor.index.showToast({
          title: "转换成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:410", "转换失败:", error);
        common_vendor.index.showToast({
          title: "转换失败",
          icon: "error"
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const generateAudioQueue = (text) => {
      const chars = text.split("");
      const queue = [];
      for (const char of chars) {
        if (char === " " || char === "\n" || /[，。！？、；：""''（）【】《》]/.test(char)) {
          queue.push({ type: "pause", duration: 300 });
          continue;
        }
        const position = queue.length % 3;
        const audioType = position === 0 ? "short" : position === 1 ? "medium" : "long";
        queue.push({
          type: "audio",
          src: audioResources[selectedMode.value.id][audioType]
        });
      }
      return queue;
    };
    const startAudio = () => {
      if (!inputText.value.trim())
        return;
      common_vendor.index.showLoading({
        title: "准备播放..."
      });
      audioQueue.value = generateAudioQueue(inputText.value);
      currentAudioIndex.value = 0;
      isPlaying.value = true;
      const firstAudio = audioQueue.value[0];
      if (firstAudio && firstAudio.type === "audio") {
        audioContext.value.src = firstAudio.src;
        audioContext.value.onCanplay(() => {
          common_vendor.index.hideLoading();
          audioContext.value.play();
          currentAudioIndex.value++;
        });
      } else {
        common_vendor.index.hideLoading();
        playNextAudio();
      }
    };
    const stopAudio = () => {
      if (audioContext.value) {
        audioContext.value.stop();
      }
      isPlaying.value = false;
      currentAudioIndex.value = 0;
      common_vendor.index.hideLoading();
    };
    const playNextAudio = () => {
      var _a;
      if (!isPlaying.value || currentAudioIndex.value >= audioQueue.value.length) {
        stopAudio();
        return;
      }
      const current = audioQueue.value[currentAudioIndex.value];
      if (current.type === "pause") {
        setTimeout(() => {
          currentAudioIndex.value++;
          playNextAudio();
        }, current.duration);
      } else {
        const audio = (_a = preloadedAudio.value[selectedMode.value.id]) == null ? void 0 : _a[current.type];
        if (!audio) {
          common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:532", "音频未加载:", current);
          currentAudioIndex.value++;
          playNextAudio();
          return;
        }
        try {
          audio.volume = volume.value;
          audio.play();
          currentAudioIndex.value++;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:543", "音频播放失败:", error);
          currentAudioIndex.value++;
          playNextAudio();
        }
      }
    };
    const handleConvert = () => {
      if (!inputText.value.trim()) {
        common_vendor.index.showToast({
          title: "请输入要转换的文字",
          icon: "none"
        });
        return;
      }
      if (isPlaying.value) {
        stopAudio();
      } else {
        startAudio();
      }
    };
    const handleCopy = () => {
      if (!outputText.value)
        return;
      common_vendor.index.setClipboardData({
        data: outputText.value,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success"
          });
        }
      });
    };
    const handleClear = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空当前输入和输出吗？",
        success: (res) => {
          if (res.confirm) {
            inputText.value = "";
            outputText.value = "";
            common_vendor.index.showToast({
              title: "已清空",
              icon: "success"
            });
          }
        }
      });
    };
    const saveHistory = () => {
      try {
        common_vendor.index.setStorageSync("beastLanguageHistory", JSON.stringify(history.value));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:603", "保存历史记录失败:", error);
      }
    };
    const loadHistory = () => {
      try {
        const savedHistory = common_vendor.index.getStorageSync("beastLanguageHistory");
        if (savedHistory) {
          history.value = JSON.parse(savedHistory);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/beast-language.vue:615", "加载历史记录失败:", error);
      }
    };
    const loadFromHistory = (record) => {
      const mode = beastModes.find((m) => m.name === record.mode);
      if (mode) {
        selectedMode.value = mode;
      }
      isEncrypt.value = record.isEncrypt;
      inputText.value = record.input;
      outputText.value = record.output;
      common_vendor.index.showToast({
        title: "已加载历史记录",
        icon: "success"
      });
    };
    const clearHistory = () => {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空所有历史记录吗？此操作不可恢复。",
        success: (res) => {
          if (res.confirm) {
            history.value = [];
            common_vendor.index.removeStorageSync("beastLanguageHistory");
            common_vendor.index.showToast({
              title: "历史记录已清空",
              icon: "success"
            });
          }
        }
      });
    };
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleString("zh-CN", {
        month: "numeric",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    };
    const setSelectedMode = (mode) => {
      selectedMode.value = mode;
      if (inputText.value && outputText.value) {
        convert();
      }
    };
    const setIsEncrypt = (value) => {
      isEncrypt.value = value;
      outputText.value = "";
      inputText.value = "";
    };
    const handleVolumeChange = (value) => {
      volume.value = value;
      if (audioContext.value) {
        audioContext.value.volume = value;
      }
    };
    common_vendor.onUnmounted(() => {
      if (audioContext.value) {
        audioContext.value.destroy();
      }
      Object.values(preloadedAudio.value).forEach((modeAudios) => {
        Object.values(modeAudios).forEach((audio) => {
          if (audio) {
            audio.destroy();
          }
        });
      });
    });
    common_vendor.onMounted(() => {
      loadHistory();
      preloadAudios();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(beastModes, (mode, k0, i0) => {
          return {
            a: common_vendor.t(mode.name),
            b: common_vendor.t(mode.chars.join(" ")),
            c: mode.id,
            d: selectedMode.value.id === mode.id ? 1 : "",
            e: common_vendor.o(($event) => setSelectedMode(mode), mode.id)
          };
        }),
        b: isEncrypt.value ? 1 : "",
        c: common_vendor.o(($event) => setIsEncrypt(true)),
        d: !isEncrypt.value ? 1 : "",
        e: common_vendor.o(($event) => setIsEncrypt(false)),
        f: common_vendor.t(isEncrypt.value ? "输入要加密的文字" : "输入要解密的兽语"),
        g: isEncrypt.value ? "请输入要转换为兽语的文字..." : "请输入要解密的兽语...",
        h: inputText.value,
        i: common_vendor.o(($event) => inputText.value = $event.detail.value),
        j: common_vendor.t(inputText.value.length),
        k: common_vendor.t(volume.value > 0 ? "🔊" : "🔇"),
        l: volume.value * 100,
        m: common_vendor.o((e) => handleVolumeChange(e.detail.value / 100)),
        n: common_vendor.t(isPlaying.value ? "⏸️" : isLoading.value ? "⌛" : "▶️"),
        o: isPlaying.value ? 1 : "",
        p: common_vendor.t(isPlaying.value ? "暂停播放" : isLoading.value ? "加载中..." : "播放兽语"),
        q: isPlaying.value
      }, isPlaying.value ? {} : {}, {
        r: isPlaying.value ? 1 : "",
        s: common_vendor.o(handleConvert),
        t: !inputText.value.trim() || isLoading.value,
        v: common_vendor.o(handleClear),
        w: outputText.value
      }, outputText.value ? {
        x: common_vendor.o(handleCopy),
        y: common_vendor.o((...args) => _ctx.saveToHistory && _ctx.saveToHistory(...args)),
        z: common_vendor.t(outputText.value)
      } : {}, {
        A: history.value.length > 0
      }, history.value.length > 0 ? {
        B: common_vendor.o(clearHistory),
        C: common_vendor.f(history.value, (record, index, i0) => {
          return {
            a: common_vendor.t(record.mode),
            b: common_vendor.t(record.isEncrypt ? "加密" : "解密"),
            c: common_vendor.t(formatTime(record.timestamp)),
            d: common_vendor.t(record.input.substring(0, 50)),
            e: common_vendor.t(record.input.length > 50 ? "..." : ""),
            f: common_vendor.t(record.output.substring(0, 50)),
            g: common_vendor.t(record.output.length > 50 ? "..." : ""),
            h: common_vendor.o(($event) => loadFromHistory(record), index),
            i: index
          };
        })
      } : {}, {
        D: isLoading.value
      }, isLoading.value ? {
        E: common_vendor.t(audioLoadStatus.value.loaded),
        F: common_vendor.t(audioLoadStatus.value.total)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6c2f8856"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/beast-language.js.map
