/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-0b025161 {
  display: flex;
}
.flex-1.data-v-0b025161 {
  flex: 1;
}
.items-center.data-v-0b025161 {
  align-items: center;
}
.justify-center.data-v-0b025161 {
  justify-content: center;
}
.justify-between.data-v-0b025161 {
  justify-content: space-between;
}
.text-center.data-v-0b025161 {
  text-align: center;
}
.rounded.data-v-0b025161 {
  border-radius: 3px;
}
.rounded-lg.data-v-0b025161 {
  border-radius: 6px;
}
.shadow.data-v-0b025161 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-0b025161 {
  padding: 16rpx;
}
.m-4.data-v-0b025161 {
  margin: 16rpx;
}
.mb-4.data-v-0b025161 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-0b025161 {
  margin-top: 16rpx;
}
.time-calculator.data-v-0b025161 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.tips-card.data-v-0b025161, .history-card.data-v-0b025161, .result-card.data-v-0b025161, .time-card.data-v-0b025161, .preset-card.data-v-0b025161, .header-card.data-v-0b025161 {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.tips-card.data-v-0b025161:hover, .history-card.data-v-0b025161:hover, .result-card.data-v-0b025161:hover, .time-card.data-v-0b025161:hover, .preset-card.data-v-0b025161:hover, .header-card.data-v-0b025161:hover {
  box-shadow: 0 12rpx 32rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.card-header.data-v-0b025161 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1rpx solid #e8ecff;
}
.card-header .header-icon.data-v-0b025161 {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.card-header .header-title.data-v-0b025161 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.header-card.data-v-0b025161 {
  margin-bottom: 40rpx;
}
.header-card .header-content.data-v-0b025161 {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
.header-card .header-content .header-icon.data-v-0b025161 {
  font-size: 64rpx;
  margin-right: 30rpx;
  animation: pulse-0b025161 2s infinite;
}
.header-card .header-content .header-info.data-v-0b025161 {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-0b025161 {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
  color: white;
}
.header-card .header-content .header-info .header-subtitle.data-v-0b025161 {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
  color: white;
}
.preset-card .preset-grid.data-v-0b025161 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 30rpx;
}
.preset-card .preset-item.data-v-0b025161 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2rpx solid #e8ecff;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.preset-card .preset-item.data-v-0b025161:active {
  transform: scale(0.98);
}
.preset-card .preset-item.data-v-0b025161:hover {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.15);
}
.preset-card .preset-item .preset-icon.data-v-0b025161 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.preset-card .preset-item .preset-title.data-v-0b025161 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.preset-card .preset-item .preset-desc.data-v-0b025161 {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}
.time-card.start-card.data-v-0b025161 {
  border-left: 6rpx solid #10b981;
}
.time-card.start-card .start-dot.data-v-0b025161 {
  color: #10b981;
  animation: pulse-0b025161 2s infinite;
}
.time-card.end-card.data-v-0b025161 {
  border-left: 6rpx solid #ef4444;
}
.time-card.end-card .end-dot.data-v-0b025161 {
  color: #ef4444;
  animation: pulse-0b025161 2s infinite;
}
.time-card .status-dot.data-v-0b025161 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.time-card .time-inputs.data-v-0b025161 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  padding: 30rpx;
}
.time-card .input-group .input-label.data-v-0b025161 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.time-card .input-group .picker-display.data-v-0b025161 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 20rpx;
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-height: 88rpx;
}
.time-card .input-group .picker-display.data-v-0b025161:hover {
  border-color: #6366f1;
  background: #f0f4ff;
}
.time-card .input-group .picker-display .picker-value.data-v-0b025161 {
  font-size: 30rpx;
  color: #1f2937;
  font-weight: 500;
}
.time-card .input-group .picker-display .picker-arrow.data-v-0b025161 {
  font-size: 24rpx;
  color: #6b7280;
}
.time-card .quick-actions.data-v-0b025161 {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx 30rpx;
}
.time-card .quick-actions .quick-btn.data-v-0b025161 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx;
  background: #f3f4f6;
  border: none;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.time-card .quick-actions .quick-btn.data-v-0b025161:hover {
  background: #e5e7eb;
}
.time-card .quick-actions .quick-btn.data-v-0b025161:active {
  transform: scale(0.98);
}
.time-card .quick-actions .quick-btn .btn-icon.data-v-0b025161 {
  font-size: 24rpx;
}
.time-card .quick-actions .quick-btn .btn-text.data-v-0b025161 {
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
}
.action-buttons.data-v-0b025161 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.action-buttons .calc-btn.data-v-0b025161 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 20rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-buttons .calc-btn.data-v-0b025161:hover {
  box-shadow: 0 12rpx 32rpx rgba(99, 102, 241, 0.4);
  transform: translateY(-2rpx);
}
.action-buttons .calc-btn.data-v-0b025161:active {
  transform: scale(0.98);
}
.action-buttons .calc-btn.calc-btn-disabled.data-v-0b025161 {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.action-buttons .calc-btn.calc-btn-disabled.data-v-0b025161:hover {
  transform: none;
  box-shadow: none;
}
.action-buttons .calc-btn .btn-icon.data-v-0b025161 {
  font-size: 32rpx;
}
.action-buttons .calc-btn .btn-text.data-v-0b025161 {
  font-size: 32rpx;
  font-weight: 600;
}
.action-buttons .clear-btn.data-v-0b025161 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 32rpx 28rpx;
  background: #ffffff;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.action-buttons .clear-btn.data-v-0b025161:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}
.action-buttons .clear-btn.data-v-0b025161:active {
  transform: scale(0.98);
}
.action-buttons .clear-btn .btn-icon.data-v-0b025161 {
  font-size: 28rpx;
}
.action-buttons .clear-btn .btn-text.data-v-0b025161 {
  font-size: 28rpx;
  font-weight: 500;
}
.result-card.data-v-0b025161 {
  border: 2rpx solid #e0e7ff;
}
.result-card .save-btn.data-v-0b025161 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.result-card .save-btn.data-v-0b025161:hover {
  background: #5856eb;
}
.result-card .save-btn.data-v-0b025161:active {
  transform: scale(0.98);
}
.result-card .primary-result.data-v-0b025161 {
  padding: 30rpx;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-radius: 20rpx;
  margin: 30rpx;
  border: 1rpx solid #93c5fd;
}
.result-card .primary-result .result-header.data-v-0b025161 {
  margin-bottom: 20rpx;
}
.result-card .primary-result .result-header .result-title.data-v-0b025161 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e40af;
}
.result-card .primary-result .result-main.data-v-0b025161 {
  margin-bottom: 16rpx;
}
.result-card .primary-result .result-main .result-text.data-v-0b025161 {
  font-size: 36rpx;
  font-weight: 700;
  color: #1e3a8a;
  line-height: 1.4;
}
.result-card .primary-result .result-main .result-text .time-unit.data-v-0b025161 {
  margin-right: 8rpx;
}
.result-card .primary-result .result-summary text.data-v-0b025161 {
  font-size: 26rpx;
  color: #3730a3;
  opacity: 0.8;
}
.result-card .units-grid.data-v-0b025161 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}
.result-card .units-grid .unit-item.data-v-0b025161 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #ffffff;
  border: 2rpx solid #f3f4f6;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.result-card .units-grid .unit-item.data-v-0b025161:hover {
  border-color: #6366f1;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.1);
}
.result-card .units-grid .unit-item .unit-number.data-v-0b025161 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}
.result-card .units-grid .unit-item .unit-label.data-v-0b025161 {
  font-size: 24rpx;
  color: #6b7280;
}
.result-card .comparison-section.data-v-0b025161 {
  margin: 30rpx;
  padding: 30rpx;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  border-radius: 20rpx;
  border: 1rpx solid #6ee7b7;
}
.result-card .comparison-section .comparison-header.data-v-0b025161 {
  margin-bottom: 20rpx;
}
.result-card .comparison-section .comparison-header .comparison-title.data-v-0b025161 {
  font-size: 32rpx;
  font-weight: 600;
  color: #065f46;
}
.result-card .comparison-section .comparison-list .comparison-item.data-v-0b025161 {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.result-card .comparison-section .comparison-list .comparison-item.data-v-0b025161:last-child {
  margin-bottom: 0;
}
.result-card .comparison-section .comparison-list .comparison-item .comparison-icon.data-v-0b025161 {
  font-size: 28rpx;
  margin-right: 16rpx;
}
.result-card .comparison-section .comparison-list .comparison-item .comparison-text.data-v-0b025161 {
  font-size: 26rpx;
  color: #047857;
  line-height: 1.4;
}
.result-card .result-actions.data-v-0b025161 {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
}
.result-card .result-actions .action-btn.data-v-0b025161 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 24rpx;
  background: #ffffff;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.result-card .result-actions .action-btn.data-v-0b025161:hover {
  border-color: #d1d5db;
  background: #f9fafb;
}
.result-card .result-actions .action-btn.data-v-0b025161:active {
  transform: scale(0.98);
}
.result-card .result-actions .action-btn .btn-icon.data-v-0b025161 {
  font-size: 24rpx;
}
.result-card .result-actions .action-btn .btn-text.data-v-0b025161 {
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
}
.history-card .clear-history-btn.data-v-0b025161 {
  padding: 8rpx 16rpx;
  background: #fef2f2;
  color: #dc2626;
  border: 1rpx solid #fecaca;
  border-radius: 8rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}
.history-card .clear-history-btn.data-v-0b025161:hover {
  background: #fee2e2;
}
.history-card .history-scroll.data-v-0b025161 {
  max-height: 400rpx;
  padding: 30rpx;
}
.history-card .history-list .history-item.data-v-0b025161 {
  padding: 24rpx;
  background: #f9fafb;
  border: 1rpx solid #f3f4f6;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.history-card .history-list .history-item.data-v-0b025161:last-child {
  margin-bottom: 0;
}
.history-card .history-list .history-item.data-v-0b025161:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
}
.history-card .history-list .history-item.data-v-0b025161:active {
  transform: scale(0.98);
}
.history-card .history-list .history-item .history-content.data-v-0b025161 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.history-card .history-list .history-item .history-content .history-main.data-v-0b025161 {
  flex: 1;
}
.history-card .history-list .history-item .history-content .history-main .history-desc.data-v-0b025161 {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}
.history-card .history-list .history-item .history-content .history-main .history-result.data-v-0b025161 {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}
.history-card .history-list .history-item .history-content .history-time .time-text.data-v-0b025161 {
  font-size: 22rpx;
  color: #9ca3af;
}
.tips-card .tips-list.data-v-0b025161 {
  padding: 30rpx;
}
.tips-card .tips-list .tip-item.data-v-0b025161 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tips-card .tips-list .tip-item.data-v-0b025161:last-child {
  margin-bottom: 0;
}
.tips-card .tips-list .tip-item .tip-bullet.data-v-0b025161 {
  font-size: 28rpx;
  color: #6366f1;
  margin-right: 16rpx;
  margin-top: 4rpx;
  font-weight: bold;
}
.tips-card .tips-list .tip-item .tip-text.data-v-0b025161 {
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
  flex: 1;
}
@keyframes pulse-0b025161 {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.6;
}
}
@keyframes fadeIn-0b025161 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}
@media (max-width: 750rpx) {
.preset-grid.data-v-0b025161 {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.time-inputs.data-v-0b025161 {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.units-grid.data-v-0b025161 {
    grid-template-columns: repeat(2, 1fr);
    gap: 16rpx;
}
.action-buttons.data-v-0b025161 {
    flex-direction: column;
}
.action-buttons .clear-btn.data-v-0b025161 {
    padding: 24rpx;
}
}