/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-557a13b8 {
  display: flex;
}
.flex-1.data-v-557a13b8 {
  flex: 1;
}
.items-center.data-v-557a13b8 {
  align-items: center;
}
.justify-center.data-v-557a13b8 {
  justify-content: center;
}
.justify-between.data-v-557a13b8 {
  justify-content: space-between;
}
.text-center.data-v-557a13b8 {
  text-align: center;
}
.rounded.data-v-557a13b8 {
  border-radius: 3px;
}
.rounded-lg.data-v-557a13b8 {
  border-radius: 6px;
}
.shadow.data-v-557a13b8 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-557a13b8 {
  padding: 16rpx;
}
.m-4.data-v-557a13b8 {
  margin: 16rpx;
}
.mb-4.data-v-557a13b8 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-557a13b8 {
  margin-top: 16rpx;
}
.special-tail.data-v-557a13b8 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-557a13b8 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-557a13b8 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-557a13b8 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-557a13b8 {
  font-size: 48rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}
.title-text.data-v-557a13b8 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.subtitle.data-v-557a13b8 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-557a13b8, .type-section.data-v-557a13b8, .style-section.data-v-557a13b8, .result-section.data-v-557a13b8, .history-section.data-v-557a13b8, .help-section.data-v-557a13b8 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.input-section.data-v-557a13b8:hover, .type-section.data-v-557a13b8:hover, .style-section.data-v-557a13b8:hover, .result-section.data-v-557a13b8:hover, .history-section.data-v-557a13b8:hover, .help-section.data-v-557a13b8:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.section-header.data-v-557a13b8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-557a13b8 {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.section-title.data-v-557a13b8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-wrapper.data-v-557a13b8 {
  position: relative;
  margin-bottom: 16rpx;
}
.text-input.data-v-557a13b8 {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  padding-bottom: 40rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
}
.text-input.data-v-557a13b8:focus {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.15);
}
.input-counter.data-v-557a13b8 {
  position: absolute;
  right: 24rpx;
  bottom: 12rpx;
  font-size: 24rpx;
  color: #94a3b8;
}
.input-actions.data-v-557a13b8 {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}
.clear-btn.data-v-557a13b8 {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
  display: flex;
  align-items: center;
}
.clear-btn.data-v-557a13b8:hover {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);
}
.clear-btn.data-v-557a13b8:active {
  transform: translateY(0) scale(0.98);
}
.clear-icon.data-v-557a13b8 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.clear-text.data-v-557a13b8 {
  font-size: 24rpx;
}
.type-tabs.data-v-557a13b8 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.type-tab.data-v-557a13b8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
  overflow: hidden;
}
.type-tab.data-v-557a13b8::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(29, 78, 216, 0.1));
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 0;
}
.type-tab.active.data-v-557a13b8 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-color: #3b82f6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  transform: translateY(-2rpx);
}
.type-tab.active.data-v-557a13b8::before {
  opacity: 0.15;
}
.type-tab.data-v-557a13b8:hover:not(.active) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.type-tab.data-v-557a13b8:hover:not(.active)::before {
  opacity: 1;
}
.tab-icon.data-v-557a13b8 {
  font-size: 32rpx;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.tab-name.data-v-557a13b8 {
  font-size: 24rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}
.style-grid.data-v-557a13b8 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.style-item.data-v-557a13b8 {
  padding: 28rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
  overflow: hidden;
}
.style-item.data-v-557a13b8::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(29, 78, 216, 0.05));
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 0;
}
.style-item.data-v-557a13b8:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.style-item.data-v-557a13b8:hover::before {
  opacity: 1;
}
.tail-preview.data-v-557a13b8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
}
.preview-text.data-v-557a13b8 {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
  font-weight: 500;
}
.tail-symbol.data-v-557a13b8 {
  font-size: 32rpx;
  margin-left: 16rpx;
  color: #4f46e5;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.style-actions.data-v-557a13b8 {
  display: flex;
  justify-content: flex-end;
  position: relative;
  z-index: 1;
}
.apply-btn.data-v-557a13b8 {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 26rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.25);
}
.apply-btn.data-v-557a13b8:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(16, 185, 129, 0.35);
}
.apply-btn.data-v-557a13b8:active {
  transform: translateY(0) scale(0.98);
}
.btn-text.data-v-557a13b8 {
  font-size: 26rpx;
  letter-spacing: 0.5rpx;
}
.result-card.data-v-557a13b8 {
  background: linear-gradient(135deg, #ede9fe 0%, #ddd6fe 100%);
  border-radius: 20rpx;
  padding: 36rpx;
  border: 1rpx solid #c4b5fd;
  margin-bottom: 24rpx;
  box-shadow: 0 6rpx 16rpx rgba(124, 58, 237, 0.1);
  position: relative;
  overflow: hidden;
}
.result-card.data-v-557a13b8::before {
  content: "";
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.3), rgba(196, 181, 253, 0.1));
  border-radius: 50%;
  z-index: 0;
}
.result-text.data-v-557a13b8 {
  font-size: 32rpx;
  font-weight: 600;
  color: #5b21b6;
  margin-bottom: 32rpx;
  display: block;
  word-break: break-all;
  position: relative;
  z-index: 1;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.8);
}
.result-actions.data-v-557a13b8 {
  display: flex;
  gap: 20rpx;
  position: relative;
  z-index: 1;
}
.action-btn.data-v-557a13b8 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 50rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-557a13b8 {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.25);
}
.action-btn.primary.data-v-557a13b8:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(99, 102, 241, 0.35);
}
.action-btn.primary.data-v-557a13b8:active {
  transform: translateY(0) scale(0.98);
}
.action-btn.secondary.data-v-557a13b8 {
  background: rgba(255, 255, 255, 0.85);
  color: #6d28d9;
  border: 1.5rpx solid #c4b5fd;
}
.action-btn.secondary.data-v-557a13b8:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(124, 58, 237, 0.1);
}
.action-btn.secondary.data-v-557a13b8:active {
  transform: translateY(0) scale(0.98);
}
.btn-icon.data-v-557a13b8 {
  font-size: 28rpx;
  margin-right: 12rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.clear-history-btn.data-v-557a13b8 {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.2);
}
.clear-history-btn.data-v-557a13b8:hover {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.3);
}
.clear-history-btn.data-v-557a13b8:active {
  transform: translateY(0) scale(0.98);
}
.history-list.data-v-557a13b8 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.history-item.data-v-557a13b8 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.history-item.data-v-557a13b8:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.history-text.data-v-557a13b8 {
  font-size: 28rpx;
  color: #374151;
  flex: 1;
  word-break: break-all;
  font-weight: 500;
}
.history-actions.data-v-557a13b8 {
  display: flex;
  gap: 12rpx;
}
.mini-btn.data-v-557a13b8 {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  border: 1rpx solid #e2e8f0;
}
.mini-btn.data-v-557a13b8:hover {
  background: #e2e8f0;
  transform: translateY(-2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.mini-btn.data-v-557a13b8:active {
  transform: translateY(0) scale(0.95);
}
.mini-btn.remove.data-v-557a13b8 {
  background: #fee2e2;
  border-color: #fecaca;
}
.mini-btn.remove.data-v-557a13b8:hover {
  background: #fecaca;
}
.mini-icon.data-v-557a13b8 {
  font-size: 24rpx;
}
.help-content.data-v-557a13b8 {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-557a13b8 {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 28rpx;
}
.help-item.data-v-557a13b8:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #3b82f6;
  font-weight: bold;
}
.help-item.data-v-557a13b8:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-557a13b8 {
    padding: 30rpx 20rpx;
}
.type-tabs.data-v-557a13b8 {
    grid-template-columns: repeat(2, 1fr);
}
.result-actions.data-v-557a13b8 {
    flex-direction: column;
    gap: 12rpx;
}
.style-item.data-v-557a13b8 {
    padding: 20rpx;
}
.apply-btn.data-v-557a13b8 {
    padding: 12rpx 24rpx;
}
}