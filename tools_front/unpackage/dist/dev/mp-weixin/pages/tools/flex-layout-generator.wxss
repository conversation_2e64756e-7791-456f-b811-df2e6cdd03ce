/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-e3128b2a {
  display: flex;
}
.flex-1.data-v-e3128b2a {
  flex: 1;
}
.items-center.data-v-e3128b2a {
  align-items: center;
}
.justify-center.data-v-e3128b2a {
  justify-content: center;
}
.justify-between.data-v-e3128b2a {
  justify-content: space-between;
}
.text-center.data-v-e3128b2a {
  text-align: center;
}
.rounded.data-v-e3128b2a {
  border-radius: 3px;
}
.rounded-lg.data-v-e3128b2a {
  border-radius: 6px;
}
.shadow.data-v-e3128b2a {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-e3128b2a {
  padding: 16rpx;
}
.m-4.data-v-e3128b2a {
  margin: 16rpx;
}
.mb-4.data-v-e3128b2a {
  margin-bottom: 16rpx;
}
.mt-4.data-v-e3128b2a {
  margin-top: 16rpx;
}
.flex-generator-container.data-v-e3128b2a {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-e3128b2a, .basic-card.data-v-e3128b2a, .advanced-card.data-v-e3128b2a, .templates-card.data-v-e3128b2a, .preview-card.data-v-e3128b2a, .code-card.data-v-e3128b2a, .tips-card.data-v-e3128b2a {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-e3128b2a {
  display: flex;
  align-items: center;
}
.header-icon.data-v-e3128b2a {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-title.data-v-e3128b2a {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-e3128b2a {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-e3128b2a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-e3128b2a {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.basic-settings.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.setting-row.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.setting-label.data-v-e3128b2a {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.option-buttons.data-v-e3128b2a {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12rpx;
}
.option-grid.data-v-e3128b2a {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}
.option-btn.data-v-e3128b2a {
  padding: 20rpx 16rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.option-btn.small.data-v-e3128b2a {
  padding: 16rpx 12rpx;
}
.option-btn.active.data-v-e3128b2a {
  border-color: #007AFF;
  background: #007AFF;
}
.option-btn.active .btn-text.data-v-e3128b2a, .option-btn.active .btn-icon.data-v-e3128b2a {
  color: white;
}
.btn-icon.data-v-e3128b2a {
  font-size: 20rpx;
  margin-bottom: 4rpx;
  color: #666;
}
.btn-text.data-v-e3128b2a {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}
.advanced-settings.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.value-group.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.value-label.data-v-e3128b2a {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.template-grid.data-v-e3128b2a {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.template-item.data-v-e3128b2a {
  padding: 16rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.template-item.data-v-e3128b2a:active {
  transform: scale(0.98);
  border-color: #007AFF;
}
.template-preview.data-v-e3128b2a {
  display: flex;
  flex-wrap: wrap;
  gap: 4rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
  padding: 8rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}
.template-block.data-v-e3128b2a {
  height: 20rpx;
  border-radius: 4rpx;
  flex: 1;
  min-width: 30rpx;
}
.template-name.data-v-e3128b2a {
  font-size: 22rpx;
  color: #333;
}
.action-btn.data-v-e3128b2a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.preview-container.data-v-e3128b2a {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.flex-preview.data-v-e3128b2a {
  background: white;
}
.flex-item.data-v-e3128b2a {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 100rpx;
  min-height: 60rpx;
  transition: all 0.3s ease;
}
.flex-item.highlight.data-v-e3128b2a {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  transform: scale(1.05);
}
.flex-item.data-v-e3128b2a:active {
  transform: scale(0.95);
}
.item-text.data-v-e3128b2a {
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
}
.copy-btn.data-v-e3128b2a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.code-block.data-v-e3128b2a {
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx;
}
.code-text.data-v-e3128b2a {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 22rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
}
.tips-content.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-e3128b2a {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-e3128b2a {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-e3128b2a {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.preview-header.data-v-e3128b2a,
.code-header.data-v-e3128b2a {
  position: relative;
}
.right-btn.data-v-e3128b2a {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  display: flex;
  align-items: center;
  gap: 8rpx;
}