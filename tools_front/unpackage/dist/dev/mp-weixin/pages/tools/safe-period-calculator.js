"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      lastPeriodDate: "",
      cycleLength: "28",
      periodLength: "5",
      results: null
    };
  },
  computed: {
    currentStatus() {
      if (!this.results)
        return null;
      const today = /* @__PURE__ */ new Date();
      if (this.isInPeriod(today, this.results.dangerPeriod.start, this.results.dangerPeriod.end)) {
        return {
          status: "danger",
          text: "危险期",
          colorClass: "danger-color",
          bgClass: "danger-bg"
        };
      } else if (this.isInPeriod(today, this.results.safePeriod1.start, this.results.safePeriod1.end) || this.isInPeriod(today, this.results.safePeriod2.start, this.results.safePeriod2.end)) {
        return {
          status: "safe",
          text: "安全期",
          colorClass: "safe-color",
          bgClass: "safe-bg"
        };
      }
      return {
        status: "normal",
        text: "普通期",
        colorClass: "normal-color",
        bgClass: "normal-bg"
      };
    }
  },
  methods: {
    onDateChange(e) {
      this.lastPeriodDate = e.detail.value;
    },
    calculateSafePeriod() {
      if (!this.lastPeriodDate) {
        common_vendor.index.showToast({
          title: "请选择上次月经开始日期",
          icon: "none"
        });
        return;
      }
      const startDate = new Date(this.lastPeriodDate);
      const cycle = parseInt(this.cycleLength);
      const period = parseInt(this.periodLength);
      if (isNaN(cycle) || isNaN(period) || cycle < 21 || cycle > 35) {
        common_vendor.index.showToast({
          title: "请输入有效的周期长度（21-35天）",
          icon: "none"
        });
        return;
      }
      if (period < 3 || period > 8) {
        common_vendor.index.showToast({
          title: "请输入有效的经期长度（3-8天）",
          icon: "none"
        });
        return;
      }
      const nextPeriodDate = new Date(startDate);
      nextPeriodDate.setDate(startDate.getDate() + cycle);
      const ovulationDate = new Date(nextPeriodDate);
      ovulationDate.setDate(nextPeriodDate.getDate() - 14);
      const dangerStart = new Date(ovulationDate);
      dangerStart.setDate(ovulationDate.getDate() - 5);
      const dangerEnd = new Date(ovulationDate);
      dangerEnd.setDate(ovulationDate.getDate() + 4);
      const safePeriod1End = new Date(dangerStart);
      safePeriod1End.setDate(dangerStart.getDate() - 1);
      const safePeriod2Start = new Date(dangerEnd);
      safePeriod2Start.setDate(dangerEnd.getDate() + 1);
      this.results = {
        nextPeriod: nextPeriodDate,
        ovulation: ovulationDate,
        dangerPeriod: { start: dangerStart, end: dangerEnd },
        safePeriod1: { start: startDate, end: safePeriod1End },
        safePeriod2: { start: safePeriod2Start, end: nextPeriodDate }
      };
      common_vendor.index.showToast({
        title: "计算完成",
        icon: "success"
      });
    },
    formatDate(date) {
      const options = { month: "long", day: "numeric" };
      return date.toLocaleDateString("zh-CN", options);
    },
    getDaysFromNow(date) {
      const now = /* @__PURE__ */ new Date();
      const diffTime = date.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1e3 * 60 * 60 * 24));
      if (diffDays === 0)
        return "今天";
      if (diffDays === 1)
        return "明天";
      if (diffDays === -1)
        return "昨天";
      if (diffDays > 0)
        return `${diffDays}天后`;
      return `${Math.abs(diffDays)}天前`;
    },
    isInPeriod(date, start, end) {
      return date >= start && date <= end;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.lastPeriodDate || "请选择日期"),
    b: $data.lastPeriodDate,
    c: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    d: $data.cycleLength,
    e: common_vendor.o(($event) => $data.cycleLength = $event.detail.value),
    f: $data.periodLength,
    g: common_vendor.o(($event) => $data.periodLength = $event.detail.value),
    h: common_vendor.o((...args) => $options.calculateSafePeriod && $options.calculateSafePeriod(...args)),
    i: $data.results && $options.currentStatus
  }, $data.results && $options.currentStatus ? {
    j: common_vendor.t($options.currentStatus.text),
    k: common_vendor.n($options.currentStatus.colorClass),
    l: common_vendor.n($options.currentStatus.bgClass),
    m: common_vendor.t($options.getDaysFromNow($data.results.nextPeriod))
  } : {}, {
    n: $data.results
  }, $data.results ? {
    o: common_vendor.t($options.formatDate($data.results.nextPeriod)),
    p: common_vendor.t($options.getDaysFromNow($data.results.nextPeriod)),
    q: common_vendor.t($options.formatDate($data.results.ovulation)),
    r: common_vendor.t($options.getDaysFromNow($data.results.ovulation)),
    s: common_vendor.t($options.formatDate($data.results.dangerPeriod.start)),
    t: common_vendor.t($options.formatDate($data.results.dangerPeriod.end)),
    v: common_vendor.t($options.formatDate($data.results.safePeriod1.start)),
    w: common_vendor.t($options.formatDate($data.results.safePeriod1.end)),
    x: common_vendor.t($options.formatDate($data.results.safePeriod2.start)),
    y: common_vendor.t($options.formatDate($data.results.safePeriod2.end))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8343362b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/safe-period-calculator.js.map
