<view class="{{['data-v-9fc4fa69', 'fullscreen-clock', ag]}}" style="{{'background:' + ah}}"><view wx:if="{{a}}" class="digital-clock data-v-9fc4fa69"><view class="time-display data-v-9fc4fa69" style="{{'color:' + d}}"><view class="time-main data-v-9fc4fa69">{{b}}:{{c}}</view></view><view class="date-display data-v-9fc4fa69" style="{{'color:' + g}}"><view class="date-main data-v-9fc4fa69">{{e}}</view><view class="date-week data-v-9fc4fa69">{{f}}</view></view></view><view wx:if="{{h}}" class="analog-clock data-v-9fc4fa69"><view class="clock-face data-v-9fc4fa69" style="{{'border-color:' + t}}"><view wx:for="{{i}}" wx:for-item="i" wx:key="a" class="hour-mark data-v-9fc4fa69" style="{{'transform:' + i.b + ';' + ('background-color:' + j)}}"></view><view wx:for="{{k}}" wx:for-item="i" wx:key="a" class="minute-mark data-v-9fc4fa69" style="{{'transform:' + i.b + ';' + ('background-color:' + l)}}"></view><view class="hour-hand data-v-9fc4fa69" style="{{'transform:' + m + ';' + ('background-color:' + n)}}"></view><view class="minute-hand data-v-9fc4fa69" style="{{'transform:' + o + ';' + ('background-color:' + p)}}"></view><view class="second-hand data-v-9fc4fa69" style="{{'transform:' + q + ';' + ('background-color:' + r)}}"></view><view class="center-dot data-v-9fc4fa69" style="{{'background-color:' + s}}"></view></view><view class="time-info data-v-9fc4fa69"><view class="digital-time data-v-9fc4fa69" style="{{'color:' + y}}">{{v}}:{{w}}:{{x}}</view><view class="date-display data-v-9fc4fa69" style="{{'color:' + A}}">{{z}}</view></view></view><view wx:if="{{B}}" class="flip-clock data-v-9fc4fa69"><view class="flip-container data-v-9fc4fa69"><view class="flip-unit data-v-9fc4fa69"><view class="flip-card data-v-9fc4fa69"><view class="flip-number data-v-9fc4fa69" style="{{'color:' + D}}">{{C}}</view></view><view class="flip-label data-v-9fc4fa69" style="{{'color:' + E}}">时</view></view><view class="flip-separator data-v-9fc4fa69" style="{{'color:' + F}}">:</view><view class="flip-unit data-v-9fc4fa69"><view class="flip-card data-v-9fc4fa69"><view class="flip-number data-v-9fc4fa69" style="{{'color:' + H}}">{{G}}</view></view><view class="flip-label data-v-9fc4fa69" style="{{'color:' + I}}">分</view></view><view class="flip-separator data-v-9fc4fa69" style="{{'color:' + J}}">:</view><view class="flip-unit data-v-9fc4fa69"><view class="flip-card data-v-9fc4fa69"><view class="flip-number data-v-9fc4fa69" style="{{'color:' + L}}">{{K}}</view></view><view class="flip-label data-v-9fc4fa69" style="{{'color:' + M}}">秒</view></view></view></view><view class="{{['control-panel', 'data-v-9fc4fa69', T && 'hidden']}}"><view class="mode-switcher data-v-9fc4fa69"><button wx:for="{{N}}" wx:for-item="mode" wx:key="b" class="{{['data-v-9fc4fa69', 'mode-btn', mode.c]}}" bindtap="{{mode.d}}">{{mode.a}}</button></view><view class="theme-switcher data-v-9fc4fa69"><button wx:for="{{O}}" wx:for-item="theme" wx:key="a" class="{{['data-v-9fc4fa69', 'theme-btn', theme.b]}}" style="{{'background-color:' + theme.c}}" bindtap="{{theme.d}}"></button></view><view class="other-controls data-v-9fc4fa69"><button class="control-btn data-v-9fc4fa69" bindtap="{{Q}}">{{P}}</button><button class="control-btn data-v-9fc4fa69" bindtap="{{S}}">{{R}}</button></view></view><view class="control-toggle data-v-9fc4fa69" bindtap="{{V}}"><view class="toggle-icon data-v-9fc4fa69">{{U}}</view></view><view wx:if="{{W}}" class="time-picker-modal data-v-9fc4fa69" bindtap="{{af}}"><view class="picker-content data-v-9fc4fa69" catchtap="{{ae}}"><view class="picker-header data-v-9fc4fa69"><view class="picker-title data-v-9fc4fa69">设置闹钟</view><view class="close-btn data-v-9fc4fa69" bindtap="{{X}}">×</view></view><picker-view class="picker-view data-v-9fc4fa69" value="{{aa}}" bindchange="{{ab}}"><picker-view-column class="data-v-9fc4fa69"><view wx:for="{{Y}}" wx:for-item="hour" wx:key="b" class="picker-item data-v-9fc4fa69">{{hour.a}}</view></picker-view-column><picker-view-column class="data-v-9fc4fa69"><view wx:for="{{Z}}" wx:for-item="minute" wx:key="b" class="picker-item data-v-9fc4fa69">{{minute.a}}</view></picker-view-column></picker-view><view class="picker-actions data-v-9fc4fa69"><button class="picker-btn cancel data-v-9fc4fa69" bindtap="{{ac}}">取消</button><button class="picker-btn confirm data-v-9fc4fa69" bindtap="{{ad}}">确定</button></view></view></view></view>