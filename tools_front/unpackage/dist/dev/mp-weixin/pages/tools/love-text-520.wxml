<view class="love-text-520 data-v-4a3237a1"><view class="container data-v-4a3237a1"><view class="header-section data-v-4a3237a1"><view class="title-container data-v-4a3237a1"><text class="title-icon data-v-4a3237a1">💕</text><text class="title-text data-v-4a3237a1">爱心文字520</text></view><text class="subtitle data-v-4a3237a1">用爱心符号装饰文字，表达浪漫情感</text></view><view class="input-section data-v-4a3237a1"><view class="section-header data-v-4a3237a1"><text class="section-icon data-v-4a3237a1">✍️</text><text class="section-title data-v-4a3237a1">输入文字</text></view><view class="input-container data-v-4a3237a1"><block wx:if="{{r0}}"><textarea class="text-input data-v-4a3237a1" placeholder="请输入要装饰的文字..." maxlength="100" auto-height="{{true}}" show-confirm-bar="{{false}}" value="{{a}}" bindinput="{{b}}"/></block><view class="input-footer data-v-4a3237a1"><text class="char-count data-v-4a3237a1">{{c}}/100</text><view class="input-actions data-v-4a3237a1"><view class="action-btn generate data-v-4a3237a1" bindtap="{{d}}"><text class="btn-icon data-v-4a3237a1">💖</text><text class="btn-text data-v-4a3237a1">生成</text></view></view></view></view></view><view wx:if="{{e}}" class="results-section data-v-4a3237a1"><view class="section-header data-v-4a3237a1"><text class="section-icon data-v-4a3237a1">💝</text><text class="section-title data-v-4a3237a1">生成结果</text></view><view class="results-grid data-v-4a3237a1"><view wx:for="{{f}}" wx:for-item="result" wx:key="c" class="result-item data-v-4a3237a1" bindtap="{{result.d}}"><text class="result-text data-v-4a3237a1">{{result.a}}</text><view class="result-actions data-v-4a3237a1"><view class="mini-btn data-v-4a3237a1" catchtap="{{result.b}}"><text class="mini-icon data-v-4a3237a1">📋</text></view></view></view></view></view><view class="examples-section data-v-4a3237a1"><view class="section-header data-v-4a3237a1"><text class="section-icon data-v-4a3237a1">💡</text><text class="section-title data-v-4a3237a1">快速示例</text></view><view class="examples-grid data-v-4a3237a1"><view wx:for="{{g}}" wx:for-item="example" wx:key="b" class="example-item data-v-4a3237a1" bindtap="{{example.c}}"><text class="example-text data-v-4a3237a1">{{example.a}}</text></view></view></view><view class="styles-section data-v-4a3237a1"><view class="section-header data-v-4a3237a1"><text class="section-icon data-v-4a3237a1">🎨</text><text class="section-title data-v-4a3237a1">样式预览</text></view><view class="styles-grid data-v-4a3237a1"><view wx:for="{{h}}" wx:for-item="style" wx:key="c" class="style-item data-v-4a3237a1"><text class="style-text data-v-4a3237a1">{{style.a}}</text><text class="style-name data-v-4a3237a1">{{style.b}}</text></view></view></view><view class="help-section data-v-4a3237a1"><view class="section-header data-v-4a3237a1"><text class="section-icon data-v-4a3237a1">📖</text><text class="section-title data-v-4a3237a1">使用说明</text></view><view class="help-content data-v-4a3237a1"><text class="help-item data-v-4a3237a1">• 输入任意文字，点击生成按钮</text><text class="help-item data-v-4a3237a1">• 支持多种爱心装饰样式</text><text class="help-item data-v-4a3237a1">• 可以收藏和分享生成的文字</text><text class="help-item data-v-4a3237a1">• 自动保存历史记录</text><text class="help-item data-v-4a3237a1">• 适用于社交媒体和聊天应用</text></view></view></view></view>