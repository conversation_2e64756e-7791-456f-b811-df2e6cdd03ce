/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-2d931c16 {
  display: flex;
}
.flex-1.data-v-2d931c16 {
  flex: 1;
}
.items-center.data-v-2d931c16 {
  align-items: center;
}
.justify-center.data-v-2d931c16 {
  justify-content: center;
}
.justify-between.data-v-2d931c16 {
  justify-content: space-between;
}
.text-center.data-v-2d931c16 {
  text-align: center;
}
.rounded.data-v-2d931c16 {
  border-radius: 3px;
}
.rounded-lg.data-v-2d931c16 {
  border-radius: 6px;
}
.shadow.data-v-2d931c16 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-2d931c16 {
  padding: 16rpx;
}
.m-4.data-v-2d931c16 {
  margin: 16rpx;
}
.mb-4.data-v-2d931c16 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-2d931c16 {
  margin-top: 16rpx;
}
.image-mosaic-maker.data-v-2d931c16 {
  min-height: 100vh;
  background: #f8f9fa;
}
.image-mosaic-maker .content.data-v-2d931c16 {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.image-mosaic-maker .section-card.data-v-2d931c16 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.image-mosaic-maker .section-card .card-header.data-v-2d931c16 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.image-mosaic-maker .section-card .card-header .header-icon.data-v-2d931c16 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.image-mosaic-maker .section-card .card-header .header-title.data-v-2d931c16 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.image-mosaic-maker .section-card .card-content .upload-area.data-v-2d931c16 {
  border: 4rpx dashed #e5e7eb;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: #f9fafb;
  transition: all 0.3s;
}
.image-mosaic-maker .section-card .card-content .upload-area.data-v-2d931c16:active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-mosaic-maker .section-card .card-content .upload-area .upload-icon.data-v-2d931c16 {
  margin-bottom: 24rpx;
}
.image-mosaic-maker .section-card .card-content .upload-area .upload-icon .icon-text.data-v-2d931c16 {
  font-size: 80rpx;
  opacity: 0.6;
}
.image-mosaic-maker .section-card .card-content .upload-area .upload-title.data-v-2d931c16 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.image-mosaic-maker .section-card .card-content .upload-area .upload-desc.data-v-2d931c16 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}
.image-mosaic-maker .section-card .card-content .upload-area .selected-file.data-v-2d931c16 {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
}
.image-mosaic-maker .section-card .card-content .upload-area .selected-file .file-name.data-v-2d931c16 {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
}
.image-mosaic-maker .section-card .card-content .image-preview.data-v-2d931c16 {
  margin-top: 32rpx;
}
.image-mosaic-maker .section-card .card-content .image-preview .preview-header.data-v-2d931c16 {
  margin-bottom: 16rpx;
}
.image-mosaic-maker .section-card .card-content .image-preview .preview-header .preview-title.data-v-2d931c16 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.image-mosaic-maker .section-card .card-content .image-preview .preview-container.data-v-2d931c16 {
  position: relative;
  width: 100%;
}
.image-mosaic-maker .section-card .card-content .image-preview .preview-container .preview-image.data-v-2d931c16 {
  width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.image-mosaic-maker .section-card .card-content .image-preview .preview-container .touch-canvas.data-v-2d931c16 {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.image-mosaic-maker .setting-group.data-v-2d931c16 {
  margin-bottom: 40rpx;
}
.image-mosaic-maker .setting-group.data-v-2d931c16:last-child {
  margin-bottom: 0;
}
.image-mosaic-maker .setting-group .setting-label.data-v-2d931c16 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.image-mosaic-maker .setting-group .mosaic-types.data-v-2d931c16 {
  display: flex;
  gap: 16rpx;
}
.image-mosaic-maker .setting-group .mosaic-types .type-item.data-v-2d931c16 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  transition: all 0.2s;
}
.image-mosaic-maker .setting-group .mosaic-types .type-item.active.data-v-2d931c16 {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-mosaic-maker .setting-group .mosaic-types .type-item .type-icon.data-v-2d931c16 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.image-mosaic-maker .setting-group .mosaic-types .type-item .type-name.data-v-2d931c16 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.image-mosaic-maker .setting-group .slider-container.data-v-2d931c16 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.image-mosaic-maker .setting-group .slider-container slider.data-v-2d931c16 {
  flex: 1;
}
.image-mosaic-maker .setting-group .slider-container .slider-value.data-v-2d931c16 {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  min-width: 80rpx;
  text-align: right;
}
.image-mosaic-maker .setting-group .intensity-presets.data-v-2d931c16 {
  display: flex;
  gap: 12rpx;
}
.image-mosaic-maker .setting-group .intensity-presets .preset-item.data-v-2d931c16 {
  flex: 1;
  padding: 16rpx 12rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  text-align: center;
  background: white;
  transition: all 0.2s;
}
.image-mosaic-maker .setting-group .intensity-presets .preset-item.active.data-v-2d931c16 {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-mosaic-maker .setting-group .intensity-presets .preset-item .preset-name.data-v-2d931c16 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.image-mosaic-maker .setting-group .mode-options.data-v-2d931c16 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.image-mosaic-maker .setting-group .mode-options .mode-item.data-v-2d931c16 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  transition: all 0.2s;
}
.image-mosaic-maker .setting-group .mode-options .mode-item.active.data-v-2d931c16 {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-mosaic-maker .setting-group .mode-options .mode-item .mode-icon.data-v-2d931c16 {
  font-size: 28rpx;
  margin-right: 16rpx;
}
.image-mosaic-maker .setting-group .mode-options .mode-item .mode-name.data-v-2d931c16 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-right: 16rpx;
}
.image-mosaic-maker .setting-group .mode-options .mode-item .mode-desc.data-v-2d931c16 {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}
.image-mosaic-maker .process-button.data-v-2d931c16 {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(139, 92, 246, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.image-mosaic-maker .process-button.data-v-2d931c16:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.2);
}
.image-mosaic-maker .process-button.disabled.data-v-2d931c16 {
  opacity: 0.6;
  background: #d1d5db;
  box-shadow: none;
}
.image-mosaic-maker .result-container .result-comparison.data-v-2d931c16 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.image-mosaic-maker .result-container .result-comparison .comparison-item.data-v-2d931c16 {
  flex: 1;
}
.image-mosaic-maker .result-container .result-comparison .comparison-item .comparison-label.data-v-2d931c16 {
  display: block;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-bottom: 12rpx;
}
.image-mosaic-maker .result-container .result-comparison .comparison-item .comparison-image.data-v-2d931c16 {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.image-mosaic-maker .result-container .result-comparison .comparison-arrow.data-v-2d931c16 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-mosaic-maker .result-container .result-comparison .comparison-arrow .arrow-icon.data-v-2d931c16 {
  font-size: 24rpx;
  color: #666;
}
.image-mosaic-maker .result-container .result-actions.data-v-2d931c16 {
  display: flex;
  gap: 16rpx;
}
.image-mosaic-maker .result-container .result-actions .action-button.data-v-2d931c16 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.image-mosaic-maker .result-container .result-actions .action-button.secondary.data-v-2d931c16 {
  background: #f3f4f6;
  color: #374151;
}
.image-mosaic-maker .result-container .result-actions .action-button.secondary.data-v-2d931c16:active {
  background: #e5e7eb;
  transform: scale(0.98);
}
.image-mosaic-maker .result-container .result-actions .action-button.primary.data-v-2d931c16 {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.image-mosaic-maker .result-container .result-actions .action-button.primary.data-v-2d931c16:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.image-mosaic-maker .result-container .result-actions .action-button .button-icon.data-v-2d931c16 {
  font-size: 24rpx;
}
.image-mosaic-maker .result-container .result-actions .action-button .button-text.data-v-2d931c16 {
  font-size: 28rpx;
}
.image-mosaic-maker .usage-list .usage-item.data-v-2d931c16 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.image-mosaic-maker .usage-list .usage-item.data-v-2d931c16:last-child {
  margin-bottom: 0;
}
.image-mosaic-maker .hidden-canvas.data-v-2d931c16 {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  z-index: -1;
}
.preview-tip.data-v-2d931c16 {
  font-size: 24rpx;
  color: #666;
  margin-left: 16rpx;
}