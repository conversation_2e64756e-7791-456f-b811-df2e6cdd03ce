/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a22f57c0 {
  display: flex;
}
.flex-1.data-v-a22f57c0 {
  flex: 1;
}
.items-center.data-v-a22f57c0 {
  align-items: center;
}
.justify-center.data-v-a22f57c0 {
  justify-content: center;
}
.justify-between.data-v-a22f57c0 {
  justify-content: space-between;
}
.text-center.data-v-a22f57c0 {
  text-align: center;
}
.rounded.data-v-a22f57c0 {
  border-radius: 3px;
}
.rounded-lg.data-v-a22f57c0 {
  border-radius: 6px;
}
.shadow.data-v-a22f57c0 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a22f57c0 {
  padding: 16rpx;
}
.m-4.data-v-a22f57c0 {
  margin: 16rpx;
}
.mb-4.data-v-a22f57c0 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a22f57c0 {
  margin-top: 16rpx;
}
.common-numbers-page.data-v-a22f57c0 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-a22f57c0 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-a22f57c0 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.card-header.data-v-a22f57c0 {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-icon.data-v-a22f57c0 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.card-header .header-icon .phone-icon.data-v-a22f57c0 {
  font-size: 60rpx;
}
.card-header .header-title.data-v-a22f57c0 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-header .header-subtitle.data-v-a22f57c0 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.main-content.data-v-a22f57c0 {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}
.search-section .search-box.data-v-a22f57c0 {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 3rpx solid #e2e8f0;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  transition: all 0.3s ease;
}
.search-section .search-box .search-icon.data-v-a22f57c0 {
  font-size: 32rpx;
  color: #64748b;
  margin-right: 20rpx;
}
.search-section .search-box .search-input.data-v-a22f57c0 {
  flex: 1;
  font-size: 28rpx;
  color: #1e293b;
  background: transparent;
}
.search-section .search-box .clear-btn.data-v-a22f57c0 {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #e2e8f0;
}
.search-section .search-box .clear-btn .clear-icon.data-v-a22f57c0 {
  font-size: 24rpx;
  color: #64748b;
}
.search-section .search-box.data-v-a22f57c0:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.category-section.data-v-a22f57c0 {
  overflow: hidden;
  /* 添加溢出隐藏 */
}
.category-section .category-scroll.data-v-a22f57c0 {
  width: 100%;
  white-space: nowrap;
  /* 移除 webkit 相关样式，在小程序中无效 */
}
.category-section .category-list.data-v-a22f57c0 {
  display: flex;
  /* 改回 flex，配合 enable-flex 使用 */
  gap: 20rpx;
  padding: 0 10rpx;
  padding-bottom: 10rpx;
}
.category-section .category-list .category-item.data-v-a22f57c0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 20rpx 24rpx;
  background: #f1f5f9;
  border: 3rpx solid transparent;
  border-radius: 20rpx;
  min-width: 120rpx;
  transition: all 0.3s ease;
}
.category-section .category-list .category-item .category-icon.data-v-a22f57c0 {
  font-size: 32rpx;
}
.category-section .category-list .category-item .category-name.data-v-a22f57c0 {
  font-size: 24rpx;
  font-weight: 500;
  color: #475569;
  white-space: nowrap;
}
.category-section .category-list .category-item.active.data-v-a22f57c0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}
.category-section .category-list .category-item.active .category-name.data-v-a22f57c0 {
  color: white;
}
.numbers-section.data-v-a22f57c0 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.numbers-section .number-card.data-v-a22f57c0 {
  background: #f8fafc;
  border-radius: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}
.numbers-section .number-card .card-content .service-info .service-header.data-v-a22f57c0 {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  margin-bottom: 24rpx;
}
.numbers-section .number-card .card-content .service-info .service-header .service-icon.data-v-a22f57c0 {
  font-size: 40rpx;
  margin-top: 4rpx;
}
.numbers-section .number-card .card-content .service-info .service-header .service-text.data-v-a22f57c0 {
  flex: 1;
}
.numbers-section .number-card .card-content .service-info .service-header .service-text .service-name.data-v-a22f57c0 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}
.numbers-section .number-card .card-content .service-info .service-header .service-text .service-desc.data-v-a22f57c0 {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.5;
}
.numbers-section .number-card .card-content .service-info .number-display.data-v-a22f57c0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
}
.numbers-section .number-card .card-content .service-info .number-display .number-text.data-v-a22f57c0 {
  font-size: 48rpx;
  font-weight: 700;
  color: #667eea;
  font-family: "Courier New", monospace;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons.data-v-a22f57c0 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .call-btn.data-v-a22f57c0 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .call-btn .call-icon.data-v-a22f57c0 {
  font-size: 24rpx;
  color: white;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .call-btn .btn-text.data-v-a22f57c0 {
  font-size: 26rpx;
  font-weight: 600;
  color: white;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .call-btn.data-v-a22f57c0:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.4);
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .copy-btn.data-v-a22f57c0 {
  width: 64rpx;
  height: 64rpx;
  background: #e2e8f0;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .copy-btn .copy-icon.data-v-a22f57c0 {
  font-size: 28rpx;
  color: #64748b;
}
.numbers-section .number-card .card-content .service-info .number-display .action-buttons .copy-btn.data-v-a22f57c0:active {
  background: #cbd5e1;
  transform: translateY(2rpx);
}
.numbers-section .number-card.data-v-a22f57c0:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.no-result.data-v-a22f57c0 {
  text-align: center;
  padding: 80rpx 40rpx;
}
.no-result .no-result-icon.data-v-a22f57c0 {
  font-size: 80rpx;
  display: block;
  margin-bottom: 24rpx;
  opacity: 0.5;
}
.no-result .no-result-text.data-v-a22f57c0 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 12rpx;
}
.no-result .no-result-desc.data-v-a22f57c0 {
  display: block;
  font-size: 26rpx;
  color: #94a3b8;
}
.emergency-reminder.data-v-a22f57c0 {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 3rpx solid #fca5a5;
  border-radius: 20rpx;
  padding: 30rpx;
}
.emergency-reminder .reminder-header.data-v-a22f57c0 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}
.emergency-reminder .reminder-header .reminder-icon.data-v-a22f57c0 {
  font-size: 32rpx;
}
.emergency-reminder .reminder-header .reminder-title.data-v-a22f57c0 {
  font-size: 30rpx;
  font-weight: 600;
  color: #dc2626;
}
.emergency-reminder .reminder-content .reminder-item.data-v-a22f57c0 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.emergency-reminder .reminder-content .reminder-item .tip-icon.data-v-a22f57c0 {
  font-size: 28rpx;
  margin-top: 2rpx;
}
.emergency-reminder .reminder-content .reminder-item .tip-text.data-v-a22f57c0 {
  flex: 1;
  font-size: 26rpx;
  color: #dc2626;
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.number-display.data-v-a22f57c0 {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 16rpx !important;
}
.number-display .action-buttons.data-v-a22f57c0 {
    align-self: flex-end;
}
}