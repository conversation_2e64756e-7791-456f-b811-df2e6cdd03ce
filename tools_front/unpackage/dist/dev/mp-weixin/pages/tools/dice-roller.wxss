/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c3fdaac2 {
  display: flex;
}
.flex-1.data-v-c3fdaac2 {
  flex: 1;
}
.items-center.data-v-c3fdaac2 {
  align-items: center;
}
.justify-center.data-v-c3fdaac2 {
  justify-content: center;
}
.justify-between.data-v-c3fdaac2 {
  justify-content: space-between;
}
.text-center.data-v-c3fdaac2 {
  text-align: center;
}
.rounded.data-v-c3fdaac2 {
  border-radius: 3px;
}
.rounded-lg.data-v-c3fdaac2 {
  border-radius: 6px;
}
.shadow.data-v-c3fdaac2 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c3fdaac2 {
  padding: 16rpx;
}
.m-4.data-v-c3fdaac2 {
  margin: 16rpx;
}
.mb-4.data-v-c3fdaac2 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c3fdaac2 {
  margin-top: 16rpx;
}
.dice-roller-tool.data-v-c3fdaac2 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-card.data-v-c3fdaac2 {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.header-card .header-content.data-v-c3fdaac2 {
  display: flex;
  align-items: center;
  color: white;
}
.header-card .header-content .header-icon.data-v-c3fdaac2 {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.header-card .header-content .header-info.data-v-c3fdaac2 {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-c3fdaac2 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-card .header-content .header-info .header-subtitle.data-v-c3fdaac2 {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.header-card .header-content .lucky-badge.data-v-c3fdaac2 {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
}
.header-card .header-content .lucky-badge .lucky-text.data-v-c3fdaac2 {
  font-size: 26rpx;
  font-weight: 600;
  color: white;
}
.dice-container.data-v-c3fdaac2 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.dice-container .dice-area.data-v-c3fdaac2 {
  padding: 40rpx;
}
.mode-selector.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.mode-selector .mode-btn.data-v-c3fdaac2 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.mode-selector .mode-btn.active.data-v-c3fdaac2 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.mode-selector .mode-btn.active .mode-icon.data-v-c3fdaac2,
.mode-selector .mode-btn.active .mode-name.data-v-c3fdaac2 {
  color: white;
}
.mode-selector .mode-btn .mode-icon.data-v-c3fdaac2 {
  font-size: 28rpx;
}
.mode-selector .mode-btn .mode-name.data-v-c3fdaac2 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
}
.dice-count-selector.data-v-c3fdaac2 {
  margin-bottom: 40rpx;
}
.dice-count-selector .selector-label.data-v-c3fdaac2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
  display: block;
}
.dice-count-selector .count-options.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12rpx;
}
.dice-count-selector .count-options .count-btn.data-v-c3fdaac2 {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.dice-count-selector .count-options .count-btn.active.data-v-c3fdaac2 {
  background: #f59e0b;
  border-color: #f59e0b;
}
.dice-count-selector .count-options .count-btn.active .count-text.data-v-c3fdaac2 {
  color: white;
}
.dice-count-selector .count-options .count-btn .count-text.data-v-c3fdaac2 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
}
.dice-display.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  margin: 60rpx 0;
  justify-items: center;
}
.dice-display .dice-item.data-v-c3fdaac2 {
  position: relative;
  perspective: 1000rpx;
}
.dice-display .dice-item.dice-rolling.data-v-c3fdaac2 {
  animation: diceShake-c3fdaac2 0.08s infinite;
}
.dice-display .dice-item.dice-rolling .dice-cube.data-v-c3fdaac2 {
  animation: dice3DRoll-c3fdaac2 0.1s infinite;
  transform-style: preserve-3d;
}
.dice-display .dice-item.dice-bounce.data-v-c3fdaac2 {
  animation: diceBounce-c3fdaac2 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}
.dice-display .dice-item.dice-glow .dice-cube.data-v-c3fdaac2 {
  box-shadow: 0 0 20rpx rgba(16, 185, 129, 0.6), 0 0 40rpx rgba(16, 185, 129, 0.4), 0 0 60rpx rgba(16, 185, 129, 0.2);
}
.dice-display .dice-item.dice-glow .dice-cube .dice-face.data-v-c3fdaac2 {
  border-color: #10b981;
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
}
.dice-display .dice-item .dice-cube.data-v-c3fdaac2 {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  transform-style: preserve-3d;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.dice-display .dice-item .dice-cube .dice-face.data-v-c3fdaac2 {
  position: absolute;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 3rpx solid #d1d5db;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 8rpx;
  width: 80rpx;
  height: 80rpx;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.data-v-c3fdaac2 {
  width: 18rpx;
  height: 18rpx;
  background: radial-gradient(circle, #1f2937, #374151);
  border-radius: 50%;
  opacity: 0;
  box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-1.data-v-c3fdaac2 {
  grid-area: 1/1;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-2.data-v-c3fdaac2 {
  grid-area: 1/2;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-3.data-v-c3fdaac2 {
  grid-area: 1/3;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-4.data-v-c3fdaac2 {
  grid-area: 2/1;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-5.data-v-c3fdaac2 {
  grid-area: 2/2;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-6.data-v-c3fdaac2 {
  grid-area: 2/3;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-7.data-v-c3fdaac2 {
  grid-area: 3/1;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-8.data-v-c3fdaac2 {
  grid-area: 3/2;
  opacity: 1;
}
.dice-display .dice-item .dice-cube .dice-face .dice-dots .dot.dot-9.data-v-c3fdaac2 {
  grid-area: 3/3;
  opacity: 1;
}
.result-display.data-v-c3fdaac2 {
  margin: 30rpx 0;
}
.result-display .result-card.data-v-c3fdaac2 {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  border: 2rpx solid #10b981;
  border-radius: 16rpx;
  overflow: hidden;
}
.result-display .result-card .result-header.data-v-c3fdaac2 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 30rpx;
  background: rgba(16, 185, 129, 0.1);
}
.result-display .result-card .result-header .result-icon.data-v-c3fdaac2 {
  font-size: 32rpx;
}
.result-display .result-card .result-header .result-title.data-v-c3fdaac2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #065f46;
}
.result-display .result-card .result-content.data-v-c3fdaac2 {
  padding: 30rpx;
}
.result-display .result-card .result-content .result-summary.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.result-display .result-card .result-content .result-summary .summary-item.data-v-c3fdaac2 {
  text-align: center;
}
.result-display .result-card .result-content .result-summary .summary-item .summary-label.data-v-c3fdaac2 {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.result-display .result-card .result-content .result-summary .summary-item .summary-value.data-v-c3fdaac2 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #10b981;
}
.result-display .result-card .result-content .game-result.data-v-c3fdaac2 {
  text-align: center;
}
.result-display .result-card .result-content .game-result .game-result-text.data-v-c3fdaac2 {
  font-size: 32rpx;
  font-weight: 700;
  color: #10b981;
}
.action-buttons.data-v-c3fdaac2 {
  margin-top: 40rpx;
}
.action-buttons .roll-button.data-v-c3fdaac2 {
  width: 100%;
  max-width: none;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.3), 0 2rpx 8rpx rgba(245, 158, 11, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}
.action-buttons .roll-button.data-v-c3fdaac2::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}
.action-buttons .roll-button.data-v-c3fdaac2:active:not(.disabled) {
  transform: scale(0.98) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.4), 0 1rpx 4rpx rgba(245, 158, 11, 0.3);
}
.action-buttons .roll-button.data-v-c3fdaac2:active:not(.disabled)::before {
  left: 100%;
}
.action-buttons .roll-button.rolling.data-v-c3fdaac2 {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  animation: buttonPulse-c3fdaac2 1.2s infinite ease-in-out;
}
.action-buttons .roll-button.rolling .roll-icon.data-v-c3fdaac2 {
  animation: iconSpin-c3fdaac2 1s linear infinite;
}
.action-buttons .roll-button.disabled.data-v-c3fdaac2 {
  opacity: 0.7;
  pointer-events: none;
  transform: none;
}
.action-buttons .roll-button .roll-icon.data-v-c3fdaac2 {
  font-size: 36rpx;
  margin-right: 16rpx;
  display: inline-block;
  transition: transform 0.3s ease;
}
.action-buttons .roll-button .roll-text.data-v-c3fdaac2 {
  font-size: 30rpx;
  font-weight: 700;
  letter-spacing: 1rpx;
}
.action-buttons .secondary-buttons.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.action-buttons .secondary-buttons .action-btn.data-v-c3fdaac2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
  padding: 24rpx 20rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  box-sizing: border-box;
}
.action-buttons .secondary-buttons .action-btn.data-v-c3fdaac2:active:not(.disabled) {
  transform: scale(0.95) translateY(1rpx);
}
.action-buttons .secondary-buttons .action-btn.reset.data-v-c3fdaac2 {
  border-color: #3b82f6;
}
.action-buttons .secondary-buttons .action-btn.reset.data-v-c3fdaac2:hover {
  background: rgba(59, 130, 246, 0.05);
}
.action-buttons .secondary-buttons .action-btn.reset .btn-icon.data-v-c3fdaac2,
.action-buttons .secondary-buttons .action-btn.reset .btn-text.data-v-c3fdaac2 {
  color: #3b82f6;
}
.action-buttons .secondary-buttons .action-btn.history.data-v-c3fdaac2 {
  border-color: #8b5cf6;
}
.action-buttons .secondary-buttons .action-btn.history.data-v-c3fdaac2:hover {
  background: rgba(139, 92, 246, 0.05);
}
.action-buttons .secondary-buttons .action-btn.history .btn-icon.data-v-c3fdaac2,
.action-buttons .secondary-buttons .action-btn.history .btn-text.data-v-c3fdaac2 {
  color: #8b5cf6;
}
.action-buttons .secondary-buttons .action-btn.sound.data-v-c3fdaac2 {
  border-color: #6b7280;
}
.action-buttons .secondary-buttons .action-btn.sound .btn-icon.data-v-c3fdaac2,
.action-buttons .secondary-buttons .action-btn.sound .btn-text.data-v-c3fdaac2 {
  color: #6b7280;
}
.action-buttons .secondary-buttons .action-btn.sound.active.data-v-c3fdaac2 {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}
.action-buttons .secondary-buttons .action-btn.sound.active .btn-icon.data-v-c3fdaac2,
.action-buttons .secondary-buttons .action-btn.sound.active .btn-text.data-v-c3fdaac2 {
  color: #10b981;
}
.action-buttons .secondary-buttons .action-btn.sound.data-v-c3fdaac2:hover:not(.active) {
  background: rgba(107, 114, 128, 0.05);
}
.action-buttons .secondary-buttons .action-btn.disabled.data-v-c3fdaac2 {
  opacity: 0.5;
  pointer-events: none;
}
.action-buttons .secondary-buttons .action-btn .btn-icon.data-v-c3fdaac2 {
  font-size: 32rpx;
  transition: transform 0.2s ease;
}
.action-buttons .secondary-buttons .action-btn .btn-text.data-v-c3fdaac2 {
  font-size: 24rpx;
  font-weight: 600;
}
.history-card.data-v-c3fdaac2,
.stats-card.data-v-c3fdaac2 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.history-header.data-v-c3fdaac2,
.stats-header.data-v-c3fdaac2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1rpx solid #e5e7eb;
}
.history-header .history-icon.data-v-c3fdaac2,
.history-header .stats-icon.data-v-c3fdaac2,
.stats-header .history-icon.data-v-c3fdaac2,
.stats-header .stats-icon.data-v-c3fdaac2 {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.history-header .history-title.data-v-c3fdaac2,
.history-header .stats-title.data-v-c3fdaac2,
.stats-header .history-title.data-v-c3fdaac2,
.stats-header .stats-title.data-v-c3fdaac2 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.history-header .clear-btn.data-v-c3fdaac2,
.stats-header .clear-btn.data-v-c3fdaac2 {
  padding: 12rpx 20rpx;
  background: #ef4444;
  color: white;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.history-header .clear-btn.disabled.data-v-c3fdaac2,
.stats-header .clear-btn.disabled.data-v-c3fdaac2 {
  opacity: 0.5;
  pointer-events: none;
}
.history-header .clear-btn .clear-text.data-v-c3fdaac2,
.stats-header .clear-btn .clear-text.data-v-c3fdaac2 {
  font-size: 24rpx;
  font-weight: 600;
}
.history-content.data-v-c3fdaac2,
.stats-content.data-v-c3fdaac2 {
  padding: 30rpx;
}
.history-empty.data-v-c3fdaac2 {
  text-align: center;
  padding: 60rpx 0;
}
.history-empty .empty-icon.data-v-c3fdaac2 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
  opacity: 0.5;
}
.history-empty .empty-text.data-v-c3fdaac2 {
  font-size: 28rpx;
  color: #6b7280;
}
.history-list .history-item.data-v-c3fdaac2 {
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-left: 4rpx solid #e5e7eb;
}
.history-list .history-item.lucky-record.data-v-c3fdaac2 {
  border-left-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}
.history-list .history-item.data-v-c3fdaac2:last-child {
  margin-bottom: 0;
}
.history-list .history-item .record-header.data-v-c3fdaac2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.history-list .history-item .record-header .record-time.data-v-c3fdaac2 {
  font-size: 22rpx;
  color: #6b7280;
}
.history-list .history-item .record-header .record-mode.data-v-c3fdaac2 {
  background: #3b82f6;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}
.history-list .history-item .record-content.data-v-c3fdaac2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.history-list .history-item .record-content .record-dice.data-v-c3fdaac2 {
  display: flex;
  gap: 8rpx;
}
.history-list .history-item .record-content .record-dice .dice-value-small.data-v-c3fdaac2 {
  width: 40rpx;
  height: 40rpx;
  background: white;
  border: 2rpx solid #d1d5db;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 700;
  color: #1f2937;
}
.history-list .history-item .record-content .record-result.data-v-c3fdaac2 {
  text-align: right;
}
.history-list .history-item .record-content .record-result .result-total.data-v-c3fdaac2 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}
.history-list .history-item .record-content .record-result .result-text.data-v-c3fdaac2 {
  font-size: 22rpx;
  color: #10b981;
}
.load-more.data-v-c3fdaac2 {
  text-align: center;
  padding: 20rpx;
  border-top: 1rpx solid #e5e7eb;
  margin-top: 20rpx;
}
.load-more .load-more-text.data-v-c3fdaac2 {
  font-size: 26rpx;
  color: #3b82f6;
}
.stats-grid.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30rpx;
  margin-bottom: 40rpx;
}
.stats-grid .stat-item.data-v-c3fdaac2 {
  text-align: center;
}
.stats-grid .stat-item .stat-value.data-v-c3fdaac2 {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8rpx;
}
.stats-grid .stat-item .stat-label.data-v-c3fdaac2 {
  font-size: 22rpx;
  color: #6b7280;
}
.probability-chart .chart-title.data-v-c3fdaac2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
  display: block;
}
.probability-chart .chart-bars.data-v-c3fdaac2 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12rpx;
  height: 200rpx;
  align-items: end;
}
.probability-chart .chart-bars .chart-bar.data-v-c3fdaac2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}
.probability-chart .chart-bars .chart-bar .bar-fill.data-v-c3fdaac2 {
  width: 100%;
  background: linear-gradient(to top, #3b82f6, #60a5fa);
  border-radius: 4rpx 4rpx 0 0;
  min-height: 10rpx;
  margin-bottom: 8rpx;
  transition: height 0.3s ease;
}
.probability-chart .chart-bars .chart-bar .bar-label.data-v-c3fdaac2 {
  font-size: 24rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}
.probability-chart .chart-bars .chart-bar .bar-count.data-v-c3fdaac2 {
  font-size: 20rpx;
  color: #6b7280;
}
@keyframes diceShake-c3fdaac2 {
0%, 100% {
    transform: translateX(0) translateY(0) rotate(0deg);
}
10% {
    transform: translateX(-3rpx) translateY(-2rpx) rotate(-1deg);
}
20% {
    transform: translateX(3rpx) translateY(2rpx) rotate(1deg);
}
30% {
    transform: translateX(-2rpx) translateY(-3rpx) rotate(-0.5deg);
}
40% {
    transform: translateX(2rpx) translateY(3rpx) rotate(0.5deg);
}
50% {
    transform: translateX(-1rpx) translateY(-1rpx) rotate(-0.2deg);
}
60% {
    transform: translateX(1rpx) translateY(1rpx) rotate(0.2deg);
}
70% {
    transform: translateX(-2rpx) translateY(0) rotate(-0.3deg);
}
80% {
    transform: translateX(2rpx) translateY(-1rpx) rotate(0.3deg);
}
90% {
    transform: translateX(-1rpx) translateY(1rpx) rotate(-0.1deg);
}
}
@keyframes dice3DRoll-c3fdaac2 {
0% {
    transform: rotateX(0deg) rotateY(0deg) rotateZ(0deg);
}
25% {
    transform: rotateX(90deg) rotateY(45deg) rotateZ(0deg);
}
50% {
    transform: rotateX(180deg) rotateY(90deg) rotateZ(45deg);
}
75% {
    transform: rotateX(270deg) rotateY(135deg) rotateZ(90deg);
}
100% {
    transform: rotateX(360deg) rotateY(180deg) rotateZ(135deg);
}
}
@keyframes diceBounce-c3fdaac2 {
0% {
    transform: scale(1) translateY(0);
}
30% {
    transform: scale(1.15) translateY(-15rpx);
}
60% {
    transform: scale(0.95) translateY(5rpx);
}
80% {
    transform: scale(1.05) translateY(-3rpx);
}
100% {
    transform: scale(1) translateY(0);
}
}
@keyframes buttonPulse-c3fdaac2 {
0%, 100% {
    opacity: 1;
    transform: scale(1);
}
50% {
    opacity: 0.9;
    transform: scale(1.02);
}
}
@keyframes iconSpin-c3fdaac2 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}