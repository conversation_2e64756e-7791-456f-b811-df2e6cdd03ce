"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "BMICalculator",
  data() {
    return {
      height: "",
      weight: "",
      age: "",
      genderIndex: 0,
      genderOptions: ["男性", "女性"],
      bmi: null,
      category: "",
      idealWeight: { min: 0, max: 0 },
      bmiCategories: [
        {
          range: "< 18.5",
          category: "偏瘦",
          colorClass: "underweight",
          advice: "建议适当增加营养摄入，多吃富含蛋白质的食物，配合适量运动增强体质。"
        },
        {
          range: "18.5 - 24.9",
          category: "正常",
          colorClass: "normal",
          advice: "恭喜！您的体重处于健康范围，请保持现有的健康生活方式。"
        },
        {
          range: "25.0 - 29.9",
          category: "超重",
          colorClass: "overweight",
          advice: "建议控制饮食并增加运动，每周至少进行150分钟中等强度运动。"
        },
        {
          range: "30.0 - 34.9",
          category: "肥胖I级",
          colorClass: "obese1",
          advice: "需要制定科学的减重计划，建议咨询营养师制定个性化方案。"
        },
        {
          range: "≥ 35.0",
          category: "肥胖II级",
          colorClass: "obese2",
          advice: "建议尽快咨询专业医生，制定综合性的健康管理方案。"
        }
      ]
    };
  },
  methods: {
    calculateBMI() {
      if (!this.height || !this.weight) {
        common_vendor.index.showToast({
          title: "请输入身高和体重",
          icon: "none"
        });
        return;
      }
      const heightInM = parseFloat(this.height) / 100;
      const bmiValue = parseFloat(this.weight) / (heightInM * heightInM);
      this.bmi = parseFloat(bmiValue.toFixed(1));
      const minIdeal = 18.5 * heightInM * heightInM;
      const maxIdeal = 24.9 * heightInM * heightInM;
      this.idealWeight = {
        min: parseFloat(minIdeal.toFixed(1)),
        max: parseFloat(maxIdeal.toFixed(1))
      };
      if (bmiValue < 18.5) {
        this.category = "偏瘦";
      } else if (bmiValue < 25) {
        this.category = "正常";
      } else if (bmiValue < 30) {
        this.category = "超重";
      } else if (bmiValue < 35) {
        this.category = "肥胖I级";
      } else {
        this.category = "肥胖II级";
      }
      common_vendor.index.showToast({
        title: "BMI计算完成",
        icon: "success"
      });
    },
    getCurrentCategory() {
      return this.bmiCategories.find((cat) => cat.category === this.category);
    },
    getBMIColorClass() {
      if (!this.bmi)
        return "bmi-default";
      if (this.bmi < 18.5)
        return "bmi-underweight";
      if (this.bmi < 25)
        return "bmi-normal";
      if (this.bmi < 30)
        return "bmi-overweight";
      if (this.bmi < 35)
        return "bmi-obese1";
      return "bmi-obese2";
    },
    getWeightDifference() {
      if (!this.bmi || !this.weight)
        return null;
      const w = parseFloat(this.weight);
      if (this.category === "正常") {
        return null;
      } else if (this.category === "偏瘦") {
        return {
          type: "gain",
          amount: (this.idealWeight.min - w).toFixed(1)
        };
      } else {
        return {
          type: "lose",
          amount: (w - this.idealWeight.max).toFixed(1)
        };
      }
    },
    onGenderChange(e) {
      this.genderIndex = e.detail.value;
    },
    clearResult() {
      this.bmi = null;
      this.category = "";
      this.idealWeight = { min: 0, max: 0 };
    },
    reset() {
      this.height = "";
      this.weight = "";
      this.age = "";
      this.genderIndex = 0;
      this.bmi = null;
      this.category = "";
      this.idealWeight = { min: 0, max: 0 };
      common_vendor.index.showToast({
        title: "已重置",
        icon: "success"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h;
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.height = $event.detail.value, (...args) => $options.clearResult && $options.clearResult(...args)]),
    b: $data.height,
    c: common_vendor.o([($event) => $data.weight = $event.detail.value, (...args) => $options.clearResult && $options.clearResult(...args)]),
    d: $data.weight,
    e: $data.age,
    f: common_vendor.o(($event) => $data.age = $event.detail.value),
    g: common_vendor.t($data.genderOptions[$data.genderIndex]),
    h: $data.genderOptions,
    i: $data.genderIndex,
    j: common_vendor.o((...args) => $options.onGenderChange && $options.onGenderChange(...args)),
    k: common_vendor.o((...args) => $options.calculateBMI && $options.calculateBMI(...args)),
    l: common_vendor.o((...args) => $options.reset && $options.reset(...args)),
    m: $data.bmi
  }, $data.bmi ? common_vendor.e({
    n: common_vendor.t($data.bmi),
    o: common_vendor.n($options.getBMIColorClass()),
    p: common_vendor.t($data.category),
    q: common_vendor.n((_a = $options.getCurrentCategory()) == null ? void 0 : _a.colorClass),
    r: common_vendor.t($data.idealWeight.min),
    s: common_vendor.t($data.idealWeight.max),
    t: $options.getWeightDifference()
  }, $options.getWeightDifference() ? common_vendor.e({
    v: ((_b = $options.getWeightDifference()) == null ? void 0 : _b.type) === "lose"
  }, ((_c = $options.getWeightDifference()) == null ? void 0 : _c.type) === "lose" ? {
    w: common_vendor.t((_d = $options.getWeightDifference()) == null ? void 0 : _d.amount)
  } : ((_e = $options.getWeightDifference()) == null ? void 0 : _e.type) === "gain" ? {
    y: common_vendor.t((_f = $options.getWeightDifference()) == null ? void 0 : _f.amount)
  } : {}, {
    x: ((_g = $options.getWeightDifference()) == null ? void 0 : _g.type) === "gain"
  }) : {}, {
    z: common_vendor.t((_h = $options.getCurrentCategory()) == null ? void 0 : _h.advice)
  }) : {}, {
    A: common_vendor.f($data.bmiCategories, (cat, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(cat.range),
        b: common_vendor.t(cat.category),
        c: cat.category === $data.category
      }, cat.category === $data.category ? {} : {}, {
        d: index,
        e: common_vendor.n(cat.category === $data.category ? "active" : ""),
        f: common_vendor.n(cat.colorClass)
      });
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b7dc2734"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/bmi-calculator.js.map
