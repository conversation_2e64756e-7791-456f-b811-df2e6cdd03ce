"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "AnimationGenerator",
  data() {
    return {
      animationType: "fadeIn",
      duration: 1,
      delay: 0,
      iterations: 1,
      isPlaying: false,
      previewKey: 0,
      iterationOptions: [
        { value: 1, label: "1次" },
        { value: 2, label: "2次" },
        { value: 3, label: "3次" },
        { value: "infinite", label: "无限" }
      ],
      animationTemplates: {
        fadeIn: {
          name: "淡入",
          icon: "🌅",
          desc: "透明度从0到1",
          keyframes: `@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}`
        },
        fadeOut: {
          name: "淡出",
          icon: "🌇",
          desc: "透明度从1到0",
          keyframes: `@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}`
        },
        slideInLeft: {
          name: "左滑入",
          icon: "👈",
          desc: "从左侧滑入",
          keyframes: `@keyframes slideInLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}`
        },
        slideInRight: {
          name: "右滑入",
          icon: "👉",
          desc: "从右侧滑入",
          keyframes: `@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}`
        },
        slideInUp: {
          name: "上滑入",
          icon: "👆",
          desc: "从上方滑入",
          keyframes: `@keyframes slideInUp {
  from { transform: translateY(-100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}`
        },
        slideInDown: {
          name: "下滑入",
          icon: "👇",
          desc: "从下方滑入",
          keyframes: `@keyframes slideInDown {
  from { transform: translateY(100%); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}`
        },
        zoomIn: {
          name: "放大",
          icon: "🔍",
          desc: "缩放放大效果",
          keyframes: `@keyframes zoomIn {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}`
        },
        zoomOut: {
          name: "缩小",
          icon: "🔎",
          desc: "缩放缩小效果",
          keyframes: `@keyframes zoomOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0); opacity: 0; }
}`
        },
        rotateIn: {
          name: "旋转入",
          icon: "🌀",
          desc: "旋转进入效果",
          keyframes: `@keyframes rotateIn {
  from { transform: rotate(-360deg) scale(0); opacity: 0; }
  to { transform: rotate(0deg) scale(1); opacity: 1; }
}`
        },
        bounce: {
          name: "弹跳",
          icon: "⚡",
          desc: "弹跳动画效果",
          keyframes: `@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-30px,0); }
  70% { transform: translate3d(0,-15px,0); }
  90% { transform: translate3d(0,-4px,0); }
}`
        },
        pulse: {
          name: "脉冲",
          icon: "💓",
          desc: "脉冲缩放效果",
          keyframes: `@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}`
        },
        shake: {
          name: "摇摆",
          icon: "📳",
          desc: "左右摇摆效果",
          keyframes: `@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
  20%, 40%, 60%, 80% { transform: translateX(10px); }
}`
        }
      }
    };
  },
  computed: {
    currentAnimation() {
      return this.animationTemplates[this.animationType];
    },
    generatedCSS() {
      const template = this.currentAnimation;
      const iterationValue = this.iterations === "infinite" ? "infinite" : this.iterations;
      return `${template.keyframes}

.animated-element {
  animation: ${this.animationType} ${this.duration}s ease ${this.delay}s ${iterationValue};
  animation-fill-mode: both;
}`;
    },
    previewStyle() {
      if (!this.isPlaying)
        return {};
      const iterationValue = this.iterations === "infinite" ? "infinite" : this.iterations;
      return {
        animation: `${this.animationType} ${this.duration}s ease ${this.delay}s ${iterationValue}`,
        animationFillMode: "both"
      };
    }
  },
  methods: {
    selectAnimationType(type) {
      this.animationType = type;
      this.restartAnimation();
      common_vendor.index.showToast({
        title: "动画类型已切换",
        icon: "success"
      });
    },
    onDurationChange(e) {
      this.duration = (e.detail.value / 10).toFixed(1);
    },
    onDelayChange(e) {
      this.delay = (e.detail.value / 10).toFixed(1);
    },
    setIterations(value) {
      this.iterations = value;
    },
    togglePreview() {
      if (this.isPlaying) {
        this.isPlaying = false;
        this.previewKey++;
      } else {
        this.isPlaying = true;
        this.previewKey++;
        this.addAnimationStyles();
      }
    },
    restartAnimation() {
      this.isPlaying = false;
      this.previewKey++;
      this.$nextTick(() => {
        this.isPlaying = true;
        this.previewKey++;
        this.addAnimationStyles();
      });
    },
    addAnimationStyles() {
      common_vendor.index.__f__("log", "at pages/tools/animation-code-generator.vue:371", "动画样式已应用:", this.currentAnimation.name);
      common_vendor.index.showToast({
        title: `预览${this.currentAnimation.name}动画`,
        icon: "success"
      });
    },
    copyToClipboard() {
      common_vendor.index.setClipboardData({
        data: this.generatedCSS,
        success: () => {
          common_vendor.index.showToast({
            title: "CSS代码已复制",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.f($data.animationTemplates, (template, key, i0) => {
      return {
        a: common_vendor.t(template.icon),
        b: common_vendor.t(template.name),
        c: common_vendor.t(template.desc),
        d: key,
        e: $data.animationType === key ? 1 : "",
        f: common_vendor.o(($event) => $options.selectAnimationType(key), key)
      };
    }),
    b: common_vendor.t($data.duration),
    c: $data.duration * 10,
    d: common_vendor.o((...args) => $options.onDurationChange && $options.onDurationChange(...args)),
    e: common_vendor.t($data.delay),
    f: $data.delay * 10,
    g: common_vendor.o((...args) => $options.onDelayChange && $options.onDelayChange(...args)),
    h: common_vendor.f($data.iterationOptions, (option, k0, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: option.value,
        c: $data.iterations === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setIterations(option.value), option.value)
      };
    }),
    i: common_vendor.t($data.isPlaying ? "⏸️" : "▶️"),
    j: common_vendor.t($data.isPlaying ? "暂停" : "播放"),
    k: common_vendor.o((...args) => $options.togglePreview && $options.togglePreview(...args)),
    l: common_vendor.o((...args) => $options.restartAnimation && $options.restartAnimation(...args)),
    m: common_vendor.n($data.animationType),
    n: common_vendor.n({
      animating: $data.isPlaying
    }),
    o: common_vendor.s($options.previewStyle),
    p: $data.previewKey,
    q: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    r: common_vendor.t($options.generatedCSS)
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4506e66d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/animation-code-generator.js.map
