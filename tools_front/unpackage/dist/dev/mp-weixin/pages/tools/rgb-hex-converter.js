"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "ColorConverter",
  data() {
    return {
      // 颜色值
      rgb: { r: 0, g: 123, b: 255 },
      hexInput: "#007BFF",
      currentColor: "#007BFF",
      // 预设颜色
      presetColors: [
        "#FF0000",
        "#FF4500",
        "#FF8C00",
        "#FFD700",
        "#FFFF00",
        "#9AFF9A",
        "#00FF00",
        "#00FF7F",
        "#00FFFF",
        "#87CEEB",
        "#0000FF",
        "#9400D3",
        "#8B00FF",
        "#FF00FF",
        "#FF1493",
        "#FFC0CB",
        "#FFFFFF",
        "#F5F5F5",
        "#DCDCDC",
        "#C0C0C0",
        "#808080",
        "#696969",
        "#2F4F4F",
        "#000000"
      ],
      // 颜色选择器相关
      showColorPicker: false,
      selectedColor: "#007BFF",
      currentHue: "#FF0000",
      canvasPosition: { x: 150, y: 100 },
      huePosition: 0,
      rgbValues: { r: 0, g: 123, b: 255 },
      canvasSize: { width: 300, height: 200 }
    };
  },
  computed: {
    // 计算HSL值
    hsl() {
      const r = this.rgb.r / 255;
      const g = this.rgb.g / 255;
      const b = this.rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h, s, l = (max + min) / 2;
      if (max === min) {
        h = s = 0;
      } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r:
            h = (g - b) / d + (g < b ? 6 : 0);
            break;
          case g:
            h = (b - r) / d + 2;
            break;
          case b:
            h = (r - g) / d + 4;
            break;
        }
        h /= 6;
      }
      return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
      };
    },
    // 检查是否为浅色
    isLightColor() {
      return this.brightness > 50;
    },
    // 计算亮度
    brightness() {
      return Math.round((this.rgb.r * 0.299 + this.rgb.g * 0.587 + this.rgb.b * 0.114) / 255 * 100);
    },
    // 亮度描述
    brightnessDesc() {
      if (this.brightness < 25)
        return "很暗";
      if (this.brightness < 50)
        return "较暗";
      if (this.brightness < 75)
        return "适中";
      return "很亮";
    },
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "px",
        top: this.canvasPosition.y + "px"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "px"
      };
    }
  },
  onLoad() {
    this.updateFromRGB();
  },
  methods: {
    // 从RGB更新
    updateFromRGB() {
      this.rgb.r = Math.max(0, Math.min(255, this.rgb.r || 0));
      this.rgb.g = Math.max(0, Math.min(255, this.rgb.g || 0));
      this.rgb.b = Math.max(0, Math.min(255, this.rgb.b || 0));
      this.currentColor = this.rgbToHex(this.rgb);
      this.hexInput = this.currentColor;
    },
    // 从HEX更新
    updateFromHEX() {
      let hex = this.hexInput.replace("#", "");
      if (hex.length === 3) {
        hex = hex.split("").map((x) => x + x).join("");
      }
      if (/^[0-9A-F]{6}$/i.test(hex)) {
        hex = "#" + hex.toUpperCase();
        this.currentColor = hex;
        this.rgb = this.hexToRgb(hex);
      }
    },
    // 选择预设颜色
    selectPresetColor(color) {
      this.currentColor = color;
      this.hexInput = color;
      this.rgb = this.hexToRgb(color);
      common_vendor.index.showToast({
        title: "颜色已选择",
        icon: "success"
      });
    },
    // 复制颜色
    copyColor(color) {
      common_vendor.index.setClipboardData({
        data: color,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success"
          });
        }
      });
    },
    // 随机颜色
    randomColor() {
      const r = Math.floor(Math.random() * 256);
      const g = Math.floor(Math.random() * 256);
      const b = Math.floor(Math.random() * 256);
      this.rgb = { r, g, b };
      this.updateFromRGB();
      common_vendor.index.showToast({
        title: "随机颜色生成成功",
        icon: "success"
      });
    },
    // 重置颜色
    resetColor() {
      this.rgb = { r: 0, g: 123, b: 255 };
      this.updateFromRGB();
      common_vendor.index.showToast({
        title: "已重置为默认颜色",
        icon: "success"
      });
    },
    // 颜色转换工具方法
    rgbToHex(rgb) {
      return "#" + [rgb.r, rgb.g, rgb.b].map((x) => x.toString(16).padStart(2, "0").toUpperCase()).join("");
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    },
    openColorPicker() {
      this.selectedColor = this.currentColor;
      this.updateRgbFromHex(this.currentColor);
      this.showColorPicker = true;
      this.$nextTick(() => {
        this.initColorCanvas();
        this.initHueSlider();
      });
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      this.currentColor = this.selectedColor;
      this.hexInput = this.selectedColor;
      this.rgb = this.hexToRgb(this.selectedColor);
      this.closeColorPicker();
    },
    initColorCanvas() {
      const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
      const { width, height } = this.canvasSize;
      const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
      gradient1.addColorStop(0, "#FFFFFF");
      gradient1.addColorStop(1, this.currentHue);
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, width, height);
      const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
      gradient2.addColorStop(0, "rgba(0,0,0,0)");
      gradient2.addColorStop(1, "#000000");
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    initHueSlider() {
      const ctx = common_vendor.index.createCanvasContext("hueCanvas", this);
      const width = 300;
      const height = 30;
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17, "#FFFF00");
      gradient.addColorStop(0.33, "#00FF00");
      gradient.addColorStop(0.5, "#00FFFF");
      gradient.addColorStop(0.67, "#0000FF");
      gradient.addColorStop(0.83, "#FF00FF");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    onCanvasTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;
          this.canvasPosition.x = Math.max(0, Math.min(rect.width, x));
          this.canvasPosition.y = Math.max(0, Math.min(rect.height, y));
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    onHueTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          this.huePosition = Math.max(0, Math.min(rect.width, x));
          this.updateHueFromSlider();
        }
      }).exec();
    },
    updateColorFromCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = this.canvasPosition.x / rect.width;
          const y = this.canvasPosition.y / rect.height;
          const hue = this.huePosition / 300 * 360;
          const saturation = x;
          const value = 1 - y;
          const rgb = this.hsvToRgb(hue, saturation, value);
          this.rgbValues = rgb;
          this.selectedColor = this.rgbToHex(rgb);
        }
      }).exec();
    },
    updateHueFromSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const hue = this.huePosition / rect.width * 360;
          const hueRgb = this.hsvToRgb(hue, 1, 1);
          this.currentHue = this.rgbToHex(hueRgb);
          this.initColorCanvas();
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    updateRgbFromHex(hex) {
      const rgb = this.hexToRgb(hex);
      if (rgb) {
        this.rgbValues = rgb;
        const hsv = this.rgbToHsv(rgb);
        this.huePosition = hsv.h / 360 * 300;
        this.canvasPosition.x = hsv.s * this.canvasSize.width;
        this.canvasPosition.y = (1 - hsv.v) * this.canvasSize.height;
        const hueRgb = this.hsvToRgb(hsv.h, 1, 1);
        this.currentHue = this.rgbToHex(hueRgb);
      }
    },
    onRgbChange() {
      this.selectedColor = this.rgbToHex(this.rgbValues);
      this.updateRgbFromHex(this.selectedColor);
    },
    // 颜色转换工具方法
    hsvToRgb(h, s, v) {
      const c = v * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = v - c;
      let r, g, b;
      if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255)
      };
    },
    rgbToHsv(rgb) {
      const r = rgb.r / 255;
      const g = rgb.g / 255;
      const b = rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;
      let h = 0;
      if (diff !== 0) {
        if (max === r) {
          h = (g - b) / diff % 6;
        } else if (max === g) {
          h = (b - r) / diff + 2;
        } else {
          h = (r - g) / diff + 4;
        }
      }
      h = h * 60;
      if (h < 0)
        h += 360;
      const s = max === 0 ? 0 : diff / max;
      const v = max;
      return { h, s, v };
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.currentColor),
    b: $options.isLightColor ? 1 : "",
    c: $data.currentColor,
    d: common_vendor.o((...args) => $options.openColorPicker && $options.openColorPicker(...args)),
    e: common_vendor.o(($event) => $options.copyColor($data.currentColor)),
    f: common_vendor.o((...args) => $options.randomColor && $options.randomColor(...args)),
    g: common_vendor.o((...args) => $options.resetColor && $options.resetColor(...args)),
    h: common_vendor.o([common_vendor.m(($event) => $data.rgb.r = $event.detail.value, {
      number: true
    }), (...args) => $options.updateFromRGB && $options.updateFromRGB(...args)]),
    i: $data.rgb.r,
    j: $data.rgb.r,
    k: common_vendor.o((e) => {
      $data.rgb.r = e.detail.value;
      $options.updateFromRGB();
    }),
    l: common_vendor.o([common_vendor.m(($event) => $data.rgb.g = $event.detail.value, {
      number: true
    }), (...args) => $options.updateFromRGB && $options.updateFromRGB(...args)]),
    m: $data.rgb.g,
    n: $data.rgb.g,
    o: common_vendor.o((e) => {
      $data.rgb.g = e.detail.value;
      $options.updateFromRGB();
    }),
    p: common_vendor.o([common_vendor.m(($event) => $data.rgb.b = $event.detail.value, {
      number: true
    }), (...args) => $options.updateFromRGB && $options.updateFromRGB(...args)]),
    q: $data.rgb.b,
    r: $data.rgb.b,
    s: common_vendor.o((e) => {
      $data.rgb.b = e.detail.value;
      $options.updateFromRGB();
    }),
    t: common_vendor.o([($event) => $data.hexInput = $event.detail.value, (...args) => $options.updateFromHEX && $options.updateFromHEX(...args)]),
    v: $data.hexInput,
    w: common_vendor.o(($event) => $options.copyColor($data.currentColor)),
    x: common_vendor.t($data.rgb.r),
    y: common_vendor.t($data.rgb.g),
    z: common_vendor.t($data.rgb.b),
    A: common_vendor.o(($event) => $options.copyColor(`rgb(${$data.rgb.r}, ${$data.rgb.g}, ${$data.rgb.b})`)),
    B: common_vendor.t($data.currentColor),
    C: common_vendor.o(($event) => $options.copyColor($data.currentColor)),
    D: common_vendor.t($options.hsl.h),
    E: common_vendor.t($options.hsl.s),
    F: common_vendor.t($options.hsl.l),
    G: common_vendor.o(($event) => $options.copyColor(`hsl(${$options.hsl.h}, ${$options.hsl.s}%, ${$options.hsl.l}%)`)),
    H: common_vendor.t($options.brightness),
    I: common_vendor.t($options.brightnessDesc),
    J: common_vendor.f($data.presetColors, (color, k0, i0) => {
      return {
        a: common_vendor.t(color),
        b: color,
        c: color,
        d: common_vendor.o(($event) => $options.selectPresetColor(color), color)
      };
    }),
    K: $data.showColorPicker
  }, $data.showColorPicker ? {
    L: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    M: $data.currentHue,
    N: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    O: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    P: common_vendor.s($options.canvasCursorStyle),
    Q: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    R: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    S: common_vendor.s($options.hueCursorStyle),
    T: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    U: $data.rgbValues.r,
    V: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    W: $data.rgbValues.g,
    X: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    Y: $data.rgbValues.b,
    Z: $data.selectedColor,
    aa: common_vendor.t($data.selectedColor),
    ab: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    ac: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    ad: common_vendor.o(() => {
    }),
    ae: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d47e139d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/rgb-hex-converter.js.map
