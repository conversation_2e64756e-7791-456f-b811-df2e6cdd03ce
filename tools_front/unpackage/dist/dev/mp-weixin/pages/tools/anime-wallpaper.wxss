/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-7e887b58 {
  display: flex;
}
.flex-1.data-v-7e887b58 {
  flex: 1;
}
.items-center.data-v-7e887b58 {
  align-items: center;
}
.justify-center.data-v-7e887b58 {
  justify-content: center;
}
.justify-between.data-v-7e887b58 {
  justify-content: space-between;
}
.text-center.data-v-7e887b58 {
  text-align: center;
}
.rounded.data-v-7e887b58 {
  border-radius: 3px;
}
.rounded-lg.data-v-7e887b58 {
  border-radius: 6px;
}
.shadow.data-v-7e887b58 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-7e887b58 {
  padding: 16rpx;
}
.m-4.data-v-7e887b58 {
  margin: 16rpx;
}
.mb-4.data-v-7e887b58 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-7e887b58 {
  margin-top: 16rpx;
}
.anime-wallpaper.data-v-7e887b58 {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 32rpx 24rpx;
}
.search-section.data-v-7e887b58 {
  margin-bottom: 32rpx;
}
.search-section .search-box.data-v-7e887b58 {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.search-section .search-box .search-icon.data-v-7e887b58 {
  font-size: 36rpx;
  color: #3b82f6;
  margin-right: 16rpx;
}
.search-section .search-box .search-input.data-v-7e887b58 {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}
.search-section .search-box .search-input.data-v-7e887b58::-webkit-input-placeholder {
  color: #999;
}
.search-section .search-box .search-input.data-v-7e887b58::placeholder {
  color: #999;
}
.filter-section.data-v-7e887b58 {
  margin-bottom: 32rpx;
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.filter-section .filter-header.data-v-7e887b58 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.filter-section .filter-header .filter-icon.data-v-7e887b58 {
  font-size: 32rpx;
  color: #10b981;
  margin-right: 16rpx;
}
.filter-section .filter-header .filter-title.data-v-7e887b58 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.filter-section .category-scroll.data-v-7e887b58 {
  width: 100%;
  white-space: nowrap;
  position: relative;
}
.filter-section .category-scroll.data-v-7e887b58::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -8rpx;
  height: 4rpx;
  background: #f0f0f0;
  border-radius: 2rpx;
}
.filter-section .category-scroll .category-list.data-v-7e887b58 {
  display: inline-flex;
  padding: 8rpx 4rpx;
}
.filter-section .category-scroll .category-list.data-v-7e887b58::-webkit-scrollbar {
  display: none;
}
.filter-section .category-scroll.data-v-7e887b58 ::-webkit-scrollbar {
  display: none;
}
.filter-section .category-scroll .scroll-bar.data-v-7e887b58 {
  position: absolute;
  left: 0;
  right: 0;
  bottom: -8rpx;
  height: 4rpx;
  background: #1a1a1a;
  border-radius: 2rpx;
  transform-origin: left;
  transition: transform 0.3s ease;
}
.filter-section .category-scroll .category-btn.data-v-7e887b58 {
  display: inline-flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  margin-right: 16rpx;
}
.filter-section .category-scroll .category-btn.data-v-7e887b58:last-child {
  margin-right: 4rpx;
}
.filter-section .category-scroll .category-btn .category-icon.data-v-7e887b58 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.filter-section .category-scroll .category-btn .category-name.data-v-7e887b58 {
  font-size: 28rpx;
  color: #333;
  white-space: nowrap;
}
.filter-section .category-scroll .category-btn.active.data-v-7e887b58 {
  background: #1a1a1a;
}
.filter-section .category-scroll .category-btn.active .category-name.data-v-7e887b58 {
  color: white;
}
.filter-section .category-scroll .category-btn.data-v-7e887b58:active {
  transform: scale(0.98);
}
.wallpaper-section .section-title.data-v-7e887b58 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.wallpaper-section .section-title .wallpaper-count.data-v-7e887b58 {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}
.wallpaper-card.data-v-7e887b58 {
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  background: #000;
}
.wallpaper-card .wallpaper-img.data-v-7e887b58 {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
  display: block;
  opacity: 0.9;
}
.wallpaper-card .wallpaper-overlay.data-v-7e887b58 {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
}
.wallpaper-card .wallpaper-overlay .wallpaper-info.data-v-7e887b58 {
  margin-bottom: 16rpx;
}
.wallpaper-card .wallpaper-overlay .wallpaper-info .wallpaper-title.data-v-7e887b58 {
  font-size: 36rpx;
  font-weight: 600;
  color: white;
  display: block;
  margin-bottom: 8rpx;
}
.wallpaper-card .wallpaper-overlay .wallpaper-info .wallpaper-series.data-v-7e887b58 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.wallpaper-card .wallpaper-overlay .wallpaper-info .wallpaper-character.data-v-7e887b58 {
  font-size: 24rpx;
  color: #f472b6;
  display: block;
}
.wallpaper-card .wallpaper-overlay .wallpaper-info .wallpaper-resolution.data-v-7e887b58 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  display: block;
  margin-top: 8rpx;
}
.wallpaper-card .wallpaper-overlay .wallpaper-tags.data-v-7e887b58 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}
.wallpaper-card .wallpaper-overlay .wallpaper-tags .tag.data-v-7e887b58 {
  padding: 8rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 32rpx;
  font-size: 24rpx;
  color: white;
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}
.wallpaper-card .action-buttons.data-v-7e887b58 {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  display: flex;
  gap: 16rpx;
}
.wallpaper-card .action-buttons .action-btn.data-v-7e887b58 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(8rpx);
          backdrop-filter: blur(8rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.wallpaper-card .action-buttons .action-btn .action-icon.data-v-7e887b58 {
  font-size: 36rpx;
  color: white;
}
.wallpaper-card .action-buttons .action-btn.favorite.active.data-v-7e887b58 {
  background: rgba(239, 68, 68, 0.8);
}
.wallpaper-card .action-buttons .action-btn.data-v-7e887b58:active {
  transform: scale(0.9);
}
.wallpaper-card .popularity.data-v-7e887b58 {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
  padding: 8rpx 16rpx;
  border-radius: 32rpx;
}
.wallpaper-card .popularity .popularity-icon.data-v-7e887b58 {
  color: #f59e0b;
  font-size: 24rpx;
  margin-right: 8rpx;
}
.wallpaper-card .popularity .popularity-value.data-v-7e887b58 {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}
.guide-section.data-v-7e887b58 {
  background: white;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-top: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.guide-section .guide-title.data-v-7e887b58 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}
.guide-section .guide-list .guide-item.data-v-7e887b58 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
}
@media (prefers-color-scheme: dark) {
.anime-wallpaper.data-v-7e887b58 {
    background: #18181c;
}
.search-section .search-box.data-v-7e887b58 {
    background: #23232a;
}
.search-section .search-box .search-input.data-v-7e887b58 {
    color: #fafafa;
}
.search-section .search-box .search-input.data-v-7e887b58::-webkit-input-placeholder {
    color: #666;
}
.search-section .search-box .search-input.data-v-7e887b58::placeholder {
    color: #666;
}
.filter-section.data-v-7e887b58 {
    background: #23232a;
}
.filter-section .filter-title.data-v-7e887b58 {
    color: #fafafa;
}
.filter-section .category-scroll.data-v-7e887b58::after {
    background: #2a2a2a;
}
.filter-section .category-scroll .scroll-bar.data-v-7e887b58 {
    background: #8b5cf6;
}
.section-title.data-v-7e887b58 {
    color: #fafafa;
}
.section-title .wallpaper-count.data-v-7e887b58 {
    color: #bdbdbd;
}
.guide-section.data-v-7e887b58 {
    background: #23232a;
}
.guide-section .guide-title.data-v-7e887b58 {
    color: #fafafa;
}
.guide-section .guide-list .guide-item.data-v-7e887b58 {
    color: #bdbdbd;
}
}