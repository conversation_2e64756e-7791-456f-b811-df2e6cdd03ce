/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-604f976a {
  display: flex;
}
.flex-1.data-v-604f976a {
  flex: 1;
}
.items-center.data-v-604f976a {
  align-items: center;
}
.justify-center.data-v-604f976a {
  justify-content: center;
}
.justify-between.data-v-604f976a {
  justify-content: space-between;
}
.text-center.data-v-604f976a {
  text-align: center;
}
.rounded.data-v-604f976a {
  border-radius: 3px;
}
.rounded-lg.data-v-604f976a {
  border-radius: 6px;
}
.shadow.data-v-604f976a {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-604f976a {
  padding: 16rpx;
}
.m-4.data-v-604f976a {
  margin: 16rpx;
}
.mb-4.data-v-604f976a {
  margin-bottom: 16rpx;
}
.mt-4.data-v-604f976a {
  margin-top: 16rpx;
}
.cpu-ladder-tool.data-v-604f976a {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 30rpx;
}
.header-card.data-v-604f976a {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.header-card .header-content.data-v-604f976a {
  display: flex;
  align-items: center;
  color: white;
}
.header-card .header-content .header-icon.data-v-604f976a {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.header-card .header-content .header-info.data-v-604f976a {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-604f976a {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-card .header-content .header-info .header-subtitle.data-v-604f976a {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.header-card .header-content .stats-badge.data-v-604f976a {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
}
.header-card .header-content .stats-badge .stats-text.data-v-604f976a {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}
.search-card.data-v-604f976a {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.search-card .search-section.data-v-604f976a {
  padding: 30rpx;
}
.search-card .search-container.data-v-604f976a {
  margin-bottom: 30rpx;
}
.search-card .search-input-wrapper.data-v-604f976a {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 25rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}
.search-card .search-input-wrapper.data-v-604f976a:focus-within {
  border-color: #6366f1;
  box-shadow: 0 0 0 6rpx rgba(99, 102, 241, 0.1);
}
.search-card .search-input-wrapper .search-icon.data-v-604f976a {
  font-size: 32rpx;
  color: #6b7280;
  margin-right: 20rpx;
}
.search-card .search-input-wrapper .search-input.data-v-604f976a {
  flex: 1;
  font-size: 28rpx;
  color: #1f2937;
  background: transparent;
  border: none;
}
.search-card .search-input-wrapper .clear-btn.data-v-604f976a {
  padding: 8rpx;
  border-radius: 50%;
  background: #e5e7eb;
}
.search-card .search-input-wrapper .clear-btn .clear-icon.data-v-604f976a {
  font-size: 20rpx;
  color: #6b7280;
}
.search-card .filter-section .filter-label.data-v-604f976a {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  display: block;
}
.search-card .filter-section .tier-scroll.data-v-604f976a {
  width: 100%;
}
.search-card .filter-section .tier-list.data-v-604f976a {
  display: flex;
  gap: 16rpx;
  white-space: nowrap;
  padding-bottom: 8rpx;
}
.search-card .filter-section .tier-item.data-v-604f976a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.search-card .filter-section .tier-item.active.data-v-604f976a {
  background: #6366f1;
  border-color: #6366f1;
}
.search-card .filter-section .tier-item.active .tier-name.data-v-604f976a,
.search-card .filter-section .tier-item.active .tier-count.data-v-604f976a {
  color: white;
}
.search-card .filter-section .tier-item .tier-name.data-v-604f976a {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}
.search-card .filter-section .tier-item .tier-count.data-v-604f976a {
  font-size: 20rpx;
  color: #6b7280;
}
.cpu-list-card.data-v-604f976a {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.cpu-list-card .list-header.data-v-604f976a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1rpx solid #e5e7eb;
}
.cpu-list-card .list-header .header-info.data-v-604f976a {
  display: flex;
  align-items: center;
}
.cpu-list-card .list-header .header-info .list-icon.data-v-604f976a {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.cpu-list-card .list-header .header-info .card-title.data-v-604f976a {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}
.cpu-list-card .list-header .sort-controls .sort-btn.data-v-604f976a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
}
.cpu-list-card .list-header .sort-controls .sort-btn .sort-icon.data-v-604f976a {
  font-size: 24rpx;
  color: #6366f1;
}
.cpu-list-card .list-header .sort-controls .sort-btn .sort-text.data-v-604f976a {
  font-size: 24rpx;
  color: #6b7280;
}
.cpu-list-card .cpu-list.data-v-604f976a {
  padding: 20rpx;
}
.cpu-list-card .loading-container.data-v-604f976a {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 30rpx;
}
.cpu-list-card .loading-spinner.data-v-604f976a {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin-604f976a 1s linear infinite;
  margin-bottom: 30rpx;
}
.cpu-list-card .loading-text.data-v-604f976a {
  font-size: 28rpx;
  color: #666;
}
@keyframes spin-604f976a {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.cpu-list-card .cpu-item.data-v-604f976a {
  margin-bottom: 24rpx;
  border-radius: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}
.cpu-list-card .cpu-item.active.data-v-604f976a {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(99, 102, 241, 0.15);
}
.cpu-list-card .cpu-item .cpu-content.data-v-604f976a {
  background: #fafbfc;
  border: 2rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 30rpx;
  transition: all 0.3s ease;
}
.cpu-list-card .cpu-item .cpu-content.data-v-604f976a:hover {
  border-color: #6366f1;
}
.cpu-list-card .cpu-header.data-v-604f976a {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.cpu-list-card .cpu-left.data-v-604f976a {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex: 1;
}
.cpu-list-card .rank-badge.data-v-604f976a {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.cpu-list-card .rank-badge.rank-top.data-v-604f976a {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}
.cpu-list-card .rank-badge.rank-high.data-v-604f976a {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
}
.cpu-list-card .rank-badge.rank-normal.data-v-604f976a {
  background: linear-gradient(135deg, #64748b, #475569);
}
.cpu-list-card .rank-badge .rank-number.data-v-604f976a {
  font-size: 24rpx;
  font-weight: 700;
  color: white;
}
.cpu-list-card .cpu-main-info.data-v-604f976a {
  flex: 1;
}
.cpu-list-card .cpu-main-info .cpu-name.data-v-604f976a {
  font-size: 34rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
  line-height: 1.2;
}
.cpu-list-card .cpu-main-info .cpu-meta.data-v-604f976a {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.cpu-list-card .cpu-main-info .tier-badge.data-v-604f976a {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 600;
}
.cpu-list-card .cpu-main-info .tier-badge.tier-flagship.data-v-604f976a {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
  color: #92400e;
}
.cpu-list-card .cpu-main-info .tier-badge.tier-high.data-v-604f976a {
  background: linear-gradient(135deg, #fecaca, #f87171);
  color: #991b1b;
}
.cpu-list-card .cpu-main-info .tier-badge.tier-mainstream.data-v-604f976a {
  background: linear-gradient(135deg, #dbeafe, #60a5fa);
  color: #1e40af;
}
.cpu-list-card .cpu-main-info .tier-badge.tier-entry.data-v-604f976a {
  background: linear-gradient(135deg, #d1fae5, #6ee7b7);
  color: #065f46;
}
.cpu-list-card .cpu-main-info .cpu-brand.data-v-604f976a {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 500;
}
.cpu-list-card .cpu-right.data-v-604f976a {
  text-align: right;
  flex-shrink: 0;
}
.cpu-list-card .cpu-right .score-value.data-v-604f976a {
  display: block;
  font-size: 56rpx;
  font-weight: 800;
  margin-bottom: 4rpx;
}
.cpu-list-card .cpu-right .score-value.score-flagship.data-v-604f976a {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.cpu-list-card .cpu-right .score-value.score-high.data-v-604f976a {
  background: linear-gradient(135deg, #ea580c, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.cpu-list-card .cpu-right .score-value.score-mainstream.data-v-604f976a {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.cpu-list-card .cpu-right .score-value.score-entry.data-v-604f976a {
  color: #6b7280;
}
.cpu-list-card .cpu-right .score-label.data-v-604f976a {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 500;
}
.cpu-list-card .cpu-specs.data-v-604f976a {
  margin-bottom: 24rpx;
}
.cpu-list-card .cpu-specs .spec-row.data-v-604f976a {
  display: flex;
  gap: 24rpx;
  margin-bottom: 12rpx;
}
.cpu-list-card .cpu-specs .spec-row.data-v-604f976a:last-child {
  margin-bottom: 0;
}
.cpu-list-card .cpu-specs .spec-item.data-v-604f976a {
  display: flex;
  align-items: center;
  gap: 8rpx;
  flex: 1;
  padding: 16rpx 20rpx;
  background: white;
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
}
.cpu-list-card .cpu-specs .spec-item.cores.data-v-604f976a {
  flex: 2;
}
.cpu-list-card .cpu-specs .spec-item .spec-icon.data-v-604f976a {
  font-size: 20rpx;
}
.cpu-list-card .cpu-specs .spec-item .spec-label.data-v-604f976a {
  font-size: 22rpx;
  color: #6b7280;
}
.cpu-list-card .cpu-specs .spec-item .spec-value.data-v-604f976a {
  font-size: 24rpx;
  font-weight: 600;
  color: #1f2937;
  margin-left: auto;
}
.cpu-list-card .cpu-specs .spec-item .spec-value.price.data-v-604f976a {
  color: #10b981;
}
.cpu-list-card .cpu-specs .spec-item .spec-value.power.data-v-604f976a {
  color: #f59e0b;
}
.cpu-list-card .performance-section.data-v-604f976a {
  margin-bottom: 20rpx;
}
.cpu-list-card .performance-section .progress-header.data-v-604f976a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.cpu-list-card .performance-section .progress-header .progress-label.data-v-604f976a {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}
.cpu-list-card .performance-section .progress-header .progress-percentage.data-v-604f976a {
  font-size: 24rpx;
  color: #1f2937;
  font-weight: 600;
}
.cpu-list-card .performance-section .progress-bar.data-v-604f976a {
  width: 100%;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}
.cpu-list-card .performance-section .progress-bar .progress-fill.data-v-604f976a {
  height: 100%;
  border-radius: 6rpx;
  transition: width 0.8s ease;
}
.cpu-list-card .performance-section .progress-bar .progress-fill.progress-flagship.data-v-604f976a {
  background: linear-gradient(90deg, #dc2626, #ef4444);
}
.cpu-list-card .performance-section .progress-bar .progress-fill.progress-high.data-v-604f976a {
  background: linear-gradient(90deg, #ea580c, #f97316);
}
.cpu-list-card .performance-section .progress-bar .progress-fill.progress-mainstream.data-v-604f976a {
  background: linear-gradient(90deg, #2563eb, #3b82f6);
}
.cpu-list-card .performance-section .progress-bar .progress-fill.progress-entry.data-v-604f976a {
  background: linear-gradient(90deg, #6b7280, #9ca3af);
}
.cpu-list-card .value-indicators.data-v-604f976a {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cpu-list-card .value-indicators .indicator-item.data-v-604f976a {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.cpu-list-card .value-indicators .indicator-item .indicator-label.data-v-604f976a {
  font-size: 22rpx;
  color: #6b7280;
}
.cpu-list-card .value-indicators .indicator-item .indicator-stars.data-v-604f976a {
  display: flex;
  gap: 4rpx;
}
.cpu-list-card .value-indicators .indicator-item .indicator-stars .star.data-v-604f976a {
  font-size: 20rpx;
  color: #e5e7eb;
}
.cpu-list-card .value-indicators .indicator-item .indicator-stars .star.active.data-v-604f976a {
  color: #fbbf24;
}
.cpu-list-card .value-indicators .indicator-item .recommendation.data-v-604f976a {
  font-size: 22rpx;
  font-weight: 600;
  color: #10b981;
}
.cpu-list-card .empty-state.data-v-604f976a {
  text-align: center;
  padding: 80rpx 40rpx;
}
.cpu-list-card .empty-state .empty-icon.data-v-604f976a {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  display: block;
}
.cpu-list-card .empty-state .empty-title.data-v-604f976a {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
  display: block;
}
.cpu-list-card .empty-state .empty-desc.data-v-604f976a {
  font-size: 26rpx;
  color: #6b7280;
  display: block;
}
.detail-card.data-v-604f976a {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.detail-card .detail-header.data-v-604f976a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f0f9ff, #dbeafe);
  border-bottom: 1rpx solid #e5e7eb;
}
.detail-card .detail-header .detail-icon.data-v-604f976a {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.detail-card .detail-header .detail-title.data-v-604f976a {
  flex: 1;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.detail-card .detail-header .close-btn.data-v-604f976a {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}
.detail-card .detail-header .close-btn .close-icon.data-v-604f976a {
  font-size: 24rpx;
  color: #6b7280;
}
.detail-card .detail-content.data-v-604f976a {
  padding: 30rpx;
}
.detail-card .detail-content .detail-grid.data-v-604f976a {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}
.detail-card .detail-content .detail-item.data-v-604f976a {
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}
.detail-card .detail-content .detail-item .detail-label.data-v-604f976a {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.detail-card .detail-content .detail-item .detail-value.data-v-604f976a {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}
.guide-card.data-v-604f976a,
.update-card.data-v-604f976a {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.guide-card .guide-header.data-v-604f976a,
.update-card .guide-header.data-v-604f976a {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
}
.guide-card .guide-header .guide-icon.data-v-604f976a,
.update-card .guide-header .guide-icon.data-v-604f976a {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.guide-card .guide-header .guide-title.data-v-604f976a,
.update-card .guide-header .guide-title.data-v-604f976a {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}
.guide-card .guide-content.data-v-604f976a,
.update-card .guide-content.data-v-604f976a {
  padding: 30rpx;
}
.guide-card .guide-section.data-v-604f976a,
.update-card .guide-section.data-v-604f976a {
  margin-bottom: 30rpx;
}
.guide-card .guide-section.data-v-604f976a:last-child,
.update-card .guide-section.data-v-604f976a:last-child {
  margin-bottom: 0;
}
.guide-card .guide-section .section-title.data-v-604f976a,
.update-card .guide-section .section-title.data-v-604f976a {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
}
.guide-card .guide-section .guide-list.data-v-604f976a,
.update-card .guide-section .guide-list.data-v-604f976a {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.guide-card .guide-section .guide-item.data-v-604f976a,
.update-card .guide-section .guide-item.data-v-604f976a {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.guide-card .guide-section .guide-item .grade-badge.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge.data-v-604f976a {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  min-width: 80rpx;
  text-align: center;
}
.guide-card .guide-section .guide-item .grade-badge.flagship.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge.flagship.data-v-604f976a {
  background: linear-gradient(135deg, #fef3c7, #fbbf24);
}
.guide-card .guide-section .guide-item .grade-badge.high-end.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge.high-end.data-v-604f976a {
  background: linear-gradient(135deg, #fecaca, #f87171);
}
.guide-card .guide-section .guide-item .grade-badge.mainstream.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge.mainstream.data-v-604f976a {
  background: linear-gradient(135deg, #dbeafe, #60a5fa);
}
.guide-card .guide-section .guide-item .grade-badge.entry.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge.entry.data-v-604f976a {
  background: linear-gradient(135deg, #d1fae5, #6ee7b7);
}
.guide-card .guide-section .guide-item .grade-badge .grade-text.data-v-604f976a,
.update-card .guide-section .guide-item .grade-badge .grade-text.data-v-604f976a {
  font-size: 22rpx;
  font-weight: 600;
  color: #1f2937;
}
.guide-card .guide-section .guide-item .grade-desc.data-v-604f976a,
.update-card .guide-section .guide-item .grade-desc.data-v-604f976a {
  flex: 1;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.4;
}
.guide-card .guide-section .advice-list.data-v-604f976a,
.update-card .guide-section .advice-list.data-v-604f976a {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.guide-card .guide-section .advice-list .advice-item.data-v-604f976a,
.update-card .guide-section .advice-list .advice-item.data-v-604f976a {
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.5;
}
.update-card .update-content.data-v-604f976a {
  padding: 30rpx;
}
.update-card .update-content .update-info.data-v-604f976a {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.update-card .update-content .update-info .update-time.data-v-604f976a,
.update-card .update-content .update-info .update-source.data-v-604f976a {
  font-size: 24rpx;
  color: #6b7280;
}
.update-card .update-content .update-note.data-v-604f976a {
  font-size: 22rpx;
  color: #9ca3af;
  text-align: center;
}
.placeholder-style.data-v-604f976a {
  color: #9ca3af;
}