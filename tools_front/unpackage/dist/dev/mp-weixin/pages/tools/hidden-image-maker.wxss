/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-ed6801f4 {
  display: flex;
}
.flex-1.data-v-ed6801f4 {
  flex: 1;
}
.items-center.data-v-ed6801f4 {
  align-items: center;
}
.justify-center.data-v-ed6801f4 {
  justify-content: center;
}
.justify-between.data-v-ed6801f4 {
  justify-content: space-between;
}
.text-center.data-v-ed6801f4 {
  text-align: center;
}
.rounded.data-v-ed6801f4 {
  border-radius: 3px;
}
.rounded-lg.data-v-ed6801f4 {
  border-radius: 6px;
}
.shadow.data-v-ed6801f4 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-ed6801f4 {
  padding: 16rpx;
}
.m-4.data-v-ed6801f4 {
  margin: 16rpx;
}
.mb-4.data-v-ed6801f4 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-ed6801f4 {
  margin-top: 16rpx;
}
.container.data-v-ed6801f4 {
  min-height: 100vh;
  background: #ffffff;
  position: relative;
}
.gradient-bg.data-v-ed6801f4 {
  display: none;
}
.header.data-v-ed6801f4 {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
}
.title-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-title.data-v-ed6801f4 {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 16rpx;
}
.sub-title.data-v-ed6801f4 {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 400;
}
.main-content.data-v-ed6801f4 {
  padding: 0 40rpx 40rpx;
}

/* 上传区域样式 */
.upload-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.section-header.data-v-ed6801f4 {
  margin-bottom: 32rpx;
}
.section-title.data-v-ed6801f4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 8rpx;
}
.section-desc.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #718096;
}
.upload-area.data-v-ed6801f4 {
  border: 3rpx dashed #cbd5e0;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
  background: rgba(247, 250, 252, 0.8);
}
.upload-placeholder.data-v-ed6801f4 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.upload-icon.data-v-ed6801f4 {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}
.upload-text.data-v-ed6801f4 {
  font-size: 32rpx;
  color: #4a5568;
  font-weight: 500;
  display: block;
  margin-bottom: 12rpx;
}
.upload-hint.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #a0aec0;
}
.image-preview.data-v-ed6801f4 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.preview-image.data-v-ed6801f4 {
  max-width: 100%;
  max-height: 400rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}
.image-info.data-v-ed6801f4 {
  background: rgba(99, 102, 241, 0.1);
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(99, 102, 241, 0.2);
}
.info-text.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #6366f1;
  font-weight: 500;
}

/* 输入区域样式 */
.input-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.input-container.data-v-ed6801f4 {
  position: relative;
  margin-bottom: 32rpx;
  width: 100%;
  box-sizing: border-box;
}
.input-container .secret-input.data-v-ed6801f4 {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  padding-bottom: 48rpx;
  /* 为字数统计留出空间 */
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #2d3748;
  background: #ffffff;
  box-sizing: border-box;
  resize: none;
  transition: all 0.3s ease;
  word-break: break-all;
  white-space: pre-wrap;
}
.input-container .secret-input.data-v-ed6801f4:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 6rpx rgba(99, 102, 241, 0.1);
}
.input-container .char-count.data-v-ed6801f4 {
  position: absolute;
  bottom: 16rpx;
  right: 24rpx;
  font-size: 22rpx;
  color: #a0aec0;
  background: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 高级选项样式 */
.advanced-options.data-v-ed6801f4 {
  border-top: 1rpx solid #e2e8f0;
  padding-top: 32rpx;
}
.option-item.data-v-ed6801f4 {
  margin-bottom: 32rpx;
}
.option-item.data-v-ed6801f4:last-child {
  margin-bottom: 0;
}
.option-label.data-v-ed6801f4 {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}
.strength-buttons.data-v-ed6801f4 {
  display: flex;
  gap: 16rpx;
}
.strength-btn.data-v-ed6801f4 {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  background: #ffffff;
}
.strength-btn.active.data-v-ed6801f4 {
  border-color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}
.strength-btn .btn-text.data-v-ed6801f4 {
  font-size: 26rpx;
  color: #4a5568;
  font-weight: 500;
}
.strength-btn.active .btn-text.data-v-ed6801f4 {
  color: #6366f1;
}
.toggle-option.data-v-ed6801f4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.toggle-label.data-v-ed6801f4 {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 500;
}
.toggle-switch.data-v-ed6801f4 {
  width: 100rpx;
  height: 56rpx;
  background: #cbd5e0;
  border-radius: 28rpx;
  position: relative;
  transition: all 0.3s ease;
}
.toggle-switch.active.data-v-ed6801f4 {
  background: #6366f1;
}
.toggle-thumb.data-v-ed6801f4 {
  width: 48rpx;
  height: 48rpx;
  background: #ffffff;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.toggle-switch.active .toggle-thumb.data-v-ed6801f4 {
  transform: translateX(44rpx);
}

/* 处理按钮样式 */
.process-section.data-v-ed6801f4 {
  margin-bottom: 32rpx;
}
.process-btn.data-v-ed6801f4 {
  width: 100%;
  padding: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}
.process-btn.data-v-ed6801f4:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}
.process-btn.processing.data-v-ed6801f4 {
  background: #a0aec0;
  cursor: not-allowed;
}
.loading-spinner.data-v-ed6801f4 {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  animation: spin-ed6801f4 1s linear infinite;
  margin-right: 16rpx;
}
@keyframes spin-ed6801f4 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
.progress-info.data-v-ed6801f4 {
  margin-top: 24rpx;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16rpx;
}
.progress-bar.data-v-ed6801f4 {
  width: 100%;
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.progress-fill.data-v-ed6801f4 {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}
.progress-text.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #4a5568;
  display: block;
  margin-bottom: 8rpx;
}
.time-info.data-v-ed6801f4 {
  font-size: 22rpx;
  color: #718096;
}

/* 结果区域样式 */
.result-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.comparison-container.data-v-ed6801f4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  gap: 16rpx;
}
.comparison-item.data-v-ed6801f4 {
  flex: 1;
  text-align: center;
}
.comparison-label.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #718096;
  display: block;
  margin-bottom: 16rpx;
}
.comparison-image.data-v-ed6801f4 {
  width: 100%;
  max-height: 300rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.comparison-arrow.data-v-ed6801f4 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
}
.arrow-icon.data-v-ed6801f4 {
  font-size: 32rpx;
}
.process-info.data-v-ed6801f4 {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}
.info-row.data-v-ed6801f4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid rgba(226, 232, 240, 0.6);
}
.info-row.data-v-ed6801f4:last-child {
  border-bottom: none;
}
.info-label.data-v-ed6801f4 {
  font-size: 26rpx;
  color: #4a5568;
}
.info-value.data-v-ed6801f4 {
  font-size: 26rpx;
  color: #2d3748;
  font-weight: 500;
}
.action-buttons.data-v-ed6801f4 {
  display: flex;
  gap: 12rpx;
  margin-top: 32rpx;
}
.action-btn.data-v-ed6801f4 {
  flex: 1;
  padding: 20rpx 16rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}
.action-btn.primary.data-v-ed6801f4 {
  background: #10b981;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}
.action-btn.secondary.data-v-ed6801f4 {
  background: #3b82f6;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}
.action-btn.tertiary.data-v-ed6801f4 {
  background: #f3f4f6;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.action-btn.data-v-ed6801f4:active {
  transform: translateY(1rpx);
}

/* 解密区域样式 */
.decrypt-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.decrypt-btn.data-v-ed6801f4 {
  width: 100%;
  padding: 32rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  border: none;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 32rpx;
  transition: all 0.3s ease;
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.3);
}
.decrypt-btn.data-v-ed6801f4:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.decrypt-btn.processing.data-v-ed6801f4 {
  background: #a0aec0;
  cursor: not-allowed;
}
.decrypt-result.data-v-ed6801f4 {
  margin-top: 32rpx;
  padding: 32rpx;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16rpx;
  border: 1rpx solid rgba(16, 185, 129, 0.2);
}
.result-content.data-v-ed6801f4 {
  padding: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  margin: 24rpx 0;
  min-height: 120rpx;
}
.decrypted-text.data-v-ed6801f4 {
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.6;
  word-break: break-all;
  white-space: pre-wrap;
}
.result-actions.data-v-ed6801f4 {
  display: flex;
  gap: 16rpx;
}
.copy-btn.data-v-ed6801f4, .reset-btn.data-v-ed6801f4 {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.copy-btn.data-v-ed6801f4 {
  background: #10b981;
  color: #ffffff;
}
.reset-btn.data-v-ed6801f4 {
  background: #ffffff;
  border: 1rpx solid #10b981;
  color: #10b981;
}
.copy-btn.data-v-ed6801f4:active, .reset-btn.data-v-ed6801f4:active {
  transform: translateY(2rpx);
  opacity: 0.9;
}

/* 说明区域样式 */
.instructions-section.data-v-ed6801f4 {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.instruction-content.data-v-ed6801f4 {
  margin-top: 24rpx;
}
.instruction-item.data-v-ed6801f4 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 16rpx;
  border-left: 6rpx solid #6366f1;
}
.instruction-item.data-v-ed6801f4:last-child {
  margin-bottom: 0;
}
.instruction-icon.data-v-ed6801f4 {
  font-size: 32rpx;
  margin-right: 20rpx;
  margin-top: 4rpx;
}
.instruction-text.data-v-ed6801f4 {
  flex: 1;
}
.instruction-title.data-v-ed6801f4 {
  font-size: 28rpx;
  color: #2d3748;
  font-weight: 600;
  display: block;
  margin-bottom: 8rpx;
}
.instruction-desc.data-v-ed6801f4 {
  font-size: 24rpx;
  color: #718096;
  line-height: 1.6;
}

/* 隐藏Canvas */
.hidden-canvas.data-v-ed6801f4 {
  position: fixed;
  left: -9999rpx;
  opacity: 0;
  z-index: -1;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.comparison-container.data-v-ed6801f4 {
    flex-direction: column;
}
.comparison-arrow.data-v-ed6801f4 {
    transform: rotate(90deg);
    margin: 16rpx 0;
}
.action-buttons.data-v-ed6801f4 {
    flex-direction: column;
    gap: 8rpx;
}
.action-btn.data-v-ed6801f4 {
    min-width: auto;
}
}