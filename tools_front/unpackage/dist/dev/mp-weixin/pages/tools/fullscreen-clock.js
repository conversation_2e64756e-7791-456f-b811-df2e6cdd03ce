"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      currentTime: {
        hours: "00",
        minutes: "00",
        seconds: "00"
      },
      currentDate: {
        date: "",
        week: ""
      },
      clockMode: "digital",
      // digital, analog, flip
      currentThemeIndex: 0,
      showControls: false,
      isAlwaysOn: false,
      isLandscape: false,
      showTimePicker: false,
      pickerValue: [0, 0],
      timer: null,
      clockModes: [
        { value: "digital", label: "数字" },
        { value: "analog", label: "模拟" },
        { value: "flip", label: "翻页" }
      ],
      themes: [
        {
          name: "经典黑",
          background: "linear-gradient(135deg, #000000, #1a1a1a)",
          textColor: "#ffffff",
          secondaryColor: "#cccccc",
          accentColor: "#1a1a1a"
        },
        {
          name: "深蓝夜",
          background: "linear-gradient(135deg, #0c0c2a, #1a1a4a)",
          textColor: "#ffffff",
          secondaryColor: "#a0a0ff",
          accentColor: "#1a1a4a"
        },
        {
          name: "暖橙",
          background: "linear-gradient(135deg, #ff9a56, #ff6b6b)",
          textColor: "#ffffff",
          secondaryColor: "#ffe0cc",
          accentColor: "#ff6b6b"
        },
        {
          name: "清新绿",
          background: "linear-gradient(135deg, #00b894, #00cec9)",
          textColor: "#ffffff",
          secondaryColor: "#e0f7fa",
          accentColor: "#00cec9"
        },
        {
          name: "优雅紫",
          background: "linear-gradient(135deg, #6c5ce7, #a29bfe)",
          textColor: "#ffffff",
          secondaryColor: "#e0e0ff",
          accentColor: "#a29bfe"
        }
      ],
      hours: Array.from({ length: 24 }, (_, i) => String(i).padStart(2, "0")),
      minutes: Array.from({ length: 60 }, (_, i) => String(i).padStart(2, "0"))
    };
  },
  computed: {
    currentTheme() {
      return this.themes[this.currentThemeIndex];
    },
    hourAngle() {
      const hours = parseInt(this.currentTime.hours) % 12;
      const minutes = parseInt(this.currentTime.minutes);
      return hours * 30 + minutes * 0.5 - 90;
    },
    minuteAngle() {
      const minutes = parseInt(this.currentTime.minutes);
      const seconds = parseInt(this.currentTime.seconds);
      return minutes * 6 + seconds * 0.1 - 90;
    },
    secondAngle() {
      const seconds = parseInt(this.currentTime.seconds);
      return seconds * 6 - 90;
    }
  },
  onLoad() {
    this.startClock();
    this.loadSettings();
    common_vendor.index.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#000000",
      animation: {
        duration: 0,
        timingFunc: "linear"
      }
    });
    common_vendor.index.hideNavigationBarLoading();
    common_vendor.index.setKeepScreenOn({
      keepScreenOn: true
    });
  },
  onShow() {
    common_vendor.index.hideHomeButton();
  },
  onUnload() {
    this.stopClock();
    this.saveSettings();
    common_vendor.index.setKeepScreenOn({
      keepScreenOn: false
    });
  },
  methods: {
    startClock() {
      this.updateTime();
      this.timer = setInterval(() => {
        this.updateTime();
      }, 1e3);
    },
    stopClock() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    updateTime() {
      const now = /* @__PURE__ */ new Date();
      this.currentTime = {
        hours: String(now.getHours()).padStart(2, "0"),
        minutes: String(now.getMinutes()).padStart(2, "0"),
        seconds: String(now.getSeconds()).padStart(2, "0")
      };
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const weekDays = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
      if (this.isLandscape) {
        this.currentDate = {
          date: `${weekDays[now.getDay()]} ${year}.${month}.${day}`,
          week: ""
        };
      } else {
        this.currentDate = {
          date: `${year}年${month}月${day}日`,
          week: weekDays[now.getDay()]
        };
      }
    },
    switchMode(mode) {
      this.clockMode = mode;
      this.saveSettings();
    },
    switchTheme(index) {
      this.currentThemeIndex = index;
      this.saveSettings();
    },
    toggleControls() {
      this.showControls = !this.showControls;
    },
    toggleAlwaysOn() {
      this.isAlwaysOn = !this.isAlwaysOn;
      common_vendor.index.setKeepScreenOn({
        keepScreenOn: this.isAlwaysOn,
        success: () => {
          common_vendor.index.showToast({
            title: this.isAlwaysOn ? "屏幕常亮已开启" : "屏幕常亮已关闭",
            icon: "none"
          });
        }
      });
    },
    showTimePickerModal() {
      this.showTimePicker = true;
    },
    closeTimePicker() {
      this.showTimePicker = false;
    },
    onPickerChange(e) {
      this.pickerValue = e.detail.value;
    },
    setAlarm() {
      const hour = this.hours[this.pickerValue[0]];
      const minute = this.minutes[this.pickerValue[1]];
      common_vendor.index.showToast({
        title: `闹钟设置为 ${hour}:${minute}`,
        icon: "success"
      });
      this.closeTimePicker();
    },
    loadSettings() {
      const settings = common_vendor.index.getStorageSync("clockSettings");
      if (settings) {
        this.clockMode = settings.clockMode || "digital";
        this.currentThemeIndex = settings.themeIndex || 0;
        this.isAlwaysOn = settings.isAlwaysOn || false;
        this.isLandscape = settings.isLandscape || false;
      }
    },
    saveSettings() {
      const settings = {
        clockMode: this.clockMode,
        themeIndex: this.currentThemeIndex,
        isAlwaysOn: this.isAlwaysOn,
        isLandscape: this.isLandscape
      };
      common_vendor.index.setStorageSync("clockSettings", settings);
    },
    toggleOrientation() {
      this.isLandscape = !this.isLandscape;
      this.showControls = false;
      common_vendor.index.setPageOrientation({
        orientation: this.isLandscape ? "landscape" : "portrait",
        success: () => {
          common_vendor.index.showToast({
            title: this.isLandscape ? "已切换到横屏模式" : "已切换到竖屏模式",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "切换方向失败",
            icon: "none"
          });
        }
      });
      this.saveSettings();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.clockMode === "digital"
  }, $data.clockMode === "digital" ? {
    b: common_vendor.t($data.currentTime.hours),
    c: common_vendor.t($data.currentTime.minutes),
    d: $options.currentTheme.textColor,
    e: common_vendor.t($data.currentDate.date),
    f: common_vendor.t($data.currentDate.week),
    g: $options.currentTheme.secondaryColor
  } : {}, {
    h: $data.clockMode === "analog"
  }, $data.clockMode === "analog" ? {
    i: common_vendor.f(12, (i, k0, i0) => {
      return {
        a: i,
        b: `rotate(${i * 30}deg)`
      };
    }),
    j: $options.currentTheme.textColor,
    k: common_vendor.f(60, (i, k0, i0) => {
      return {
        a: `min-${i}`,
        b: `rotate(${i * 6}deg)`
      };
    }),
    l: $options.currentTheme.secondaryColor,
    m: `rotate(${$options.hourAngle}deg)`,
    n: $options.currentTheme.textColor,
    o: `rotate(${$options.minuteAngle}deg)`,
    p: $options.currentTheme.textColor,
    q: `rotate(${$options.secondAngle}deg)`,
    r: $options.currentTheme.accentColor,
    s: $options.currentTheme.textColor,
    t: $options.currentTheme.textColor,
    v: common_vendor.t($data.currentTime.hours),
    w: common_vendor.t($data.currentTime.minutes),
    x: common_vendor.t($data.currentTime.seconds),
    y: $options.currentTheme.textColor,
    z: common_vendor.t($data.currentDate.date),
    A: $options.currentTheme.secondaryColor
  } : {}, {
    B: $data.clockMode === "flip"
  }, $data.clockMode === "flip" ? {
    C: common_vendor.t($data.currentTime.hours),
    D: $options.currentTheme.textColor,
    E: $options.currentTheme.secondaryColor,
    F: $options.currentTheme.textColor,
    G: common_vendor.t($data.currentTime.minutes),
    H: $options.currentTheme.textColor,
    I: $options.currentTheme.secondaryColor,
    J: $options.currentTheme.textColor,
    K: common_vendor.t($data.currentTime.seconds),
    L: $options.currentTheme.textColor,
    M: $options.currentTheme.secondaryColor
  } : {}, {
    N: common_vendor.f($data.clockModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.label),
        b: mode.value,
        c: common_vendor.n({
          active: $data.clockMode === mode.value
        }),
        d: common_vendor.o(($event) => $options.switchMode(mode.value), mode.value)
      };
    }),
    O: common_vendor.f($data.themes, (theme, index, i0) => {
      return {
        a: index,
        b: common_vendor.n({
          active: $data.currentThemeIndex === index
        }),
        c: theme.accentColor,
        d: common_vendor.o(($event) => $options.switchTheme(index), index)
      };
    }),
    P: common_vendor.t($data.isAlwaysOn ? "取消常亮" : "屏幕常亮"),
    Q: common_vendor.o((...args) => $options.toggleAlwaysOn && $options.toggleAlwaysOn(...args)),
    R: common_vendor.t($data.isLandscape ? "竖屏模式" : "横屏模式"),
    S: common_vendor.o((...args) => $options.toggleOrientation && $options.toggleOrientation(...args)),
    T: !$data.showControls ? 1 : "",
    U: common_vendor.t($data.showControls ? "×" : "⚙️"),
    V: common_vendor.o((...args) => $options.toggleControls && $options.toggleControls(...args)),
    W: $data.showTimePicker
  }, $data.showTimePicker ? {
    X: common_vendor.o((...args) => $options.closeTimePicker && $options.closeTimePicker(...args)),
    Y: common_vendor.f($data.hours, (hour, k0, i0) => {
      return {
        a: common_vendor.t(hour),
        b: hour
      };
    }),
    Z: common_vendor.f($data.minutes, (minute, k0, i0) => {
      return {
        a: common_vendor.t(minute),
        b: minute
      };
    }),
    aa: $data.pickerValue,
    ab: common_vendor.o((...args) => $options.onPickerChange && $options.onPickerChange(...args)),
    ac: common_vendor.o((...args) => $options.closeTimePicker && $options.closeTimePicker(...args)),
    ad: common_vendor.o((...args) => $options.setAlarm && $options.setAlarm(...args)),
    ae: common_vendor.o(() => {
    }),
    af: common_vendor.o((...args) => $options.closeTimePicker && $options.closeTimePicker(...args))
  } : {}, {
    ag: common_vendor.n({
      "landscape-mode": $data.isLandscape
    }),
    ah: $options.currentTheme.background
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9fc4fa69"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/fullscreen-clock.js.map
