
.min-h-screen.data-v-cfac2752 {
  min-height: 100vh;
}
.bg-gray-50.data-v-cfac2752 {
  background-color: #f9fafb;
}
.bg-white.data-v-cfac2752 {
  background-color: white;
}
.shadow-sm.data-v-cfac2752 {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}
.text-gray-600.data-v-cfac2752 {
  color: #4b5563;
}
.text-gray-900.data-v-cfac2752 {
  color: #111827;
}
.text-sky-500.data-v-cfac2752 {
  color: #0ea5e9;
}
.text-sky-700.data-v-cfac2752 {
  color: #0369a1;
}
.text-sky-800.data-v-cfac2752 {
  color: #075985;
}
.text-blue-700.data-v-cfac2752 {
  color: #1d4ed8;
}
.border-gray-200.data-v-cfac2752 {
  border-color: #e5e7eb;
}
.border-sky-500.data-v-cfac2752 {
  border-color: #0ea5e9;
}
.bg-sky-50.data-v-cfac2752 {
  background-color: #f0f9ff;
}
.bg-sky-100.data-v-cfac2752 {
  background-color: #e0f2fe;
}
.bg-blue-50.data-v-cfac2752 {
  background-color: #eff6ff;
}
.bg-blue-100.data-v-cfac2752 {
  background-color: #dbeafe;
}
.bg-blue-600.data-v-cfac2752 {
  background-color: #2563eb;
}
.bg-blue-700.data-v-cfac2752 {
  background-color: #1d4ed8;
}
.bg-cyan-50.data-v-cfac2752 {
  background-color: #ecfeff;
}
.bg-cyan-100.data-v-cfac2752 {
  background-color: #cffafe;
}
.bg-indigo-50.data-v-cfac2752 {
  background-color: #eef2ff;
}
.bg-indigo-100.data-v-cfac2752 {
  background-color: #e0e7ff;
}
.rounded-lg.data-v-cfac2752 {
  border-radius: 0.5rem;
}
.rounded-md.data-v-cfac2752 {
  border-radius: 0.375rem;
}
.border-l-4.data-v-cfac2752 {
  border-left-width: 4px;
}
.hover-bg-sky-100.data-v-cfac2752:hover {
  background-color: #e0f2fe;
}
.hover-bg-blue-100.data-v-cfac2752:hover {
  background-color: #dbeafe;
}
.hover-bg-blue-700.data-v-cfac2752:hover {
  background-color: #1d4ed8;
}
.hover-bg-cyan-100.data-v-cfac2752:hover {
  background-color: #cffafe;
}
.hover-bg-indigo-100.data-v-cfac2752:hover {
  background-color: #e0e7ff;
}
.transition-colors.data-v-cfac2752 {
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.grid.data-v-cfac2752 {
  display: grid;
}
.grid-cols-2.data-v-cfac2752 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-4.data-v-cfac2752 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.gap-3.data-v-cfac2752 {
  gap: 0.75rem;
}
.gap-4.data-v-cfac2752 {
  gap: 1rem;
}
.space-y-2.data-v-cfac2752 {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.space-y-4.data-v-cfac2752 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.p-3.data-v-cfac2752 {
  padding: 0.75rem;
}
.p-4.data-v-cfac2752 {
  padding: 1rem;
}
.p-6.data-v-cfac2752 {
  padding: 1.5rem;
}
.px-4.data-v-cfac2752 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-2.data-v-cfac2752 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.mr-2.data-v-cfac2752 {
  margin-right: 0.5rem;
}
.mr-4.data-v-cfac2752 {
  margin-right: 1rem;
}
.mb-4.data-v-cfac2752 {
  margin-bottom: 1rem;
}
.w-4.data-v-cfac2752 {
  width: 1rem;
}
.h-4.data-v-cfac2752 {
  height: 1rem;
}
.w-5.data-v-cfac2752 {
  width: 1.25rem;
}
.h-5.data-v-cfac2752 {
  height: 1.25rem;
}
.w-6.data-v-cfac2752 {
  width: 1.5rem;
}
.h-6.data-v-cfac2752 {
  height: 1.5rem;
}
.w-full.data-v-cfac2752 {
  width: 100%;
}
.flex.data-v-cfac2752 {
  display: flex;
}
.items-center.data-v-cfac2752 {
  align-items: center;
}
.justify-between.data-v-cfac2752 {
  justify-content: space-between;
}
.justify-center.data-v-cfac2752 {
  justify-content: center;
}
.text-center.data-v-cfac2752 {
  text-align: center;
}
.text-sm.data-v-cfac2752 {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-base.data-v-cfac2752 {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg.data-v-cfac2752 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-3xl.data-v-cfac2752 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.font-medium.data-v-cfac2752 {
  font-weight: 500;
}
.font-semibold.data-v-cfac2752 {
  font-weight: 600;
}
.font-mono.data-v-cfac2752 {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}
.cursor-pointer.data-v-cfac2752 {
  cursor: pointer;
}
.whitespace-pre.data-v-cfac2752 {
  white-space: pre;
}
.whitespace-pre-wrap.data-v-cfac2752 {
  white-space: pre-wrap;
}
.border.data-v-cfac2752 {
  border-width: 1px;
}
.border-b.data-v-cfac2752 {
  border-bottom-width: 1px;
}
.text-white.data-v-cfac2752 {
  color: white;
}
