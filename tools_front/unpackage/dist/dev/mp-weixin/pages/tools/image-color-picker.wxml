<view class="image-color-picker data-v-61ae3098"><view class="container data-v-61ae3098"><view class="header-section data-v-61ae3098"><view class="icon-wrapper data-v-61ae3098"><text class="tool-icon data-v-61ae3098">🎨</text></view><text class="tool-title data-v-61ae3098">图片颜色提取器</text><text class="tool-desc data-v-61ae3098">从图片中智能提取主要颜色，点击图片任意位置可直接取色</text></view><view class="upload-section data-v-61ae3098"><view wx:if="{{a}}" class="upload-area data-v-61ae3098" bindtap="{{b}}"><text class="upload-icon data-v-61ae3098">📷</text><text class="upload-title data-v-61ae3098">选择图片</text><text class="upload-desc data-v-61ae3098">支持 JPG、PNG、WebP 格式</text></view><view wx:if="{{c}}" class="image-preview data-v-61ae3098"><view class="preview-wrapper data-v-61ae3098"><image src="{{d}}" class="preview-image data-v-61ae3098" mode="aspectFit" bindtap="{{e}}" bindtouchstart="{{f}}" catchtouchmove="{{g}}" id="previewImage"/><view wx:if="{{h}}" class="color-picker-cursor data-v-61ae3098" style="{{i}}"><view class="cursor-inner data-v-61ae3098"></view></view></view><view wx:if="{{j}}" class="picked-color-info data-v-61ae3098"><view class="color-preview data-v-61ae3098" style="{{'background-color:' + k}}"></view><text class="color-value data-v-61ae3098">{{l}}</text><view class="copy-btn data-v-61ae3098" bindtap="{{m}}"><text class="copy-icon data-v-61ae3098">📋</text></view></view><view class="image-actions data-v-61ae3098"><view class="action-btn secondary data-v-61ae3098" bindtap="{{n}}"><text class="action-icon data-v-61ae3098">🔄</text><text class="action-text data-v-61ae3098">重新选择</text></view><view wx:if="{{o}}" class="action-btn primary data-v-61ae3098" bindtap="{{p}}"><text class="action-icon data-v-61ae3098">🎨</text><text class="action-text data-v-61ae3098">提取全部颜色</text></view><view wx:if="{{q}}" class="action-btn processing data-v-61ae3098"><text class="action-icon data-v-61ae3098">⏳</text><text class="action-text data-v-61ae3098">处理中...</text></view></view></view></view><view wx:if="{{r}}" class="results-section data-v-61ae3098"><view class="results-header data-v-61ae3098"><text class="results-title data-v-61ae3098">提取的颜色</text><text class="results-count data-v-61ae3098">{{s}} 种颜色</text></view><view class="colors-grid data-v-61ae3098"><view wx:for="{{t}}" wx:for-item="color" wx:key="c" class="color-item data-v-61ae3098" bindtap="{{color.d}}"><view class="color-block data-v-61ae3098" style="{{'background-color:' + color.a}}"></view><text class="color-code data-v-61ae3098">{{color.b}}</text><view class="copy-indicator data-v-61ae3098"><text class="copy-text data-v-61ae3098">点击复制</text></view></view></view><view class="batch-actions data-v-61ae3098"><view class="action-btn primary data-v-61ae3098" bindtap="{{v}}"><text class="action-icon data-v-61ae3098">📋</text><text class="action-text data-v-61ae3098">复制全部</text></view></view></view><view wx:if="{{w}}" class="analysis-section data-v-61ae3098"><text class="section-title data-v-61ae3098">颜色分析</text><view class="analysis-grid data-v-61ae3098"><view class="analysis-item data-v-61ae3098"><text class="analysis-label data-v-61ae3098">主色调</text><view class="dominant-color data-v-61ae3098"><view class="color-sample data-v-61ae3098" style="{{'background-color:' + x}}"></view><text class="color-name data-v-61ae3098">{{y}}</text></view></view><view class="analysis-item data-v-61ae3098"><text class="analysis-label data-v-61ae3098">色彩丰富度</text><text class="analysis-value data-v-61ae3098">{{z}}</text></view><view class="analysis-item data-v-61ae3098"><text class="analysis-label data-v-61ae3098">整体色温</text><text class="analysis-value data-v-61ae3098">{{A}}</text></view></view></view><view class="instructions-section data-v-61ae3098"><text class="section-title data-v-61ae3098">使用说明</text><view class="instructions-list data-v-61ae3098"><text class="instruction-item data-v-61ae3098">• <text class="highlight data-v-61ae3098">智能提取</text>：自动分析图片中的主要颜色</text><text class="instruction-item data-v-61ae3098">• <text class="highlight data-v-61ae3098">调色板</text>：生成8种最具代表性的颜色</text><text class="instruction-item data-v-61ae3098">• <text class="highlight data-v-61ae3098">HEX格式</text>：提供标准的十六进制颜色代码</text><text class="instruction-item data-v-61ae3098">• <text class="highlight data-v-61ae3098">一键复制</text>：点击颜色块快速复制颜色值</text><text class="instruction-item data-v-61ae3098">• <text class="highlight data-v-61ae3098">设计应用</text>：适合网页设计、UI设计等场景</text></view></view><canvas canvas-id="colorCanvas" class="hidden-canvas data-v-61ae3098" style="width:500px;height:500px"></canvas></view></view>