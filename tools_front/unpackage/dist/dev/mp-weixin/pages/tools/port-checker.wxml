<view class="min-h-screen bg-gray-50 data-v-85ea2c4b"><view class="p-4 space-y-4 data-v-85ea2c4b"><view class="bg-white rounded-lg shadow-sm p-4 data-v-85ea2c4b"><view class="flex items-center mb-3 data-v-85ea2c4b"><wifi wx:if="{{a}}" class="mr-2 text-blue-500 data-v-85ea2c4b" u-i="85ea2c4b-0" bind:__l="__l" u-p="{{a}}"/><view class="text-base font-semibold data-v-85ea2c4b">端口检测</view></view><view class="space-y-4 data-v-85ea2c4b"><view class="flex gap-2 data-v-85ea2c4b"><input type="text" placeholder="输入主机地址 (如: google.com)" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus-ring-2 focus-ring-blue-500 focus-border-blue-500 data-v-85ea2c4b" value="{{b}}" bindinput="{{c}}"/></view><view class="flex gap-2 data-v-85ea2c4b"><input type="number" placeholder="端口号 (如: 80)" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus-ring-2 focus-ring-blue-500 focus-border-blue-500 data-v-85ea2c4b" value="{{d}}" bindinput="{{e}}"/><button bindtap="{{g}}" disabled="{{h}}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover-bg-blue-600 disabled-opacity-50 data-v-85ea2c4b">{{f}}</button></view></view></view><view wx:if="{{i}}" class="bg-white rounded-lg shadow-sm p-4 data-v-85ea2c4b"><view class="flex items-center mb-3 data-v-85ea2c4b"><activity wx:if="{{j}}" class="mr-2 text-green-500 data-v-85ea2c4b" u-i="85ea2c4b-1" bind:__l="__l" u-p="{{j}}"/><view class="text-base font-semibold data-v-85ea2c4b">检测结果</view></view><view class="{{['data-v-85ea2c4b', 'p-4 rounded-lg', r]}}"><view class="flex items-center mb-2 data-v-85ea2c4b"><view class="{{['data-v-85ea2c4b', 'w-3 h-3 rounded-full mr-3', k]}}"></view><label class="font-medium data-v-85ea2c4b">{{l}}:{{m}} - {{n}}</label></view><view class="text-sm text-gray-600 data-v-85ea2c4b"><view class="data-v-85ea2c4b">服务: {{o}}</view><view class="data-v-85ea2c4b">响应时间: {{p}}ms</view><view class="data-v-85ea2c4b">检测时间: {{q}}</view></view></view></view><view class="bg-white rounded-lg shadow-sm p-4 data-v-85ea2c4b"><view class="flex items-center mb-3 data-v-85ea2c4b"><view class="flex items-center flex-1 data-v-85ea2c4b"><server wx:if="{{s}}" class="mr-2 text-purple-500 data-v-85ea2c4b" u-i="85ea2c4b-2" bind:__l="__l" u-p="{{s}}"/><view class="text-base font-semibold data-v-85ea2c4b">常用端口</view></view><view class="status-width data-v-85ea2c4b"><button bindtap="{{v}}" disabled="{{w}}" class="scan-btn data-v-85ea2c4b">{{t}}</button></view></view><view class="grid grid-cols-1 gap-2 data-v-85ea2c4b"><view wx:for="{{x}}" wx:for-item="port" wx:key="f" class="flex items-center bg-gray-50 rounded-lg port-item data-v-85ea2c4b"><view class="flex items-center flex-1 p-3 data-v-85ea2c4b"><view class="{{['data-v-85ea2c4b', 'w-3 h-3 rounded-full mr-3', port.a]}}"></view><view class="data-v-85ea2c4b"><view class="font-medium data-v-85ea2c4b">{{port.b}} - {{port.c}}</view><view class="text-sm text-gray-600 data-v-85ea2c4b">{{port.d}}</view></view></view><view class="text-sm text-right status-width status-text data-v-85ea2c4b">{{port.e}}</view></view></view></view><view class="bg-white rounded-lg shadow-sm p-4 data-v-85ea2c4b"><view class="flex items-center mb-3 data-v-85ea2c4b"><search wx:if="{{y}}" class="mr-2 text-orange-500 data-v-85ea2c4b" u-i="85ea2c4b-3" bind:__l="__l" u-p="{{y}}"/><view class="text-base font-semibold data-v-85ea2c4b">端口范围扫描</view></view><view class="space-y-4 data-v-85ea2c4b"><view class="flex items-center gap-2 data-v-85ea2c4b"><input type="number" placeholder="起始端口" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus-ring-2 focus-ring-orange-500 focus-border-orange-500 data-v-85ea2c4b" value="{{z}}" bindinput="{{A}}"/><input type="number" placeholder="结束端口" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus-ring-2 focus-ring-orange-500 focus-border-orange-500 data-v-85ea2c4b" value="{{B}}" bindinput="{{C}}"/><button bindtap="{{E}}" disabled="{{F}}" class="scan-range-btn data-v-85ea2c4b">{{D}}</button></view><view wx:if="{{G}}" class="space-y-2 data-v-85ea2c4b"><view class="flex justify-between text-sm data-v-85ea2c4b"><label class="data-v-85ea2c4b">扫描进度</label><label class="data-v-85ea2c4b">{{H}}%</label></view><view class="w-full bg-gray-200 rounded-full h-2 data-v-85ea2c4b"><view class="bg-orange-500 h-2 rounded-full transition-all duration-300 data-v-85ea2c4b" style="{{'width:' + I}}"></view></view></view></view></view><view wx:if="{{J}}" class="bg-white rounded-lg shadow-sm p-4 data-v-85ea2c4b"><view class="flex items-center mb-3 data-v-85ea2c4b"><shield wx:if="{{K}}" class="mr-2 text-green-500 data-v-85ea2c4b" u-i="85ea2c4b-4" bind:__l="__l" u-p="{{K}}"/><view class="text-base font-semibold data-v-85ea2c4b">发现的开放端口</view></view><view class="space-y-2 data-v-85ea2c4b"><view wx:for="{{L}}" wx:for-item="port" wx:key="c" class="flex items-center justify-between p-3 bg-green-50 rounded-lg data-v-85ea2c4b"><view class="flex items-center data-v-85ea2c4b"><view class="w-3 h-3 bg-green-500 rounded-full mr-3 data-v-85ea2c4b"></view><label class="font-medium data-v-85ea2c4b">端口 {{port.a}}</label></view><label class="text-sm text-gray-600 data-v-85ea2c4b">{{port.b}}</label></view></view></view><view class="bg-white rounded-lg shadow-sm p-6 data-v-85ea2c4b"><view class="text-base font-semibold mb-3 data-v-85ea2c4b">使用说明</view><view class="text-sm text-gray-600 space-y-2 data-v-85ea2c4b"><view class="data-v-85ea2c4b">• 输入主机地址和端口号进行单个端口检测</view><view class="data-v-85ea2c4b">• 点击"批量扫描"检测常用端口状态</view><view class="data-v-85ea2c4b">• 使用端口范围扫描功能检测指定范围内的端口</view><view class="data-v-85ea2c4b">• 绿色表示端口开放，红色表示端口关闭</view><view class="data-v-85ea2c4b">• 注意：某些网络可能会阻止端口扫描</view></view></view></view></view>