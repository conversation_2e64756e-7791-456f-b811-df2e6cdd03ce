"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "PhoneDetector",
  data() {
    return {
      deviceInfo: {
        deviceType: "检测中...",
        platform: "检测中...",
        system: "检测中...",
        brand: "检测中...",
        model: "检测中...",
        language: "检测中..."
      },
      screenInfo: {
        screenWidth: 0,
        screenHeight: 0,
        windowWidth: 0,
        windowHeight: 0,
        pixelRatio: 1,
        statusBarHeight: 0
      },
      networkInfo: {
        networkType: "unknown",
        isConnected: false
      },
      appInfo: {
        appName: "检测中...",
        version: "检测中...",
        SDKVersion: "检测中...",
        host: "检测中..."
      },
      featureSupport: [
        { name: "本地存储", description: "localStorage支持", supported: false },
        { name: "相机拍照", description: "设备相机功能", supported: false },
        { name: "地理定位", description: "GPS定位服务", supported: false },
        { name: "文件系统", description: "文件读写权限", supported: false },
        { name: "剪贴板", description: "复制粘贴功能", supported: false },
        { name: "震动反馈", description: "设备震动功能", supported: false }
      ],
      performanceInfo: {},
      speedResult: {},
      testing: false,
      performanceTesting: false
    };
  },
  onLoad() {
    this.detectDevice();
    this.detectFeatures();
  },
  methods: {
    async detectDevice() {
      var _a;
      try {
        const systemInfo = await this.getSystemInfo();
        this.deviceInfo = {
          deviceType: this.getDeviceTypeText(systemInfo.platform),
          platform: systemInfo.platform,
          system: systemInfo.system,
          brand: systemInfo.brand || "未知",
          model: systemInfo.model || "未知",
          language: systemInfo.language || "zh_CN"
        };
        this.screenInfo = {
          screenWidth: systemInfo.screenWidth,
          screenHeight: systemInfo.screenHeight,
          windowWidth: systemInfo.windowWidth,
          windowHeight: systemInfo.windowHeight,
          pixelRatio: systemInfo.pixelRatio,
          statusBarHeight: systemInfo.statusBarHeight || 0
        };
        this.appInfo = {
          appName: "微信小程序",
          version: systemInfo.version,
          SDKVersion: systemInfo.SDKVersion,
          host: ((_a = systemInfo.host) == null ? void 0 : _a.appName) || "微信"
        };
        this.getNetworkInfo();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/phone-detector.vue:362", "设备检测失败:", error);
        common_vendor.index.showToast({
          title: "检测失败，请重试",
          icon: "none"
        });
      }
    },
    getSystemInfo() {
      return new Promise((resolve, reject) => {
        common_vendor.index.getSystemInfo({
          success: resolve,
          fail: reject
        });
      });
    },
    getNetworkInfo() {
      common_vendor.index.getNetworkType({
        success: (res) => {
          this.networkInfo = {
            networkType: res.networkType,
            isConnected: res.networkType !== "none"
          };
        }
      });
    },
    getDeviceTypeText(platform) {
      const platformLower = platform.toLowerCase();
      if (platformLower.includes("android"))
        return "Android设备";
      if (platformLower.includes("ios"))
        return "iOS设备";
      if (platformLower.includes("devtools"))
        return "开发工具";
      return "未知设备";
    },
    getNetworkTypeText(type) {
      const types = {
        "wifi": "WiFi",
        "2g": "2G网络",
        "3g": "3G网络",
        "4g": "4G网络",
        "5g": "5G网络",
        "unknown": "未知网络",
        "none": "无网络"
      };
      return types[type] || type;
    },
    getNetworkStatus(type) {
      if (type === "none")
        return "offline";
      if (["wifi", "4g", "5g"].includes(type))
        return "good";
      if (["3g"].includes(type))
        return "medium";
      return "poor";
    },
    getSignalStrength() {
      const type = this.networkInfo.networkType;
      if (type === "wifi" || type === "5g")
        return "优秀";
      if (type === "4g")
        return "良好";
      if (type === "3g")
        return "一般";
      if (type === "2g")
        return "较弱";
      return "未知";
    },
    async detectFeatures() {
      this.featureSupport = [
        {
          name: "本地存储",
          description: "uni.setStorage支持",
          supported: this.checkStorageSupport()
        },
        {
          name: "相机拍照",
          description: "设备相机功能",
          supported: this.checkCameraSupport()
        },
        {
          name: "地理定位",
          description: "GPS定位服务",
          supported: this.checkLocationSupport()
        },
        {
          name: "文件系统",
          description: "文件读写权限",
          supported: this.checkFileSupport()
        },
        {
          name: "剪贴板",
          description: "复制粘贴功能",
          supported: this.checkClipboardSupport()
        },
        {
          name: "震动反馈",
          description: "设备震动功能",
          supported: this.checkVibrateSupport()
        }
      ];
    },
    checkStorageSupport() {
      try {
        common_vendor.index.setStorageSync("test", "test");
        common_vendor.index.removeStorageSync("test");
        return true;
      } catch (e) {
        return false;
      }
    },
    checkCameraSupport() {
      return true;
    },
    checkLocationSupport() {
      return true;
    },
    checkFileSupport() {
      return true;
    },
    checkClipboardSupport() {
      return true;
    },
    checkVibrateSupport() {
      return true;
    },
    async testNetworkSpeed() {
      this.testing = true;
      this.speedResult = {};
      try {
        const startTime = Date.now();
        await this.pingTest();
        const pingTime = Date.now() - startTime;
        const downloadSpeed = this.estimateSpeed();
        this.speedResult = {
          ping: pingTime,
          downloadSpeed
        };
        common_vendor.index.showToast({
          title: "网络测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "网络测试失败",
          icon: "error"
        });
      } finally {
        this.testing = false;
      }
    },
    pingTest() {
      return new Promise((resolve) => {
        common_vendor.index.request({
          url: "https://www.baidu.com",
          method: "HEAD",
          timeout: 5e3,
          success: () => resolve(),
          fail: () => resolve()
        });
      });
    },
    estimateSpeed() {
      const type = this.networkInfo.networkType;
      const speeds = {
        "wifi": (Math.random() * 50 + 50).toFixed(1),
        "5g": (Math.random() * 100 + 100).toFixed(1),
        "4g": (Math.random() * 20 + 20).toFixed(1),
        "3g": (Math.random() * 5 + 2).toFixed(1),
        "2g": (Math.random() * 1 + 0.5).toFixed(1)
      };
      return speeds[type] || "未知";
    },
    async testPerformance() {
      this.performanceTesting = true;
      try {
        const startTime = Date.now();
        await this.sleep(1e3);
        const memory = this.getMemoryUsage();
        const launchTime = Date.now() - startTime;
        const renderScore = this.calculateRenderScore();
        this.performanceInfo = {
          memoryUsage: memory,
          launchTime,
          renderScore
        };
        common_vendor.index.showToast({
          title: "性能测试完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "性能测试失败",
          icon: "error"
        });
      } finally {
        this.performanceTesting = false;
      }
    },
    getMemoryUsage() {
      return (Math.random() * 200 + 100).toFixed(1);
    },
    calculateRenderScore() {
      const scores = ["优秀", "良好", "一般", "较差"];
      return scores[Math.floor(Math.random() * scores.length)];
    },
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
    refreshDetection() {
      common_vendor.index.showLoading({ title: "检测中..." });
      this.detectDevice().then(() => {
        this.detectFeatures();
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "刷新完成",
          icon: "success"
        });
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.refreshDetection && $options.refreshDetection(...args)),
    b: common_vendor.t($data.deviceInfo.deviceType),
    c: common_vendor.t($data.deviceInfo.platform),
    d: common_vendor.t($data.deviceInfo.system),
    e: common_vendor.t($data.deviceInfo.brand),
    f: common_vendor.t($data.deviceInfo.model),
    g: common_vendor.t($data.deviceInfo.language),
    h: common_vendor.t($data.screenInfo.screenWidth),
    i: common_vendor.t($data.screenInfo.screenHeight),
    j: common_vendor.t($data.screenInfo.windowWidth),
    k: common_vendor.t($data.screenInfo.windowHeight),
    l: common_vendor.t($data.screenInfo.pixelRatio),
    m: common_vendor.t($data.screenInfo.statusBarHeight),
    n: common_vendor.t($options.getNetworkTypeText($data.networkInfo.networkType)),
    o: common_vendor.n("status-" + $options.getNetworkStatus($data.networkInfo.networkType)),
    p: common_vendor.t($data.networkInfo.isConnected ? "已连接" : "未连接"),
    q: common_vendor.n($data.networkInfo.isConnected ? "status-online" : "status-offline"),
    r: common_vendor.t($options.getSignalStrength()),
    s: common_vendor.t($data.testing ? "测试中..." : "开始测试"),
    t: common_vendor.o((...args) => $options.testNetworkSpeed && $options.testNetworkSpeed(...args)),
    v: $data.testing,
    w: $data.speedResult.ping
  }, $data.speedResult.ping ? {
    x: common_vendor.t($data.speedResult.ping),
    y: common_vendor.t($data.speedResult.downloadSpeed)
  } : {}, {
    z: common_vendor.t($data.appInfo.appName),
    A: common_vendor.t($data.appInfo.version),
    B: common_vendor.t($data.appInfo.SDKVersion),
    C: common_vendor.t($data.appInfo.host),
    D: common_vendor.f($data.featureSupport, (feature, k0, i0) => {
      return {
        a: common_vendor.t(feature.supported ? "✅" : "❌"),
        b: common_vendor.t(feature.name),
        c: common_vendor.t(feature.description),
        d: feature.name
      };
    }),
    E: common_vendor.t($data.performanceTesting ? "测试中..." : "开始测试"),
    F: common_vendor.o((...args) => $options.testPerformance && $options.testPerformance(...args)),
    G: $data.performanceTesting,
    H: $data.performanceInfo.memoryUsage
  }, $data.performanceInfo.memoryUsage ? {
    I: common_vendor.t($data.performanceInfo.memoryUsage),
    J: common_vendor.t($data.performanceInfo.launchTime),
    K: common_vendor.t($data.performanceInfo.renderScore)
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-51a4a745"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/phone-detector.js.map
