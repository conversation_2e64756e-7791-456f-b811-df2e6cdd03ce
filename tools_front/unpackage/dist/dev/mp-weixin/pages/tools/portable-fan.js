"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "PortableFan",
  data() {
    return {
      isRunning: false,
      speedLevel: 1,
      currentMode: "normal",
      soundEnabled: true,
      showTemperature: false,
      runStartTime: null,
      totalRunTime: "0小时",
      todayRunTime: "0分钟",
      powerSaved: 0,
      currentTemp: 26,
      humidity: 65,
      windParticles: [],
      particleId: 0,
      timerMinutes: 0,
      timerActive: false,
      timerRemaining: 0,
      timerInterval: null,
      modes: [
        { value: "normal", name: "标准", icon: "🌀" },
        { value: "natural", name: "自然风", icon: "🍃" },
        { value: "sleep", name: "睡眠", icon: "😴" },
        { value: "turbo", name: "强劲", icon: "💨" }
      ],
      timerOptions: [
        { value: 15, label: "15分钟" },
        { value: 30, label: "30分钟" },
        { value: 60, label: "1小时" },
        { value: 120, label: "2小时" }
      ]
    };
  },
  onLoad() {
    this.loadData();
    this.startEnvironmentUpdate();
  },
  onUnload() {
    this.saveData();
    this.clearTimers();
  },
  methods: {
    togglePower() {
      this.isRunning = !this.isRunning;
      if (this.isRunning) {
        this.runStartTime = Date.now();
        this.startWindEffect();
        if (this.soundEnabled) {
          this.playFanSound();
        }
      } else {
        this.stopWindEffect();
        this.updateRunTime();
        this.runStartTime = null;
      }
      this.saveData();
    },
    setSpeedLevel(level) {
      if (!this.isRunning)
        return;
      this.speedLevel = level;
      this.updateWindEffect();
      this.saveData();
    },
    setMode(mode) {
      this.currentMode = mode;
      if (this.isRunning) {
        this.updateWindEffect();
      }
      this.saveData();
    },
    toggleSound() {
      this.soundEnabled = !this.soundEnabled;
      this.saveData();
    },
    toggleTemperature() {
      this.showTemperature = !this.showTemperature;
      this.saveData();
    },
    getBladeClass() {
      if (!this.isRunning)
        return "";
      const classes = {
        normal: "blades-normal",
        natural: "blades-natural",
        sleep: "blades-slow",
        turbo: "blades-fast"
      };
      return classes[this.currentMode] || "blades-normal";
    },
    getAnimationDuration() {
      if (!this.isRunning)
        return 0;
      const baseDuration = {
        normal: 0.5,
        natural: 0.8,
        sleep: 1.2,
        turbo: 0.2
      };
      const duration = baseDuration[this.currentMode] || 0.5;
      return duration / this.speedLevel;
    },
    getWindLevelName() {
      const levels = ["微风", "清风", "强风", "劲风", "暴风"];
      return levels[this.speedLevel - 1] || "微风";
    },
    startWindEffect() {
      let interval = 1e3;
      switch (this.currentMode) {
        case "normal":
          interval = 800 / this.speedLevel;
          break;
        case "natural":
          interval = 1200 / this.speedLevel;
          break;
        case "sleep":
          interval = 1500 / this.speedLevel;
          break;
        case "turbo":
          interval = 400 / this.speedLevel;
          break;
      }
      this.windEffectTimer = setInterval(() => {
        const particleCount = this.speedLevel >= 4 ? 2 : 1;
        for (let i = 0; i < particleCount; i++) {
          setTimeout(() => {
            this.createWindParticle();
          }, i * 100);
        }
      }, interval);
    },
    stopWindEffect() {
      if (this.windEffectTimer) {
        clearInterval(this.windEffectTimer);
        this.windEffectTimer = null;
      }
      this.windParticles = [];
    },
    updateWindEffect() {
      this.stopWindEffect();
      if (this.isRunning) {
        this.startWindEffect();
      }
    },
    createWindParticle() {
      if (!this.isRunning)
        return;
      const centerX = 250;
      const centerY = 250;
      const angle = Math.random() * Math.PI * 2;
      const startRadius = 50 + Math.random() * 50;
      const particle = {
        id: this.particleId++,
        x: centerX + Math.cos(angle) * startRadius,
        y: centerY + Math.sin(angle) * startRadius,
        angle,
        speed: this.speedLevel * 2,
        delay: Math.random() * 200,
        duration: 1500 - this.speedLevel * 200 + Math.random() * 500
      };
      this.windParticles.push(particle);
      setTimeout(() => {
        const index = this.windParticles.findIndex((p) => p.id === particle.id);
        if (index > -1) {
          this.windParticles.splice(index, 1);
        }
      }, particle.duration + 500);
    },
    playFanSound() {
      common_vendor.index.__f__("log", "at pages/tools/portable-fan.vue:482", "播放风扇音效");
    },
    formatRunTime() {
      if (!this.runStartTime)
        return "0分钟";
      const minutes = Math.floor((Date.now() - this.runStartTime) / 6e4);
      if (minutes < 60) {
        return `${minutes}分钟`;
      }
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}小时${remainingMinutes}分钟`;
    },
    updateRunTime() {
      if (!this.runStartTime)
        return;
      Math.floor((Date.now() - this.runStartTime) / 6e4);
    },
    getComfortLevel() {
      if (this.currentTemp < 20)
        return "偏冷";
      if (this.currentTemp < 26)
        return "舒适";
      if (this.currentTemp < 30)
        return "温暖";
      return "炎热";
    },
    startEnvironmentUpdate() {
      this.envTimer = setInterval(() => {
        this.currentTemp += (Math.random() - 0.5) * 0.5;
        this.currentTemp = Math.max(18, Math.min(35, this.currentTemp));
        this.currentTemp = Math.round(this.currentTemp * 10) / 10;
        this.humidity += (Math.random() - 0.5) * 2;
        this.humidity = Math.max(30, Math.min(90, this.humidity));
        this.humidity = Math.round(this.humidity);
      }, 5e3);
    },
    setTimer(minutes) {
      this.timerMinutes = minutes;
    },
    startTimer() {
      if (!this.timerMinutes || this.timerActive)
        return;
      this.timerActive = true;
      this.timerRemaining = this.timerMinutes * 60;
      this.timerInterval = setInterval(() => {
        this.timerRemaining--;
        if (this.timerRemaining <= 0) {
          this.cancelTimer();
          if (this.isRunning) {
            this.togglePower();
          }
          common_vendor.index.showToast({
            title: "定时关机",
            icon: "success"
          });
        }
      }, 1e3);
    },
    cancelTimer() {
      this.timerActive = false;
      this.timerRemaining = 0;
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }
    },
    formatTimer() {
      const minutes = Math.floor(this.timerRemaining / 60);
      const seconds = this.timerRemaining % 60;
      return `${minutes}:${seconds.toString().padStart(2, "0")}`;
    },
    clearTimers() {
      if (this.windEffectTimer) {
        clearInterval(this.windEffectTimer);
      }
      if (this.envTimer) {
        clearInterval(this.envTimer);
      }
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }
    },
    loadData() {
      try {
        const data = common_vendor.index.getStorageSync("portable-fan-data");
        if (data) {
          const parsed = JSON.parse(data);
          this.soundEnabled = parsed.soundEnabled !== false;
          this.showTemperature = parsed.showTemperature || false;
          this.totalRunTime = parsed.totalRunTime || "0小时";
          this.todayRunTime = parsed.todayRunTime || "0分钟";
          this.powerSaved = parsed.powerSaved || 0;
        }
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/portable-fan.vue:587", "加载数据失败", e);
      }
    },
    saveData() {
      try {
        const data = {
          soundEnabled: this.soundEnabled,
          showTemperature: this.showTemperature,
          totalRunTime: this.totalRunTime,
          todayRunTime: this.todayRunTime,
          powerSaved: this.powerSaved,
          lastSaveDate: (/* @__PURE__ */ new Date()).toDateString()
        };
        common_vendor.index.setStorageSync("portable-fan-data", JSON.stringify(data));
      } catch (e) {
        common_vendor.index.__f__("log", "at pages/tools/portable-fan.vue:603", "保存数据失败", e);
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.isRunning ? "运行中" : "已停止"),
    b: $data.isRunning ? 1 : "",
    c: common_vendor.f($data.windParticles, (wind, index, i0) => {
      return {
        a: wind.id,
        b: wind.x + "rpx",
        c: wind.y + "rpx",
        d: wind.delay + "ms",
        e: wind.duration + "ms"
      };
    }),
    d: common_vendor.f(3, (i, k0, i0) => {
      return {
        a: "h" + i
      };
    }),
    e: common_vendor.f(3, (i, k0, i0) => {
      return {
        a: "v" + i
      };
    }),
    f: common_vendor.f(5, (i, k0, i0) => {
      return {
        a: i,
        b: `rotate(${(i - 1) * 72}deg)`
      };
    }),
    g: common_vendor.n($options.getBladeClass()),
    h: $options.getAnimationDuration() + "s",
    i: $data.isRunning ? "running" : "paused",
    j: common_vendor.t($data.isRunning ? "⚡" : "⏸"),
    k: common_vendor.t($data.isRunning ? "⏸️" : "▶️"),
    l: $data.isRunning ? 1 : "",
    m: common_vendor.o((...args) => $options.togglePower && $options.togglePower(...args)),
    n: $data.isRunning
  }, $data.isRunning ? {
    o: common_vendor.f($data.speedLevel, (i, k0, i0) => {
      return {
        a: i,
        b: i * 0.3 + "s"
      };
    }),
    p: 2 - $data.speedLevel * 0.2 + "s"
  } : {}, {
    q: common_vendor.t($data.speedLevel),
    r: common_vendor.t($options.getWindLevelName()),
    s: common_vendor.t($options.formatRunTime()),
    t: common_vendor.f(5, (level, k0, i0) => {
      return {
        a: common_vendor.t(level),
        b: common_vendor.f(level, (dot, k1, i1) => {
          return {
            a: dot
          };
        }),
        c: level,
        d: $data.speedLevel === level ? 1 : "",
        e: common_vendor.o(($event) => $options.setSpeedLevel(level), level)
      };
    }),
    v: !$data.isRunning ? 1 : "",
    w: common_vendor.f($data.modes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: mode.value,
        d: $data.currentMode === mode.value ? 1 : "",
        e: common_vendor.o(($event) => $options.setMode(mode.value), mode.value)
      };
    }),
    x: $data.soundEnabled ? 1 : "",
    y: common_vendor.o((...args) => $options.toggleSound && $options.toggleSound(...args)),
    z: $data.showTemperature ? 1 : "",
    A: common_vendor.o((...args) => $options.toggleTemperature && $options.toggleTemperature(...args)),
    B: $data.showTemperature
  }, $data.showTemperature ? {
    C: common_vendor.t($data.currentTemp),
    D: common_vendor.t($data.humidity),
    E: common_vendor.t($options.getComfortLevel())
  } : {}, {
    F: common_vendor.t($data.totalRunTime),
    G: common_vendor.t($data.todayRunTime),
    H: common_vendor.t($data.powerSaved),
    I: $data.timerActive
  }, $data.timerActive ? {
    J: common_vendor.t($options.formatTimer())
  } : {}, {
    K: common_vendor.f($data.timerOptions, (option, k0, i0) => {
      return {
        a: common_vendor.t(option.label),
        b: option.value,
        c: $data.timerMinutes === option.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setTimer(option.value), option.value)
      };
    }),
    L: common_vendor.o((...args) => $options.startTimer && $options.startTimer(...args)),
    M: !$data.timerMinutes || $data.timerActive ? 1 : "",
    N: common_vendor.o((...args) => $options.cancelTimer && $options.cancelTimer(...args)),
    O: !$data.timerActive ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f92699d7"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/portable-fan.js.map
