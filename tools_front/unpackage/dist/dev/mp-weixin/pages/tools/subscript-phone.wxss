/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-78b74d23 {
  display: flex;
}
.flex-1.data-v-78b74d23 {
  flex: 1;
}
.items-center.data-v-78b74d23 {
  align-items: center;
}
.justify-center.data-v-78b74d23 {
  justify-content: center;
}
.justify-between.data-v-78b74d23 {
  justify-content: space-between;
}
.text-center.data-v-78b74d23 {
  text-align: center;
}
.rounded.data-v-78b74d23 {
  border-radius: 3px;
}
.rounded-lg.data-v-78b74d23 {
  border-radius: 6px;
}
.shadow.data-v-78b74d23 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-78b74d23 {
  padding: 16rpx;
}
.m-4.data-v-78b74d23 {
  margin: 16rpx;
}
.mb-4.data-v-78b74d23 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-78b74d23 {
  margin-top: 16rpx;
}
.subscript-phone.data-v-78b74d23 {
  min-height: 100vh;
  background: #f9fafb;
}
.container.data-v-78b74d23 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-78b74d23 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-78b74d23 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-78b74d23 {
  font-size: 48rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.title-text.data-v-78b74d23 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.subtitle.data-v-78b74d23 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-78b74d23, .result-section.data-v-78b74d23, .example-section.data-v-78b74d23, .mapping-section.data-v-78b74d23, .comparison-tool-section.data-v-78b74d23, .help-section.data-v-78b74d23 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.input-section.data-v-78b74d23:hover, .result-section.data-v-78b74d23:hover, .example-section.data-v-78b74d23:hover, .mapping-section.data-v-78b74d23:hover, .comparison-tool-section.data-v-78b74d23:hover, .help-section.data-v-78b74d23:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.section-header.data-v-78b74d23 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-78b74d23 {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.section-title.data-v-78b74d23 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-wrapper.data-v-78b74d23 {
  position: relative;
}
.phone-input.data-v-78b74d23 {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  text-align: center;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
}
.phone-input.data-v-78b74d23:focus {
  border-color: #059669;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(5, 150, 105, 0.15);
  outline: none;
}
.input-actions.data-v-78b74d23 {
  display: flex;
  justify-content: center;
  margin-top: 16rpx;
}
.clear-btn.data-v-78b74d23 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  background: #f3f4f6;
  color: #4b5563;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.clear-btn.data-v-78b74d23:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.clear-btn.data-v-78b74d23:active {
  transform: translateY(0) scale(0.98);
}
.btn-icon.data-v-78b74d23 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-78b74d23 {
  font-size: 24rpx;
}
.result-card.data-v-78b74d23 {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #bbf7d0;
  margin-bottom: 24rpx;
}
.comparison-section.data-v-78b74d23 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
}
.comparison-item.data-v-78b74d23 {
  flex: 1;
  text-align: center;
}
.comparison-label.data-v-78b74d23 {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
  margin-bottom: 8rpx;
}
.comparison-value.data-v-78b74d23 {
  font-size: 32rpx;
  font-weight: 600;
  display: block;
  word-break: break-all;
}
.comparison-value.original.data-v-78b74d23 {
  color: #374151;
}
.comparison-value.subscript.data-v-78b74d23 {
  color: #047857;
}
.comparison-arrow.data-v-78b74d23 {
  font-size: 28rpx;
  color: #9ca3af;
  margin: 0 16rpx;
}
.result-actions-full.data-v-78b74d23 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-78b74d23 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 50rpx;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-78b74d23 {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(5, 150, 105, 0.25);
}
.action-btn.primary.data-v-78b74d23:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(5, 150, 105, 0.35);
}
.action-btn.primary.data-v-78b74d23:active {
  transform: translateY(0) scale(0.98);
}
.example-grid.data-v-78b74d23 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.example-item.data-v-78b74d23 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.example-item.data-v-78b74d23:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.example-content.data-v-78b74d23 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.example-original.data-v-78b74d23, .example-subscript.data-v-78b74d23 {
  font-size: 28rpx;
  color: #374151;
  word-break: break-all;
}
.example-actions.data-v-78b74d23 {
  display: flex;
  gap: 8rpx;
}
.copy-btn.data-v-78b74d23 {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  border: 1rpx solid #e2e8f0;
}
.copy-btn.data-v-78b74d23:hover {
  background: #e2e8f0;
  transform: translateY(-2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.copy-btn.data-v-78b74d23:active {
  transform: translateY(0) scale(0.95);
}
.copy-icon.data-v-78b74d23 {
  font-size: 24rpx;
}
.mapping-grid.data-v-78b74d23 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx;
}
.mapping-item.data-v-78b74d23 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.mapping-item.data-v-78b74d23:hover {
  background: #f1f5f9;
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.mapping-original.data-v-78b74d23, .mapping-subscript.data-v-78b74d23 {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}
.mapping-arrow.data-v-78b74d23 {
  font-size: 20rpx;
  color: #9ca3af;
  margin: 4rpx 0;
}
.comparison-tool-grid.data-v-78b74d23 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.comparison-tool-item.data-v-78b74d23 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  position: relative;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.comparison-tool-item.data-v-78b74d23:hover {
  background: #f1f5f9;
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.comparison-tool-label.data-v-78b74d23 {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
  font-weight: 500;
}
.comparison-tool-value.data-v-78b74d23 {
  font-size: 32rpx;
  color: #374151;
  font-weight: 600;
  margin-bottom: 16rpx;
  word-break: break-all;
  text-align: center;
  min-height: 40rpx;
}
.comparison-tool-action.data-v-78b74d23 {
  position: absolute;
  top: 12rpx;
  right: 12rpx;
}
.help-content.data-v-78b74d23 {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1rpx solid #e5e7eb;
}
.help-item.data-v-78b74d23 {
  display: block;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 28rpx;
}
.help-item.data-v-78b74d23:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #059669;
  font-weight: bold;
}
.help-item.data-v-78b74d23:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-78b74d23 {
    padding: 30rpx 20rpx;
}
.comparison-section.data-v-78b74d23 {
    flex-direction: column;
    gap: 16rpx;
}
.comparison-arrow.data-v-78b74d23 {
    transform: rotate(90deg);
}
.mapping-grid.data-v-78b74d23 {
    grid-template-columns: repeat(3, 1fr);
}
.comparison-tool-grid.data-v-78b74d23 {
    grid-template-columns: 1fr;
    gap: 24rpx;
}
}