
.container.data-v-579a7357 {
  min-height: 100vh;
  background-color: #ffffff;
  padding-bottom: env(safe-area-inset-bottom);
}
.main-content.data-v-579a7357 {
  padding: 32rpx;
}

/* 通用部分样式 */
.section.data-v-579a7357 {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title.data-v-579a7357 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.title-icon.data-v-579a7357 {
  font-size: 40rpx;
  margin-right: 16rpx;
}
.title-text.data-v-579a7357 {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

/* 上传区域样式 */
.upload-area.data-v-579a7357 {
  border: 2rpx dashed #d1d5db;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  background-color: #f9fafb;
  margin-bottom: 24rpx;
}
.upload-icon.data-v-579a7357 {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}
.upload-text.data-v-579a7357 {
  display: block;
  font-size: 32rpx;
  color: #6b7280;
  margin-bottom: 32rpx;
}
.upload-btn.data-v-579a7357 {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 24rpx;
}
.upload-tip.data-v-579a7357 {
  display: block;
  font-size: 28rpx;
  color: #9ca3af;
}

/* 文件信息样式 */
.file-info.data-v-579a7357 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background-color: #f3f4f6;
  border-radius: 16rpx;
}
.file-details.data-v-579a7357 {
  display: flex;
  align-items: center;
}
.file-icon.data-v-579a7357 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.file-name.data-v-579a7357 {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}
.action-btn.data-v-579a7357 {
  width: 64rpx;
  height: 64rpx;
  background-color: #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.action-icon.data-v-579a7357 {
  font-size: 28rpx;
}

/* 剪辑设置样式 */
.settings-grid.data-v-579a7357 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.setting-item.data-v-579a7357 {
  display: flex;
  flex-direction: column;
}
.setting-label.data-v-579a7357 {
  font-size: 28rpx;
  color: #4b5563;
  margin-bottom: 12rpx;
}
.setting-input.data-v-579a7357 {
  height: 88rpx;
  background-color: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #1f2937;
}

/* 音频波形图样式 */
.audio-waveform.data-v-579a7357 {
  height: 160rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}
.waveform-placeholder.data-v-579a7357 {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.waveform-icon.data-v-579a7357 {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}
.waveform-text.data-v-579a7357 {
  font-size: 28rpx;
  color: #9ca3af;
}

/* 音频控制按钮样式 */
.audio-controls.data-v-579a7357 {
  display: flex;
  gap: 24rpx;
}
.control-btn.data-v-579a7357 {
  flex: 1;
  height: 80rpx;
  background-color: #f3f4f6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.control-btn.playing.data-v-579a7357 {
  background-color: #4f46e5;
}
.control-btn.playing .control-text.data-v-579a7357 {
  color: #ffffff;
}
.control-icon.data-v-579a7357 {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.control-text.data-v-579a7357 {
  font-size: 28rpx;
  color: #4b5563;
}

/* 操作按钮样式 */
.process-btn.data-v-579a7357, .download-btn.data-v-579a7357 {
  width: 100%;
  height: 88rpx;
  border-radius: 16rpx;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin-bottom: 24rpx;
}
.process-btn.data-v-579a7357 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}
.download-btn.data-v-579a7357 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.3);
}
.process-btn.disabled.data-v-579a7357, .download-btn.disabled.data-v-579a7357 {
  background: #d1d5db;
  color: #9ca3af;
  box-shadow: none;
}
.btn-icon.data-v-579a7357 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.btn-icon.loading.data-v-579a7357 {
  animation: spin-579a7357 1s linear infinite;
}
@keyframes spin-579a7357 {
from { transform: rotate(0deg);
}
to { transform: rotate(360deg);
}
}
.btn-text.data-v-579a7357 {
  font-size: 32rpx;
}

/* 使用说明样式 */
.instructions-section.data-v-579a7357 {
  background-color: #fffbeb;
  border: 2rpx solid #fbbf24;
}
.instruction-list.data-v-579a7357 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.instruction-item.data-v-579a7357 {
  display: flex;
  align-items: center;
}
.item-number.data-v-579a7357 {
  width: 40rpx;
  height: 40rpx;
  background-color: #fbbf24;
  border-radius: 50%;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.item-text.data-v-579a7357 {
  font-size: 28rpx;
  color: #92400e;
  line-height: 1.5;
}
.waveform-container.data-v-579a7357 {
  position: relative;
  width: 100%;
  margin-bottom: 32rpx;
}
.waveform-canvas.data-v-579a7357 {
  width: 100%;
  height: 160rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
}
.timeline.data-v-579a7357 {
  position: relative;
  width: 100%;
  height: 40rpx;
  background-color: #f3f4f6;
  border-radius: 8rpx;
  margin-top: 16rpx;
  touch-action: none; /* 防止页面滚动 */
}
.progress-bar.data-v-579a7357 {
  position: absolute;
  height: 100%;
  background-color: rgba(79, 70, 229, 0.2);
  border-radius: 8rpx;
  transition: width 0.1s linear;
}
.time-marker.data-v-579a7357 {
  position: absolute;
  top: 0;
  width: 32rpx; /* 增加标记的可点击区域 */
  height: 40rpx;
  transform: translateX(-50%);
  z-index: 1;
}
.marker-handle.data-v-579a7357 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32rpx;
  height: 32rpx;
  background-color: #ffffff;
  border: 4rpx solid #4f46e5;
  border-radius: 50%;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.marker-line.data-v-579a7357 {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  width: 4rpx;
  height: 40rpx;
  background-color: #4f46e5;
}
.start-marker .marker-handle.data-v-579a7357 {
  border-color: #10b981;
}
.start-marker .marker-line.data-v-579a7357 {
  background-color: #10b981;
}
.end-marker .marker-handle.data-v-579a7357 {
  border-color: #ef4444;
}
.end-marker .marker-line.data-v-579a7357 {
  background-color: #ef4444;
}
.marker-time.data-v-579a7357 {
  position: absolute;
  top: -30rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24rpx;
  color: #4f46e5;
  white-space: nowrap;
  pointer-events: none;
}
.time-display.data-v-579a7357 {
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 8rpx;
  text-align: center;
  font-weight: 500;
}
.clip-duration.data-v-579a7357 {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border: 2rpx solid #d1d5db;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-top: 16rpx;
}
.clip-duration .setting-label.data-v-579a7357 {
  color: #374151;
  font-weight: 600;
  font-size: 28rpx;
}
.duration-info.data-v-579a7357 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 12rpx;
  gap: 8rpx;
}
.duration-value.data-v-579a7357 {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 700;
  padding: 8rpx 16rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}
.duration-seconds.data-v-579a7357 {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}
