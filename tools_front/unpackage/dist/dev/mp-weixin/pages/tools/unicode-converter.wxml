<view class="unicode-converter data-v-f5ba67cb"><view class="content data-v-f5ba67cb"><view class="card data-v-f5ba67cb"><view class="card-header data-v-f5ba67cb"><text class="header-title data-v-f5ba67cb">转换模式</text></view><view class="card-content data-v-f5ba67cb"><view class="mode-selector data-v-f5ba67cb"><view class="{{['mode-btn', 'data-v-f5ba67cb', a && 'active']}}" bindtap="{{b}}"><text class="data-v-f5ba67cb">文本 → Unicode</text></view><view class="swap-btn data-v-f5ba67cb" bindtap="{{c}}"><text class="data-v-f5ba67cb">⇄</text></view><view class="{{['mode-btn', 'data-v-f5ba67cb', d && 'active']}}" bindtap="{{e}}"><text class="data-v-f5ba67cb">Unicode → 文本</text></view></view></view></view><view class="card data-v-f5ba67cb"><view class="card-header data-v-f5ba67cb"><text class="header-title data-v-f5ba67cb">{{f}}</text></view><view class="card-content data-v-f5ba67cb"><block wx:if="{{r0}}"><textarea class="text-area data-v-f5ba67cb" placeholder="{{g}}" auto-height maxlength="{{h}}" value="{{i}}" bindinput="{{j}}"/></block><button class="convert-btn data-v-f5ba67cb" bindtap="{{k}}" disabled="{{l}}"> 转换 </button></view></view><view wx:if="{{m}}" class="card data-v-f5ba67cb"><view class="card-header data-v-f5ba67cb"><text class="header-title data-v-f5ba67cb">转换结果</text><view class="copy-btn data-v-f5ba67cb" bindtap="{{n}}"><text class="copy-icon data-v-f5ba67cb">📋</text><text class="copy-text data-v-f5ba67cb">复制</text></view></view><view class="card-content data-v-f5ba67cb"><textarea class="text-area readonly data-v-f5ba67cb" value="{{o}}" disabled auto-height maxlength="{{p}}"/></view></view><view class="card data-v-f5ba67cb"><view class="card-header data-v-f5ba67cb"><text class="header-title data-v-f5ba67cb">常用Unicode字符</text></view><view class="card-content data-v-f5ba67cb"><view class="unicode-grid data-v-f5ba67cb"><view wx:for="{{q}}" wx:for-item="item" wx:key="d" class="unicode-item data-v-f5ba67cb" bindtap="{{item.e}}"><view class="char-display data-v-f5ba67cb"><text class="char data-v-f5ba67cb">{{item.a}}</text><view class="char-info data-v-f5ba67cb"><text class="char-desc data-v-f5ba67cb">{{item.b}}</text><text class="char-code data-v-f5ba67cb">{{item.c}}</text></view></view></view></view></view></view><view class="card data-v-f5ba67cb"><view class="card-header data-v-f5ba67cb"><text class="header-title data-v-f5ba67cb">使用说明</text></view><view class="card-content data-v-f5ba67cb"><view class="instructions data-v-f5ba67cb"><text class="instruction-item data-v-f5ba67cb">• Unicode是国际标准字符编码</text><text class="instruction-item data-v-f5ba67cb">• 支持文字与Unicode编码互相转换</text><text class="instruction-item data-v-f5ba67cb">• 格式：\\uXXXX (XXXX为4位十六进制数)</text><text class="instruction-item data-v-f5ba67cb">• 适用于程序开发和国际化处理</text><text class="instruction-item data-v-f5ba67cb">• 支持表情符号和特殊字符</text></view></view></view></view></view>