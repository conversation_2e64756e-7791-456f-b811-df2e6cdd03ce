"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  name: "RandomAvatar",
  data() {
    return {
      selectedStyle: "cartoon",
      selectedGender: "male",
      selectedGirlStyle: 1,
      avatars: [],
      imageLoadStates: {},
      // 图片加载状态
      isGenerating: false,
      showPreview: false,
      previewUrl: "",
      toolService: new utils_toolService.ToolService(),
      styles: [
        { id: "cartoon", name: "卡通风格", icon: "🎨", desc: "可爱的卡通头像" },
        { id: "realistic", name: "写实风格", icon: "📸", desc: "真实感头像" },
        { id: "pixel", name: "像素风格", icon: "🎮", desc: "8位像素头像" },
        { id: "abstract", name: "抽象风格", icon: "🌈", desc: "艺术抽象头像" }
      ],
      genderOptions: [
        { id: "male", name: "男性", icon: "👨", desc: "男性头像" },
        { id: "female", name: "女性", icon: "👩", desc: "女性头像" },
        { id: "random", name: "随机", icon: "🎲", desc: "随机性别" }
      ],
      girlStyles: [
        { id: 1, name: "酷girl" },
        { id: 2, name: "动漫" },
        { id: 3, name: "手绘" },
        { id: 4, name: "黑白" },
        { id: 5, name: "欧美" },
        { id: 6, name: "森系" },
        { id: 7, name: "伤感" }
      ],
      usageTips: [
        "适用于社交平台、游戏账号等场景",
        "可批量生成，提供多种选择",
        "支持不同风格，满足个性需求",
        "一键下载，方便保存使用"
      ]
    };
  },
  methods: {
    selectStyle(style) {
      this.selectedStyle = style.id;
    },
    selectGender(gender) {
      this.selectedGender = gender.id;
    },
    selectGirlStyle(style) {
      this.selectedGirlStyle = style.id;
    },
    async generateAvatars() {
      this.isGenerating = true;
      try {
        const params = {
          style: this.selectedStyle,
          gender: this.selectedGender,
          girlType: this.selectedGirlStyle,
          count: 3
        };
        const result = await this.toolService.getRandomAvatars(params);
        if (result.success) {
          this.avatars = result.data.avatars;
          this.imageLoadStates = {};
          result.data.avatars.forEach((avatar, index) => {
            this.$set(this.imageLoadStates, index, false);
          });
          common_vendor.index.showToast({
            title: "生成成功",
            icon: "success",
            duration: 1500
          });
        } else {
          throw new Error(result.message || "生成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/random-avatar.vue:225", "生成头像失败:", error);
        common_vendor.index.showToast({
          title: error.message || "生成失败",
          icon: "error",
          duration: 2e3
        });
        this.avatars = [];
      } finally {
        this.isGenerating = false;
      }
    },
    downloadAvatar(avatarUrl, index) {
      common_vendor.index.downloadFile({
        url: avatarUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.showToast({
                  title: "保存成功",
                  icon: "success",
                  duration: 2e3
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/tools/random-avatar.vue:253", "保存失败:", err);
                common_vendor.index.showToast({
                  title: "保存失败",
                  icon: "error",
                  duration: 2e3
                });
              }
            });
          }
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/random-avatar.vue:264", "下载失败:", err);
          common_vendor.index.showToast({
            title: "下载失败",
            icon: "error",
            duration: 2e3
          });
        }
      });
    },
    downloadAll() {
      if (this.avatars.length === 0) {
        common_vendor.index.showToast({
          title: "暂无头像",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在保存..."
      });
      let savedCount = 0;
      this.avatars.forEach((avatar, index) => {
        setTimeout(() => {
          common_vendor.index.downloadFile({
            url: avatar,
            success: (res) => {
              if (res.statusCode === 200) {
                common_vendor.index.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: () => {
                    savedCount++;
                    if (savedCount === this.avatars.length) {
                      common_vendor.index.hideLoading();
                      common_vendor.index.showToast({
                        title: "全部保存成功",
                        icon: "success",
                        duration: 2e3
                      });
                    }
                  },
                  fail: () => {
                    savedCount++;
                    if (savedCount === this.avatars.length) {
                      common_vendor.index.hideLoading();
                      common_vendor.index.showToast({
                        title: "部分保存失败",
                        icon: "error",
                        duration: 2e3
                      });
                    }
                  }
                });
              }
            }
          });
        }, index * 200);
      });
    },
    previewAvatar(avatarUrl) {
      this.previewUrl = avatarUrl;
      this.showPreview = true;
    },
    closePreview() {
      this.showPreview = false;
      this.previewUrl = "";
    },
    onImageError(e) {
      common_vendor.index.__f__("error", "at pages/tools/random-avatar.vue:338", "图片加载失败:", e);
    },
    onImageLoad(e) {
      common_vendor.index.__f__("log", "at pages/tools/random-avatar.vue:343", "图片加载成功:", e);
      setTimeout(() => {
        this.avatars.forEach((avatar, index) => {
          this.$set(this.imageLoadStates, index, true);
        });
      }, 100);
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_svg = common_vendor.resolveComponent("svg");
  (_component_path + _component_svg)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.styles, (style, k0, i0) => {
      return {
        a: common_vendor.t(style.icon),
        b: common_vendor.t(style.name),
        c: common_vendor.t(style.desc),
        d: style.id,
        e: common_vendor.o(($event) => $options.selectStyle(style), style.id),
        f: common_vendor.n($data.selectedStyle === style.id ? "selected" : "")
      };
    }),
    b: common_vendor.f($data.genderOptions, (gender, k0, i0) => {
      return {
        a: common_vendor.t(gender.icon),
        b: common_vendor.t(gender.name),
        c: gender.id,
        d: common_vendor.o(($event) => $options.selectGender(gender), gender.id),
        e: common_vendor.n($data.selectedGender === gender.id ? "selected" : "")
      };
    }),
    c: $data.selectedGender === "female"
  }, $data.selectedGender === "female" ? {
    d: common_vendor.f($data.girlStyles, (style, k0, i0) => {
      return {
        a: common_vendor.t(style.name),
        b: style.id,
        c: common_vendor.o(($event) => $options.selectGirlStyle(style), style.id),
        d: common_vendor.n($data.selectedGirlStyle === style.id ? "selected" : "")
      };
    })
  } : {}, {
    e: common_vendor.t($data.isGenerating ? "🔄" : "🔄"),
    f: common_vendor.t($data.isGenerating ? "生成中..." : "生成随机头像"),
    g: common_vendor.o((...args) => $options.generateAvatars && $options.generateAvatars(...args)),
    h: $data.isGenerating,
    i: common_vendor.n($data.isGenerating ? "loading" : ""),
    j: $data.avatars.length > 0
  }, $data.avatars.length > 0 ? {
    k: common_vendor.o((...args) => $options.downloadAll && $options.downloadAll(...args)),
    l: common_vendor.f($data.avatars, (avatar, index, i0) => {
      return common_vendor.e({
        a: avatar,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), index),
        c: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args), index),
        d: index,
        e: !$data.imageLoadStates[index]
      }, !$data.imageLoadStates[index] ? {} : {}, {
        f: index,
        g: common_vendor.o(($event) => $options.previewAvatar(avatar), index)
      });
    })
  } : {}, {
    m: $data.showPreview
  }, $data.showPreview ? {
    n: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    o: $data.previewUrl,
    p: common_vendor.p({
      d: "M12 5v14M5 12l7 7 7-7"
    }),
    q: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "28",
      height: "24",
      fill: "none",
      stroke: "white",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    r: common_vendor.o(($event) => $options.downloadAvatar($data.previewUrl, 0)),
    s: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {}, {
    t: common_vendor.f($data.usageTips, (tip, index, i0) => {
      return {
        a: common_vendor.t(tip),
        b: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b4dba492"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/random-avatar.js.map
