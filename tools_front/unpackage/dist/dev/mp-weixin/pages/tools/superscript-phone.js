"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      phoneNumber: "",
      exampleNumbers: [
        "13800138000",
        "+86 138 0013 8000",
        "(010) 12345678",
        "************",
        "020-88888888"
      ],
      superscriptMap: {
        "0": "⁰",
        "1": "¹",
        "2": "²",
        "3": "³",
        "4": "⁴",
        "5": "⁵",
        "6": "⁶",
        "7": "⁷",
        "8": "⁸",
        "9": "⁹",
        "+": "⁺",
        "-": "⁻",
        "(": "⁽",
        ")": "⁾",
        " ": " "
      }
    };
  },
  computed: {
    superscriptResult() {
      return this.phoneNumber ? this.convertToSuperscript(this.phoneNumber) : "";
    }
  },
  methods: {
    handleInput() {
      if (this.phoneNumber.length > 30) {
        this.phoneNumber = this.phoneNumber.substring(0, 30);
        common_vendor.index.showToast({
          title: "电话号码过长",
          icon: "none",
          duration: 1500
        });
      }
    },
    convertToSuperscript(text) {
      return text.split("").map((char) => this.superscriptMap[char] || char).join("");
    },
    clearInput() {
      this.phoneNumber = "";
    },
    copyText(text) {
      if (!text) {
        common_vendor.index.showToast({
          title: "没有可复制的内容",
          icon: "none"
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
          if (common_vendor.index.vibrateShort) {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }
        }
      });
    },
    setExample(example) {
      this.phoneNumber = example;
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort({
          type: "light"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.phoneNumber = $event.detail.value, (...args) => $options.handleInput && $options.handleInput(...args)]),
    b: $data.phoneNumber,
    c: $data.phoneNumber.length > 0
  }, $data.phoneNumber.length > 0 ? {
    d: common_vendor.o((...args) => $options.clearInput && $options.clearInput(...args))
  } : {}, {
    e: $options.superscriptResult
  }, $options.superscriptResult ? {
    f: common_vendor.t($data.phoneNumber),
    g: common_vendor.t($options.superscriptResult),
    h: common_vendor.o(($event) => $options.copyText($options.superscriptResult))
  } : {}, {
    i: common_vendor.f($data.exampleNumbers, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: common_vendor.t($options.convertToSuperscript(example)),
        c: common_vendor.o(($event) => $options.copyText($options.convertToSuperscript(example)), index),
        d: index,
        e: common_vendor.o(($event) => $options.setExample(example), index)
      };
    }),
    j: common_vendor.f($data.superscriptMap, (superscript, original, i0) => {
      return {
        a: common_vendor.t(original),
        b: common_vendor.t(superscript),
        c: original
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-036c56cd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/superscript-phone.js.map
