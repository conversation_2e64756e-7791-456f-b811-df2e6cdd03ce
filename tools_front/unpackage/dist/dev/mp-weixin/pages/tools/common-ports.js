"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchQuery: "",
      selectedCategory: "全部",
      categories: ["全部", "TCP", "UDP", "TCP/UDP"],
      ports: [
        // Web服务
        { port: 80, service: "HTTP", protocol: "TCP", description: "Web服务器默认端口" },
        { port: 443, service: "HTTPS", protocol: "TCP", description: "SSL/TLS加密Web服务" },
        { port: 8080, service: "HTTP-Alt", protocol: "TCP", description: "HTTP备用端口" },
        { port: 3e3, service: "Node.js", protocol: "TCP", description: "Node.js开发服务器" },
        { port: 8e3, service: "HTTP-Alt", protocol: "TCP", description: "HTTP开发服务器" },
        { port: 8081, service: "Nexus", protocol: "TCP", description: "Nexus私服" },
        { port: 5601, service: "Kibana", protocol: "TCP", description: "Kibana可视化" },
        { port: 8888, service: "Jupyter", protocol: "TCP", description: "Jupyter Notebook" },
        { port: 8443, service: "HTTPS-Alt", protocol: "TCP", description: "HTTPS备用端口" },
        { port: 25565, service: "Minecraft", protocol: "TCP", description: "Minecraft游戏服务器" },
        { port: 25575, service: "Minecraft RCON", protocol: "TCP", description: "Minecraft远程控制" },
        { port: 7001, service: "WebLogic", protocol: "TCP", description: "WebLogic控制台" },
        { port: 7002, service: "WebLogic SSL", protocol: "TCP", description: "WebLogic SSL" },
        // 邮件服务
        { port: 25, service: "SMTP", protocol: "TCP", description: "简单邮件传输协议" },
        { port: 110, service: "POP3", protocol: "TCP", description: "邮局协议版本3" },
        { port: 143, service: "IMAP", protocol: "TCP", description: "互联网消息访问协议" },
        { port: 993, service: "IMAPS", protocol: "TCP", description: "IMAP over SSL" },
        { port: 995, service: "POP3S", protocol: "TCP", description: "POP3 over SSL" },
        // 文件传输
        { port: 20, service: "FTP-Data", protocol: "TCP", description: "FTP数据传输" },
        { port: 21, service: "FTP", protocol: "TCP", description: "文件传输协议" },
        { port: 22, service: "SSH/SFTP", protocol: "TCP", description: "安全外壳协议" },
        { port: 23, service: "Telnet", protocol: "TCP", description: "远程登录协议" },
        { port: 69, service: "TFTP", protocol: "UDP", description: "简单文件传输协议" },
        { port: 990, service: "FTPS", protocol: "TCP", description: "FTP over SSL" },
        { port: 2049, service: "NFS", protocol: "TCP", description: "网络文件系统" },
        { port: 873, service: "Rsync", protocol: "TCP", description: "Rsync文件同步" },
        // 数据库
        { port: 3306, service: "MySQL", protocol: "TCP", description: "MySQL数据库" },
        { port: 33060, service: "MySQL X", protocol: "TCP", description: "MySQL X协议" },
        { port: 5432, service: "PostgreSQL", protocol: "TCP", description: "PostgreSQL数据库" },
        { port: 1521, service: "Oracle", protocol: "TCP", description: "Oracle数据库" },
        { port: 1433, service: "SQL Server", protocol: "TCP", description: "Microsoft SQL Server" },
        { port: 27017, service: "MongoDB", protocol: "TCP", description: "MongoDB数据库" },
        { port: 27018, service: "MongoDB Shard", protocol: "TCP", description: "MongoDB分片" },
        { port: 27019, service: "MongoDB Config", protocol: "TCP", description: "MongoDB配置服务器" },
        { port: 6379, service: "Redis", protocol: "TCP", description: "Redis数据库" },
        { port: 11211, service: "Memcached", protocol: "TCP", description: "Memcached缓存" },
        { port: 5e4, service: "DB2", protocol: "TCP", description: "IBM DB2数据库" },
        // 消息队列/中间件
        { port: 5672, service: "RabbitMQ", protocol: "TCP", description: "RabbitMQ消息队列" },
        { port: 15672, service: "RabbitMQ管理", protocol: "TCP", description: "RabbitMQ管理界面" },
        { port: 61616, service: "ActiveMQ", protocol: "TCP", description: "ActiveMQ消息队列" },
        { port: 9092, service: "Kafka", protocol: "TCP", description: "Kafka消息队列" },
        { port: 2181, service: "Zookeeper", protocol: "TCP", description: "Zookeeper协调服务" },
        { port: 2888, service: "Zookeeper Follower", protocol: "TCP", description: "Zookeeper集群Follower" },
        { port: 3888, service: "Zookeeper Election", protocol: "TCP", description: "Zookeeper集群选举" },
        { port: 6379, service: "Redis", protocol: "TCP", description: "Redis数据库" },
        // 云原生/容器
        { port: 2375, service: "Docker", protocol: "TCP", description: "Docker远程API" },
        { port: 2376, service: "Docker SSL", protocol: "TCP", description: "Docker远程API(SSL)" },
        { port: 5e3, service: "Docker Registry", protocol: "TCP", description: "Docker私有仓库" },
        { port: 5001, service: "Docker Registry SSL", protocol: "TCP", description: "Docker私有仓库(SSL)" },
        { port: 6443, service: "K8s API", protocol: "TCP", description: "Kubernetes API Server" },
        { port: 10250, service: "Kubelet", protocol: "TCP", description: "Kubernetes Kubelet" },
        { port: 10255, service: "Kubelet ReadOnly", protocol: "TCP", description: "Kubelet只读端口" },
        { port: 2379, service: "etcd", protocol: "TCP", description: "etcd主节点" },
        { port: 2380, service: "etcd Peer", protocol: "TCP", description: "etcd集群通信" },
        // 监控/可视化
        { port: 9090, service: "Prometheus", protocol: "TCP", description: "Prometheus监控" },
        { port: 8086, service: "InfluxDB", protocol: "TCP", description: "InfluxDB时序数据库" },
        { port: 3e3, service: "Grafana", protocol: "TCP", description: "Grafana可视化" },
        // DNS和网络
        { port: 53, service: "DNS", protocol: "TCP/UDP", description: "域名解析服务" },
        { port: 5353, service: "mDNS", protocol: "UDP", description: "多播DNS" },
        { port: 67, service: "DHCP Server", protocol: "UDP", description: "DHCP服务器" },
        { port: 68, service: "DHCP Client", protocol: "UDP", description: "DHCP客户端" },
        { port: 123, service: "NTP", protocol: "UDP", description: "网络时间协议" },
        { port: 161, service: "SNMP", protocol: "UDP", description: "简单网络管理协议" },
        { port: 162, service: "SNMP Trap", protocol: "UDP", description: "SNMP陷阱" },
        { port: 389, service: "LDAP", protocol: "TCP", description: "轻量目录访问协议" },
        { port: 636, service: "LDAPS", protocol: "TCP", description: "LDAP over SSL" },
        { port: 514, service: "Syslog", protocol: "UDP", description: "系统日志协议" },
        { port: 873, service: "Rsync", protocol: "TCP", description: "Rsync文件同步" },
        // 远程访问
        { port: 3389, service: "RDP", protocol: "TCP", description: "远程桌面协议" },
        { port: 5900, service: "VNC", protocol: "TCP", description: "虚拟网络计算" },
        { port: 4505, service: "SaltStack Master", protocol: "TCP", description: "SaltStack主控" },
        { port: 4506, service: "SaltStack Minion", protocol: "TCP", description: "SaltStack被控" },
        // IoT/工业/其他
        { port: 1883, service: "MQTT", protocol: "TCP", description: "物联网MQTT协议" },
        { port: 8883, service: "MQTT SSL", protocol: "TCP", description: "MQTT加密协议" },
        { port: 54321, service: "ZeroMQ", protocol: "TCP", description: "ZeroMQ消息队列" },
        { port: 2049, service: "NFS", protocol: "TCP", description: "网络文件系统" },
        { port: 6e3, service: "X11", protocol: "TCP", description: "X Window系统" },
        { port: 6001, service: "X11-1", protocol: "TCP", description: "X Window系统" },
        { port: 6002, service: "X11-2", protocol: "TCP", description: "X Window系统" },
        { port: 6003, service: "X11-3", protocol: "TCP", description: "X Window系统" },
        { port: 6004, service: "X11-4", protocol: "TCP", description: "X Window系统" },
        { port: 6005, service: "X11-5", protocol: "TCP", description: "X Window系统" }
      ]
    };
  },
  computed: {
    filteredPorts() {
      return this.ports.filter((port) => {
        const matchesSearch = this.searchQuery === "" || port.port.toString().includes(this.searchQuery) || port.service.toLowerCase().includes(this.searchQuery.toLowerCase()) || port.description.toLowerCase().includes(this.searchQuery.toLowerCase());
        const matchesCategory = this.selectedCategory === "全部" || port.protocol === this.selectedCategory;
        return matchesSearch && matchesCategory;
      });
    }
  },
  methods: {
    setSelectedCategory(category) {
      this.selectedCategory = category;
    },
    copyToClipboard(port) {
      common_vendor.index.setClipboardData({
        data: port.toString(),
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        }
      });
    },
    getProtocolColorClass(protocol) {
      const colorMap = {
        "TCP": "protocol-tcp",
        "UDP": "protocol-udp",
        "TCP/UDP": "protocol-both"
      };
      return colorMap[protocol] || "protocol-default";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.searchQuery,
    b: common_vendor.o(($event) => $data.searchQuery = $event.detail.value),
    c: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category),
        b: category,
        c: $data.selectedCategory === category ? 1 : "",
        d: common_vendor.o(($event) => $options.setSelectedCategory(category), category)
      };
    }),
    d: common_vendor.f($options.filteredPorts, (port, index, i0) => {
      return {
        a: common_vendor.t(port.port),
        b: common_vendor.t(port.protocol),
        c: common_vendor.n($options.getProtocolColorClass(port.protocol)),
        d: common_vendor.o(($event) => $options.copyToClipboard(port.port), index),
        e: common_vendor.t(port.service),
        f: common_vendor.t(port.description),
        g: index
      };
    }),
    e: $options.filteredPorts.length === 0
  }, $options.filteredPorts.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a8cf9a83"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/common-ports.js.map
