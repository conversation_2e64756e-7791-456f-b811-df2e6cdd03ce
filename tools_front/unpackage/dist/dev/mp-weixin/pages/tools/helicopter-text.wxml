<view class="min-h-screen bg-gray-50 data-v-cfac2752"><view class="p-4 space-y-4 data-v-cfac2752"><view class="bg-white rounded-lg shadow-sm data-v-cfac2752"><view class="p-4 border-b border-gray-200 data-v-cfac2752"><view class="text-base font-medium data-v-cfac2752">ASCII直升机</view></view><view class="p-4 data-v-cfac2752"><view class="space-y-4 data-v-cfac2752"><view wx:for="{{a}}" wx:for-item="helicopter" wx:key="b" class="p-4 bg-sky-50 rounded-lg border hover-bg-sky-100 cursor-pointer transition-colors data-v-cfac2752" bindtap="{{helicopter.c}}"><view class="text-center font-mono text-sm text-sky-700 whitespace-pre data-v-cfac2752">{{helicopter.a}}</view></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-cfac2752"><view class="p-4 border-b border-gray-200 data-v-cfac2752"><view class="text-base font-medium data-v-cfac2752">ASCII飞机</view></view><view class="p-4 data-v-cfac2752"><view class="space-y-4 data-v-cfac2752"><view wx:for="{{b}}" wx:for-item="airplane" wx:key="b" class="p-4 bg-blue-50 rounded-lg border hover-bg-blue-100 cursor-pointer transition-colors data-v-cfac2752" bindtap="{{airplane.c}}"><view class="text-center font-mono text-sm text-blue-700 whitespace-pre data-v-cfac2752">{{airplane.a}}</view></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-cfac2752"><view class="p-4 border-b border-gray-200 data-v-cfac2752"><view class="text-base font-medium data-v-cfac2752">飞行表情</view></view><view class="p-4 data-v-cfac2752"><view class="grid grid-cols-4 gap-3 data-v-cfac2752"><view wx:for="{{c}}" wx:for-item="emoji" wx:key="b" class="p-4 bg-cyan-50 rounded-lg border hover-bg-cyan-100 cursor-pointer transition-colors text-center data-v-cfac2752" bindtap="{{emoji.c}}"><label class="text-3xl data-v-cfac2752">{{emoji.a}}</label></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-cfac2752"><view class="p-4 border-b border-gray-200 data-v-cfac2752"><view class="text-base font-medium data-v-cfac2752">航空符号</view></view><view class="p-4 data-v-cfac2752"><view class="grid grid-cols-2 gap-3 data-v-cfac2752"><view wx:for="{{d}}" wx:for-item="symbol" wx:key="b" class="p-3 bg-indigo-50 rounded-lg border hover-bg-indigo-100 cursor-pointer transition-colors text-center data-v-cfac2752" bindtap="{{symbol.c}}"><label class="text-lg data-v-cfac2752">{{symbol.a}}</label></view></view></view></view><view class="bg-white rounded-lg shadow-sm data-v-cfac2752"><view class="p-4 border-b border-gray-200 data-v-cfac2752"><view class="text-base font-medium data-v-cfac2752">使用说明</view></view><view class="p-4 text-sm text-gray-600 space-y-2 data-v-cfac2752"><view class="data-v-cfac2752">• 提供直升机和飞机的ASCII艺术</view><view class="data-v-cfac2752">• 包含各种飞行主题符号</view><view class="data-v-cfac2752">• 适用于航空、军事等主题</view><view class="data-v-cfac2752">• 点击任意图案即可复制</view><view class="data-v-cfac2752">• 展示飞行器的文字艺术美</view></view></view></view></view>