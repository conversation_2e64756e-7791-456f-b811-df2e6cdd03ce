/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-74a8804b {
  display: flex;
}
.flex-1.data-v-74a8804b {
  flex: 1;
}
.items-center.data-v-74a8804b {
  align-items: center;
}
.justify-center.data-v-74a8804b {
  justify-content: center;
}
.justify-between.data-v-74a8804b {
  justify-content: space-between;
}
.text-center.data-v-74a8804b {
  text-align: center;
}
.rounded.data-v-74a8804b {
  border-radius: 3px;
}
.rounded-lg.data-v-74a8804b {
  border-radius: 6px;
}
.shadow.data-v-74a8804b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-74a8804b {
  padding: 16rpx;
}
.m-4.data-v-74a8804b {
  margin: 16rpx;
}
.mb-4.data-v-74a8804b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-74a8804b {
  margin-top: 16rpx;
}
.gas-price-page.data-v-74a8804b {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-74a8804b {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-74a8804b {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.card-header.data-v-74a8804b {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-icon.data-v-74a8804b {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}
.card-header .header-icon .gas-icon.data-v-74a8804b {
  font-size: 60rpx;
}
.card-header .header-title.data-v-74a8804b {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-header .header-subtitle.data-v-74a8804b {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.main-content.data-v-74a8804b {
  display: flex;
  flex-direction: column;
  gap: 50rpx;
}
.section-title.data-v-74a8804b {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.region-section .region-picker .picker-content.data-v-74a8804b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}
.region-section .region-picker .picker-content .picker-icon.data-v-74a8804b {
  font-size: 32rpx;
  color: white;
}
.region-section .region-picker .picker-content .picker-text.data-v-74a8804b {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}
.region-section .region-picker .picker-content .picker-arrow.data-v-74a8804b {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.price-section .section-header.data-v-74a8804b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.price-section .section-header .refresh-btn.data-v-74a8804b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.price-section .section-header .refresh-btn .refresh-icon.data-v-74a8804b {
  font-size: 24rpx;
  color: #6b7280;
}
.price-section .section-header .refresh-btn .refresh-text.data-v-74a8804b {
  font-size: 24rpx;
  color: #6b7280;
}
.price-section .section-header .refresh-btn.data-v-74a8804b:active {
  background: #e5e7eb;
  transform: translateY(2rpx);
}
.price-section .price-grid.data-v-74a8804b {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.price-section .price-grid .price-card.data-v-74a8804b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  position: relative;
  overflow: hidden;
}
.price-section .price-grid .price-card .fuel-info.data-v-74a8804b {
  position: relative;
  z-index: 2;
}
.price-section .price-grid .price-card .fuel-info .fuel-type.data-v-74a8804b {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 16rpx;
}
.price-section .price-grid .price-card .fuel-info .price-display.data-v-74a8804b {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}
.price-section .price-grid .price-card .fuel-info .price-display .price-symbol.data-v-74a8804b {
  font-size: 28rpx;
  color: white;
  margin-right: 4rpx;
}
.price-section .price-grid .price-card .fuel-info .price-display .price-value.data-v-74a8804b {
  font-size: 48rpx;
  font-weight: 700;
  color: white;
}
.price-section .price-grid .price-card .fuel-info .price-display .price-unit.data-v-74a8804b {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 8rpx;
}
.price-section .price-grid .price-card .fuel-info .tank-cost.data-v-74a8804b {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
}
.price-section .price-grid .price-card .fuel-icon.data-v-74a8804b {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
  opacity: 0.3;
}
.trend-section .trend-list.data-v-74a8804b {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.trend-section .trend-list .trend-item.data-v-74a8804b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-radius: 16rpx;
}
.trend-section .trend-list .trend-item .trend-indicator.data-v-74a8804b {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.trend-section .trend-list .trend-item .trend-indicator .trend-dot.data-v-74a8804b {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}
.trend-section .trend-list .trend-item .trend-indicator .trend-dot.up.data-v-74a8804b {
  background: #ef4444;
}
.trend-section .trend-list .trend-item .trend-indicator .trend-dot.down.data-v-74a8804b {
  background: #10b981;
}
.trend-section .trend-list .trend-item .trend-indicator .trend-dot.neutral.data-v-74a8804b {
  background: #3b82f6;
}
.trend-section .trend-list .trend-item .trend-indicator .trend-label.data-v-74a8804b {
  font-size: 28rpx;
  color: #374151;
}
.trend-section .trend-list .trend-item .trend-value.data-v-74a8804b {
  font-size: 28rpx;
  font-weight: 600;
}
.trend-section .trend-list .trend-item .trend-value.up.data-v-74a8804b {
  color: #ef4444;
}
.trend-section .trend-list .trend-item .trend-value.down.data-v-74a8804b {
  color: #10b981;
}
.trend-section .trend-list .trend-item .trend-value.neutral.data-v-74a8804b {
  color: #3b82f6;
}
.trend-section .trend-list .trend-item.trend-up.data-v-74a8804b {
  background: #fef2f2;
}
.trend-section .trend-list .trend-item.trend-down.data-v-74a8804b {
  background: #f0fdf4;
}
.trend-section .trend-list .trend-item.trend-neutral.data-v-74a8804b {
  background: #eff6ff;
}
.tips-section .tips-list .tip-item.data-v-74a8804b {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  margin-bottom: 20rpx;
}
.tips-section .tips-list .tip-item .tip-icon.data-v-74a8804b {
  font-size: 32rpx;
  margin-top: 4rpx;
}
.tips-section .tips-list .tip-item .tip-text.data-v-74a8804b {
  flex: 1;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
}
.update-time.data-v-74a8804b {
  text-align: center;
  margin-top: 20rpx;
}
.update-time .update-text.data-v-74a8804b {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.price-grid.data-v-74a8804b {
    grid-template-columns: 1fr !important;
}
.section-header.data-v-74a8804b {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 20rpx;
}
}