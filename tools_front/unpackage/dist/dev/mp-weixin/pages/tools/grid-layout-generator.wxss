/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b660904b {
  display: flex;
}
.flex-1.data-v-b660904b {
  flex: 1;
}
.items-center.data-v-b660904b {
  align-items: center;
}
.justify-center.data-v-b660904b {
  justify-content: center;
}
.justify-between.data-v-b660904b {
  justify-content: space-between;
}
.text-center.data-v-b660904b {
  text-align: center;
}
.rounded.data-v-b660904b {
  border-radius: 3px;
}
.rounded-lg.data-v-b660904b {
  border-radius: 6px;
}
.shadow.data-v-b660904b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b660904b {
  padding: 16rpx;
}
.m-4.data-v-b660904b {
  margin: 16rpx;
}
.mb-4.data-v-b660904b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b660904b {
  margin-top: 16rpx;
}
.grid-generator-container.data-v-b660904b {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-b660904b, .settings-card.data-v-b660904b, .templates-card.data-v-b660904b, .preview-card.data-v-b660904b, .code-card.data-v-b660904b, .tips-card.data-v-b660904b {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-b660904b {
  display: flex;
  align-items: center;
}
.header-icon.data-v-b660904b {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-title.data-v-b660904b {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-b660904b {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-b660904b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-b660904b {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.settings-row.data-v-b660904b {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.setting-group.data-v-b660904b {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.setting-label.data-v-b660904b {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.template-grid.data-v-b660904b {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.template-item.data-v-b660904b {
  padding: 16rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.template-item.data-v-b660904b:active {
  transform: scale(0.98);
  border-color: #007AFF;
}
.template-preview.data-v-b660904b {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 4rpx;
  height: 80rpx;
  margin-bottom: 12rpx;
}
.template-block.data-v-b660904b {
  border-radius: 4rpx;
}
.template-name.data-v-b660904b {
  font-size: 22rpx;
  color: #333;
}
.action-btn.data-v-b660904b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.preview-container.data-v-b660904b {
  border: 2rpx dashed #d1d5db;
  border-radius: 12rpx;
  padding: 24rpx;
}
.grid-preview.data-v-b660904b {
  width: 100%;
}
.grid-item.data-v-b660904b {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}
.grid-item.data-v-b660904b:active {
  transform: scale(0.95);
}
.item-text.data-v-b660904b {
  font-size: 20rpx;
  font-weight: 500;
  color: white;
  text-align: center;
}
.copy-btn.data-v-b660904b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.code-block.data-v-b660904b {
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx;
}
.code-text.data-v-b660904b {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 22rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
}
.tips-content.data-v-b660904b {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-b660904b {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-b660904b {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-b660904b {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.modal-overlay.data-v-b660904b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-b660904b {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin: 0 40rpx;
  max-width: 600rpx;
  width: 100%;
}
.modal-header.data-v-b660904b {
  margin-bottom: 24rpx;
}
.modal-title.data-v-b660904b {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.modal-body.data-v-b660904b {
  margin-bottom: 32rpx;
}
.area-input.data-v-b660904b {
  width: 100%;
  padding: 16rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 26rpx;
}
.modal-footer.data-v-b660904b {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
}
.modal-btn.data-v-b660904b {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.modal-btn.cancel.data-v-b660904b {
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  color: #666;
}
.modal-btn.confirm.data-v-b660904b {
  background: #007AFF;
  color: white;
  border: none;
}
.preview-header.data-v-b660904b,
.code-header.data-v-b660904b {
  position: relative;
}
.right-btn.data-v-b660904b {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 0;
  display: flex;
  align-items: center;
  gap: 8rpx;
}