/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-da0a0d3e {
  display: flex;
}
.flex-1.data-v-da0a0d3e {
  flex: 1;
}
.items-center.data-v-da0a0d3e {
  align-items: center;
}
.justify-center.data-v-da0a0d3e {
  justify-content: center;
}
.justify-between.data-v-da0a0d3e {
  justify-content: space-between;
}
.text-center.data-v-da0a0d3e {
  text-align: center;
}
.rounded.data-v-da0a0d3e {
  border-radius: 3px;
}
.rounded-lg.data-v-da0a0d3e {
  border-radius: 6px;
}
.shadow.data-v-da0a0d3e {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-da0a0d3e {
  padding: 16rpx;
}
.m-4.data-v-da0a0d3e {
  margin: 16rpx;
}
.mb-4.data-v-da0a0d3e {
  margin-bottom: 16rpx;
}
.mt-4.data-v-da0a0d3e {
  margin-top: 16rpx;
}
.image-watermarker.data-v-da0a0d3e {
  min-height: 100vh;
  background: #f8f9fa;
}
.image-watermarker .content.data-v-da0a0d3e {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.image-watermarker .section-card.data-v-da0a0d3e {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.image-watermarker .section-card .card-header.data-v-da0a0d3e {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.image-watermarker .section-card .card-header .header-icon.data-v-da0a0d3e {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.image-watermarker .section-card .card-header .header-title.data-v-da0a0d3e {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.image-watermarker .section-card .card-content .upload-area.data-v-da0a0d3e {
  border: 4rpx dashed #e5e7eb;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: #f9fafb;
  transition: all 0.3s;
}
.image-watermarker .section-card .card-content .upload-area.data-v-da0a0d3e:active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-watermarker .section-card .card-content .upload-area .upload-icon.data-v-da0a0d3e {
  margin-bottom: 24rpx;
}
.image-watermarker .section-card .card-content .upload-area .upload-icon .icon-text.data-v-da0a0d3e {
  font-size: 80rpx;
  opacity: 0.6;
}
.image-watermarker .section-card .card-content .upload-area .upload-title.data-v-da0a0d3e {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.image-watermarker .section-card .card-content .upload-area .upload-desc.data-v-da0a0d3e {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}
.image-watermarker .section-card .card-content .upload-area .selected-file.data-v-da0a0d3e {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
}
.image-watermarker .section-card .card-content .upload-area .selected-file .file-name.data-v-da0a0d3e {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
}
.image-watermarker .section-card .card-content .image-preview.data-v-da0a0d3e {
  margin-top: 32rpx;
}
.image-watermarker .section-card .card-content .image-preview .preview-header.data-v-da0a0d3e {
  margin-bottom: 16rpx;
}
.image-watermarker .section-card .card-content .image-preview .preview-header .preview-title.data-v-da0a0d3e {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.image-watermarker .section-card .card-content .image-preview .preview-image.data-v-da0a0d3e {
  width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.image-watermarker .setting-group.data-v-da0a0d3e {
  margin-bottom: 40rpx;
}
.image-watermarker .setting-group.data-v-da0a0d3e:last-child {
  margin-bottom: 0;
}
.image-watermarker .setting-group .setting-label.data-v-da0a0d3e {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.image-watermarker .setting-group .text-input.data-v-da0a0d3e {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  background: white;
}
.image-watermarker .setting-group .text-input.data-v-da0a0d3e:focus {
  border-color: #3b82f6;
  outline: none;
}
.image-watermarker .setting-group .watermark-types.data-v-da0a0d3e {
  display: flex;
  gap: 16rpx;
}
.image-watermarker .setting-group .watermark-types .type-item.data-v-da0a0d3e {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx 16rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: white;
  transition: all 0.2s;
}
.image-watermarker .setting-group .watermark-types .type-item.active.data-v-da0a0d3e {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-watermarker .setting-group .watermark-types .type-item .type-icon.data-v-da0a0d3e {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}
.image-watermarker .setting-group .watermark-types .type-item .type-name.data-v-da0a0d3e {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.image-watermarker .setting-group .position-grid.data-v-da0a0d3e {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}
.image-watermarker .setting-group .position-grid .position-item.data-v-da0a0d3e {
  padding: 24rpx 16rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  text-align: center;
  background: white;
  transition: all 0.2s;
}
.image-watermarker .setting-group .position-grid .position-item.active.data-v-da0a0d3e {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-watermarker .setting-group .position-grid .position-item .position-name.data-v-da0a0d3e {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.image-watermarker .setting-group .slider-container.data-v-da0a0d3e {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.image-watermarker .setting-group .slider-container slider.data-v-da0a0d3e {
  flex: 1;
}
.image-watermarker .setting-group .slider-container .slider-value.data-v-da0a0d3e {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  min-width: 80rpx;
  text-align: right;
}
.image-watermarker .setting-group .color-options.data-v-da0a0d3e {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}
.image-watermarker .setting-group .color-options .color-item.data-v-da0a0d3e {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 3rpx solid #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.image-watermarker .setting-group .color-options .color-item.active.data-v-da0a0d3e {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.2);
}
.image-watermarker .setting-group .color-options .color-item .color-check.data-v-da0a0d3e {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
}
.image-watermarker .process-button.data-v-da0a0d3e {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.image-watermarker .process-button.data-v-da0a0d3e:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
}
.image-watermarker .process-button.disabled.data-v-da0a0d3e {
  opacity: 0.6;
  background: #d1d5db;
  box-shadow: none;
}
.image-watermarker .result-container .result-image.data-v-da0a0d3e {
  width: 100%;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.image-watermarker .result-container .result-actions.data-v-da0a0d3e {
  display: flex;
  gap: 16rpx;
}
.image-watermarker .result-container .result-actions .action-button.data-v-da0a0d3e {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.image-watermarker .result-container .result-actions .action-button.secondary.data-v-da0a0d3e {
  background: #f3f4f6;
  color: #374151;
}
.image-watermarker .result-container .result-actions .action-button.secondary.data-v-da0a0d3e:active {
  background: #e5e7eb;
  transform: scale(0.98);
}
.image-watermarker .result-container .result-actions .action-button.primary.data-v-da0a0d3e {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.image-watermarker .result-container .result-actions .action-button.primary.data-v-da0a0d3e:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.image-watermarker .result-container .result-actions .action-button .button-icon.data-v-da0a0d3e {
  font-size: 24rpx;
}
.image-watermarker .result-container .result-actions .action-button .button-text.data-v-da0a0d3e {
  font-size: 28rpx;
}
.image-watermarker .usage-list .usage-item.data-v-da0a0d3e {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.image-watermarker .usage-list .usage-item.data-v-da0a0d3e:last-child {
  margin-bottom: 0;
}
.image-watermarker .hidden-canvas.data-v-da0a0d3e {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  z-index: -1;
}
.upload-card.data-v-da0a0d3e {
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.glass-effect.data-v-da0a0d3e {
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.7);
  border: 0.5px solid rgba(255, 255, 255, 0.3);
}
.upload-area.data-v-da0a0d3e {
  padding: 32px;
  border: 1px dashed rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.upload-area.data-v-da0a0d3e:active {
  transform: scale(0.98);
}
.upload-icon-container.data-v-da0a0d3e {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
}
.upload-icon.data-v-da0a0d3e {
  font-size: 32px;
}
.upload-text-container.data-v-da0a0d3e {
  padding: 12px 24px;
  border-radius: 12px;
  text-align: center;
}
.upload-title.data-v-da0a0d3e {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4px;
}
.upload-format.data-v-da0a0d3e {
  font-size: 14px;
  color: #666;
}
.selected-file.data-v-da0a0d3e {
  margin-top: 16px;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.file-name.data-v-da0a0d3e {
  font-size: 14px;
  color: #1a1a1a;
  flex: 1;
  margin-right: 12px;
}
.file-preview.data-v-da0a0d3e {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
}
.preview-thumbnail.data-v-da0a0d3e {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (prefers-color-scheme: dark) {
.glass-effect.data-v-da0a0d3e {
    background: rgba(26, 26, 26, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
}
.upload-title.data-v-da0a0d3e {
    color: #f5f5f5;
}
.upload-format.data-v-da0a0d3e {
    color: #999;
}
.file-name.data-v-da0a0d3e {
    color: #f5f5f5;
}
}
.action-card.data-v-da0a0d3e {
  margin-top: 24px;
}
.process-button.data-v-da0a0d3e {
  width: 100%;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  border: none;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
  overflow: hidden;
}
.process-button.data-v-da0a0d3e:active {
  transform: scale(0.98);
}
.process-button.processing.data-v-da0a0d3e {
  background: linear-gradient(135deg, #1e40af, #1d4ed8);
  pointer-events: none;
}
.button-icon.data-v-da0a0d3e {
  font-size: 20px;
  margin-right: 8px;
}
.button-text.data-v-da0a0d3e {
  font-size: 16px;
}
.progress-ring.data-v-da0a0d3e {
  position: absolute;
  right: 16px;
  width: 20px;
  height: 20px;
}
.progress-circle.data-v-da0a0d3e {
  width: 100%;
  height: 100%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: #fff;
  border-radius: 50%;
  animation: spin-da0a0d3e 1s linear infinite;
}
@keyframes spin-da0a0d3e {
to {
    transform: rotate(360deg);
}
}
.result-card.data-v-da0a0d3e {
  margin-top: 24px;
}
.result-container.data-v-da0a0d3e {
  padding: 16px;
  border-radius: 16px;
}
.result-image.data-v-da0a0d3e {
  width: 100%;
  border-radius: 8px;
  margin-bottom: 16px;
}
.result-actions.data-v-da0a0d3e {
  display: flex;
  gap: 12px;
}
.action-button.data-v-da0a0d3e {
  flex: 1;
  height: 44px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  border: none;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-button.data-v-da0a0d3e:active {
  transform: scale(0.98);
}
.action-button.secondary.data-v-da0a0d3e {
  color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}
.action-button.primary.data-v-da0a0d3e {
  color: #fff;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}
@media (prefers-color-scheme: dark) {
.process-button.data-v-da0a0d3e {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}
.process-button.processing.data-v-da0a0d3e {
    background: linear-gradient(135deg, #1e40af, #1e3a8a);
}
.action-button.secondary.data-v-da0a0d3e {
    color: #60a5fa;
    background: rgba(96, 165, 250, 0.1);
}
.action-button.primary.data-v-da0a0d3e {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}
}