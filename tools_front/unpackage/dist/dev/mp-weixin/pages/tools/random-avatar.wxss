/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b4dba492 {
  display: flex;
}
.flex-1.data-v-b4dba492 {
  flex: 1;
}
.items-center.data-v-b4dba492 {
  align-items: center;
}
.justify-center.data-v-b4dba492 {
  justify-content: center;
}
.justify-between.data-v-b4dba492 {
  justify-content: space-between;
}
.text-center.data-v-b4dba492 {
  text-align: center;
}
.rounded.data-v-b4dba492 {
  border-radius: 3px;
}
.rounded-lg.data-v-b4dba492 {
  border-radius: 6px;
}
.shadow.data-v-b4dba492 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b4dba492 {
  padding: 16rpx;
}
.m-4.data-v-b4dba492 {
  margin: 16rpx;
}
.mb-4.data-v-b4dba492 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b4dba492 {
  margin-top: 16rpx;
}
.avatar-generator.data-v-b4dba492 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-b4dba492 {
  padding: 32rpx;
}
.section.data-v-b4dba492 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-b4dba492 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  position: relative;
}
.section-icon.data-v-b4dba492 {
  font-size: 24rpx;
  margin-right: 12rpx;
}
.section-title.data-v-b4dba492 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.download-all-btn.data-v-b4dba492 {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  color: #666666;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.download-all-btn .btn-icon.data-v-b4dba492 {
  margin-right: 8rpx;
  font-size: 20rpx;
}
.download-all-btn .btn-text.data-v-b4dba492 {
  font-size: 24rpx;
}
.style-grid.data-v-b4dba492 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.style-card.data-v-b4dba492 {
  background: #fafafa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.style-card.selected.data-v-b4dba492 {
  background: #f8f9fa;
  border-color: #495057;
  box-shadow: 0 4rpx 12rpx rgba(73, 80, 87, 0.15);
}
.style-card.data-v-b4dba492:active {
  transform: scale(0.98);
}
.style-content.data-v-b4dba492 {
  text-align: left;
}
.style-header.data-v-b4dba492 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.style-icon.data-v-b4dba492 {
  font-size: 24rpx;
  margin-right: 12rpx;
}
.style-name.data-v-b4dba492 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.style-desc.data-v-b4dba492 {
  font-size: 22rpx;
  color: #666666;
  line-height: 1.4;
}
.gender-grid.data-v-b4dba492 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}
.girl-style-grid.data-v-b4dba492 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}
.gender-card.data-v-b4dba492 {
  background: #fafafa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.gender-card.selected.data-v-b4dba492 {
  background: #f8f9fa;
  border-color: #495057;
  box-shadow: 0 4rpx 12rpx rgba(73, 80, 87, 0.15);
}
.gender-card.data-v-b4dba492:active {
  transform: scale(0.98);
}
.girl-style-card.data-v-b4dba492 {
  background: #fafafa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.girl-style-card.selected.data-v-b4dba492 {
  background: #f8f9fa;
  border-color: #495057;
  box-shadow: 0 4rpx 12rpx rgba(73, 80, 87, 0.15);
}
.girl-style-card.data-v-b4dba492:active {
  transform: scale(0.98);
}
.gender-icon.data-v-b4dba492 {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  display: block;
}
.gender-name.data-v-b4dba492 {
  font-size: 24rpx;
  font-weight: 500;
  color: #1a1a1a;
}
.girl-style-name.data-v-b4dba492 {
  font-size: 24rpx;
  font-weight: 500;
  color: #1a1a1a;
}
.generate-section.data-v-b4dba492 {
  margin-bottom: 24rpx;
}
.generate-btn.data-v-b4dba492 {
  width: 100%;
  background: #343a40;
  color: #ffffff;
  border: none;
  border-radius: 12rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s cubic-bezier(0.2, 0, 0.1, 1);
}
.generate-btn.data-v-b4dba492:active {
  transform: translateY(1rpx);
  background: #23272b;
}
.generate-btn.loading.data-v-b4dba492 {
  background: #6c757d;
  opacity: 0.8;
}
.generate-btn .btn-icon.data-v-b4dba492 {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.generate-btn .btn-text.data-v-b4dba492 {
  font-size: 32rpx;
}
.avatar-grid.data-v-b4dba492 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.avatar-item.data-v-b4dba492 {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
}
.avatar-image.data-v-b4dba492 {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 12rpx;
  background-color: #f8f9fa;
}
.loading-overlay.data-v-b4dba492 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
}
.loading-text.data-v-b4dba492 {
  font-size: 24rpx;
  color: #666;
}

/* 头像预览弹窗样式 */
.preview-mask.data-v-b4dba492 {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-dialog.data-v-b4dba492 {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 400rpx;
  max-width: 90vw;
  position: relative;
}
.preview-close.data-v-b4dba492 {
  position: absolute;
  top: 18rpx;
  right: 18rpx;
  color: #222;
  font-size: 40rpx;
  font-weight: 600;
  line-height: 1;
  z-index: 2;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.15s, color 0.15s;
  background: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.preview-close.data-v-b4dba492:active,
.preview-close.data-v-b4dba492:hover {
  opacity: 1;
  color: #000;
}
.preview-image.data-v-b4dba492 {
  width: 320rpx;
  height: 320rpx;
  border-radius: 12rpx;
  object-fit: contain;
  margin-bottom: 32rpx;
  margin-top: 8rpx;
}
.preview-download.data-v-b4dba492 {
  background: #111;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 0 48rpx;
  height: 56rpx;
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: background 0.15s;
}
.preview-download.data-v-b4dba492:active {
  background: #333;
}
.download-svg.data-v-b4dba492 {
  display: inline-block;
  vertical-align: middle;
}
.download-text.data-v-b4dba492 {
  font-size: 28rpx;
  margin-left: 2rpx;
}
.tips-section.data-v-b4dba492 {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1rpx solid #dee2e6;
}
.tips-list.data-v-b4dba492 {
  margin: 0;
}
.tip-item.data-v-b4dba492 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.tip-item.data-v-b4dba492:last-child {
  margin-bottom: 0;
}
.tip-bullet.data-v-b4dba492 {
  color: #495057;
  font-size: 24rpx;
  margin-right: 12rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}
.tip-text.data-v-b4dba492 {
  font-size: 26rpx;
  color: #495057;
  line-height: 1.5;
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.style-grid.data-v-b4dba492 {
    grid-template-columns: 1fr;
}
.gender-grid.data-v-b4dba492 {
    grid-template-columns: repeat(2, 1fr);
}
.avatar-grid.data-v-b4dba492 {
    grid-template-columns: repeat(2, 1fr);
}
.preview-dialog.data-v-b4dba492 {
    min-width: 0;
    width: 90vw;
    padding: 24rpx 8rpx 16rpx 8rpx;
}
.preview-image.data-v-b4dba492 {
    width: 80vw;
    height: 80vw;
}
.preview-close.data-v-b4dba492 {
    top: 8rpx;
    right: 8rpx;
    width: 40rpx;
    height: 40rpx;
    font-size: 32rpx;
}
}