/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-57fc8a40 {
  display: flex;
}
.flex-1.data-v-57fc8a40 {
  flex: 1;
}
.items-center.data-v-57fc8a40 {
  align-items: center;
}
.justify-center.data-v-57fc8a40 {
  justify-content: center;
}
.justify-between.data-v-57fc8a40 {
  justify-content: space-between;
}
.text-center.data-v-57fc8a40 {
  text-align: center;
}
.rounded.data-v-57fc8a40 {
  border-radius: 3px;
}
.rounded-lg.data-v-57fc8a40 {
  border-radius: 6px;
}
.shadow.data-v-57fc8a40 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-57fc8a40 {
  padding: 16rpx;
}
.m-4.data-v-57fc8a40 {
  margin: 16rpx;
}
.mb-4.data-v-57fc8a40 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-57fc8a40 {
  margin-top: 16rpx;
}
.md5-modifier.data-v-57fc8a40 {
  min-height: 100vh;
  background: #f8f9fa;
}
.md5-modifier .content.data-v-57fc8a40 {
  padding: 40rpx;
}
.md5-modifier .section-card.data-v-57fc8a40 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.md5-modifier .section-card .section-title.data-v-57fc8a40 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 32rpx;
}
.md5-modifier .section-card .section-title.success.data-v-57fc8a40 {
  color: #10b981;
}
.md5-modifier .upload-area.data-v-57fc8a40 {
  border: 4rpx dashed #ff6b6b;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  transition: all 0.3s;
}
.md5-modifier .upload-area.data-v-57fc8a40:active {
  border-color: #ff5252;
  background: rgba(255, 107, 107, 0.05);
}
.md5-modifier .upload-area .upload-icon.data-v-57fc8a40 {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}
.md5-modifier .upload-area .upload-title.data-v-57fc8a40 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}
.md5-modifier .upload-area .upload-desc.data-v-57fc8a40 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}
.md5-modifier .upload-area .upload-tip.data-v-57fc8a40 {
  font-size: 24rpx;
  color: #ff6b6b;
  display: block;
}
.md5-modifier .file-info.data-v-57fc8a40 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24rpx;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 16rpx;
  margin-top: 24rpx;
  border-left: 8rpx solid #ff6b6b;
  flex-wrap: wrap;
  gap: 16rpx;
}
.md5-modifier .file-info .file-details.data-v-57fc8a40 {
  flex: 1;
  min-width: 0;
}
.md5-modifier .file-info .file-details .file-name.data-v-57fc8a40 {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff6b6b;
  display: block;
  margin-bottom: 8rpx;
  word-break: break-all;
  line-height: 1.4;
}
.md5-modifier .file-info .file-details .file-size.data-v-57fc8a40 {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.md5-modifier .file-info .clear-button.data-v-57fc8a40 {
  width: 48rpx;
  height: 48rpx;
  background: #ff6b6b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.md5-modifier .file-info .clear-button .clear-icon.data-v-57fc8a40 {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}
.md5-modifier .info-list .info-item.data-v-57fc8a40, .md5-modifier .info-list .result-item.data-v-57fc8a40, .md5-modifier .result-list .info-item.data-v-57fc8a40, .md5-modifier .result-list .result-item.data-v-57fc8a40 {
  display: flex;
  flex-direction: column;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.md5-modifier .info-list .info-item.data-v-57fc8a40:last-child, .md5-modifier .info-list .result-item.data-v-57fc8a40:last-child, .md5-modifier .result-list .info-item.data-v-57fc8a40:last-child, .md5-modifier .result-list .result-item.data-v-57fc8a40:last-child {
  margin-bottom: 0;
}
.md5-modifier .info-list .info-item.success.data-v-57fc8a40, .md5-modifier .info-list .result-item.success.data-v-57fc8a40, .md5-modifier .result-list .info-item.success.data-v-57fc8a40, .md5-modifier .result-list .result-item.success.data-v-57fc8a40 {
  background: rgba(16, 185, 129, 0.1);
  border-left: 8rpx solid #10b981;
}
.md5-modifier .info-list .info-item .info-label.data-v-57fc8a40, .md5-modifier .info-list .info-item .result-label.data-v-57fc8a40, .md5-modifier .info-list .result-item .info-label.data-v-57fc8a40, .md5-modifier .info-list .result-item .result-label.data-v-57fc8a40, .md5-modifier .result-list .info-item .info-label.data-v-57fc8a40, .md5-modifier .result-list .info-item .result-label.data-v-57fc8a40, .md5-modifier .result-list .result-item .info-label.data-v-57fc8a40, .md5-modifier .result-list .result-item .result-label.data-v-57fc8a40 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 12rpx;
}
.md5-modifier .info-list .info-item .info-value.data-v-57fc8a40, .md5-modifier .info-list .info-item .result-value.data-v-57fc8a40, .md5-modifier .info-list .result-item .info-value.data-v-57fc8a40, .md5-modifier .info-list .result-item .result-value.data-v-57fc8a40, .md5-modifier .result-list .info-item .info-value.data-v-57fc8a40, .md5-modifier .result-list .info-item .result-value.data-v-57fc8a40, .md5-modifier .result-list .result-item .info-value.data-v-57fc8a40, .md5-modifier .result-list .result-item .result-value.data-v-57fc8a40 {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 16rpx;
}
.md5-modifier .info-list .info-item .info-value .md5-text.data-v-57fc8a40, .md5-modifier .info-list .info-item .result-value .md5-text.data-v-57fc8a40, .md5-modifier .info-list .result-item .info-value .md5-text.data-v-57fc8a40, .md5-modifier .info-list .result-item .result-value .md5-text.data-v-57fc8a40, .md5-modifier .result-list .info-item .info-value .md5-text.data-v-57fc8a40, .md5-modifier .result-list .info-item .result-value .md5-text.data-v-57fc8a40, .md5-modifier .result-list .result-item .info-value .md5-text.data-v-57fc8a40, .md5-modifier .result-list .result-item .result-value .md5-text.data-v-57fc8a40 {
  font-size: 24rpx;
  font-family: monospace;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  padding: 16rpx;
  border-radius: 8rpx;
  word-break: break-all;
  line-height: 1.4;
  flex: 1;
  min-width: 0;
}
.md5-modifier .info-list .info-item .info-value .copy-button.data-v-57fc8a40, .md5-modifier .info-list .info-item .result-value .copy-button.data-v-57fc8a40, .md5-modifier .info-list .result-item .info-value .copy-button.data-v-57fc8a40, .md5-modifier .info-list .result-item .result-value .copy-button.data-v-57fc8a40, .md5-modifier .result-list .info-item .info-value .copy-button.data-v-57fc8a40, .md5-modifier .result-list .info-item .result-value .copy-button.data-v-57fc8a40, .md5-modifier .result-list .result-item .info-value .copy-button.data-v-57fc8a40, .md5-modifier .result-list .result-item .result-value .copy-button.data-v-57fc8a40 {
  width: 48rpx;
  height: 48rpx;
  background: #ff6b6b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.md5-modifier .info-list .info-item .info-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .info-list .info-item .result-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .info-list .result-item .info-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .info-list .result-item .result-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .result-list .info-item .info-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .result-list .info-item .result-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .result-list .result-item .info-value .copy-button .copy-icon.data-v-57fc8a40, .md5-modifier .result-list .result-item .result-value .copy-button .copy-icon.data-v-57fc8a40 {
  font-size: 24rpx;
  color: white;
}
.md5-modifier .methods-grid.data-v-57fc8a40 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 32rpx;
}
.md5-modifier .methods-grid .method-option.data-v-57fc8a40 {
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s;
}
.md5-modifier .methods-grid .method-option.active.data-v-57fc8a40 {
  border-color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
}
.md5-modifier .methods-grid .method-option .method-title.data-v-57fc8a40 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.md5-modifier .methods-grid .method-option .method-desc.data-v-57fc8a40 {
  font-size: 24rpx;
  color: #666;
  display: block;
}
.md5-modifier .modify-button.data-v-57fc8a40 {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  border-radius: 20rpx;
  padding: 32rpx;
  text-align: center;
  transition: all 0.3s;
}
.md5-modifier .modify-button.disabled.data-v-57fc8a40 {
  opacity: 0.5;
  background: #d1d5db;
}
.md5-modifier .modify-button .button-text.data-v-57fc8a40 {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}
.md5-modifier .action-buttons.data-v-57fc8a40 {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;
}
.md5-modifier .action-buttons .action-button.data-v-57fc8a40 {
  flex: 1;
  padding: 32rpx;
  border-radius: 20rpx;
  text-align: center;
  transition: all 0.2s ease;
}
.md5-modifier .action-buttons .action-button.data-v-57fc8a40:active {
  transform: scale(0.98);
}
.md5-modifier .action-buttons .action-button.download.data-v-57fc8a40 {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.md5-modifier .action-buttons .action-button.download.data-v-57fc8a40:active {
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.md5-modifier .action-buttons .action-button.reset.data-v-57fc8a40 {
  background: white;
  border: 2rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.md5-modifier .action-buttons .action-button.reset.data-v-57fc8a40:active {
  background: #f9fafb;
  border-color: #d1d5db;
}
.md5-modifier .action-buttons .action-button .button-text.data-v-57fc8a40 {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}
.md5-modifier .action-buttons .action-button .button-text.reset-text.data-v-57fc8a40 {
  color: #374151;
}
.md5-modifier .usage-list .usage-item.data-v-57fc8a40 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.md5-modifier .usage-list .usage-item.data-v-57fc8a40:last-child {
  margin-bottom: 0;
}
.md5-modifier .warning-section .warning-card.data-v-57fc8a40 {
  background: #fff7ed;
  border-radius: 20rpx;
  padding: 32rpx;
  border-left: 8rpx solid #fbbf24;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.md5-modifier .warning-section .warning-card .warning-header.data-v-57fc8a40 {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.md5-modifier .warning-section .warning-card .warning-header .warning-icon.data-v-57fc8a40 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.md5-modifier .warning-section .warning-card .warning-header .warning-title.data-v-57fc8a40 {
  font-size: 32rpx;
  font-weight: 600;
  color: #92400e;
}
.md5-modifier .warning-section .warning-card .warning-list .warning-item.data-v-57fc8a40 {
  font-size: 26rpx;
  color: #92400e;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.5;
}
.md5-modifier .warning-section .warning-card .warning-list .warning-item.data-v-57fc8a40:last-child {
  margin-bottom: 0;
}