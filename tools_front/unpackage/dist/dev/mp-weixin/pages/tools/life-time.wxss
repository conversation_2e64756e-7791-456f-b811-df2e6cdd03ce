/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-78b447dc {
  display: flex;
}
.flex-1.data-v-78b447dc {
  flex: 1;
}
.items-center.data-v-78b447dc {
  align-items: center;
}
.justify-center.data-v-78b447dc {
  justify-content: center;
}
.justify-between.data-v-78b447dc {
  justify-content: space-between;
}
.text-center.data-v-78b447dc {
  text-align: center;
}
.rounded.data-v-78b447dc {
  border-radius: 3px;
}
.rounded-lg.data-v-78b447dc {
  border-radius: 6px;
}
.shadow.data-v-78b447dc {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-78b447dc {
  padding: 16rpx;
}
.m-4.data-v-78b447dc {
  margin: 16rpx;
}
.mb-4.data-v-78b447dc {
  margin-bottom: 16rpx;
}
.mt-4.data-v-78b447dc {
  margin-top: 16rpx;
}
.time-flows-tool.data-v-78b447dc {
  min-height: 100vh;
  background: #f9fafb;
  padding: 30rpx;
}
.tips-card.data-v-78b447dc, .reminder-card.data-v-78b447dc, .stage-card.data-v-78b447dc, .quote-card.data-v-78b447dc, .stats-card.data-v-78b447dc, .progress-card.data-v-78b447dc, .settings-card.data-v-78b447dc, .header-card.data-v-78b447dc {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.tips-card.data-v-78b447dc:hover, .reminder-card.data-v-78b447dc:hover, .stage-card.data-v-78b447dc:hover, .quote-card.data-v-78b447dc:hover, .stats-card.data-v-78b447dc:hover, .progress-card.data-v-78b447dc:hover, .settings-card.data-v-78b447dc:hover, .header-card.data-v-78b447dc:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.card-header.data-v-78b447dc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1rpx solid #e8ecff;
}
.card-header .header-icon.data-v-78b447dc {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.card-header .header-title.data-v-78b447dc {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.header-card.data-v-78b447dc {
  margin-bottom: 40rpx;
}
.header-card .header-content.data-v-78b447dc {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}
.header-card .header-content .header-icon.data-v-78b447dc {
  font-size: 64rpx;
  margin-right: 30rpx;
  animation: pulse-78b447dc 2s infinite;
}
.header-card .header-content .header-info.data-v-78b447dc {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.header-card .header-content .header-info .header-title.data-v-78b447dc {
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  line-height: 1.2;
}
.header-card .header-content .header-info .header-subtitle.data-v-78b447dc {
  font-size: 28rpx;
  opacity: 0.9;
  color: white;
  line-height: 1.4;
}
.settings-card .settings-content.data-v-78b447dc {
  padding: 30rpx;
}
.settings-card .setting-row.data-v-78b447dc {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.settings-card .setting-item .setting-label.data-v-78b447dc {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 16rpx;
}
.settings-card .setting-item .date-picker .date-display.data-v-78b447dc {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1rpx solid #e8ecff;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.settings-card .setting-item .date-picker .date-display.data-v-78b447dc:hover {
  border-color: #6366f1;
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.1);
}
.settings-card .setting-item .date-picker .date-display .date-text.data-v-78b447dc {
  font-size: 28rpx;
  color: #1a1a1a;
}
.settings-card .setting-item .date-picker .date-display .date-icon.data-v-78b447dc {
  font-size: 32rpx;
}
.settings-card .setting-item .life-expectancy-picker.data-v-78b447dc {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.settings-card .setting-item .life-expectancy-picker .expectancy-slider.data-v-78b447dc {
  flex: 1;
}
.settings-card .setting-item .life-expectancy-picker .expectancy-value.data-v-78b447dc {
  font-size: 26rpx;
  font-weight: 600;
  color: #6366f1;
  min-width: 80rpx;
  text-align: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8ecff 100%);
  border-radius: 12rpx;
}
.progress-card .progress-percentage.data-v-78b447dc {
  font-size: 32rpx;
  font-weight: 700;
  color: #6366f1;
  background: linear-gradient(135deg, #f0f4ff 0%, #e8ecff 100%);
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
}
.progress-card .progress-content.data-v-78b447dc {
  padding: 30rpx;
}
.progress-card .progress-bar-wrapper.data-v-78b447dc {
  margin-bottom: 40rpx;
}
.progress-card .progress-bar-wrapper .progress-bar.data-v-78b447dc {
  width: 100%;
  height: 20rpx;
  background: #e5e7eb;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}
.progress-card .progress-bar-wrapper .progress-bar .progress-fill.data-v-78b447dc {
  height: 100%;
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 100%);
  transition: width 0.8s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
}
.progress-card .progress-bar-wrapper .progress-bar .progress-fill.data-v-78b447dc::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 20rpx;
  height: 100%;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 100%);
  animation: shimmer-78b447dc 2s infinite;
}
.progress-card .progress-bar-wrapper .progress-text.data-v-78b447dc {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
}
.progress-card .progress-bar-wrapper .progress-text .progress-lived.data-v-78b447dc {
  color: #10b981;
  font-weight: 500;
}
.progress-card .progress-bar-wrapper .progress-text .progress-remaining.data-v-78b447dc {
  color: #f59e0b;
  font-weight: 500;
}
.progress-card .milestone-indicators.data-v-78b447dc {
  position: relative;
  height: 60rpx;
  margin-top: 30rpx;
}
.progress-card .milestone-indicators .milestone.data-v-78b447dc {
  position: relative;
}
.progress-card .milestone-indicators .milestone .milestone-dot.data-v-78b447dc {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #e5e7eb;
  transform: translateX(-50%);
  transition: all 0.3s ease;
}
.progress-card .milestone-indicators .milestone .milestone-dot.reached.data-v-78b447dc {
  background: #6366f1;
  box-shadow: 0 0 0 4rpx rgba(99, 102, 241, 0.2);
}
.progress-card .milestone-indicators .milestone .milestone-dot .milestone-label.data-v-78b447dc {
  position: absolute;
  top: 24rpx;
  left: 50%;
  transform: translateX(-50%);
  font-size: 20rpx;
  color: #6b7280;
  white-space: nowrap;
}
.stats-card .stats-content.data-v-78b447dc {
  padding: 30rpx;
}
.stats-card .stats-grid.data-v-78b447dc {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.stats-card .stats-row.data-v-78b447dc {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.stats-card .stat-item.data-v-78b447dc {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1rpx solid #e8ecff;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}
.stats-card .stat-item.data-v-78b447dc:hover {
  border-color: #6366f1;
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.15);
  transform: translateY(-4rpx);
}
.stats-card .stat-item .stat-number.data-v-78b447dc {
  font-size: 32rpx;
  font-weight: 700;
  color: #6366f1;
  margin-bottom: 8rpx;
}
.stats-card .stat-item .stat-label.data-v-78b447dc {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}
.stats-card .stat-item .stat-icon.data-v-78b447dc {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  font-size: 24rpx;
  opacity: 0.6;
}
.quote-card .refresh-quote-btn.data-v-78b447dc {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: #6366f1;
  border: none;
  border-radius: 50%;
  transition: all 0.3s ease;
}
.quote-card .refresh-quote-btn.data-v-78b447dc:hover {
  background: #5856eb;
  transform: rotate(180deg);
}
.quote-card .refresh-quote-btn .refresh-icon.data-v-78b447dc {
  font-size: 28rpx;
  color: white;
}
.quote-card .quote-content.data-v-78b447dc {
  padding: 40rpx 30rpx;
  text-align: center;
}
.quote-card .quote-content .quote-text.data-v-78b447dc {
  display: block;
  font-size: 32rpx;
  line-height: 1.6;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  font-style: italic;
}
.quote-card .quote-content .quote-author.data-v-78b447dc {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
}
.stage-card .stage-content.data-v-78b447dc {
  padding: 30rpx;
}
.stage-card .current-stage.data-v-78b447dc {
  padding: 30rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  text-align: center;
}
.stage-card .current-stage .stage-name.data-v-78b447dc {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 12rpx;
}
.stage-card .current-stage .stage-desc.data-v-78b447dc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}
.stage-card .stage-timeline.data-v-78b447dc {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.stage-card .stage-timeline .stage-item.data-v-78b447dc {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.stage-card .stage-timeline .stage-item.completed.data-v-78b447dc {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}
.stage-card .stage-timeline .stage-item.completed .stage-dot.data-v-78b447dc {
  background: #10b981;
}
.stage-card .stage-timeline .stage-item.current.data-v-78b447dc {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 2rpx solid #6366f1;
}
.stage-card .stage-timeline .stage-item.current .stage-dot.data-v-78b447dc {
  background: #6366f1;
  animation: pulse-78b447dc 2s infinite;
}
.stage-card .stage-timeline .stage-item.future.data-v-78b447dc {
  background: #f9fafb;
}
.stage-card .stage-timeline .stage-item.future .stage-dot.data-v-78b447dc {
  background: #d1d5db;
}
.stage-card .stage-timeline .stage-item .stage-dot.data-v-78b447dc {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}
.stage-card .stage-timeline .stage-item .stage-title.data-v-78b447dc {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.stage-card .stage-timeline .stage-item .stage-ages.data-v-78b447dc {
  font-size: 24rpx;
  color: #6b7280;
}
.reminder-card .reminder-content.data-v-78b447dc {
  padding: 30rpx;
}
.reminder-card .reminder-item.data-v-78b447dc {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.reminder-card .reminder-item.data-v-78b447dc:last-child {
  margin-bottom: 0;
}
.reminder-card .reminder-item.data-v-78b447dc:hover {
  transform: translateX(8rpx);
  box-shadow: 0 6rpx 20rpx rgba(245, 158, 11, 0.2);
}
.reminder-card .reminder-item.data-v-78b447dc::before {
  content: "💡";
  font-size: 28rpx;
  margin-right: 16rpx;
}
.reminder-card .reminder-item .reminder-text.data-v-78b447dc {
  font-size: 26rpx;
  color: #92400e;
  line-height: 1.5;
}
.tips-card .tips-content.data-v-78b447dc {
  padding: 30rpx;
}
.tips-card .tip-item.data-v-78b447dc {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tips-card .tip-item.data-v-78b447dc:last-child {
  margin-bottom: 0;
}
.tips-card .tip-item .tip-bullet.data-v-78b447dc {
  font-size: 28rpx;
  color: #6366f1;
  margin-right: 16rpx;
  margin-top: 4rpx;
  font-weight: bold;
}
.tips-card .tip-item .tip-text.data-v-78b447dc {
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
  flex: 1;
}
@keyframes pulse-78b447dc {
0%, 100% {
    opacity: 1;
    transform: scale(1);
}
50% {
    opacity: 0.8;
    transform: scale(1.1);
}
}
@keyframes shimmer-78b447dc {
0% {
    opacity: 0;
}
50% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@media (max-width: 750rpx) {
.stats-grid.data-v-78b447dc {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.stats-row.data-v-78b447dc {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.setting-row.data-v-78b447dc {
    gap: 24rpx;
}
}