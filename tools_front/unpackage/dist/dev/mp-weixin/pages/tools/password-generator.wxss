/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-94dcd830 {
  display: flex;
}
.flex-1.data-v-94dcd830 {
  flex: 1;
}
.items-center.data-v-94dcd830 {
  align-items: center;
}
.justify-center.data-v-94dcd830 {
  justify-content: center;
}
.justify-between.data-v-94dcd830 {
  justify-content: space-between;
}
.text-center.data-v-94dcd830 {
  text-align: center;
}
.rounded.data-v-94dcd830 {
  border-radius: 3px;
}
.rounded-lg.data-v-94dcd830 {
  border-radius: 6px;
}
.shadow.data-v-94dcd830 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-94dcd830 {
  padding: 16rpx;
}
.m-4.data-v-94dcd830 {
  margin: 16rpx;
}
.mb-4.data-v-94dcd830 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-94dcd830 {
  margin-top: 16rpx;
}
.password-generator.data-v-94dcd830 {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-94dcd830 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-94dcd830 {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-94dcd830 {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-94dcd830 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-94dcd830 {
  padding: 30rpx;
}
.settings-card.data-v-94dcd830 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.settings-card .card-header.data-v-94dcd830 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.settings-card .card-header .key-icon.data-v-94dcd830 {
  font-size: 36rpx;
  margin-right: 16rpx;
  color: #3b82f6;
}
.settings-card .card-header .header-title.data-v-94dcd830 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.settings-card .card-content .setting-item.data-v-94dcd830 {
  margin-bottom: 40rpx;
}
.settings-card .card-content .setting-item .setting-label.data-v-94dcd830 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}
.settings-card .card-content .setting-item .length-slider.data-v-94dcd830 {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  padding-right: 36rpx;
}
.settings-card .card-content .character-options.data-v-94dcd830 {
  margin-bottom: 40rpx;
}
.settings-card .card-content .character-options .option-item.data-v-94dcd830 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.settings-card .card-content .character-options .option-item checkbox.data-v-94dcd830 {
  margin-right: 20rpx;
}
.settings-card .card-content .character-options .option-item .option-label.data-v-94dcd830 {
  font-size: 26rpx;
  color: #666;
}
.settings-card .card-content .batch-setting.data-v-94dcd830 {
  display: flex;
  align-items: center;
}
.settings-card .card-content .batch-setting .batch-label.data-v-94dcd830 {
  font-size: 26rpx;
  color: #666;
  margin-right: 20rpx;
}
.settings-card .card-content .batch-setting .batch-input.data-v-94dcd830 {
  width: 120rpx;
  height: 60rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 26rpx;
  text-align: center;
}
.generate-btn.data-v-94dcd830 {
  background: #3b82f6;
  color: white;
  padding: 40rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 20rpx rgba(59, 130, 246, 0.3);
}
.generate-btn .generate-icon.data-v-94dcd830 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.generate-btn .generate-text.data-v-94dcd830 {
  font-size: 32rpx;
  font-weight: 600;
}
.passwords-card.data-v-94dcd830 {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.passwords-card .card-header.data-v-94dcd830 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f3f4f6;
}
.passwords-card .card-header .header-left.data-v-94dcd830 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.passwords-card .card-header .header-left .shield-icon.data-v-94dcd830 {
  font-size: 36rpx;
}
.passwords-card .card-header .header-left .header-title.data-v-94dcd830 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.passwords-card .password-item.data-v-94dcd830 {
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}
.passwords-card .password-item.data-v-94dcd830:hover {
  border-color: #3b82f6;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
}
.passwords-card .password-item .password-content.data-v-94dcd830 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.passwords-card .password-item .password-content .password-text.data-v-94dcd830 {
  font-family: "Courier New", monospace;
  font-size: 28rpx;
  color: #1f2937;
  word-break: break-all;
  line-height: 1.5;
  flex: 1;
  margin-right: 20rpx;
  padding: 12rpx 16rpx;
  background: white;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
}
.passwords-card .password-item .password-content .action-buttons.data-v-94dcd830 {
  display: flex;
  gap: 12rpx;
}
.passwords-card .password-item .password-content .action-buttons .save-btn.data-v-94dcd830, .passwords-card .password-item .password-content .action-buttons .copy-btn.data-v-94dcd830 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}
.passwords-card .password-item .password-content .action-buttons .save-btn.data-v-94dcd830:active, .passwords-card .password-item .password-content .action-buttons .copy-btn.data-v-94dcd830:active {
  transform: scale(0.95);
}
.passwords-card .password-item .password-content .action-buttons .save-btn.data-v-94dcd830 {
  background: #dbeafe;
}
.passwords-card .password-item .password-content .action-buttons .save-btn .save-icon.data-v-94dcd830 {
  font-size: 32rpx;
  color: #3b82f6;
}
.passwords-card .password-item .password-content .action-buttons .save-btn.data-v-94dcd830:active {
  background: #bfdbfe;
}
.passwords-card .password-item .password-content .action-buttons .copy-btn.data-v-94dcd830 {
  background: #f3f4f6;
}
.passwords-card .password-item .password-content .action-buttons .copy-btn .copy-icon.data-v-94dcd830 {
  font-size: 32rpx;
  color: #4b5563;
}
.passwords-card .password-item .password-content .action-buttons .copy-btn.data-v-94dcd830:active {
  background: #e5e7eb;
}
.passwords-card .password-item .password-info.data-v-94dcd830 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 0 12rpx;
}
.passwords-card .password-item .password-info .strength-text.data-v-94dcd830 {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.passwords-card .password-item .password-info .strength-text.weak-strength.data-v-94dcd830 {
  color: #ef4444;
  background: #fee2e2;
}
.passwords-card .password-item .password-info .strength-text.medium-strength.data-v-94dcd830 {
  color: #f59e0b;
  background: #fef3c7;
}
.passwords-card .password-item .password-info .strength-text.strong-strength.data-v-94dcd830 {
  color: #10b981;
  background: #d1fae5;
}
.passwords-card .password-item .password-info .length-text.data-v-94dcd830 {
  font-size: 24rpx;
  color: #6b7280;
}
.saved-password-item.data-v-94dcd830 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.3s ease;
}
.saved-password-item.data-v-94dcd830:hover {
  border-color: #3b82f6;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
}
.saved-password-item .saved-password-header.data-v-94dcd830 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.saved-password-item .saved-password-header .purpose-wrapper.data-v-94dcd830 {
  flex: 1;
  margin-right: 20rpx;
}
.saved-password-item .saved-password-header .purpose-wrapper .purpose-label.data-v-94dcd830 {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  display: block;
}
.saved-password-item .saved-password-header .purpose-wrapper .purpose-text.data-v-94dcd830 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
}
.saved-password-item .saved-password-header .action-group.data-v-94dcd830 {
  display: flex;
  gap: 16rpx;
}
.saved-password-item .saved-password-header .action-group .copy-btn.data-v-94dcd830, .saved-password-item .saved-password-header .action-group .delete-btn.data-v-94dcd830 {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}
.saved-password-item .saved-password-header .action-group .copy-btn.data-v-94dcd830:active, .saved-password-item .saved-password-header .action-group .delete-btn.data-v-94dcd830:active {
  transform: scale(0.95);
}
.saved-password-item .saved-password-header .action-group .copy-btn.data-v-94dcd830 {
  background: #e5e7eb;
}
.saved-password-item .saved-password-header .action-group .copy-btn .copy-icon.data-v-94dcd830 {
  font-size: 28rpx;
  color: #4b5563;
}
.saved-password-item .saved-password-header .action-group .copy-btn.data-v-94dcd830:active {
  background: #d1d5db;
}
.saved-password-item .saved-password-header .action-group .delete-btn.data-v-94dcd830 {
  background: #fee2e2;
}
.saved-password-item .saved-password-header .action-group .delete-btn .delete-icon.data-v-94dcd830 {
  font-size: 28rpx;
  color: #ef4444;
}
.saved-password-item .saved-password-header .action-group .delete-btn.data-v-94dcd830:active {
  background: #fecaca;
}
.saved-password-item .password-section.data-v-94dcd830 {
  background: white;
  border-radius: 12rpx;
  padding: 16rpx;
  margin-bottom: 16rpx;
}
.saved-password-item .password-section .password-label.data-v-94dcd830 {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
  display: block;
}
.saved-password-item .password-section .password-content.data-v-94dcd830 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.saved-password-item .password-section .password-content .password-text.data-v-94dcd830 {
  font-family: "Courier New", monospace;
  font-size: 28rpx;
  color: #1f2937;
  word-break: break-all;
  line-height: 1.5;
  flex: 1;
}
.saved-password-item .password-section .password-content .toggle-visibility.data-v-94dcd830 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  background: #f3f4f6;
  transition: all 0.2s ease;
}
.saved-password-item .password-section .password-content .toggle-visibility.data-v-94dcd830:active {
  background: #e5e7eb;
  transform: scale(0.95);
}
.saved-password-item .password-section .password-content .toggle-visibility .visibility-icon.data-v-94dcd830 {
  font-size: 32rpx;
  color: #4b5563;
}
.saved-password-item .item-footer.data-v-94dcd830 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.saved-password-item .item-footer .save-date.data-v-94dcd830 {
  font-size: 22rpx;
  color: #9ca3af;
}
.tips-card.data-v-94dcd830 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.tips-card .card-header.data-v-94dcd830 {
  margin-bottom: 24rpx;
}
.tips-card .card-header .header-title.data-v-94dcd830 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.tips-card .card-content .tips-list .tip-item.data-v-94dcd830 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.action-buttons.data-v-94dcd830 {
  display: flex;
  gap: 20rpx;
}
.save-btn.data-v-94dcd830 {
  width: 60rpx;
  height: 60rpx;
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save-btn .save-icon.data-v-94dcd830 {
  font-size: 28rpx;
  color: #666;
}
.modal-overlay.data-v-94dcd830 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-94dcd830 {
  width: 80%;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}
.modal-content .modal-header.data-v-94dcd830 {
  margin-bottom: 30rpx;
}
.modal-content .modal-header .modal-title.data-v-94dcd830 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.modal-content .modal-body.data-v-94dcd830 {
  margin-bottom: 30rpx;
}
.modal-content .modal-body .input-label.data-v-94dcd830 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}
.modal-content .modal-body .purpose-input.data-v-94dcd830 {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}
.modal-content .modal-body .password-preview.data-v-94dcd830 {
  font-family: monospace;
  font-size: 26rpx;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  word-break: break-all;
}
.modal-content .modal-footer.data-v-94dcd830 {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}
.modal-content .modal-footer .modal-btn.data-v-94dcd830 {
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
}
.modal-content .modal-footer .cancel-btn.data-v-94dcd830 {
  background: #f3f4f6;
  color: #666;
}
.modal-content .modal-footer .confirm-btn.data-v-94dcd830 {
  background: #3b82f6;
  color: white;
}