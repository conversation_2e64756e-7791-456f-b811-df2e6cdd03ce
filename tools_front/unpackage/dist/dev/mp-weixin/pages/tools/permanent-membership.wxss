/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-f649a93e {
  display: flex;
}
.flex-1.data-v-f649a93e {
  flex: 1;
}
.items-center.data-v-f649a93e {
  align-items: center;
}
.justify-center.data-v-f649a93e {
  justify-content: center;
}
.justify-between.data-v-f649a93e {
  justify-content: space-between;
}
.text-center.data-v-f649a93e {
  text-align: center;
}
.rounded.data-v-f649a93e {
  border-radius: 3px;
}
.rounded-lg.data-v-f649a93e {
  border-radius: 6px;
}
.shadow.data-v-f649a93e {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-f649a93e {
  padding: 16rpx;
}
.m-4.data-v-f649a93e {
  margin: 16rpx;
}
.mb-4.data-v-f649a93e {
  margin-bottom: 16rpx;
}
.mt-4.data-v-f649a93e {
  margin-top: 16rpx;
}
.permanent-membership.data-v-f649a93e {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-f649a93e {
  padding: 30rpx;
}
.hero-card.data-v-f649a93e {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 30rpx;
  text-align: center;
}
.hero-card .hero-content .crown-icon.data-v-f649a93e {
  font-size: 96rpx;
  display: block;
  margin-bottom: 30rpx;
}
.hero-card .hero-content .hero-title.data-v-f649a93e {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  display: block;
}
.hero-card .hero-content .hero-desc.data-v-f649a93e {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  display: block;
}
.benefits-card.data-v-f649a93e {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.benefits-card .card-header.data-v-f649a93e {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.benefits-card .card-header .gift-icon.data-v-f649a93e {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.benefits-card .card-header .header-title.data-v-f649a93e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.benefits-card .benefits-grid.data-v-f649a93e {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.benefits-card .benefits-grid .benefit-item.data-v-f649a93e {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 30rpx;
}
.benefits-card .benefits-grid .benefit-item .benefit-icon.data-v-f649a93e {
  font-size: 48rpx;
  margin-right: 20rpx;
}
.benefits-card .benefits-grid .benefit-item .benefit-content .benefit-title.data-v-f649a93e {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  display: block;
}
.benefits-card .benefits-grid .benefit-item .benefit-content .benefit-desc.data-v-f649a93e {
  font-size: 22rpx;
  color: #666;
  display: block;
}
.plans-card.data-v-f649a93e {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.plans-card .card-header.data-v-f649a93e {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.plans-card .card-header .star-icon.data-v-f649a93e {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.plans-card .card-header .header-title.data-v-f649a93e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.plans-card .plans-list .plan-item.data-v-f649a93e {
  position: relative;
  border: 4rpx solid #e5e7eb;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}
.plans-card .plans-list .plan-item.selected.data-v-f649a93e {
  border-color: #8b5cf6;
  background: rgba(139, 92, 246, 0.05);
}
.plans-card .plans-list .plan-item .popular-badge.data-v-f649a93e {
  position: absolute;
  top: -16rpx;
  left: 32rpx;
  background: #8b5cf6;
  border-radius: 20rpx;
  padding: 8rpx 24rpx;
}
.plans-card .plans-list .plan-item .popular-badge .badge-text.data-v-f649a93e {
  font-size: 22rpx;
  color: white;
  font-weight: 500;
}
.plans-card .plans-list .plan-item .plan-header.data-v-f649a93e {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.plans-card .plans-list .plan-item .plan-header .plan-icon.data-v-f649a93e {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}
.plans-card .plans-list .plan-item .plan-header .plan-icon.blue-bg.data-v-f649a93e {
  background: #dbeafe;
}
.plans-card .plans-list .plan-item .plan-header .plan-icon.purple-bg.data-v-f649a93e {
  background: #ede9fe;
}
.plans-card .plans-list .plan-item .plan-header .plan-icon.yellow-bg.data-v-f649a93e {
  background: #fef3c7;
}
.plans-card .plans-list .plan-item .plan-header .plan-icon .icon.data-v-f649a93e {
  font-size: 48rpx;
}
.plans-card .plans-list .plan-item .plan-header .plan-info.data-v-f649a93e {
  flex: 1;
}
.plans-card .plans-list .plan-item .plan-header .plan-info .plan-name.data-v-f649a93e {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
}
.plans-card .plans-list .plan-item .plan-header .plan-info .plan-price.data-v-f649a93e {
  display: flex;
  align-items: center;
}
.plans-card .plans-list .plan-item .plan-header .plan-info .plan-price .current-price.data-v-f649a93e {
  font-size: 48rpx;
  font-weight: 700;
  color: #8b5cf6;
  margin-right: 16rpx;
}
.plans-card .plans-list .plan-item .plan-header .plan-info .plan-price .original-price.data-v-f649a93e {
  font-size: 26rpx;
  color: #9ca3af;
  text-decoration: line-through;
}
.plans-card .plans-list .plan-item .plan-header .selected-icon.data-v-f649a93e {
  width: 48rpx;
  height: 48rpx;
  background: #8b5cf6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.plans-card .plans-list .plan-item .plan-header .selected-icon .check-icon.data-v-f649a93e {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
}
.plans-card .plans-list .plan-item .plan-features .feature-item.data-v-f649a93e {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.plans-card .plans-list .plan-item .plan-features .feature-item .feature-check.data-v-f649a93e {
  font-size: 32rpx;
  color: #10b981;
  margin-right: 16rpx;
  font-weight: bold;
}
.plans-card .plans-list .plan-item .plan-features .feature-item .feature-text.data-v-f649a93e {
  font-size: 26rpx;
  color: #666;
}
.purchase-btn.data-v-f649a93e {
  background: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
  color: white;
  padding: 48rpx;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(139, 92, 246, 0.3);
}
.purchase-btn .btn-icon.data-v-f649a93e {
  font-size: 40rpx;
  margin-right: 16rpx;
}
.purchase-btn .btn-text.data-v-f649a93e {
  font-size: 36rpx;
  font-weight: 600;
}
.disclaimer.data-v-f649a93e {
  text-align: center;
  padding: 0 40rpx;
}
.disclaimer .disclaimer-text.data-v-f649a93e {
  font-size: 24rpx;
  color: #9ca3af;
  margin-bottom: 8rpx;
  display: block;
}