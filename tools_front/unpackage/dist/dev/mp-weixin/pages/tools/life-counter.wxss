/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-e7106439 {
  display: flex;
}
.flex-1.data-v-e7106439 {
  flex: 1;
}
.items-center.data-v-e7106439 {
  align-items: center;
}
.justify-center.data-v-e7106439 {
  justify-content: center;
}
.justify-between.data-v-e7106439 {
  justify-content: space-between;
}
.text-center.data-v-e7106439 {
  text-align: center;
}
.rounded.data-v-e7106439 {
  border-radius: 3px;
}
.rounded-lg.data-v-e7106439 {
  border-radius: 6px;
}
.shadow.data-v-e7106439 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-e7106439 {
  padding: 16rpx;
}
.m-4.data-v-e7106439 {
  margin: 16rpx;
}
.mb-4.data-v-e7106439 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-e7106439 {
  margin-top: 16rpx;
}
.life-counter.data-v-e7106439 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-e7106439 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-e7106439 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-e7106439 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-e7106439 {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-e7106439 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-e7106439 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.quote-section.data-v-e7106439, .category-section.data-v-e7106439, .favorites-section.data-v-e7106439, .featured-section.data-v-e7106439, .help-section.data-v-e7106439 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-e7106439 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-e7106439 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-e7106439 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.quote-card.data-v-e7106439 {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #93c5fd;
  text-align: center;
}
.quote-text.data-v-e7106439 {
  font-size: 32rpx;
  font-weight: 500;
  color: #1d4ed8;
  line-height: 1.6;
  margin-bottom: 32rpx;
  display: block;
}
.quote-actions.data-v-e7106439 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-e7106439 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-e7106439 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
}
.action-btn.primary.data-v-e7106439:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(59, 130, 246, 0.35);
}
.action-btn.primary.loading.data-v-e7106439 {
  opacity: 0.7;
  pointer-events: none;
}
.action-btn.secondary.data-v-e7106439 {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #e5e7eb;
}
.action-btn.secondary.data-v-e7106439:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.btn-icon.data-v-e7106439 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-e7106439 {
  font-size: 26rpx;
}
.category-grid.data-v-e7106439 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.category-item.data-v-e7106439 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bae6fd;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.category-item.data-v-e7106439:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #7dd3fc;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.15);
}
.category-item.active.data-v-e7106439 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: #1d4ed8;
  color: white;
}
.category-item.active .category-name.data-v-e7106439, .category-item.active .category-count.data-v-e7106439 {
  color: white;
}
.category-name.data-v-e7106439 {
  font-size: 26rpx;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 8rpx;
}
.category-count.data-v-e7106439 {
  font-size: 22rpx;
  color: #0284c7;
}
.count-badge.data-v-e7106439 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.favorites-list.data-v-e7106439, .featured-list.data-v-e7106439 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.favorite-item.data-v-e7106439, .featured-item.data-v-e7106439 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bbf7d0;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.favorite-item.data-v-e7106439:hover, .featured-item.data-v-e7106439:hover {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(5, 150, 105, 0.15);
}
.favorite-text.data-v-e7106439, .featured-text.data-v-e7106439 {
  font-size: 26rpx;
  color: #059669;
  line-height: 1.5;
  flex: 1;
}
.favorite-actions.data-v-e7106439, .featured-actions.data-v-e7106439 {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.mini-btn.data-v-e7106439 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mini-btn.data-v-e7106439:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.mini-btn.remove.data-v-e7106439 {
  background: #fecaca;
}
.mini-btn.remove.data-v-e7106439:hover {
  background: #fca5a5;
}
.mini-icon.data-v-e7106439 {
  font-size: 20rpx;
}
.help-content.data-v-e7106439 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-e7106439 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-e7106439:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-e7106439 {
    padding: 30rpx 20rpx;
}
.category-grid.data-v-e7106439 {
    grid-template-columns: repeat(2, 1fr);
}
.quote-actions.data-v-e7106439 {
    flex-direction: column;
    gap: 12rpx;
}
}