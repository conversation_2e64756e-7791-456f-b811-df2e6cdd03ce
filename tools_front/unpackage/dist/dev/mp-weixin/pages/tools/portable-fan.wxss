/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-f92699d7 {
  display: flex;
}
.flex-1.data-v-f92699d7 {
  flex: 1;
}
.items-center.data-v-f92699d7 {
  align-items: center;
}
.justify-center.data-v-f92699d7 {
  justify-content: center;
}
.justify-between.data-v-f92699d7 {
  justify-content: space-between;
}
.text-center.data-v-f92699d7 {
  text-align: center;
}
.rounded.data-v-f92699d7 {
  border-radius: 3px;
}
.rounded-lg.data-v-f92699d7 {
  border-radius: 6px;
}
.shadow.data-v-f92699d7 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-f92699d7 {
  padding: 16rpx;
}
.m-4.data-v-f92699d7 {
  margin: 16rpx;
}
.mb-4.data-v-f92699d7 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-f92699d7 {
  margin-top: 16rpx;
}
.portable-fan-tool.data-v-f92699d7 {
  min-height: 100vh;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  padding: 30rpx;
}
.header-card.data-v-f92699d7 {
  background: linear-gradient(135deg, #3b82f6, #1e40af);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.header-card .header-content.data-v-f92699d7 {
  display: flex;
  align-items: center;
  color: white;
}
.header-card .header-content .header-icon.data-v-f92699d7 {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.header-card .header-content .header-info.data-v-f92699d7 {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-f92699d7 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-card .header-content .header-info .header-subtitle.data-v-f92699d7 {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.header-card .header-content .status-badge.data-v-f92699d7 {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 16rpx 24rpx;
}
.header-card .header-content .status-badge.status-on.data-v-f92699d7 {
  background: rgba(16, 185, 129, 0.3);
}
.header-card .header-content .status-badge .status-text.data-v-f92699d7 {
  font-size: 26rpx;
  font-weight: 600;
  color: white;
}
.fan-container.data-v-f92699d7 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.fan-container .fan-background.data-v-f92699d7 {
  position: relative;
  height: 600rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle, #f0f9ff 0%, #e0f2fe 100%);
}
.fan-container .wind-particle.data-v-f92699d7 {
  position: absolute;
  pointer-events: none;
  animation: windFloat-f92699d7 linear forwards;
  z-index: 5;
}
.fan-container .wind-particle .wind-emoji.data-v-f92699d7 {
  font-size: 28rpx;
  opacity: 0.7;
  color: #3b82f6;
  filter: blur(0.5rpx);
}
.fan-container .fan-unit.data-v-f92699d7 {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  transform: scale(0.9);
  display: flex;
  align-items: center;
  justify-content: center;
}
.fan-container .fan-frame.data-v-f92699d7 {
  position: absolute;
  width: 100%;
  height: 100%;
}
.fan-container .fan-frame .fan-circle.data-v-f92699d7 {
  width: 100%;
  height: 100%;
  border: 12rpx solid #ffffff;
  border-radius: 50%;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.1), inset 0 2rpx 8rpx rgba(255, 255, 255, 0.8);
}
.fan-container .fan-blades-container.data-v-f92699d7 {
  position: absolute;
  width: 340rpx;
  height: 340rpx;
  transform-origin: center center;
}
.fan-container .fan-blades-container.blades-normal.data-v-f92699d7 {
  animation: fanRotate-f92699d7 linear infinite;
}
.fan-container .fan-blades-container.blades-natural.data-v-f92699d7 {
  animation: fanRotateNatural-f92699d7 ease-in-out infinite;
}
.fan-container .fan-blades-container.blades-slow.data-v-f92699d7 {
  animation: fanRotate-f92699d7 linear infinite;
}
.fan-container .fan-blades-container.blades-fast.data-v-f92699d7 {
  animation: fanRotate-f92699d7 linear infinite;
}
.fan-container .fan-blades.data-v-f92699d7 {
  position: absolute;
  width: 100%;
  height: 100%;
}
.fan-container .fan-blades .blade.data-v-f92699d7 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 140rpx;
  height: 40rpx;
  transform-origin: 0 50%;
  margin-left: -10rpx;
}
.fan-container .fan-blades .blade .blade-body.data-v-f92699d7 {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 0 20rpx 20rpx 0;
  position: relative;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1), inset 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
  transform: perspective(1000rpx) rotateY(-10deg);
  transform-origin: left center;
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}
.fan-container .fan-center.data-v-f92699d7 {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  transform-origin: center center;
  z-index: 10;
}
.fan-container .fan-center .center-circle.data-v-f92699d7 {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a1a, #333333);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2), inset 0 2rpx 4rpx rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #444444;
  position: relative;
}
.fan-container .fan-center .center-circle .center-logo.data-v-f92699d7 {
  font-size: 28rpx;
  color: #ffd700;
  font-weight: bold;
  z-index: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  animation: centerPulse-f92699d7 2s infinite;
}
.fan-container .power-button.data-v-f92699d7 {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.3);
  transition: all 0.3s ease;
}
.fan-container .power-button.power-on.data-v-f92699d7 {
  background: linear-gradient(135deg, #10b981, #059669);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.fan-container .power-button.data-v-f92699d7:active {
  transform: scale(0.95);
}
.fan-container .power-button .power-icon.data-v-f92699d7 {
  font-size: 32rpx;
  color: white;
}
.fan-container .fan-info.data-v-f92699d7 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 2rpx;
  background: #f8fafc;
}
.fan-container .fan-info .info-item.data-v-f92699d7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: white;
}
.fan-container .fan-info .info-item .info-label.data-v-f92699d7 {
  font-size: 22rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.fan-container .fan-info .info-item .info-value.data-v-f92699d7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #3b82f6;
}
.control-panel.data-v-f92699d7,
.environment-card.data-v-f92699d7,
.stats-card.data-v-f92699d7,
.timer-card.data-v-f92699d7 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}
.panel-header.data-v-f92699d7,
.env-header.data-v-f92699d7,
.stats-header.data-v-f92699d7,
.timer-header.data-v-f92699d7 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1rpx solid #e5e7eb;
}
.panel-header .panel-icon.data-v-f92699d7,
.panel-header .env-icon.data-v-f92699d7,
.panel-header .stats-icon.data-v-f92699d7,
.panel-header .timer-icon.data-v-f92699d7,
.env-header .panel-icon.data-v-f92699d7,
.env-header .env-icon.data-v-f92699d7,
.env-header .stats-icon.data-v-f92699d7,
.env-header .timer-icon.data-v-f92699d7,
.stats-header .panel-icon.data-v-f92699d7,
.stats-header .env-icon.data-v-f92699d7,
.stats-header .stats-icon.data-v-f92699d7,
.stats-header .timer-icon.data-v-f92699d7,
.timer-header .panel-icon.data-v-f92699d7,
.timer-header .env-icon.data-v-f92699d7,
.timer-header .stats-icon.data-v-f92699d7,
.timer-header .timer-icon.data-v-f92699d7 {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.panel-header .panel-title.data-v-f92699d7,
.panel-header .env-title.data-v-f92699d7,
.panel-header .stats-title.data-v-f92699d7,
.panel-header .timer-title.data-v-f92699d7,
.env-header .panel-title.data-v-f92699d7,
.env-header .env-title.data-v-f92699d7,
.env-header .stats-title.data-v-f92699d7,
.env-header .timer-title.data-v-f92699d7,
.stats-header .panel-title.data-v-f92699d7,
.stats-header .env-title.data-v-f92699d7,
.stats-header .stats-title.data-v-f92699d7,
.stats-header .timer-title.data-v-f92699d7,
.timer-header .panel-title.data-v-f92699d7,
.timer-header .env-title.data-v-f92699d7,
.timer-header .stats-title.data-v-f92699d7,
.timer-header .timer-title.data-v-f92699d7 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.panel-header .timer-remaining.data-v-f92699d7,
.env-header .timer-remaining.data-v-f92699d7,
.stats-header .timer-remaining.data-v-f92699d7,
.timer-header .timer-remaining.data-v-f92699d7 {
  background: #ef4444;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.panel-header .timer-remaining .remaining-text.data-v-f92699d7,
.env-header .timer-remaining .remaining-text.data-v-f92699d7,
.stats-header .timer-remaining .remaining-text.data-v-f92699d7,
.timer-header .timer-remaining .remaining-text.data-v-f92699d7 {
  font-size: 24rpx;
  font-weight: 600;
}
.panel-content.data-v-f92699d7,
.env-content.data-v-f92699d7,
.stats-content.data-v-f92699d7,
.timer-content.data-v-f92699d7 {
  padding: 30rpx;
}
.speed-control.data-v-f92699d7,
.mode-control.data-v-f92699d7,
.function-control.data-v-f92699d7 {
  margin-bottom: 30rpx;
}
.speed-control.data-v-f92699d7:last-child,
.mode-control.data-v-f92699d7:last-child,
.function-control.data-v-f92699d7:last-child {
  margin-bottom: 0;
}
.speed-control .control-label.data-v-f92699d7,
.mode-control .control-label.data-v-f92699d7,
.function-control .control-label.data-v-f92699d7 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20rpx;
  display: block;
}
.speed-levels.data-v-f92699d7 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx;
}
.speed-levels .speed-btn.data-v-f92699d7 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.speed-levels .speed-btn.active.data-v-f92699d7 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.speed-levels .speed-btn.active .speed-number.data-v-f92699d7 {
  color: white;
}
.speed-levels .speed-btn.active .indicator-dot.data-v-f92699d7 {
  background: white;
}
.speed-levels .speed-btn.disabled.data-v-f92699d7 {
  opacity: 0.5;
  pointer-events: none;
}
.speed-levels .speed-btn .speed-number.data-v-f92699d7 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}
.speed-levels .speed-btn .speed-indicator.data-v-f92699d7 {
  display: flex;
  gap: 4rpx;
}
.speed-levels .speed-btn .speed-indicator .indicator-dot.data-v-f92699d7 {
  width: 8rpx;
  height: 8rpx;
  background: #d1d5db;
  border-radius: 50%;
}
.mode-options.data-v-f92699d7 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.mode-options .mode-btn.data-v-f92699d7 {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.mode-options .mode-btn.active.data-v-f92699d7 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.mode-options .mode-btn.active .mode-icon.data-v-f92699d7,
.mode-options .mode-btn.active .mode-name.data-v-f92699d7 {
  color: white;
}
.mode-options .mode-btn .mode-icon.data-v-f92699d7 {
  font-size: 28rpx;
}
.mode-options .mode-btn .mode-name.data-v-f92699d7 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
}
.function-item.data-v-f92699d7 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}
.function-item.data-v-f92699d7:last-child {
  border-bottom: none;
}
.function-item .function-info.data-v-f92699d7 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.function-item .function-info .function-icon.data-v-f92699d7 {
  font-size: 28rpx;
}
.function-item .function-info .function-name.data-v-f92699d7 {
  font-size: 28rpx;
  color: #1f2937;
}
.function-item .toggle-switch.data-v-f92699d7 {
  position: relative;
  width: 96rpx;
  height: 48rpx;
  background: #e5e7eb;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}
.function-item .toggle-switch.active.data-v-f92699d7 {
  background: #10b981;
}
.function-item .toggle-switch.active .switch-thumb.data-v-f92699d7 {
  transform: translateX(48rpx);
}
.function-item .toggle-switch .switch-thumb.data-v-f92699d7 {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  width: 40rpx;
  height: 40rpx;
  background: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.env-grid.data-v-f92699d7,
.stats-grid.data-v-f92699d7 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30rpx;
}
.env-grid .env-item.data-v-f92699d7,
.env-grid .stat-item.data-v-f92699d7,
.stats-grid .env-item.data-v-f92699d7,
.stats-grid .stat-item.data-v-f92699d7 {
  text-align: center;
}
.env-grid .env-item .env-value.data-v-f92699d7,
.env-grid .env-item .stat-value.data-v-f92699d7,
.env-grid .stat-item .env-value.data-v-f92699d7,
.env-grid .stat-item .stat-value.data-v-f92699d7,
.stats-grid .env-item .env-value.data-v-f92699d7,
.stats-grid .env-item .stat-value.data-v-f92699d7,
.stats-grid .stat-item .env-value.data-v-f92699d7,
.stats-grid .stat-item .stat-value.data-v-f92699d7 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8rpx;
}
.env-grid .env-item .env-label.data-v-f92699d7,
.env-grid .env-item .stat-label.data-v-f92699d7,
.env-grid .stat-item .env-label.data-v-f92699d7,
.env-grid .stat-item .stat-label.data-v-f92699d7,
.stats-grid .env-item .env-label.data-v-f92699d7,
.stats-grid .env-item .stat-label.data-v-f92699d7,
.stats-grid .stat-item .env-label.data-v-f92699d7,
.stats-grid .stat-item .stat-label.data-v-f92699d7 {
  font-size: 22rpx;
  color: #6b7280;
}
.timer-options.data-v-f92699d7 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.timer-options .timer-btn.data-v-f92699d7 {
  padding: 20rpx 24rpx;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.timer-options .timer-btn.active.data-v-f92699d7 {
  background: #3b82f6;
  border-color: #3b82f6;
}
.timer-options .timer-btn.active .timer-text.data-v-f92699d7 {
  color: white;
}
.timer-options .timer-btn .timer-text.data-v-f92699d7 {
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
}
.timer-actions.data-v-f92699d7 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}
.timer-actions .action-btn.data-v-f92699d7 {
  padding: 24rpx;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.timer-actions .action-btn.start.data-v-f92699d7 {
  background: #10b981;
}
.timer-actions .action-btn.start .action-text.data-v-f92699d7 {
  color: white;
}
.timer-actions .action-btn.cancel.data-v-f92699d7 {
  background: #ef4444;
}
.timer-actions .action-btn.cancel .action-text.data-v-f92699d7 {
  color: white;
}
.timer-actions .action-btn.disabled.data-v-f92699d7 {
  opacity: 0.5;
  pointer-events: none;
}
.timer-actions .action-btn .action-text.data-v-f92699d7 {
  font-size: 28rpx;
  font-weight: 600;
}
.wind-indicators.data-v-f92699d7 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}
.wind-indicators .wind-ring.data-v-f92699d7 {
  position: absolute;
  top: 50%;
  left: 50%;
  border: 2rpx solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: windRingExpand-f92699d7 linear infinite;
}
.wind-indicators .wind-ring.data-v-f92699d7:nth-child(1) {
  width: 300rpx;
  height: 300rpx;
}
.wind-indicators .wind-ring.data-v-f92699d7:nth-child(2) {
  width: 340rpx;
  height: 340rpx;
}
.wind-indicators .wind-ring.data-v-f92699d7:nth-child(3) {
  width: 380rpx;
  height: 380rpx;
}
.wind-indicators .wind-ring.data-v-f92699d7:nth-child(4) {
  width: 420rpx;
  height: 420rpx;
}
.wind-indicators .wind-ring.data-v-f92699d7:nth-child(5) {
  width: 460rpx;
  height: 460rpx;
}
@keyframes fanRotate-f92699d7 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes fanRotateNatural-f92699d7 {
0% {
    transform: rotate(0deg);
}
25% {
    transform: rotate(90deg) scale(0.99);
}
50% {
    transform: rotate(180deg) scale(0.98);
}
75% {
    transform: rotate(270deg) scale(0.99);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes windFloat-f92699d7 {
0% {
    opacity: 0;
    transform: translateX(0) translateY(0) scale(0.3) rotate(0deg);
}
10% {
    opacity: 0.8;
    transform: translateX(20rpx) translateY(-10rpx) scale(0.6) rotate(30deg);
}
30% {
    opacity: 1;
    transform: translateX(80rpx) translateY(-30rpx) scale(1) rotate(90deg);
}
60% {
    opacity: 0.8;
    transform: translateX(180rpx) translateY(-60rpx) scale(0.8) rotate(180deg);
}
80% {
    opacity: 0.4;
    transform: translateX(280rpx) translateY(-80rpx) scale(0.5) rotate(270deg);
}
100% {
    opacity: 0;
    transform: translateX(400rpx) translateY(-100rpx) scale(0.2) rotate(360deg);
}
}
@keyframes windRingExpand-f92699d7 {
0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.8);
}
50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
}
100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
}
}
@keyframes centerPulse-f92699d7 {
0%, 100% {
    opacity: 0.8;
    transform: scale(1);
}
50% {
    opacity: 1;
    transform: scale(1.1);
}
}