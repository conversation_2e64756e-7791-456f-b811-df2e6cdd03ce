<view class="glass-generator-container data-v-bc8177dc"><view class="header-card data-v-bc8177dc"><view class="header-content data-v-bc8177dc"><view class="header-icon data-v-bc8177dc">🔮</view><view class="header-info data-v-bc8177dc"><text class="header-title data-v-bc8177dc">玻璃效果生成器</text><text class="header-subtitle data-v-bc8177dc">生成现代感的毛玻璃背景效果</text></view></view></view><view class="params-card data-v-bc8177dc"><view class="card-header data-v-bc8177dc"><text class="card-title data-v-bc8177dc">⚙️ 效果参数</text></view><view class="params-content data-v-bc8177dc"><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">模糊度 ({{a}}px)</text><slider value="{{b}}" bindchange="{{c}}" min="0" max="50" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-bc8177dc"/></view><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">背景透明度 ({{d}})</text><slider value="{{e}}" bindchange="{{f}}" min="0" max="100" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-bc8177dc"/></view><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">边框透明度 ({{g}})</text><slider value="{{h}}" bindchange="{{i}}" min="0" max="100" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-bc8177dc"/></view><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">阴影强度 ({{j}})</text><slider value="{{k}}" bindchange="{{l}}" min="0" max="100" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-bc8177dc"/></view><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">圆角大小 ({{m}}px)</text><slider value="{{n}}" bindchange="{{o}}" min="0" max="50" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-bc8177dc"/></view><view class="param-group data-v-bc8177dc"><text class="param-label data-v-bc8177dc">背景颜色</text><view class="color-section data-v-bc8177dc"><view class="color-presets data-v-bc8177dc"><view wx:for="{{p}}" wx:for-item="color" wx:key="a" class="{{['color-preset', 'data-v-bc8177dc', color.b && 'active']}}" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"></view></view><color-picker-modal wx:if="{{q}}" class="data-v-bc8177dc" bindconfirm="{{r}}" bindcancel="{{s}}" u-i="bc8177dc-0" bind:__l="__l" u-p="{{t}}"/></view></view></view></view><view class="presets-card data-v-bc8177dc"><view class="card-header data-v-bc8177dc"><text class="card-title data-v-bc8177dc">🎨 预设样式</text></view><view class="presets-grid data-v-bc8177dc"><view wx:for="{{v}}" wx:for-item="preset" wx:key="c" class="preset-item data-v-bc8177dc" bindtap="{{preset.d}}"><view class="preset-preview data-v-bc8177dc" style="{{preset.a}}"><text class="preset-text data-v-bc8177dc">Aa</text></view><text class="preset-name data-v-bc8177dc">{{preset.b}}</text></view></view></view><view class="preview-card data-v-bc8177dc"><view class="card-header preview-header data-v-bc8177dc"><text class="card-title data-v-bc8177dc">👀 效果预览</text><button class="action-btn preview-btn data-v-bc8177dc" bindtap="{{w}}"><text class="btn-icon data-v-bc8177dc">🎭</text><text class="btn-text data-v-bc8177dc">换背景</text></button></view><view class="preview-container data-v-bc8177dc" style="{{'background-image:' + y}}"><view class="glass-demo data-v-bc8177dc" style="{{x}}"><text class="demo-title data-v-bc8177dc">毛玻璃效果</text><text class="demo-desc data-v-bc8177dc">这是一个现代化的毛玻璃背景效果</text><view class="demo-buttons data-v-bc8177dc"><button class="demo-btn data-v-bc8177dc">按钮示例</button><button class="demo-btn secondary data-v-bc8177dc">次要按钮</button></view></view></view></view><view class="code-card data-v-bc8177dc"><view class="card-header code-header data-v-bc8177dc"><text class="card-title data-v-bc8177dc">📄 生成的CSS代码</text><button class="copy-btn data-v-bc8177dc" bindtap="{{z}}"><text class="copy-icon data-v-bc8177dc">📋</text><text class="copy-text data-v-bc8177dc">复制代码</text></button></view><view class="code-content data-v-bc8177dc"><view class="code-block data-v-bc8177dc"><text class="code-text data-v-bc8177dc">{{A}}</text></view></view></view><view class="tips-card data-v-bc8177dc"><view class="card-header data-v-bc8177dc"><text class="card-title data-v-bc8177dc">💡 使用说明</text></view><view class="tips-content data-v-bc8177dc"><view class="tip-item data-v-bc8177dc"><text class="tip-title data-v-bc8177dc">🔧 调整参数</text><text class="tip-desc data-v-bc8177dc">使用滑块调整模糊度、透明度等参数</text></view><view class="tip-item data-v-bc8177dc"><text class="tip-title data-v-bc8177dc">🎨 选择预设</text><text class="tip-desc data-v-bc8177dc">点击预设样式快速应用常见的玻璃效果</text></view><view class="tip-item data-v-bc8177dc"><text class="tip-title data-v-bc8177dc">🌈 自定义颜色</text><text class="tip-desc data-v-bc8177dc">选择预设颜色或使用颜色选择器自定义</text></view><view class="tip-item data-v-bc8177dc"><text class="tip-title data-v-bc8177dc">⚠️ 兼容性提示</text><text class="tip-desc data-v-bc8177dc">backdrop-filter需要现代浏览器支持</text></view></view></view></view>