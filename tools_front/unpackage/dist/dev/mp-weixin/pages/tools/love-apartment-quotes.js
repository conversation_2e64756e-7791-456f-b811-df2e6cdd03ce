"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "LoveApartmentQuotes",
  data() {
    return {
      selectedQuote: "",
      selectedCharacter: "zhanbo",
      firstRowCharacters: ["zhan<PERSON>", "yifei", "ziqiao"],
      secondRowCharacters: ["meijia", "guangu", "xianzeng"],
      quotes: {
        zhanbo: {
          name: "陆展博",
          quotes: [
            "我是陆展博，我觉得我很好！",
            "好男人就是我，我就是陆展博！",
            "我不是在针对你，我是说在座的各位都是垃圾。",
            "一菲，你听我解释！",
            "我的人生格言：活着就是折腾！",
            "爱就是开心，爱就是甜蜜！",
            "我觉得我需要一个拥抱。",
            "人生最痛苦的事情，莫过于明明很饿，但是不知道吃什么。"
          ]
        },
        yifei: {
          name: "胡一菲",
          quotes: [
            "我要让全世界都知道这个鱼塘被你承包了！",
            "你的脸皮比城墙还厚，城墙早就拆了，你的脸皮还在！",
            "展博，你给我回来！",
            "我不是随便的人，但随便起来就不是人。",
            "打劫啊！交出你们的作业本！",
            "你这是在挑战我的智商吗？",
            "我一定要打到你满地找牙！",
            "一寸光阴一寸金，我就是一寸光阴！"
          ]
        },
        ziqiao: {
          name: "吕子乔",
          quotes: [
            "曾小贤，我鄙视你！",
            "哥什么都不怕，就怕没钱花！",
            "爱情这种事情，讲究的是缘分。",
            "我是吕子乔，我很帅！",
            "女人如衣服，兄弟如手足！",
            "帅有个屁用！到头来还不是被卒吃掉！",
            "我要用我的美貌征服世界！",
            "天下美女何其多，何必单恋一枝花。"
          ]
        },
        meijia: {
          name: "陈美嘉",
          quotes: [
            "关谷，你太有才了！",
            "我要做一个有故事的女人。",
            "生活就像一场戏，全靠演技。",
            "我不是在装傻，我就是傻！",
            "爱情不是游戏，因为我们玩不起。",
            "子乔哥哥最棒了！",
            "我要当一个善良的人。",
            "人生就是这样，不如意的事情十有八九。"
          ]
        },
        guangu: {
          name: "关谷神奇",
          quotes: [
            "纳尼？这是什么情况？",
            "美嘉，我爱你！",
            "日本有句古话：人不要脸，天下无敌！",
            "我是关谷神奇，来自日本！",
            "爱情这个东西，真的很奇妙。",
            "我要做一个有用的人！",
            "友情是珍贵的，要好好珍惜。",
            "每天都要开开心心的！"
          ]
        },
        xianzeng: {
          name: "曾小贤",
          quotes: [
            "大家好，我是曾小贤，我是一个主持人。",
            "好男人就是我，我就是曾小贤！",
            "人生就像愤怒的小鸟，失败了总有几只猪在笑。",
            "一菲，你听我说！",
            "我是一个很有原则的人。",
            "主持人的职业素养就是要镇定。",
            "我要做一个好男人！",
            "爱就是包容，爱就是理解！"
          ]
        }
      }
    };
  },
  mounted() {
    this.getRandomQuote();
  },
  methods: {
    getRandomQuote() {
      const allQuotes = Object.values(this.quotes).flatMap(
        (character) => character.quotes.map((quote) => ({ quote, name: character.name }))
      );
      const randomItem = allQuotes[Math.floor(Math.random() * allQuotes.length)];
      this.selectedQuote = `${randomItem.name}："${randomItem.quote}"`;
    },
    async copyQuote(quote, character) {
      try {
        const fullQuote = character ? `${character}："${quote}"` : quote;
        await common_vendor.index.setClipboardData({
          data: fullQuote
        });
        common_vendor.index.showToast({
          title: "复制成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "error"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return {
    a: common_vendor.o((...args) => $options.getRandomQuote && $options.getRandomQuote(...args)),
    b: common_vendor.t($data.selectedQuote),
    c: common_vendor.o(($event) => $options.copyQuote($data.selectedQuote)),
    d: common_vendor.f($data.firstRowCharacters, (character, k0, i0) => {
      return {
        a: common_vendor.t($data.quotes[character].name),
        b: character,
        c: $data.selectedCharacter === character ? 1 : "",
        d: common_vendor.o(($event) => $data.selectedCharacter = character, character)
      };
    }),
    e: common_vendor.f($data.secondRowCharacters, (character, k0, i0) => {
      return {
        a: common_vendor.t($data.quotes[character].name),
        b: character,
        c: $data.selectedCharacter === character ? 1 : "",
        d: common_vendor.o(($event) => $data.selectedCharacter = character, character)
      };
    }),
    f: common_vendor.t($data.quotes[$data.selectedCharacter].name),
    g: common_vendor.f($data.quotes[$data.selectedCharacter].quotes, (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: index,
        c: common_vendor.o(($event) => $options.copyQuote(quote, $data.quotes[$data.selectedCharacter].name), index)
      };
    })
  };
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2de8ce91"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/love-apartment-quotes.js.map
