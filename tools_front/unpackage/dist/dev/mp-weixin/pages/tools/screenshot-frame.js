"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "ScreenshotFrame",
  data() {
    return {
      selectedImage: null,
      selectedFrame: "iphone-11-white",
      activeCategory: "mobile",
      isProcessing: false,
      currentTime: "12:08",
      canvasReady: false,
      canvasWidth: 300,
      canvasHeight: 600,
      // 设备框架分类
      frameCategories: [
        { id: "mobile", name: "手机" },
        { id: "tablet", name: "平板" },
        { id: "computer", name: "电脑" },
        { id: "watch", name: "手表" },
        { id: "tv", name: "显示器" }
      ],
      // 设备框架配置
      frames: {
        mobile: [
          {
            id: "iphone-11-white",
            name: "iPhone 11 白色",
            templateImage: "/static/layer_png/mobile/Apple_iPhone_11_White.jpeg",
            templateImageFallback: "/static/layer/mobile/Apple_iPhone_11_White.svg",
            previewImage: "/static/layer/mobile/Apple_iPhone_11_White.svg",
            screenArea: { x: 85, y: 185, width: 858, height: 1622 },
            canvasSize: { width: 1028, height: 1992 }
          },
          {
            id: "iphone-11-yellow",
            name: "iPhone 11 黄色",
            templateImage: "/static/layer_png/mobile/Apple_iPhone_11_Yellow.jpeg",
            templateImageFallback: "/static/layer/mobile/Apple_iPhone_11_Yellow.svg",
            previewImage: "/static/layer/mobile/Apple_iPhone_11_Yellow.svg",
            screenArea: { x: 85, y: 185, width: 858, height: 1622 },
            canvasSize: { width: 1028, height: 1992 }
          },
          {
            id: "iphone-11-red",
            name: "iPhone 11 红色",
            templateImage: "/static/layer_png/mobile/Apple_iPhone_11_Red.jpeg",
            templateImageFallback: "/static/layer/mobile/Apple_iPhone_11_Red.svg",
            previewImage: "/static/layer/mobile/Apple_iPhone_11_Red.svg",
            screenArea: { x: 85, y: 185, width: 858, height: 1622 },
            canvasSize: { width: 1028, height: 1992 }
          },
          {
            id: "iphone-11-purple",
            name: "iPhone 11 紫色",
            templateImage: "/static/layer_png/mobile/Apple_iPhone_11_Purple.jpeg",
            templateImageFallback: "/static/layer/mobile/Apple_iPhone_11_Purple.svg",
            previewImage: "/static/layer/mobile/Apple_iPhone_11_Purple.svg",
            screenArea: { x: 85, y: 185, width: 858, height: 1622 },
            canvasSize: { width: 1028, height: 1992 }
          }
        ],
        tablet: [
          {
            id: "ipad-pro-13-horizontal",
            name: 'iPad Pro 13" 横屏',
            templateImage: "/static/layer_png/tablet/Apple_iPad_Pro_13_ Silver.jpeg",
            templateImageFallback: "/static/layer/tablet/Apple_iPad_Pro_13_ Silver.svg",
            previewImage: "/static/layer/tablet/Apple_iPad_Pro_13_ Silver.svg",
            screenArea: { x: 156, y: 122, width: 2820, height: 2204 },
            canvasSize: { width: 3132, height: 2448 }
          },
          {
            id: "ipad-pro-13-vertical",
            name: 'iPad Pro 13" 竖屏',
            templateImage: "/static/layer_png/tablet/Apple_iPad_Pro_13_ Silver_verticle.jpeg",
            templateImageFallback: "/static/layer/tablet/Apple_iPad_Pro_13_ Silver_verticle.svg",
            previewImage: "/static/layer/tablet/Apple_iPad_Pro_13_ Silver_verticle.svg",
            screenArea: { x: 122, y: 156, width: 2204, height: 2820 },
            canvasSize: { width: 2448, height: 3132 }
          }
        ],
        computer: [
          {
            id: "macbook-pro-15",
            name: 'MacBook Pro 15"',
            templateImage: "/static/layer_png/computer/Apple_Macbook_Pro_15_ Silver.jpeg",
            templateImageFallback: "/static/layer/computer/Apple_Macbook_Pro_15_ Silver.svg",
            previewImage: "/static/layer/computer/Apple_Macbook_Pro_15_ Silver.svg",
            screenArea: { x: 388, y: 240, width: 3104, height: 1940 },
            canvasSize: { width: 3880, height: 2400 }
          },
          {
            id: "imac",
            name: "iMac",
            templateImage: "/static/layer_png/computer/Apple_iMac.jpeg",
            templateImageFallback: "/static/layer/computer/Apple_iMac.svg",
            previewImage: "/static/layer/computer/Apple_iMac.svg",
            screenArea: { x: 200, y: 120, width: 2400, height: 1350 },
            canvasSize: { width: 2800, height: 1800 }
          }
        ],
        watch: [
          {
            id: "apple-watch-black",
            name: "Apple Watch 黑色",
            templateImage: "/static/layer_png/watch/Apple_Watch_38mm_Black.jpeg",
            templateImageFallback: "/static/layer/watch/Apple_Watch_38mm_Black.svg",
            previewImage: "/static/layer/watch/Apple_Watch_38mm_Black.svg",
            screenArea: { x: 120, y: 180, width: 340, height: 420 },
            canvasSize: { width: 580, height: 780 }
          },
          {
            id: "moto-360",
            name: "Moto 360",
            templateImage: "/static/layer_png/watch/Motorola_Moto_360_Men_Black.jpeg",
            templateImageFallback: "/static/layer/watch/Motorola_Moto_360_Men_Black.svg",
            previewImage: "/static/layer/watch/Motorola_Moto_360_Men_Black.svg",
            screenArea: { x: 120, y: 180, width: 340, height: 420 },
            canvasSize: { width: 580, height: 780 }
          }
        ],
        tv: [
          {
            id: "pro-display-xdr",
            name: "Pro Display XDR",
            templateImage: "/static/layer_png/tv/Apple_Pro_Display_XDR.jpeg",
            templateImageFallback: "/static/layer/tv/Apple_Pro_Display_XDR.svg",
            previewImage: "/static/layer/tv/Apple_Pro_Display_XDR.svg",
            screenArea: { x: 40, y: 20, width: 500, height: 260 },
            // 调整屏幕区域位置和大小
            canvasSize: { width: 580, height: 400 }
          }
        ]
      }
    };
  },
  computed: {
    currentFrames() {
      return this.frames[this.activeCategory] || [];
    },
    currentFrameData() {
      const frames = this.frames[this.activeCategory] || [];
      return frames.find((f) => f.id === this.selectedFrame);
    }
  },
  watch: {
    selectedFrame() {
      this.generatePreview();
    }
  },
  mounted() {
    this.updateTime();
    setInterval(this.updateTime, 6e4);
  },
  methods: {
    // 添加格式化文件大小的方法
    formatFileSize(bytes) {
      if (!bytes)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    updateTime() {
      const now = /* @__PURE__ */ new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      this.currentTime = `${hours}:${minutes}`;
    },
    selectImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getImageInfo({
            src: tempFilePath,
            success: (info) => {
              this.selectedImage = {
                name: `screenshot_${Date.now()}.jpg`,
                path: tempFilePath,
                size: info.width * info.height * 4,
                width: info.width,
                height: info.height
              };
              this.autoSelectFrame(info.width, info.height);
              this.$nextTick(() => {
                this.generatePreview();
              });
            }
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    autoSelectFrame(width, height) {
      const ratio = width / height;
      if (ratio > 1.5) {
        this.activeCategory = "computer";
        this.selectedFrame = "macbook-pro-15";
      } else if (ratio > 0.9) {
        if (width < 500) {
          this.activeCategory = "watch";
          this.selectedFrame = "apple-watch-black";
        } else {
          this.activeCategory = "tablet";
          this.selectedFrame = "ipad-pro-13-horizontal";
        }
      } else {
        this.activeCategory = "mobile";
        this.selectedFrame = "iphone-11-white";
      }
    },
    generatePreview() {
      if (!this.selectedImage || !this.currentFrameData)
        return;
      this.canvasReady = false;
      const frameData = this.currentFrameData;
      const maxWidth = 280;
      const maxHeight = 450;
      const scaleX = maxWidth / frameData.canvasSize.width;
      const scaleY = maxHeight / frameData.canvasSize.height;
      const scale = Math.min(scaleX, scaleY);
      this.canvasWidth = Math.floor(frameData.canvasSize.width * scale);
      this.canvasHeight = Math.floor(frameData.canvasSize.height * scale);
      common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:389", "Canvas尺寸:", this.canvasWidth, "x", this.canvasHeight);
      this.$nextTick(() => {
        this.drawFramedImage();
      });
    },
    drawFramedImage() {
      const ctx = common_vendor.index.createCanvasContext("previewCanvas", this);
      const frameData = this.currentFrameData;
      if (!frameData || !this.selectedImage) {
        common_vendor.index.__f__("error", "at pages/tools/screenshot-frame.vue:402", "frameData或selectedImage为空");
        return;
      }
      ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      const screenArea = frameData.screenArea;
      const scaleX = this.canvasWidth / frameData.canvasSize.width;
      const scaleY = this.canvasHeight / frameData.canvasSize.height;
      if (frameData.id === "pro-display-xdr") {
        const imageAspect = this.selectedImage.width / this.selectedImage.height;
        const screenAspect = screenArea.width / screenArea.height;
        let drawWidth = screenArea.width * scaleX;
        let drawHeight = screenArea.height * scaleY;
        let drawX = screenArea.x * scaleX;
        let drawY = screenArea.y * scaleY;
        if (imageAspect > screenAspect) {
          const scale = screenArea.height * scaleY / this.selectedImage.height;
          drawWidth = screenArea.width * scaleX;
          drawHeight = screenArea.height * scaleY;
          const sourceWidth = drawWidth / scale;
          const sourceX = (this.selectedImage.width - sourceWidth) / 2;
          ctx.drawImage(
            this.selectedImage.path,
            sourceX,
            0,
            sourceWidth,
            this.selectedImage.height,
            drawX,
            drawY,
            drawWidth,
            drawHeight
          );
        } else {
          const scale = screenArea.width * scaleX / this.selectedImage.width;
          drawWidth = screenArea.width * scaleX;
          drawHeight = screenArea.height * scaleY;
          const sourceHeight = drawHeight / scale;
          const sourceY = (this.selectedImage.height - sourceHeight) / 2;
          ctx.drawImage(
            this.selectedImage.path,
            0,
            sourceY,
            this.selectedImage.width,
            sourceHeight,
            drawX,
            drawY,
            drawWidth,
            drawHeight
          );
        }
      } else {
        if (frameData.id === "moto-360") {
          const centerX = screenArea.x * scaleX + screenArea.width * scaleX / 2;
          const centerY = screenArea.y * scaleY + screenArea.height * scaleY / 2;
          const radius = screenArea.width * scaleX / 2;
          ctx.save();
          ctx.beginPath();
          ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
          ctx.clip();
          const imageAspect = this.selectedImage.width / this.selectedImage.height;
          let drawWidth = screenArea.width * scaleX * 1.2;
          let drawHeight = screenArea.width * scaleX * 1.2;
          if (imageAspect > 1) {
            drawHeight = screenArea.width * scaleX * 1.2;
            drawWidth = drawHeight * imageAspect;
          } else {
            drawWidth = screenArea.width * scaleX * 1.2;
            drawHeight = drawWidth / imageAspect;
          }
          const drawX = centerX - drawWidth / 2;
          const drawY = centerY - drawHeight / 2;
          ctx.drawImage(
            this.selectedImage.path,
            drawX,
            drawY,
            drawWidth,
            drawHeight
          );
          ctx.restore();
        } else {
          ctx.drawImage(
            this.selectedImage.path,
            screenArea.x * scaleX,
            screenArea.y * scaleY,
            screenArea.width * scaleX,
            screenArea.height * scaleY
          );
        }
      }
      ctx.drawImage(
        frameData.templateImageFallback,
        0,
        0,
        this.canvasWidth,
        this.canvasHeight
      );
      ctx.draw(false, () => {
        common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:522", "Canvas绘制完成");
        this.canvasReady = true;
      });
    },
    drawDeviceTemplate(ctx, frameData, callback) {
      const templatePath = frameData.templateImageFallback;
      common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:531", "绘制设备模板(SVG):", templatePath);
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      try {
        ctx.drawImage(
          templatePath,
          0,
          0,
          this.canvasWidth,
          this.canvasHeight
        );
        common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:546", "SVG设备模板绘制完成，执行回调");
        callback();
      } catch (error) {
        common_vendor.index.__f__("warn", "at pages/tools/screenshot-frame.vue:549", "SVG绘制失败，使用白色背景备用方案:", error);
        this.drawWhiteBackgroundFrame(ctx, frameData);
        callback();
      }
    },
    drawWhiteBackgroundFrame(ctx, frameData) {
      const screenArea = frameData.screenArea;
      const scaleX = this.canvasWidth / frameData.canvasSize.width;
      const scaleY = this.canvasHeight / frameData.canvasSize.height;
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.setStrokeStyle("#e5e7eb");
      ctx.setLineWidth(2);
      ctx.strokeRect(2, 2, this.canvasWidth - 4, this.canvasHeight - 4);
      const screenX = screenArea.x * scaleX;
      const screenY = screenArea.y * scaleY;
      const screenW = screenArea.width * scaleX;
      const screenH = screenArea.height * scaleY;
      ctx.setStrokeStyle("#d1d5db");
      ctx.setLineWidth(1);
      ctx.strokeRect(screenX - 1, screenY - 1, screenW + 2, screenH + 2);
      common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:581", "白色背景设备框架绘制完成");
    },
    drawFallbackFrame(ctx, frameData, callback) {
      const borderWidth = 8;
      const screenArea = frameData.screenArea;
      const scaleX = this.canvasWidth / frameData.canvasSize.width;
      const scaleY = this.canvasHeight / frameData.canvasSize.height;
      ctx.setFillStyle("#2d3748");
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.setFillStyle("#4a5568");
      ctx.fillRect(
        borderWidth,
        borderWidth,
        this.canvasWidth - borderWidth * 2,
        this.canvasHeight - borderWidth * 2
      );
      const screenX = screenArea.x * scaleX;
      const screenY = screenArea.y * scaleY;
      const screenW = screenArea.width * scaleX;
      const screenH = screenArea.height * scaleY;
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenX - 2, screenY - 2, screenW + 4, screenH + 4);
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(screenX, screenY, screenW, screenH);
      if (frameData.id.includes("iphone") || frameData.id.includes("mobile")) {
        ctx.setFillStyle("#1a202c");
        const speakerWidth = 60 * scaleX;
        const speakerHeight = 4 * scaleY;
        const speakerX = (this.canvasWidth - speakerWidth) / 2;
        const speakerY = Math.max(20 * scaleY, borderWidth + 10);
        ctx.fillRect(speakerX, speakerY, speakerWidth, speakerHeight);
        ctx.setFillStyle("#000000");
        const cameraRadius = 6 * Math.min(scaleX, scaleY);
        const cameraX = this.canvasWidth / 2 + 40 * scaleX;
        const cameraY = Math.max(30 * scaleY, borderWidth + 15);
        ctx.beginPath();
        ctx.arc(cameraX, cameraY, cameraRadius, 0, 2 * Math.PI);
        ctx.fill();
        const indicatorWidth = 80 * scaleX;
        const indicatorHeight = 3 * scaleY;
        const indicatorX = (this.canvasWidth - indicatorWidth) / 2;
        const indicatorY = this.canvasHeight - Math.max(20 * scaleY, borderWidth + 10);
        ctx.setFillStyle("#666666");
        ctx.fillRect(indicatorX, indicatorY, indicatorWidth, indicatorHeight);
      }
      if (frameData.id.includes("ipad") || frameData.id.includes("tablet")) {
        ctx.setFillStyle("#333333");
        const homeButtonRadius = 15 * Math.min(scaleX, scaleY);
        const homeButtonX = this.canvasWidth / 2;
        const homeButtonY = this.canvasHeight - Math.max(30 * scaleY, borderWidth + 20);
        ctx.beginPath();
        ctx.arc(homeButtonX, homeButtonY, homeButtonRadius, 0, 2 * Math.PI);
        ctx.fill();
      }
      if (frameData.id.includes("macbook") || frameData.id.includes("computer")) {
        ctx.setFillStyle("#e2e8f0");
        const keyboardHeight = 40 * scaleY;
        const keyboardY = this.canvasHeight - keyboardHeight - borderWidth;
        ctx.fillRect(
          borderWidth,
          keyboardY,
          this.canvasWidth - borderWidth * 2,
          keyboardHeight
        );
        ctx.setFillStyle("#d1d5db");
        for (let i = 0; i < 3; i++) {
          const lineY = keyboardY + (i + 1) * (keyboardHeight / 4);
          ctx.fillRect(borderWidth + 10, lineY, this.canvasWidth - borderWidth * 2 - 20, 1);
        }
      }
    },
    clearImage() {
      this.selectedImage = null;
      this.selectedFrame = "iphone-11-white";
      this.activeCategory = "mobile";
      this.canvasReady = false;
    },
    handleProcess() {
      if (!this.selectedImage || !this.selectedFrame || this.isProcessing)
        return;
      this.isProcessing = true;
      common_vendor.index.showLoading({
        title: "正在生成...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.canvasToTempFilePath({
          canvasId: "previewCanvas",
          x: 0,
          y: 0,
          width: this.canvasWidth,
          height: this.canvasHeight,
          destWidth: this.canvasWidth * 2,
          destHeight: this.canvasHeight * 2,
          fileType: "png",
          quality: 1,
          success: (res) => {
            common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:706", "生成图片成功:", res.tempFilePath);
            this.saveToAlbum(res.tempFilePath);
          },
          fail: (error) => {
            common_vendor.index.__f__("error", "at pages/tools/screenshot-frame.vue:710", "生成图片失败:", error);
            this.handleError("生成图片失败: " + (error.errMsg || "未知错误"));
          }
        }, this);
      }, 300);
    },
    // 新增：绘制设备装饰元素的方法
    drawDeviceDecorations(ctx, frameData, canvasWidth, canvasHeight) {
      frameData.screenArea;
      if (frameData.id.includes("iphone")) {
        ctx.setFillStyle("#000000");
        const speakerWidth = 200;
        const speakerHeight = 20;
        const speakerX = (canvasWidth - speakerWidth) / 2;
        const speakerY = 80;
        ctx.fillRect(speakerX, speakerY, speakerWidth, speakerHeight);
        ctx.beginPath();
        ctx.arc(canvasWidth / 2 + 150, 120, 30, 0, 2 * Math.PI);
        ctx.fill();
        const indicatorWidth = 200;
        const indicatorHeight = 10;
        const indicatorX = (canvasWidth - indicatorWidth) / 2;
        const indicatorY = canvasHeight - 60;
        ctx.setFillStyle("#666666");
        ctx.fillRect(indicatorX, indicatorY, indicatorWidth, indicatorHeight);
      } else if (frameData.id.includes("ipad")) {
        ctx.setFillStyle("#333333");
        ctx.beginPath();
        ctx.arc(canvasWidth / 2, canvasHeight - 80, 40, 0, 2 * Math.PI);
        ctx.fill();
      }
    },
    // 新增：绘制设备边框的方法
    drawDeviceFrame(ctx, frameData, canvasWidth, canvasHeight) {
      common_vendor.index.__f__("log", "at pages/tools/screenshot-frame.vue:754", "绘制设备边框，设备ID:", frameData.id);
      if (frameData.id.includes("iphone")) {
        this.drawIPhoneFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("ipad")) {
        this.drawIPadFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("macbook")) {
        this.drawMacBookFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("imac")) {
        this.drawIMacFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("watch")) {
        this.drawWatchFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("moto-360")) {
        this.drawMoto360Frame(ctx, frameData, canvasWidth, canvasHeight);
      } else if (frameData.id.includes("pro-display-xdr")) {
        this.drawDisplayFrame(ctx, frameData, canvasWidth, canvasHeight);
      } else {
        this.drawGenericFrame(ctx, frameData, canvasWidth, canvasHeight);
      }
    },
    // 绘制iPhone边框
    drawIPhoneFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      const borderWidth = 20;
      ctx.setFillStyle("#1f2937");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle("#374151");
      ctx.fillRect(borderWidth, borderWidth, canvasWidth - borderWidth * 2, canvasHeight - borderWidth * 2);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 10, screenArea.y - 10, screenArea.width + 20, screenArea.height + 20);
      ctx.setFillStyle("#000000");
      const speakerWidth = 200;
      const speakerHeight = 20;
      const speakerX = (canvasWidth - speakerWidth) / 2;
      const speakerY = 80;
      ctx.fillRect(speakerX, speakerY, speakerWidth, speakerHeight);
      ctx.setFillStyle("#000000");
      ctx.beginPath();
      ctx.arc(canvasWidth / 2 + 150, 120, 30, 0, 2 * Math.PI);
      ctx.fill();
      const indicatorWidth = 200;
      const indicatorHeight = 10;
      const indicatorX = (canvasWidth - indicatorWidth) / 2;
      const indicatorY = canvasHeight - 60;
      ctx.setFillStyle("#666666");
      ctx.fillRect(indicatorX, indicatorY, indicatorWidth, indicatorHeight);
    },
    // 绘制iPad边框
    drawIPadFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      const borderWidth = 30;
      ctx.setFillStyle("#e5e7eb");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle("#f3f4f6");
      ctx.fillRect(borderWidth, borderWidth, canvasWidth - borderWidth * 2, canvasHeight - borderWidth * 2);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 15, screenArea.y - 15, screenArea.width + 30, screenArea.height + 30);
      ctx.setFillStyle("#333333");
      ctx.beginPath();
      ctx.arc(canvasWidth / 2, canvasHeight - 80, 40, 0, 2 * Math.PI);
      ctx.fill();
    },
    // 绘制MacBook边框
    drawMacBookFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      ctx.setFillStyle("#2d3748");
      ctx.fillRect(0, 0, canvasWidth, screenArea.y + screenArea.height + 50);
      ctx.setFillStyle("#1a202c");
      ctx.fillRect(screenArea.x - 20, screenArea.y - 20, screenArea.width + 40, screenArea.height + 40);
      ctx.setFillStyle("#e2e8f0");
      const keyboardY = screenArea.y + screenArea.height + 50;
      ctx.fillRect(0, keyboardY, canvasWidth, canvasHeight - keyboardY);
      ctx.setFillStyle("#d1d5db");
      for (let i = 0; i < 4; i++) {
        const lineY = keyboardY + 30 + i * 30;
        ctx.fillRect(50, lineY, canvasWidth - 100, 2);
      }
      ctx.setFillStyle("#9ca3af");
      const trackpadWidth = 300;
      const trackpadHeight = 200;
      const trackpadX = (canvasWidth - trackpadWidth) / 2;
      const trackpadY = keyboardY + 150;
      ctx.fillRect(trackpadX, trackpadY, trackpadWidth, trackpadHeight);
    },
    // 绘制iMac边框
    drawIMacFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      ctx.setFillStyle("#f8fafc");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle("#e2e8f0");
      ctx.fillRect(screenArea.x - 30, screenArea.y - 30, screenArea.width + 60, screenArea.height + 60);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 10, screenArea.y - 10, screenArea.width + 20, screenArea.height + 20);
      const standWidth = 400;
      const standHeight = 100;
      const standX = (canvasWidth - standWidth) / 2;
      const standY = canvasHeight - standHeight - 20;
      ctx.setFillStyle("#d1d5db");
      ctx.fillRect(standX, standY, standWidth, standHeight);
    },
    // 绘制手表边框
    drawWatchFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      ctx.setFillStyle("#1f2937");
      ctx.fillRect(0, centerY - 100, canvasWidth, 200);
      ctx.setFillStyle("#374151");
      ctx.fillRect(centerX - 200, centerY - 250, 400, 500);
      ctx.setFillStyle("#4b5563");
      ctx.fillRect(centerX - 180, centerY - 230, 360, 460);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 10, screenArea.y - 10, screenArea.width + 20, screenArea.height + 20);
      ctx.setFillStyle("#6b7280");
      ctx.fillRect(centerX + 180, centerY - 30, 20, 60);
    },
    // 绘制Moto 360边框
    drawMoto360Frame(ctx, frameData, canvasWidth, canvasHeight) {
      const centerX = canvasWidth / 2;
      const centerY = canvasHeight / 2;
      const radius = 200;
      ctx.setFillStyle("#1f2937");
      ctx.fillRect(0, centerY - 80, canvasWidth, 160);
      ctx.setFillStyle("#374151");
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius + 20, 0, 2 * Math.PI);
      ctx.fill();
      ctx.setFillStyle("#4b5563");
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.fill();
      ctx.setFillStyle("#000000");
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius - 20, 0, 2 * Math.PI);
      ctx.fill();
    },
    // 绘制显示器边框
    drawDisplayFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      ctx.setFillStyle("#f8fafc");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle("#e5e7eb");
      ctx.fillRect(screenArea.x - 20, screenArea.y - 20, screenArea.width + 40, screenArea.height + 40);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 5, screenArea.y - 5, screenArea.width + 10, screenArea.height + 10);
      const standWidth = 200;
      const standHeight = 120;
      const standX = (canvasWidth - standWidth) / 2;
      const standY = screenArea.y + screenArea.height + 30;
      ctx.setFillStyle("#d1d5db");
      ctx.fillRect(standX, standY, standWidth, standHeight);
      const baseWidth = 300;
      const baseHeight = 40;
      const baseX = (canvasWidth - baseWidth) / 2;
      const baseY = canvasHeight - baseHeight - 10;
      ctx.setFillStyle("#9ca3af");
      ctx.fillRect(baseX, baseY, baseWidth, baseHeight);
    },
    // 绘制通用边框
    drawGenericFrame(ctx, frameData, canvasWidth, canvasHeight) {
      const screenArea = frameData.screenArea;
      const borderWidth = 20;
      ctx.setFillStyle("#374151");
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle("#4b5563");
      ctx.fillRect(borderWidth, borderWidth, canvasWidth - borderWidth * 2, canvasHeight - borderWidth * 2);
      ctx.setFillStyle("#000000");
      ctx.fillRect(screenArea.x - 10, screenArea.y - 10, screenArea.width + 20, screenArea.height + 20);
    },
    // 保存图片到相册
    saveToAlbum(tempFilePath) {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: tempFilePath,
        success: () => {
          common_vendor.index.hideLoading();
          this.isProcessing = false;
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/tools/screenshot-frame.vue:1021", "保存到相册失败:", error);
          this.isProcessing = false;
          common_vendor.index.hideLoading();
          if (error.errMsg.includes("auth")) {
            common_vendor.index.showModal({
              title: "提示",
              content: "需要授权访问相册权限才能保存图片",
              confirmText: "去设置",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    },
    // 错误处理
    handleError(message) {
      common_vendor.index.__f__("error", "at pages/tools/screenshot-frame.vue:1049", "处理错误:", message);
      this.isProcessing = false;
      common_vendor.index.hideLoading();
      common_vendor.index.showModal({
        title: "处理失败",
        content: message || "图片处理过程中出现错误，请重试",
        showCancel: false,
        confirmText: "确定"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.selectImage && $options.selectImage(...args)),
    b: $data.selectedImage
  }, $data.selectedImage ? {
    c: $data.selectedImage.path,
    d: common_vendor.t($data.selectedImage.name),
    e: common_vendor.t($options.formatFileSize($data.selectedImage.size)),
    f: common_vendor.o((...args) => $options.clearImage && $options.clearImage(...args))
  } : {}, {
    g: $data.selectedImage
  }, $data.selectedImage ? {
    h: common_vendor.f($data.frameCategories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: category.id,
        c: $data.activeCategory === category.id ? 1 : "",
        d: common_vendor.o(($event) => $data.activeCategory = category.id, category.id)
      };
    }),
    i: common_vendor.f($options.currentFrames, (frame, k0, i0) => {
      return {
        a: frame.previewImage,
        b: common_vendor.t(frame.name),
        c: frame.id,
        d: $data.selectedFrame === frame.id ? 1 : "",
        e: common_vendor.o(($event) => $data.selectedFrame = frame.id, frame.id)
      };
    })
  } : {}, {
    j: $data.selectedImage && $data.selectedFrame
  }, $data.selectedImage && $data.selectedFrame ? common_vendor.e({
    k: $data.canvasWidth + "px",
    l: $data.canvasHeight + "px",
    m: $data.canvasReady ? "block" : "none",
    n: !$data.canvasReady
  }, !$data.canvasReady ? {} : {}) : {}, {
    o: $data.selectedImage && $data.selectedFrame
  }, $data.selectedImage && $data.selectedFrame ? {
    p: common_vendor.t($data.isProcessing ? "处理中..." : "生成带框截图"),
    q: $data.isProcessing ? 1 : "",
    r: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    s: $data.isProcessing
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6baee0b2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/screenshot-frame.js.map
