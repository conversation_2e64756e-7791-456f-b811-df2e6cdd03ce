"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "ImageColorPicker",
  data() {
    return {
      selectedImage: null,
      extractedColors: [],
      isProcessing: false,
      pickedColor: null,
      showCursor: false,
      cursorPosition: {
        x: 0,
        y: 0
      },
      canvasContext: null
    };
  },
  computed: {
    cursorStyle() {
      return {
        left: this.cursorPosition.x + "px",
        top: this.cursorPosition.y + "px"
      };
    }
  },
  methods: {
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0];
          this.extractedColors = [];
        }
      });
    },
    extractColors() {
      if (!this.selectedImage)
        return;
      this.isProcessing = true;
      common_vendor.index.getImageInfo({
        src: this.selectedImage,
        success: (imageInfo) => {
          const offscreen = common_vendor.index.createOffscreenCanvas({
            type: "2d",
            width: imageInfo.width,
            height: imageInfo.height
          });
          const ctx = offscreen.getContext("2d");
          const img = offscreen.createImage();
          img.src = this.selectedImage;
          img.onload = () => {
            ctx.drawImage(img, 0, 0, imageInfo.width, imageInfo.height);
            try {
              const imageData = ctx.getImageData(0, 0, imageInfo.width, imageInfo.height);
              const colors = this.analyzeImageColors(imageData.data, imageInfo.width, imageInfo.height);
              this.extractedColors = colors;
            } catch (err) {
              common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:205", "提取颜色失败:", err);
              common_vendor.index.showToast({
                title: "颜色提取失败",
                icon: "none"
              });
            } finally {
              this.isProcessing = false;
            }
          };
          img.onerror = () => {
            common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:216", "图片加载失败");
            common_vendor.index.showToast({
              title: "图片加载失败",
              icon: "none"
            });
            this.isProcessing = false;
          };
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:225", "获取图片信息失败:", err);
          common_vendor.index.showToast({
            title: "无法获取图片信息",
            icon: "none"
          });
          this.isProcessing = false;
        }
      });
    },
    analyzeImageColors(data, width, height) {
      const colorMap = /* @__PURE__ */ new Map();
      const sampleStep = Math.max(1, Math.floor(Math.min(width, height) / 50));
      const quantize = (value) => Math.round(value / 32) * 32;
      for (let y = 0; y < height; y += sampleStep) {
        for (let x = 0; x < width; x += sampleStep) {
          const i = (y * width + x) * 4;
          const r = quantize(data[i]);
          const g = quantize(data[i + 1]);
          const b = quantize(data[i + 2]);
          const a = data[i + 3];
          if (a < 128 || r > 250 && g > 250 && b > 250)
            continue;
          const hex = this.rgbToHex({ r, g, b });
          colorMap.set(hex, (colorMap.get(hex) || 0) + 1);
        }
      }
      const minOccurrence = width * height / (sampleStep * sampleStep * 100);
      const sortedColors = Array.from(colorMap.entries()).filter(([_, count]) => count > minOccurrence).sort((a, b) => b[1] - a[1]).map(([color]) => color);
      return sortedColors.slice(0, 8);
    },
    copyColor(color) {
      common_vendor.index.setClipboardData({
        data: color,
        success: () => {
          common_vendor.index.showToast({
            title: "颜色已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    copyAllColors() {
      const colorsText = this.extractedColors.join(", ");
      common_vendor.index.setClipboardData({
        data: colorsText,
        success: () => {
          common_vendor.index.showToast({
            title: "调色板已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    getColorName(hex) {
      const colorNames = {
        "#FF0000": "红色",
        "#00FF00": "绿色",
        "#0000FF": "蓝色",
        "#FFFF00": "黄色",
        "#FF00FF": "紫色",
        "#00FFFF": "青色",
        "#000000": "黑色",
        "#FFFFFF": "白色",
        "#808080": "灰色"
      };
      return colorNames[hex] || "自定义色";
    },
    getColorRichness() {
      if (this.extractedColors.length >= 6)
        return "丰富";
      if (this.extractedColors.length >= 4)
        return "适中";
      return "简单";
    },
    getColorTemperature() {
      const warmColors = this.extractedColors.filter((color) => {
        const r = parseInt(color.substr(1, 2), 16);
        const g = parseInt(color.substr(3, 2), 16);
        const b = parseInt(color.substr(5, 2), 16);
        return r > g && r > b;
      });
      if (warmColors.length > this.extractedColors.length / 2)
        return "暖色调";
      return "冷色调";
    },
    async pickColorFromImage(e) {
      const touch = e.touches ? e.touches[0] : e.changedTouches ? e.changedTouches[0] : e;
      if (!touch) {
        common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:329", "无法获取触摸坐标");
        return;
      }
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select("#previewImage").boundingClientRect((data) => {
        if (!data) {
          common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:337", "无法获取图片元素信息");
          return;
        }
        let x = touch.clientX - data.left;
        let y = touch.clientY - data.top;
        x = Math.max(0, Math.min(x, data.width));
        y = Math.max(0, Math.min(y, data.height));
        common_vendor.index.__f__("log", "at pages/tools/image-color-picker.vue:349", "触摸坐标:", { clientX: touch.clientX, clientY: touch.clientY });
        common_vendor.index.__f__("log", "at pages/tools/image-color-picker.vue:350", "图片位置:", { left: data.left, top: data.top });
        common_vendor.index.__f__("log", "at pages/tools/image-color-picker.vue:351", "相对坐标:", { x, y });
        this.cursorPosition = { x, y };
        this.showCursor = true;
        const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
        const canvasWidth = data.width;
        const canvasHeight = data.height;
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        ctx.drawImage(this.selectedImage, 0, 0, canvasWidth, canvasHeight);
        ctx.draw(false, () => {
          setTimeout(() => {
            common_vendor.index.canvasGetImageData({
              canvasId: "colorCanvas",
              x: Math.round(x),
              y: Math.round(y),
              width: 1,
              height: 1,
              success: (res) => {
                const r = res.data[0];
                const g = res.data[1];
                const b = res.data[2];
                this.pickedColor = this.rgbToHex({ r, g, b });
                common_vendor.index.__f__("log", "at pages/tools/image-color-picker.vue:384", "取色成功:", {
                  坐标: { x, y },
                  RGB: { r, g, b },
                  颜色: this.pickedColor
                });
              },
              fail: (err) => {
                common_vendor.index.__f__("error", "at pages/tools/image-color-picker.vue:391", "取色失败:", err);
                common_vendor.index.showToast({
                  title: "取色失败，请重试",
                  icon: "none"
                });
              }
            });
          }, 100);
        });
      }).exec();
    },
    rgbToHex(rgb) {
      return "#" + [rgb.r, rgb.g, rgb.b].map((x) => x.toString(16).padStart(2, "0").toUpperCase()).join("");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedImage
  }, !$data.selectedImage ? {
    b: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args))
  } : {}, {
    c: $data.selectedImage
  }, $data.selectedImage ? common_vendor.e({
    d: $data.selectedImage,
    e: common_vendor.o((...args) => $options.pickColorFromImage && $options.pickColorFromImage(...args)),
    f: common_vendor.o((...args) => $options.pickColorFromImage && $options.pickColorFromImage(...args)),
    g: common_vendor.o((...args) => $options.pickColorFromImage && $options.pickColorFromImage(...args)),
    h: $data.showCursor
  }, $data.showCursor ? {
    i: common_vendor.s($options.cursorStyle)
  } : {}, {
    j: $data.pickedColor
  }, $data.pickedColor ? {
    k: $data.pickedColor,
    l: common_vendor.t($data.pickedColor),
    m: common_vendor.o(($event) => $options.copyColor($data.pickedColor))
  } : {}, {
    n: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    o: !$data.isProcessing
  }, !$data.isProcessing ? {
    p: common_vendor.o((...args) => $options.extractColors && $options.extractColors(...args))
  } : {}, {
    q: $data.isProcessing
  }, $data.isProcessing ? {} : {}) : {}, {
    r: $data.extractedColors.length > 0
  }, $data.extractedColors.length > 0 ? {
    s: common_vendor.t($data.extractedColors.length),
    t: common_vendor.f($data.extractedColors, (color, index, i0) => {
      return {
        a: color,
        b: common_vendor.t(color),
        c: index,
        d: common_vendor.o(($event) => $options.copyColor(color), index)
      };
    }),
    v: common_vendor.o((...args) => $options.copyAllColors && $options.copyAllColors(...args))
  } : {}, {
    w: $data.extractedColors.length > 0
  }, $data.extractedColors.length > 0 ? {
    x: $data.extractedColors[0],
    y: common_vendor.t($options.getColorName($data.extractedColors[0])),
    z: common_vendor.t($options.getColorRichness()),
    A: common_vendor.t($options.getColorTemperature())
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-61ae3098"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-color-picker.js.map
