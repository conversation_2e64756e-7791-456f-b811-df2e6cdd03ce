"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      inputMode: "search",
      // 默认为搜索模式
      url: "",
      songName: "",
      artist: "",
      isAnalyzing: false,
      searchResults: [],
      // 搜索结果列表
      selectedSong: null,
      // 当前选中的歌曲
      selectedSongIndex: -1,
      // 选中歌曲的索引
      playingSongIndex: -1,
      // 正在播放的歌曲索引
      isPlaying: false,
      audioContext: null,
      originalKeyword: "",
      // 原始搜索关键词
      qualityOptions: [
        { type: "128K", size: "3.2MB", icon: "💿" },
        { type: "320K", size: "8.1MB", icon: "📀" },
        { type: "无损", size: "32.5MB", icon: "✨" }
      ],
      platforms: [
        { name: "网易云音乐", icon: "🎵", code: "netease", quality: ["320K", "歌词", "封面"], url: "https://music.163.com" },
        { name: "QQ音乐", icon: "🎼", code: "qq", quality: ["歌词"], url: "https://y.qq.com" },
        { name: "酷狗音乐", icon: "🎧", code: "kugou", quality: ["基础"], url: "http://www.kugou.com" },
        { name: "咪咕音乐", icon: "🎹", code: "migu", quality: ["无损", "320K", "封面", "歌词"], url: "http://www.migu.cn" },
        { name: "百度音乐", icon: "🎤", code: "baidu", quality: ["320K", "封面", "歌词"], url: "http://music.baidu.com" }
      ],
      usageSteps: [
        "选择搜索模式，输入歌曲名称和歌手",
        "或选择链接模式，粘贴音乐平台链接",
        "从搜索结果中选择想要的歌曲",
        "选择合适的音质，点击下载按钮",
        "微信小程序：可选择保存到本地或复制链接到浏览器下载"
      ]
    };
  },
  computed: {
    canSearch() {
      if (this.inputMode === "search") {
        return this.songName && this.songName.trim().length > 0;
      } else {
        return this.url && this.url.trim().length > 0;
      }
    }
  },
  onUnload() {
    if (this.audioContext) {
      this.audioContext.destroy();
    }
  },
  methods: {
    // 切换输入模式
    switchMode(mode) {
      this.inputMode = mode;
      this.clearResults();
      if (mode === "search") {
        this.url = "";
      } else {
        this.songName = "";
        this.artist = "";
      }
    },
    // 清空搜索结果
    clearResults() {
      this.searchResults = [];
      this.selectedSong = null;
      this.selectedSongIndex = -1;
      this.playingSongIndex = -1;
      this.isPlaying = false;
      this.originalKeyword = "";
      if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
      }
    },
    async handleAnalyze() {
      if (!this.canSearch || this.isAnalyzing)
        return;
      this.isAnalyzing = true;
      this.clearResults();
      try {
        let params = {};
        if (this.inputMode === "search") {
          params = {
            songName: this.songName.trim(),
            artist: this.artist ? this.artist.trim() : ""
          };
        } else {
          params = {
            musicUrl: this.url.trim()
          };
        }
        const result = await utils_toolService.toolService.downloadMusic(params);
        if (result.success) {
          const data = result.data || result;
          if (data.searchResults && data.searchResults.length > 0) {
            this.searchResults = data.searchResults;
            utils_index.showSuccess(`找到 ${data.searchResults.length} 首相关歌曲！`);
          } else {
            utils_index.showError("没有找到相关歌曲，请尝试其他关键词");
          }
        } else {
          utils_index.showError(result.message || "搜索失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:372", "音乐搜索失败:", error);
        utils_index.showError(error.message || "搜索失败，请重试");
      } finally {
        this.isAnalyzing = false;
      }
    },
    // 选择歌曲
    selectSong(song, index) {
      this.selectedSong = song;
      this.selectedSongIndex = index;
      if (this.inputMode === "search") {
        this.originalKeyword = this.songName.trim();
      } else {
        this.originalKeyword = this.url.trim();
      }
      if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
      }
      this.isPlaying = false;
      this.playingSongIndex = -1;
      utils_index.showSuccess(`已选择：${song.title} - ${song.artist}`);
    },
    // 播放/暂停
    async togglePlay(song, index) {
      if (this.playingSongIndex === index && this.isPlaying) {
        if (this.audioContext) {
          this.audioContext.pause();
          this.isPlaying = false;
        }
      } else {
        if (this.audioContext) {
          this.audioContext.destroy();
        }
        this.audioContext = common_vendor.index.createInnerAudioContext();
        try {
          const previewUrl = await this.getPreviewUrl(song);
          if (previewUrl) {
            this.audioContext.src = previewUrl;
            this.audioContext.onPlay(() => {
              this.isPlaying = true;
              this.playingSongIndex = index;
              utils_index.showSuccess(`正在播放：${song.title} - ${song.artist}`);
            });
            this.audioContext.onEnded(() => {
              this.isPlaying = false;
              this.playingSongIndex = -1;
            });
            this.audioContext.onError((res) => {
              common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:437", "音频播放错误:", res.errMsg);
              this.isPlaying = false;
              this.playingSongIndex = -1;
              utils_index.showError("播放失败，可能版权受限");
            });
            this.audioContext.play();
          } else {
            throw new Error("无法获取预览链接");
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:448", "播放失败:", error);
          this.audioContext.src = "/static/audio/wooden-fish-knock.mp3";
          this.audioContext.onPlay(() => {
            this.isPlaying = true;
            this.playingSongIndex = index;
            utils_index.showSuccess(`正在播放：${song.title}（演示音频）`);
          });
          this.audioContext.onEnded(() => {
            this.isPlaying = false;
            this.playingSongIndex = -1;
          });
          this.audioContext.onError((res) => {
            common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:465", "演示音频播放错误:", res.errMsg);
            this.isPlaying = false;
            this.playingSongIndex = -1;
            utils_index.showError("播放失败");
          });
          this.audioContext.play();
        }
      }
    },
    // 获取预览播放URL
    async getPreviewUrl(song) {
      try {
        const params = {
          songName: song.title,
          artist: song.artist,
          source: song.source,
          quality: "preview"
          // 预览质量
        };
        const result = await utils_toolService.toolService.getPreviewUrl(params);
        if (result.success && result.data && result.data.previewUrl) {
          return result.data.previewUrl;
        }
        return null;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:494", "获取预览URL失败:", error);
        return null;
      }
    },
    // 处理平台点击
    handlePlatformClick(platform) {
      if (platform.url) {
        common_vendor.index.showModal({
          title: platform.name,
          content: `即将跳转到${platform.name}官网
支持功能：${platform.quality.join("、")}`,
          showCancel: true,
          cancelText: "取消",
          confirmText: "前往",
          success: (res) => {
            if (res.confirm) {
              common_vendor.index.setClipboardData({
                data: platform.url,
                success: () => {
                  utils_index.showSuccess("链接已复制，请在浏览器中打开");
                }
              });
            }
          }
        });
      }
    },
    // 生成封面图片
    generateCoverImage() {
      return "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjEyMCIgaGVpZ2h0PSIxMjAiIGZpbGw9IiNmM2Y0ZjYiLz48cGF0aCBkPSJNNjAgNDBDNDQuNTM2IDQwIDMyIDUyLjUzNiAzMiA2OEM3MiA2OCA4OCA2OCA4OCA2OEM4OCA1Mi41MzYgNzUuNDY0IDQwIDYwIDQwWiIgZmlsbD0iI2UyZTRlNyIvPjxjaXJjbGUgY3g9IjYwIiBjeT0iMzYiIHI9IjEyIiBmaWxsPSIjZTJlNGU3Ii8+PC9zdmc+";
    },
    // 下载音乐文件
    async downloadMusic(quality = { type: "320K", value: "320" }) {
      if (!this.selectedSong) {
        utils_index.showError("请先选择一首歌曲");
        return;
      }
      try {
        utils_index.showSuccess(`正在准备下载${this.selectedSong.title}...`);
        const fileName = `${this.selectedSong.title} - ${this.selectedSong.artist}.mp3`;
        common_vendor.index.showModal({
          title: "下载方式选择",
          content: `准备下载：${fileName}

请选择下载方式：`,
          cancelText: "复制链接",
          confirmText: "保存到本地",
          success: (res) => {
            if (res.confirm) {
              this.downloadToWechatLocal(null, fileName, quality);
            } else if (res.cancel) {
              this.downloadAndCopyLink(fileName, quality);
            }
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:660", "音乐下载失败:", error);
        utils_index.showError(error.message || "下载失败，请重试");
        common_vendor.index.showModal({
          title: "下载提示",
          content: `由于网络或其他原因，无法直接下载 ${this.selectedSong.title}。

建议：
1. 检查网络连接
2. 稍后重试
3. 或前往原平台（${this.selectedSong.source}）下载`,
          showCancel: false,
          confirmText: "确定"
        });
      }
    },
    // 下载到服务器并复制链接（微信小程序备用方案）
    async downloadAndCopyLink(fileName, quality) {
      try {
        common_vendor.index.showLoading({
          title: "正在准备链接..."
        });
        const downloadParams = {
          songName: this.selectedSong.title,
          artist: this.selectedSong.artist,
          quality: quality.type.toLowerCase(),
          format: "mp3",
          originalKeyword: this.originalKeyword || this.selectedSong.title,
          songIndex: this.selectedSongIndex,
          source: this.selectedSong.source ? this.selectedSong.source.toLowerCase() : null
        };
        const downloadResult = await utils_toolService.toolService.downloadMusicFile(downloadParams);
        if (downloadResult.success && downloadResult.data) {
          const data = downloadResult.data;
          if (data.success && data.filePath) {
            const streamUrl = `http://localhost:8080/api/tools/media/download-file?filePath=${encodeURIComponent(data.filePath)}&fileName=${encodeURIComponent(fileName)}`;
            common_vendor.index.hideLoading();
            common_vendor.index.setClipboardData({
              data: streamUrl,
              success: () => {
                utils_index.showSuccess("下载链接已复制到剪贴板");
                common_vendor.index.showModal({
                  title: "使用说明",
                  content: "请将链接粘贴到浏览器中打开进行下载，或者发送给朋友通过电脑下载。",
                  showCancel: false,
                  confirmText: "知道了"
                });
              },
              fail: () => {
                utils_index.showError("复制失败，请重试");
              }
            });
          } else {
            common_vendor.index.hideLoading();
            utils_index.showError(data.message || "准备链接失败");
          }
        } else {
          common_vendor.index.hideLoading();
          utils_index.showError(downloadResult.message || "准备链接失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:727", "准备链接失败:", error);
        utils_index.showError("准备链接失败，请重试");
      }
    },
    // 微信小程序端下载到本地
    async downloadToWechatLocal(url, fileName, quality) {
      try {
        common_vendor.index.showLoading({
          title: "正在下载..."
        });
        const streamParams = {
          songName: this.selectedSong.title,
          artist: this.selectedSong.artist,
          quality: quality.type.toLowerCase(),
          // 使用传递的音质参数
          format: "mp3",
          source: this.selectedSong.source ? this.selectedSong.source.toLowerCase() : null,
          songIndex: this.selectedSongIndex,
          originalKeyword: this.originalKeyword || this.selectedSong.title
        };
        try {
          const response = await fetch("http://localhost:8080/api/tools/media/download-music-stream", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Accept": "audio/mpeg"
            },
            body: JSON.stringify(streamParams)
          });
          if (!response.ok) {
            throw new Error(`下载失败: ${response.status}`);
          }
          const musicBlob = await response.blob();
          const musicArrayBuffer = await musicBlob.arrayBuffer();
          const musicBase64 = common_vendor.index.arrayBufferToBase64(musicArrayBuffer);
          const tempFilePath = `${common_vendor.wx$1.env.USER_DATA_PATH}/${fileName}`;
          const fs = common_vendor.wx$1.getFileSystemManager();
          fs.writeFileSync(tempFilePath, musicBase64, "base64");
          common_vendor.index.hideLoading();
          common_vendor.index.saveFile({
            tempFilePath,
            success: (saveRes) => {
              utils_index.showSuccess("下载完成！文件已保存到本地");
              common_vendor.index.showModal({
                title: "下载完成",
                content: `${fileName} 已保存到本地

是否要查看文件？`,
                cancelText: "稍后查看",
                confirmText: "立即查看",
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    common_vendor.index.openDocument({
                      filePath: saveRes.savedFilePath,
                      success: () => {
                        common_vendor.index.__f__("log", "at pages/tools/music-downloader.vue:799", "文件打开成功");
                      },
                      fail: (err) => {
                        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:802", "文件打开失败:", err);
                        utils_index.showError("文件已保存，但无法直接打开");
                      }
                    });
                  }
                }
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:811", "文件保存失败:", err);
              utils_index.showError("文件保存失败，请检查存储权限");
            }
          });
        } catch (fetchError) {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:818", "获取音乐数据失败:", fetchError);
          this.fallbackDownload(url, fileName);
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:826", "下载过程出错:", error);
        utils_index.showError("下载过程出错，请重试");
      }
    },
    // 降级下载方式
    async fallbackDownload(url, fileName) {
      try {
        common_vendor.index.showLoading({
          title: "正在下载..."
        });
        const downloadTask = common_vendor.index.downloadFile({
          url,
          success: (res) => {
            common_vendor.index.hideLoading();
            if (res.statusCode === 200) {
              common_vendor.index.saveFile({
                tempFilePath: res.tempFilePath,
                success: (saveRes) => {
                  utils_index.showSuccess("下载完成！文件已保存到本地");
                  common_vendor.index.showModal({
                    title: "下载完成",
                    content: `${fileName} 已保存到本地

是否要查看文件？`,
                    cancelText: "稍后查看",
                    confirmText: "立即查看",
                    success: (modalRes) => {
                      if (modalRes.confirm) {
                        common_vendor.index.openDocument({
                          filePath: saveRes.savedFilePath,
                          success: () => {
                            common_vendor.index.__f__("log", "at pages/tools/music-downloader.vue:863", "文件打开成功");
                          },
                          fail: (err) => {
                            common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:866", "文件打开失败:", err);
                            utils_index.showError("文件已保存，但无法直接打开");
                          }
                        });
                      }
                    }
                  });
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:875", "文件保存失败:", err);
                  utils_index.showError("文件保存失败，请检查存储权限");
                }
              });
            } else {
              utils_index.showError(`下载失败，状态码: ${res.statusCode}`);
            }
          },
          fail: (err) => {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:885", "下载失败:", err);
            utils_index.showError("下载失败，请检查网络连接");
            common_vendor.index.showModal({
              title: "下载失败",
              content: "无法直接下载文件，是否要复制下载链接？",
              cancelText: "取消",
              confirmText: "复制链接",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.setClipboardData({
                    data: url,
                    success: () => {
                      utils_index.showSuccess("下载链接已复制到剪贴板");
                      common_vendor.index.showModal({
                        title: "使用说明",
                        content: "请将链接发送给朋友或在电脑浏览器中打开下载。",
                        showCancel: false,
                        confirmText: "知道了"
                      });
                    }
                  });
                }
              }
            });
          }
        });
        downloadTask.onProgressUpdate((res) => {
          const progress = Math.round(res.progress);
          common_vendor.index.showLoading({
            title: `下载中... ${progress}%`
          });
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/tools/music-downloader.vue:924", "降级下载失败:", error);
        utils_index.showError("下载失败，请重试");
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputMode === "search" ? 1 : "",
    b: common_vendor.o(($event) => $options.switchMode("search")),
    c: $data.inputMode === "url" ? 1 : "",
    d: common_vendor.o(($event) => $options.switchMode("url")),
    e: $data.inputMode === "search"
  }, $data.inputMode === "search" ? {
    f: $data.songName,
    g: common_vendor.o(($event) => $data.songName = $event.detail.value),
    h: $data.artist,
    i: common_vendor.o(($event) => $data.artist = $event.detail.value)
  } : {
    j: $data.url,
    k: common_vendor.o(($event) => $data.url = $event.detail.value)
  }, {
    l: !$data.isAnalyzing
  }, !$data.isAnalyzing ? {
    m: common_vendor.t($data.inputMode === "search" ? "搜索" : "解析")
  } : {}, {
    n: !$options.canSearch || $data.isAnalyzing ? 1 : "",
    o: !$options.canSearch || $data.isAnalyzing,
    p: common_vendor.o((...args) => $options.handleAnalyze && $options.handleAnalyze(...args)),
    q: common_vendor.t($data.inputMode === "search" ? "输入歌曲和歌手信息进行搜索" : "支持网易云音乐、QQ音乐、酷狗音乐等主流平台链接"),
    r: $data.searchResults.length > 0
  }, $data.searchResults.length > 0 ? {
    s: common_vendor.t($data.searchResults.length),
    t: common_vendor.f($data.searchResults, (song, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(song.title),
        c: common_vendor.t(song.artist),
        d: common_vendor.t(song.size),
        e: common_vendor.t(song.duration),
        f: common_vendor.t(song.source),
        g: common_vendor.t($data.playingSongIndex === index ? "⏸️" : "▶️"),
        h: common_vendor.o(($event) => $options.togglePlay(song, index), index),
        i: $data.playingSongIndex === index ? 1 : "",
        j: index,
        k: common_vendor.o(($event) => $options.selectSong(song, index), index),
        l: $data.selectedSongIndex === index ? 1 : ""
      };
    }),
    v: $options.generateCoverImage()
  } : {}, {
    w: $data.selectedSong
  }, $data.selectedSong ? {
    x: $options.generateCoverImage(),
    y: common_vendor.t($data.playingSongIndex === $data.selectedSongIndex ? "⏸️" : "▶️"),
    z: common_vendor.o(($event) => $options.togglePlay($data.selectedSong, $data.selectedSongIndex)),
    A: common_vendor.t($data.selectedSong.title),
    B: common_vendor.t($data.selectedSong.artist),
    C: common_vendor.t($data.selectedSong.album || "未知专辑"),
    D: common_vendor.t($data.selectedSong.size),
    E: common_vendor.t($data.selectedSong.duration),
    F: common_vendor.t($data.selectedSong.source)
  } : {}, {
    G: $data.selectedSong
  }, $data.selectedSong ? {
    H: common_vendor.f($data.qualityOptions, (quality, index, i0) => {
      return {
        a: common_vendor.t(quality.icon),
        b: common_vendor.t(quality.type),
        c: common_vendor.t(quality.size),
        d: common_vendor.o(($event) => $options.downloadMusic(quality), index),
        e: index,
        f: quality.type === "无损" ? 1 : ""
      };
    })
  } : {}, {
    I: common_vendor.f($data.platforms, (platform, k0, i0) => {
      return {
        a: common_vendor.t(platform.icon),
        b: common_vendor.t(platform.name),
        c: common_vendor.f(platform.quality, (quality, k1, i1) => {
          return {
            a: common_vendor.t(quality),
            b: quality
          };
        }),
        d: platform.name,
        e: common_vendor.o(($event) => $options.handlePlatformClick(platform), platform.name)
      };
    }),
    J: common_vendor.f($data.usageSteps, (item, index, i0) => {
      return {
        a: common_vendor.t(index + 1),
        b: common_vendor.t(item),
        c: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b15160cf"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/music-downloader.js.map
