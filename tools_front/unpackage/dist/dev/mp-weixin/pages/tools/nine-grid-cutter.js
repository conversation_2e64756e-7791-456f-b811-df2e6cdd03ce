"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedFile: null,
      imagePreview: null,
      isCutting: false,
      cutImages: [],
      imageWidth: 0,
      imageHeight: 0,
      canvasSize: 300,
      // Canvas尺寸
      cutImagePaths: []
      // 存储切片的临时路径
    };
  },
  methods: {
    // 使用uni-app API选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            const tempFilePath = res.tempFilePaths[0];
            this.selectedFile = {
              name: tempFilePath.split("/").pop() || `image_${Date.now()}.jpg`,
              path: tempFilePath
            };
            this.cutImages = [];
            this.cutImagePaths = [];
            this.imagePreview = tempFilePath;
            this.getImageInfo(tempFilePath);
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 获取图片信息（尺寸等）
    getImageInfo(src) {
      common_vendor.index.getImageInfo({
        src,
        success: (res) => {
          this.imageWidth = res.width;
          this.imageHeight = res.height;
          common_vendor.index.__f__("log", "at pages/tools/nine-grid-cutter.vue:169", "图片尺寸:", res.width, "x", res.height);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:172", "获取图片信息失败:", err);
          this.imageWidth = 300;
          this.imageHeight = 300;
        }
      });
    },
    // 切图处理
    handleCut() {
      if (!this.selectedFile || this.isCutting)
        return;
      if (!this.imageWidth || !this.imageHeight) {
        common_vendor.index.showToast({
          title: "图片尺寸获取失败，请重新选择",
          icon: "none"
        });
        return;
      }
      this.isCutting = true;
      setTimeout(() => {
        this.createCanvasSlices();
      }, 500);
    },
    // 使用Canvas实现真正的九宫格切图
    createCanvasSlices() {
      this.createCssSlices();
      this.tryCanvasCutting();
    },
    // 尝试Canvas切图
    async tryCanvasCutting() {
      try {
        const query = common_vendor.index.createSelectorQuery().in(this);
        query.select("#cutCanvas").fields({ node: true, size: true }).exec((res) => {
          if (res[0] && res[0].node) {
            this.performCanvasCutting(res[0].node);
          } else {
            this.performLegacyCanvasCutting();
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:224", "Canvas切图失败:", error);
      }
    },
    // 使用新的Canvas 2D API切图
    performCanvasCutting(canvas) {
      const ctx = canvas.getContext("2d");
      const dpr = common_vendor.index.getSystemInfoSync().pixelRatio;
      canvas.width = this.canvasSize * dpr;
      canvas.height = this.canvasSize * dpr;
      ctx.scale(dpr, dpr);
      const img = canvas.createImage();
      img.onload = () => {
        ctx.drawImage(img, 0, 0, this.canvasSize, this.canvasSize);
        this.generateCanvasSlices(canvas, ctx);
      };
      img.src = this.imagePreview;
    },
    // 使用旧的Canvas API切图
    performLegacyCanvasCutting() {
      const ctx = common_vendor.index.createCanvasContext("cutCanvas", this);
      ctx.drawImage(this.imagePreview, 0, 0, this.canvasSize, this.canvasSize);
      ctx.draw(false, () => {
        this.generateLegacySlices(0);
      });
    },
    // 生成Canvas切片（新API）
    generateCanvasSlices(canvas, ctx) {
      const sliceWidth = this.canvasSize / 3;
      const sliceHeight = this.canvasSize / 3;
      this.cutImagePaths = [];
      for (let i = 0; i < 9; i++) {
        const row = Math.floor(i / 3);
        const col = i % 3;
        const x = col * sliceWidth;
        const y = row * sliceHeight;
        common_vendor.index.canvasToTempFilePath({
          canvas,
          x,
          y,
          width: sliceWidth,
          height: sliceHeight,
          success: (res) => {
            this.cutImagePaths[i] = res.tempFilePath;
            common_vendor.index.__f__("log", "at pages/tools/nine-grid-cutter.vue:281", `切片${i + 1}生成成功:`, res.tempFilePath);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:284", `切片${i + 1}生成失败:`, err);
          }
        }, this);
      }
    },
    // 生成切片（旧API）
    generateLegacySlices(index) {
      if (index >= 9) {
        return;
      }
      const row = Math.floor(index / 3);
      const col = index % 3;
      const sliceWidth = this.canvasSize / 3;
      const sliceHeight = this.canvasSize / 3;
      const x = col * sliceWidth;
      const y = row * sliceHeight;
      common_vendor.index.canvasToTempFilePath({
        canvasId: "cutCanvas",
        x,
        y,
        width: sliceWidth,
        height: sliceHeight,
        success: (res) => {
          this.cutImagePaths[index] = res.tempFilePath;
          common_vendor.index.__f__("log", "at pages/tools/nine-grid-cutter.vue:311", `切片${index + 1}生成成功:`, res.tempFilePath);
          this.generateLegacySlices(index + 1);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:315", `切片${index + 1}生成失败:`, err);
          this.generateLegacySlices(index + 1);
        }
      }, this);
    },
    // CSS方案作为后备
    createCssSlices() {
      this.cutImages = [];
      for (let row = 0; row < 3; row++) {
        for (let col = 0; col < 3; col++) {
          this.cutImages.push({
            id: row * 3 + col + 1,
            row,
            col,
            name: `cut_${row * 3 + col + 1}.jpg`
          });
        }
      }
      this.isCutting = false;
      common_vendor.index.showToast({
        title: "切图完成",
        icon: "success"
      });
    },
    // 获取图片样式（用于CSS切图显示）
    getImageStyle(index) {
      const positions = [
        { x: "-0%", y: "-0%" },
        // 左上 (1)
        { x: "-33.333%", y: "-0%" },
        // 中上 (2)  
        { x: "-66.666%", y: "-0%" },
        // 右上 (3)
        { x: "-0%", y: "-33.333%" },
        // 左中 (4)
        { x: "-33.333%", y: "-33.333%" },
        // 中中 (5)
        { x: "-66.666%", y: "-33.333%" },
        // 右中 (6)
        { x: "-0%", y: "-66.666%" },
        // 左下 (7)
        { x: "-33.333%", y: "-66.666%" },
        // 中下 (8)
        { x: "-66.666%", y: "-66.666%" }
        // 右下 (9)
      ];
      return {
        width: "300%",
        height: "300%",
        objectFit: "cover",
        position: "absolute",
        top: "0",
        left: "0",
        transform: `translate(${positions[index].x}, ${positions[index].y})`
      };
    },
    // 获取每个切片的容器样式
    getContainerStyle(img) {
      return {
        overflow: "hidden",
        position: "relative",
        width: "100%",
        height: "100%"
      };
    },
    downloadAll() {
      if (this.cutImages.length === 0)
        return;
      common_vendor.index.showToast({
        title: "开始批量保存",
        icon: "none"
      });
      this.saveAllSlices(0);
    },
    // 递归保存所有切片
    saveAllSlices(index) {
      if (index >= this.cutImages.length) {
        common_vendor.index.showToast({
          title: "全部保存完成",
          icon: "success"
        });
        return;
      }
      const filePath = this.cutImagePaths[index] || this.imagePreview;
      common_vendor.index.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          common_vendor.index.__f__("log", "at pages/tools/nine-grid-cutter.vue:407", `第${index + 1}张保存成功`);
          setTimeout(() => {
            this.saveAllSlices(index + 1);
          }, 200);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:414", `第${index + 1}张保存失败:`, err);
          setTimeout(() => {
            this.saveAllSlices(index + 1);
          }, 200);
        }
      });
    },
    downloadSingle(index) {
      const image = this.cutImages[index];
      if (!image)
        return;
      common_vendor.index.showToast({
        title: `保存第${image.id}张图片`,
        icon: "none"
      });
      if (this.cutImagePaths[index]) {
        this.saveImageToAlbum(this.cutImagePaths[index]);
      } else {
        this.generateSingleSlice(index);
      }
    },
    // 生成单个切片
    generateSingleSlice(index) {
      const row = Math.floor(index / 3);
      const col = index % 3;
      const sliceWidth = this.canvasSize / 3;
      const sliceHeight = this.canvasSize / 3;
      const x = col * sliceWidth;
      const y = row * sliceHeight;
      const ctx = common_vendor.index.createCanvasContext("cutCanvas", this);
      ctx.drawImage(this.imagePreview, 0, 0, this.canvasSize, this.canvasSize);
      ctx.draw(false, () => {
        common_vendor.index.canvasToTempFilePath({
          canvasId: "cutCanvas",
          x,
          y,
          width: sliceWidth,
          height: sliceHeight,
          success: (res) => {
            this.cutImagePaths[index] = res.tempFilePath;
            this.saveImageToAlbum(res.tempFilePath);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:466", `生成切片${index + 1}失败:`, err);
            common_vendor.index.showToast({
              title: "切片生成失败",
              icon: "none"
            });
          }
        }, this);
      });
    },
    // 保存图片到相册
    saveImageToAlbum(filePath) {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          common_vendor.index.showToast({
            title: "图片已保存到相册",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/nine-grid-cutter.vue:487", "保存失败:", err);
          common_vendor.index.showToast({
            title: "保存失败，请检查权限",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    b: $data.imagePreview
  }, $data.imagePreview ? {
    c: $data.imagePreview,
    d: common_vendor.f(9, (i, k0, i0) => {
      return {
        a: i
      };
    })
  } : {}, {
    e: $data.selectedFile
  }, $data.selectedFile ? {
    f: common_vendor.t($data.isCutting ? "切图中..." : "开始切图"),
    g: $data.isCutting ? 1 : "",
    h: common_vendor.o((...args) => $options.handleCut && $options.handleCut(...args))
  } : {}, {
    i: $data.cutImages.length > 0
  }, $data.cutImages.length > 0 ? {
    j: common_vendor.f($data.cutImages, (img, index, i0) => {
      return {
        a: common_vendor.s($options.getImageStyle(index)),
        b: common_vendor.t(img.id),
        c: index,
        d: common_vendor.o(($event) => $options.downloadSingle(index), index)
      };
    }),
    k: $data.imagePreview,
    l: common_vendor.o((...args) => $options.downloadAll && $options.downloadAll(...args))
  } : {}, {
    m: $data.canvasSize + "px",
    n: $data.canvasSize + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-796a2d06"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/nine-grid-cutter.js.map
