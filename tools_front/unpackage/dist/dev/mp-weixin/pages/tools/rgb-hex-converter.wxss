/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-d47e139d {
  display: flex;
}
.flex-1.data-v-d47e139d {
  flex: 1;
}
.items-center.data-v-d47e139d {
  align-items: center;
}
.justify-center.data-v-d47e139d {
  justify-content: center;
}
.justify-between.data-v-d47e139d {
  justify-content: space-between;
}
.text-center.data-v-d47e139d {
  text-align: center;
}
.rounded.data-v-d47e139d {
  border-radius: 3px;
}
.rounded-lg.data-v-d47e139d {
  border-radius: 6px;
}
.shadow.data-v-d47e139d {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-d47e139d {
  padding: 16rpx;
}
.m-4.data-v-d47e139d {
  margin: 16rpx;
}
.mb-4.data-v-d47e139d {
  margin-bottom: 16rpx;
}
.mt-4.data-v-d47e139d {
  margin-top: 16rpx;
}
.color-converter-container.data-v-d47e139d {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-d47e139d, .color-display-card.data-v-d47e139d, .control-card.data-v-d47e139d,
.info-card.data-v-d47e139d, .palette-card.data-v-d47e139d, .tips-card.data-v-d47e139d {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-d47e139d {
  display: flex;
  align-items: center;
}
.header-icon.data-v-d47e139d {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-info.data-v-d47e139d {
  flex: 1;
}
.header-title.data-v-d47e139d {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-d47e139d {
  font-size: 24rpx;
  color: #666;
}
.main-color-preview.data-v-d47e139d {
  height: 200rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  position: relative;
  transition: all 0.3s ease;
}
.main-color-preview.data-v-d47e139d:active {
  transform: scale(0.98);
}
.color-text.data-v-d47e139d {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.color-text.light.data-v-d47e139d {
  color: #333;
  text-shadow: 0 2rpx 4rpx rgba(255, 255, 255, 0.3);
}
.color-actions.data-v-d47e139d {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-d47e139d {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border: none;
  border-radius: 12rpx;
  color: #666;
  font-size: 24rpx;
}
.action-btn.data-v-d47e139d:active {
  background: #e9ecef;
}
.btn-icon.data-v-d47e139d {
  font-size: 32rpx;
}
.card-header.data-v-d47e139d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-d47e139d {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.rgb-controls.data-v-d47e139d {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.rgb-item.data-v-d47e139d {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.rgb-header.data-v-d47e139d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.rgb-label.data-v-d47e139d {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}
.rgb-input-wrapper.data-v-d47e139d {
  position: relative;
  width: 120rpx;
  height: 60rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}
.rgb-input-wrapper.data-v-d47e139d:focus-within {
  background: #ffffff;
  box-shadow: 0 0 0 2rpx #3b82f6;
}
.rgb-input.data-v-d47e139d {
  width: 100%;
  height: 100%;
  padding: 0 16rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  font-family: "SF Mono", "Monaco", monospace;
  background: transparent;
  border: none;
}
.slider-container.data-v-d47e139d {
  position: relative;
  padding: 8rpx 0;
}
.rgb-slider.data-v-d47e139d {
  width: 100%;
  height: 40rpx;
  margin: 0;
  transition: all 0.2s ease;
}
.rgb-slider.data-v-d47e139d:active {
  transform: scale(1.02);
}
.data-v-d47e139d .uni-slider-handle {
  width: 28rpx !important;
  height: 28rpx !important;
  border-radius: 50% !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2) !important;
  transition: transform 0.2s ease !important;
}
.data-v-d47e139d .uni-slider-handle:active {
  transform: scale(1.2) !important;
}
.data-v-d47e139d .uni-slider-track {
  height: 8rpx !important;
  border-radius: 4rpx !important;
}
.hex-input-container.data-v-d47e139d {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.hex-input.data-v-d47e139d {
  flex: 1;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-family: "Monaco", "Consolas", monospace;
}
.hex-input.data-v-d47e139d:focus {
  border-color: #007AFF;
}
.copy-hex-btn.data-v-d47e139d {
  padding: 16rpx 20rpx;
  background: #007AFF;
  border: none;
  border-radius: 12rpx;
  color: white;
  font-size: 28rpx;
}
.color-formats.data-v-d47e139d {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.format-item.data-v-d47e139d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.format-item.data-v-d47e139d:active {
  background: #e9ecef;
  transform: translateY(1rpx);
}
.format-label.data-v-d47e139d {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  min-width: 80rpx;
}
.format-value.data-v-d47e139d {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  font-family: "Monaco", "Consolas", monospace;
  text-align: center;
}
.copy-hint.data-v-d47e139d, .brightness-desc.data-v-d47e139d {
  font-size: 20rpx;
  color: #999;
}
.preset-colors.data-v-d47e139d {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
  gap: 16rpx;
}
.preset-color.data-v-d47e139d {
  aspect-ratio: 1;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #e5e5e5;
  position: relative;
  overflow: hidden;
}
.preset-color.data-v-d47e139d:active {
  transform: scale(0.95);
}
.color-code.data-v-d47e139d {
  font-size: 20rpx;
  color: white;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.5);
  font-family: "Monaco", "Consolas", monospace;
}
.tips-content.data-v-d47e139d {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-d47e139d {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-d47e139d {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-d47e139d {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.color-picker-modal.data-v-d47e139d {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 99999 !important;
  animation: fadeIn-d47e139d 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
@keyframes fadeIn-d47e139d {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.color-picker-container.data-v-d47e139d {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 24rpx;
  padding: 48rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 100%;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15), 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  animation: slideUp-d47e139d 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
@keyframes slideUp-d47e139d {
from {
    opacity: 0;
    transform: translateY(40rpx) scale(0.95);
}
to {
    opacity: 1;
    transform: translateY(0) scale(1);
}
}
.picker-header.data-v-d47e139d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.picker-title.data-v-d47e139d {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}
.picker-close.data-v-d47e139d {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
}
.picker-close.data-v-d47e139d:hover {
  background-color: #e5e7eb;
  transform: scale(1.1);
}
.close-icon.data-v-d47e139d {
  font-size: 36rpx;
  color: #6b7280;
  font-weight: 300;
}

/* 主颜色画布 */
.color-canvas-container.data-v-d47e139d {
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.color-canvas.data-v-d47e139d {
  width: 600rpx;
  height: 400rpx;
  display: block;
}
.canvas-cursor.data-v-d47e139d {
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 4rpx solid #ffffff;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

/* 色相滑块 */
.hue-slider-container.data-v-d47e139d {
  position: relative;
  margin-bottom: 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.hue-slider.data-v-d47e139d {
  width: 600rpx;
  height: 60rpx;
  display: block;
}
.hue-cursor.data-v-d47e139d {
  position: absolute;
  top: 0;
  width: 6rpx;
  height: 60rpx;
  background-color: #ffffff;
  box-shadow: 0 0 8rpx rgba(0, 0, 0, 0.5);
  transform: translateX(-50%);
  pointer-events: none;
  z-index: 10;
}

/* RGB输入 */
.rgb-inputs.data-v-d47e139d {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.rgb-group.data-v-d47e139d {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.rgb-label.data-v-d47e139d {
  font-size: 24rpx;
  font-weight: 600;
  color: #6b7280;
}
.rgb-input.data-v-d47e139d {
  width: 100%;
  height: 60rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1f2937;
  text-align: center;
  font-family: "SF Mono", "Monaco", monospace;
  transition: border-color 0.2s ease;
}
.rgb-input.data-v-d47e139d:focus {
  border-color: #3b82f6;
  outline: none;
}

/* 颜色预览 */
.picker-preview.data-v-d47e139d {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background-color: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}
.preview-color.data-v-d47e139d {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  position: relative;
  overflow: hidden;
}
.preview-color.data-v-d47e139d::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0), linear-gradient(45deg, #f0f0f0 25%, transparent 25%, transparent 75%, #f0f0f0 75%, #f0f0f0);
  background-size: 16rpx 16rpx;
  background-position: 0 0, 8rpx 8rpx;
  z-index: -1;
}
.hex-value.data-v-d47e139d {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  font-family: "SF Mono", "Monaco", monospace;
  letter-spacing: 0.05em;
}

/* 操作按钮 */
.picker-actions.data-v-d47e139d {
  display: flex;
  gap: 20rpx;
}
.picker-btn.data-v-d47e139d {
  flex: 1;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.picker-btn.cancel.data-v-d47e139d {
  background-color: #f3f4f6;
  color: #6b7280;
}
.picker-btn.cancel.data-v-d47e139d:hover {
  background-color: #e5e7eb;
  transform: translateY(-2rpx);
}
.picker-btn.confirm.data-v-d47e139d {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.picker-btn.confirm.data-v-d47e139d:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.4);
}
.picker-btn.data-v-d47e139d:active {
  transform: translateY(0);
}

/* 响应式优化 */
@media (max-width: 750rpx) {
.color-picker-container.data-v-d47e139d {
    margin: 20rpx;
    padding: 32rpx;
}
.color-canvas.data-v-d47e139d {
    width: 100%;
    height: 300rpx;
}
.hue-slider.data-v-d47e139d {
    width: 100%;
}
.rgb-inputs.data-v-d47e139d {
    gap: 12rpx;
}
}