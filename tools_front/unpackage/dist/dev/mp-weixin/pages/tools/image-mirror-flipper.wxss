/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-912165e0 {
  display: flex;
}
.flex-1.data-v-912165e0 {
  flex: 1;
}
.items-center.data-v-912165e0 {
  align-items: center;
}
.justify-center.data-v-912165e0 {
  justify-content: center;
}
.justify-between.data-v-912165e0 {
  justify-content: space-between;
}
.text-center.data-v-912165e0 {
  text-align: center;
}
.rounded.data-v-912165e0 {
  border-radius: 3px;
}
.rounded-lg.data-v-912165e0 {
  border-radius: 6px;
}
.shadow.data-v-912165e0 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-912165e0 {
  padding: 16rpx;
}
.m-4.data-v-912165e0 {
  margin: 16rpx;
}
.mb-4.data-v-912165e0 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-912165e0 {
  margin-top: 16rpx;
}
.image-flipper-container.data-v-912165e0 {
  min-height: 100vh;
  background: #ffffff;
  padding: 0 32rpx 40rpx;
}
.header.data-v-912165e0 {
  padding: 80rpx 0 40rpx;
  text-align: center;
}
.header .header-content .title.data-v-912165e0 {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}
.header .header-content .subtitle.data-v-912165e0 {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 400;
}
.main-content .upload-section.data-v-912165e0 {
  margin-bottom: 40rpx;
}
.main-content .upload-section .upload-card.data-v-912165e0 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .upload-section .upload-card .upload-icon .icon.data-v-912165e0 {
  font-size: 120rpx;
  display: block;
  margin-bottom: 24rpx;
}
.main-content .upload-section .upload-card .upload-title.data-v-912165e0 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12rpx;
}
.main-content .upload-section .upload-card .upload-desc.data-v-912165e0 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 40rpx;
  line-height: 1.5;
}
.main-content .upload-section .upload-card .upload-btn.data-v-912165e0 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: inline-block;
  box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}
.main-content .upload-section .upload-card .upload-btn.data-v-912165e0:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(59, 130, 246, 0.4);
}
.main-content .upload-section .camera-option.data-v-912165e0 {
  background: #f8fafc;
  border: 2rpx dashed #cbd5e0;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}
.main-content .upload-section .camera-option .camera-icon.data-v-912165e0 {
  font-size: 32rpx;
}
.main-content .upload-section .camera-option text.data-v-912165e0 {
  color: #4a5568;
  font-size: 28rpx;
  font-weight: 500;
}
.main-content .preview-section .preview-card.data-v-912165e0,
.main-content .preview-section .result-card.data-v-912165e0,
.main-content .result-section .preview-card.data-v-912165e0,
.main-content .result-section .result-card.data-v-912165e0 {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 40rpx;
  border: 2rpx solid #f3f4f6;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.main-content .preview-section .preview-card .section-title.data-v-912165e0,
.main-content .preview-section .result-card .section-title.data-v-912165e0,
.main-content .result-section .preview-card .section-title.data-v-912165e0,
.main-content .result-section .result-card .section-title.data-v-912165e0 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
  text-align: center;
}
.main-content .image-preview.data-v-912165e0 {
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  background: #f8fafc;
}
.main-content .image-preview .preview-image.data-v-912165e0 {
  width: 100%;
  height: 400rpx;
}
.main-content .flip-modes.data-v-912165e0 {
  margin-bottom: 32rpx;
}
.main-content .flip-modes .mode-title.data-v-912165e0 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 24rpx;
}
.main-content .flip-modes .mode-grid.data-v-912165e0 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.main-content .flip-modes .mode-grid .mode-item.data-v-912165e0 {
  padding: 24rpx;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  text-align: center;
  transition: all 0.3s ease;
}
.main-content .flip-modes .mode-grid .mode-item.active.data-v-912165e0 {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.1);
}
.main-content .flip-modes .mode-grid .mode-item .mode-icon.data-v-912165e0 {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}
.main-content .flip-modes .mode-grid .mode-item .mode-name.data-v-912165e0 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6rpx;
}
.main-content .flip-modes .mode-grid .mode-item .mode-desc.data-v-912165e0 {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
}
.main-content .advanced-options.data-v-912165e0 {
  margin-bottom: 32rpx;
}
.main-content .advanced-options .options-title.data-v-912165e0 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 20rpx;
}
.main-content .advanced-options .option-item.data-v-912165e0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}
.main-content .advanced-options .option-item.data-v-912165e0:last-child {
  border-bottom: none;
}
.main-content .advanced-options .option-item .option-label.data-v-912165e0 {
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
}
.main-content .process-btn.data-v-912165e0 {
  width: 100%;
  background: linear-gradient(135deg, #10b981, #059669);
  color: #ffffff;
  padding: 28rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 25rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}
.main-content .process-btn.data-v-912165e0:active:not([disabled]) {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(16, 185, 129, 0.4);
}
.main-content .process-btn[disabled].data-v-912165e0 {
  opacity: 0.7;
  background: #9ca3af;
}
.main-content .comparison-view.data-v-912165e0 {
  margin-bottom: 32rpx;
}
.main-content .comparison-view .comparison-item.data-v-912165e0 {
  margin-bottom: 24rpx;
  text-align: center;
}
.main-content .comparison-view .comparison-item .comparison-label.data-v-912165e0 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}
.main-content .comparison-view .comparison-item .image-container.data-v-912165e0 {
  border-radius: 12rpx;
  overflow: hidden;
  background: #f8fafc;
  border: 2rpx solid #e5e7eb;
}
.main-content .comparison-view .comparison-item .image-container.processed.data-v-912165e0 {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}
.main-content .comparison-view .comparison-item .image-container .comparison-image.data-v-912165e0 {
  width: 100%;
  height: 300rpx;
}
.main-content .comparison-view .comparison-arrow.data-v-912165e0 {
  text-align: center;
  margin: 20rpx 0;
}
.main-content .comparison-view .comparison-arrow .arrow-icon.data-v-912165e0 {
  display: block;
  font-size: 32rpx;
  color: #3b82f6;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.main-content .comparison-view .comparison-arrow .arrow-text.data-v-912165e0 {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
}
.main-content .process-info.data-v-912165e0 {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.main-content .process-info .info-item.data-v-912165e0 {
  flex: 1;
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.main-content .process-info .info-item .info-icon.data-v-912165e0 {
  font-size: 28rpx;
}
.main-content .process-info .info-item .info-content .info-label.data-v-912165e0 {
  display: block;
  font-size: 20rpx;
  color: #6b7280;
  margin-bottom: 4rpx;
}
.main-content .process-info .info-item .info-content .info-value.data-v-912165e0 {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: #374151;
}
.main-content .action-buttons.data-v-912165e0 {
  display: flex;
  gap: 12rpx;
  margin-top: 32rpx;
}
.main-content .action-buttons button.data-v-912165e0 {
  flex: 1;
  padding: 20rpx 16rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}
.main-content .action-buttons button.data-v-912165e0:active {
  transform: translateY(1rpx);
}
.main-content .action-buttons .save-btn.data-v-912165e0 {
  background: #10b981;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}
.main-content .action-buttons .share-btn.data-v-912165e0 {
  background: #3b82f6;
  color: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.3);
}
.main-content .action-buttons .reset-btn.data-v-912165e0 {
  background: #f3f4f6;
  color: #6b7280;
  border: 1rpx solid #e5e7eb;
}
.main-content .instructions-section.data-v-912165e0 {
  margin-top: 40rpx;
}
.main-content .instructions-section .instructions-card.data-v-912165e0 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 40rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.main-content .instructions-section .instructions-card .instructions-title.data-v-912165e0 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  text-align: center;
}
.main-content .instructions-section .instructions-card .instruction-list.data-v-912165e0 {
  margin-bottom: 32rpx;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item.data-v-912165e0 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item .instruction-number.data-v-912165e0 {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  margin-right: 16rpx;
  flex-shrink: 0;
}
.main-content .instructions-section .instructions-card .instruction-list .instruction-item .instruction-text.data-v-912165e0 {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
  flex: 1;
}
.main-content .instructions-section .instructions-card .mode-explanation .explanation-title.data-v-912165e0 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16rpx;
}
.main-content .instructions-section .instructions-card .mode-explanation .explanation-list .explanation-item.data-v-912165e0 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.8;
  margin-bottom: 8rpx;
}
.hidden-canvas.data-v-912165e0 {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  opacity: 0;
  z-index: -1;
}