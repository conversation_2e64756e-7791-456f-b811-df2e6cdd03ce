/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-51a4a745 {
  display: flex;
}
.flex-1.data-v-51a4a745 {
  flex: 1;
}
.items-center.data-v-51a4a745 {
  align-items: center;
}
.justify-center.data-v-51a4a745 {
  justify-content: center;
}
.justify-between.data-v-51a4a745 {
  justify-content: space-between;
}
.text-center.data-v-51a4a745 {
  text-align: center;
}
.rounded.data-v-51a4a745 {
  border-radius: 3px;
}
.rounded-lg.data-v-51a4a745 {
  border-radius: 6px;
}
.shadow.data-v-51a4a745 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-51a4a745 {
  padding: 16rpx;
}
.m-4.data-v-51a4a745 {
  margin: 16rpx;
}
.mb-4.data-v-51a4a745 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-51a4a745 {
  margin-top: 16rpx;
}
.phone-detector-tool.data-v-51a4a745 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 30rpx;
}
.header-card.data-v-51a4a745 {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
.header-card .header-content.data-v-51a4a745 {
  display: flex;
  align-items: center;
  color: white;
}
.header-card .header-content .header-icon.data-v-51a4a745 {
  font-size: 60rpx;
  margin-right: 30rpx;
}
.header-card .header-content .header-info.data-v-51a4a745 {
  flex: 1;
}
.header-card .header-content .header-info .header-title.data-v-51a4a745 {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.header-card .header-content .header-info .header-subtitle.data-v-51a4a745 {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}
.header-card .header-content .refresh-btn.data-v-51a4a745 {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-card .header-content .refresh-btn .refresh-icon.data-v-51a4a745 {
  font-size: 32rpx;
  color: white;
}
.info-card.data-v-51a4a745 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.info-card .card-header.data-v-51a4a745 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1rpx solid #e5e7eb;
}
.info-card .card-header .header-icon.data-v-51a4a745 {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.info-card .card-header .header-title.data-v-51a4a745 {
  flex: 1;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}
.info-card .card-header .test-btn.data-v-51a4a745 {
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
}
.info-card .card-header .test-btn.data-v-51a4a745:disabled {
  opacity: 0.6;
}
.info-card .info-content.data-v-51a4a745 {
  padding: 30rpx;
}
.info-grid.data-v-51a4a745 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}
.info-item .info-label.data-v-51a4a745 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.info-item .info-value.data-v-51a4a745 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.info-item .info-value.status-online.data-v-51a4a745 {
  color: #10b981;
}
.info-item .info-value.status-offline.data-v-51a4a745 {
  color: #ef4444;
}
.info-item .info-value.status-good.data-v-51a4a745 {
  color: #10b981;
}
.info-item .info-value.status-medium.data-v-51a4a745 {
  color: #f59e0b;
}
.info-item .info-value.status-poor.data-v-51a4a745 {
  color: #ef4444;
}
.info-item .test-btn.data-v-51a4a745 {
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  font-size: 24rpx;
}
.info-item .test-btn.data-v-51a4a745:disabled {
  opacity: 0.6;
}
.speed-result.data-v-51a4a745 {
  margin-top: 24rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0fdf4, #dcfce7);
  border-radius: 12rpx;
  display: flex;
  justify-content: space-around;
}
.speed-result .speed-item.data-v-51a4a745 {
  text-align: center;
}
.speed-result .speed-item .speed-label.data-v-51a4a745 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.speed-result .speed-item .speed-value.data-v-51a4a745 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #10b981;
}
.feature-list.data-v-51a4a745 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.feature-item.data-v-51a4a745 {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
}
.feature-item .feature-icon.data-v-51a4a745 {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.feature-item .feature-info.data-v-51a4a745 {
  flex: 1;
}
.feature-item .feature-info .feature-name.data-v-51a4a745 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}
.feature-item .feature-info .feature-desc.data-v-51a4a745 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}
.performance-grid.data-v-51a4a745 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.performance-item.data-v-51a4a745 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #fefce8, #fef3c7);
  border-radius: 12rpx;
}
.performance-item .performance-icon.data-v-51a4a745 {
  font-size: 40rpx;
  margin-right: 24rpx;
}
.performance-item .performance-details.data-v-51a4a745 {
  flex: 1;
}
.performance-item .performance-details .performance-label.data-v-51a4a745 {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.performance-item .performance-details .performance-value.data-v-51a4a745 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #d97706;
}
.tips-card.data-v-51a4a745 {
  background: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.tips-card .card-header.data-v-51a4a745 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f0f9ff, #dbeafe);
}
.tips-card .card-header .header-icon.data-v-51a4a745 {
  font-size: 36rpx;
  margin-right: 20rpx;
}
.tips-card .card-header .header-title.data-v-51a4a745 {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}
.tips-card .tips-content.data-v-51a4a745 {
  padding: 30rpx;
}
.tip-item.data-v-51a4a745 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tip-item.data-v-51a4a745:last-child {
  margin-bottom: 0;
}
.tip-item .tip-bullet.data-v-51a4a745 {
  font-size: 28rpx;
  color: #6366f1;
  margin-right: 16rpx;
  margin-top: 2rpx;
}
.tip-item .tip-text.data-v-51a4a745 {
  flex: 1;
  font-size: 28rpx;
  color: #4b5563;
  line-height: 1.6;
}