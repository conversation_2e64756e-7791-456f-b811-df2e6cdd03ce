"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      borderedResults: [],
      examples: ["重要通知", "HELLO", "新年快乐", "注意事项"]
    };
  },
  methods: {
    setInputText(text) {
      this.inputText = text;
      this.generateBorderedText();
    },
    generateBorderedText() {
      if (!this.inputText.trim()) {
        this.borderedResults = [];
        return;
      }
      const results = [];
      const text = this.inputText;
      const length = text.length;
      const padding = 2;
      const simpleBorder = `┌${"─".repeat(length + padding * 2)}┐
│${" ".repeat(padding)}${text}${" ".repeat(padding)}│
└${"─".repeat(length + padding * 2)}┘`;
      results.push(simpleBorder);
      const doubleBorder = `╔${"═".repeat(length + padding * 2)}╗
║${" ".repeat(padding)}${text}${" ".repeat(padding)}║
╚${"═".repeat(length + padding * 2)}╝`;
      results.push(doubleBorder);
      const thickBorder = `┏${"━".repeat(length + padding * 2)}┓
┃${" ".repeat(padding)}${text}${" ".repeat(padding)}┃
┗${"━".repeat(length + padding * 2)}┛`;
      results.push(thickBorder);
      const roundBorder = `╭${"─".repeat(length + padding * 2)}╮
│${" ".repeat(padding)}${text}${" ".repeat(padding)}│
╰${"─".repeat(length + padding * 2)}╯`;
      results.push(roundBorder);
      const decorativeBorder = `┌─「 ${text} 」─┐
│${" ".repeat(length + 6)}│
└${"─".repeat(length + 6)}┘`;
      results.push(decorativeBorder);
      const starBorder = `${"*".repeat(length + 6)}
**  ${text}  **
${"*".repeat(length + 6)}`;
      results.push(starBorder);
      const equalBorder = `${"=".repeat(length + 4)}
  ${text}  
${"=".repeat(length + 4)}`;
      results.push(equalBorder);
      this.borderedResults = results;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 1500
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.generateBorderedText && $options.generateBorderedText(...args)]),
    b: $data.inputText,
    c: $data.borderedResults.length > 0
  }, $data.borderedResults.length > 0 ? {
    d: common_vendor.f($data.borderedResults, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: common_vendor.o(($event) => $options.copyText(result), index),
        c: index,
        d: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    e: common_vendor.f($data.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.setInputText(example), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4138af9f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/bordered-text.js.map
