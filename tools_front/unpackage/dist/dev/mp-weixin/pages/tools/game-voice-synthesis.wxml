<view class="game-voice-synthesis data-v-070343ce"><view class="content data-v-070343ce"><view class="section-card data-v-070343ce"><view class="card-header data-v-070343ce"><text class="header-icon data-v-070343ce">🎤</text><text class="header-title data-v-070343ce">输入文本</text></view><view class="card-content data-v-070343ce"><text class="label data-v-070343ce">要合成的文字内容</text><block wx:if="{{r0}}"><textarea class="text-input data-v-070343ce" placeholder="输入要转换为语音的文字..." maxlength="500" value="{{a}}" bindinput="{{b}}"/></block><text class="char-count data-v-070343ce">{{c}}/500</text></view></view><view class="section-card data-v-070343ce"><view class="card-header data-v-070343ce"><text class="header-title data-v-070343ce">选择声音类型</text></view><view class="card-content data-v-070343ce"><view class="voice-list data-v-070343ce"><view wx:for="{{d}}" wx:for-item="voice" wx:key="c" class="{{['voice-item', 'data-v-070343ce', voice.d && 'active']}}" bindtap="{{voice.e}}"><text class="voice-name data-v-070343ce">{{voice.a}}</text><text class="voice-desc data-v-070343ce">{{voice.b}}</text></view></view></view></view><view wx:if="{{e}}" class="section-card data-v-070343ce"><view class="card-content data-v-070343ce"><button class="{{['generate-button', 'data-v-070343ce', g && 'disabled']}}" bindtap="{{h}}" disabled="{{i}}">{{f}}</button></view></view><view wx:if="{{j}}" class="section-card data-v-070343ce"><view class="card-header data-v-070343ce"><text class="header-title data-v-070343ce">语音预览</text></view><view class="card-content data-v-070343ce"><view class="audio-player data-v-070343ce"><view class="audio-info data-v-070343ce"><text class="audio-title data-v-070343ce">{{k}}语音</text><text class="audio-desc data-v-070343ce">{{l}}{{m}}</text></view><view class="audio-controls data-v-070343ce"><view class="play-button data-v-070343ce" bindtap="{{o}}"><text class="play-icon data-v-070343ce">{{n}}</text></view><view class="progress-container data-v-070343ce"><view class="progress-bar data-v-070343ce"><view class="progress-fill data-v-070343ce" style="{{'width:' + p}}"></view></view><text class="time-info data-v-070343ce">{{q}} / {{r}}</text></view><view class="volume-control data-v-070343ce"><text class="volume-icon data-v-070343ce">🔊</text></view></view><view class="audio-actions data-v-070343ce"><button class="action-button secondary data-v-070343ce" bindtap="{{s}}"><text class="button-icon data-v-070343ce">🔄</text><text class="button-text data-v-070343ce">重新生成</text></button><button class="action-button primary data-v-070343ce" bindtap="{{t}}"><text class="button-icon data-v-070343ce">⬇️</text><text class="button-text data-v-070343ce">下载语音</text></button></view></view></view></view><view class="section-card data-v-070343ce"><view class="card-header data-v-070343ce"><text class="header-title data-v-070343ce">使用说明</text></view><view class="card-content data-v-070343ce"><view class="usage-list data-v-070343ce"><text class="usage-item data-v-070343ce">• 输入想要转换为语音的文字内容</text><text class="usage-item data-v-070343ce">• 选择适合的角色声音类型</text><text class="usage-item data-v-070343ce">• 点击生成语音按钮开始合成</text><text class="usage-item data-v-070343ce">• 可以预览播放进度和下载语音文件</text><text class="usage-item data-v-070343ce">• 支持多种游戏角色风格的声音模拟</text><text class="usage-item data-v-070343ce">• 演示版本使用模拟播放，正式版将支持真实音频</text></view></view></view></view></view>