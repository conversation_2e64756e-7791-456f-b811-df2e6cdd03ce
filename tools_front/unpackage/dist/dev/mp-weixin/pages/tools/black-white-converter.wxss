/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-4b8efca6 {
  display: flex;
}
.flex-1.data-v-4b8efca6 {
  flex: 1;
}
.items-center.data-v-4b8efca6 {
  align-items: center;
}
.justify-center.data-v-4b8efca6 {
  justify-content: center;
}
.justify-between.data-v-4b8efca6 {
  justify-content: space-between;
}
.text-center.data-v-4b8efca6 {
  text-align: center;
}
.rounded.data-v-4b8efca6 {
  border-radius: 3px;
}
.rounded-lg.data-v-4b8efca6 {
  border-radius: 6px;
}
.shadow.data-v-4b8efca6 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-4b8efca6 {
  padding: 16rpx;
}
.m-4.data-v-4b8efca6 {
  margin: 16rpx;
}
.mb-4.data-v-4b8efca6 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-4b8efca6 {
  margin-top: 16rpx;
}
.black-white-converter.data-v-4b8efca6 {
  min-height: 100vh;
  background: #f8f9fa;
}
.black-white-converter .content.data-v-4b8efca6 {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.black-white-converter .section-card.data-v-4b8efca6 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.black-white-converter .section-card .card-header.data-v-4b8efca6 {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.black-white-converter .section-card .card-header .header-icon.data-v-4b8efca6 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.black-white-converter .section-card .card-header .header-title.data-v-4b8efca6 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.black-white-converter .section-card .card-content .upload-area.data-v-4b8efca6 {
  border: 4rpx dashed #e5e7eb;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: #f9fafb;
  transition: all 0.3s;
}
.black-white-converter .section-card .card-content .upload-area.data-v-4b8efca6:active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.black-white-converter .section-card .card-content .upload-area .upload-icon.data-v-4b8efca6 {
  margin-bottom: 24rpx;
}
.black-white-converter .section-card .card-content .upload-area .upload-icon .icon-text.data-v-4b8efca6 {
  font-size: 80rpx;
  opacity: 0.6;
}
.black-white-converter .section-card .card-content .upload-area .upload-title.data-v-4b8efca6 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.black-white-converter .section-card .card-content .upload-area .upload-desc.data-v-4b8efca6 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}
.black-white-converter .section-card .card-content .upload-area .selected-file.data-v-4b8efca6 {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
  padding: 16rpx 24rpx;
}
.black-white-converter .section-card .card-content .upload-area .selected-file .file-name.data-v-4b8efca6 {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
}
.black-white-converter .section-card .card-content .image-preview.data-v-4b8efca6 {
  margin-top: 32rpx;
}
.black-white-converter .section-card .card-content .image-preview .preview-header.data-v-4b8efca6 {
  margin-bottom: 16rpx;
}
.black-white-converter .section-card .card-content .image-preview .preview-header .preview-title.data-v-4b8efca6 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.black-white-converter .section-card .card-content .image-preview .preview-image.data-v-4b8efca6 {
  width: 100%;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.black-white-converter .mode-list .mode-item.data-v-4b8efca6 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin-bottom: 16rpx;
  border: 3rpx solid #e5e7eb;
  border-radius: 20rpx;
  background: white;
  transition: all 0.2s;
}
.black-white-converter .mode-list .mode-item.data-v-4b8efca6:last-child {
  margin-bottom: 0;
}
.black-white-converter .mode-list .mode-item.active.data-v-4b8efca6 {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.15);
}
.black-white-converter .mode-list .mode-item .mode-info.data-v-4b8efca6 {
  flex: 1;
}
.black-white-converter .mode-list .mode-item .mode-info .mode-header.data-v-4b8efca6 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.black-white-converter .mode-list .mode-item .mode-info .mode-header .mode-icon.data-v-4b8efca6 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.black-white-converter .mode-list .mode-item .mode-info .mode-header .mode-name.data-v-4b8efca6 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.black-white-converter .mode-list .mode-item .mode-info .mode-desc.data-v-4b8efca6 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.black-white-converter .mode-list .mode-item .mode-indicator.data-v-4b8efca6 {
  width: 48rpx;
  height: 48rpx;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.black-white-converter .mode-list .mode-item .mode-indicator .indicator-icon.data-v-4b8efca6 {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.black-white-converter .convert-button.data-v-4b8efca6 {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #1f2937, #111827);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(31, 41, 55, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.black-white-converter .convert-button.data-v-4b8efca6:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(31, 41, 55, 0.2);
}
.black-white-converter .convert-button.disabled.data-v-4b8efca6 {
  opacity: 0.6;
  background: #d1d5db;
  box-shadow: none;
}
.black-white-converter .result-container .result-image.data-v-4b8efca6 {
  width: 100%;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.black-white-converter .result-container .result-actions.data-v-4b8efca6 {
  display: flex;
  gap: 16rpx;
}
.black-white-converter .result-container .result-actions .action-button.data-v-4b8efca6 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.black-white-converter .result-container .result-actions .action-button.secondary.data-v-4b8efca6 {
  background: #f3f4f6;
  color: #374151;
}
.black-white-converter .result-container .result-actions .action-button.secondary.data-v-4b8efca6:active {
  background: #e5e7eb;
  transform: scale(0.98);
}
.black-white-converter .result-container .result-actions .action-button.primary.data-v-4b8efca6 {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.black-white-converter .result-container .result-actions .action-button.primary.data-v-4b8efca6:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.black-white-converter .result-container .result-actions .action-button .button-icon.data-v-4b8efca6 {
  font-size: 24rpx;
}
.black-white-converter .result-container .result-actions .action-button .button-text.data-v-4b8efca6 {
  font-size: 28rpx;
}
.black-white-converter .usage-list .usage-item.data-v-4b8efca6 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.black-white-converter .usage-list .usage-item.data-v-4b8efca6:last-child {
  margin-bottom: 0;
}
.black-white-converter .hidden-canvas.data-v-4b8efca6 {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  z-index: -1;
}