"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedCategory: "all",
      favorites: [],
      loading: false,
      canvasId: "textWallpaperCanvas",
      previewVisible: false,
      currentWallpaper: null,
      // 自定义文字功能
      customText: "",
      customAuthor: "",
      customTextColor: "#ffffff",
      customFontSize: 24,
      selectedBgType: "gradient",
      customSolidColor: "#4facfe",
      customOpacity: 1,
      customGradient: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
      // 背景类型选项
      bgTypes: [
        { id: "solid", name: "纯色", icon: "🎨" },
        { id: "gradient", name: "渐变", icon: "🌈" }
      ],
      // 纯色背景选项
      solidColors: [
        "#4facfe",
        "#00f2fe",
        "#667eea",
        "#764ba2",
        "#f093fb",
        "#f5576c",
        "#fa709a",
        "#fee140",
        "#a8edea",
        "#fed6e3",
        "#ffecd2",
        "#fcb69f",
        "#89f7fe",
        "#66a6ff",
        "#c471f5",
        "#fa71cd",
        "#ff9a9e",
        "#fecfef",
        "#ffeaa7",
        "#fdcb6e",
        "#74b9ff",
        "#0984e3",
        "#6c5ce7",
        "#a29bfe"
      ],
      // 渐变背景预设
      gradientPresets: [
        { id: 1, style: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)" },
        { id: 2, style: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)" },
        { id: 3, style: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)" },
        { id: 4, style: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)" },
        { id: 5, style: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)" },
        { id: 6, style: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)" },
        { id: 7, style: "linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)" },
        { id: 8, style: "linear-gradient(135deg, #c471f5 0%, #fa71cd 100%)" },
        { id: 9, style: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)" },
        { id: 10, style: "linear-gradient(135deg, #ffeaa7 0%, #fdcb6e 100%)" },
        { id: 11, style: "linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)" },
        { id: 12, style: "linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)" }
      ],
      // 文字颜色选项
      textColors: [
        "#ffffff",
        "#000000",
        "#333333",
        "#666666",
        "#4facfe",
        "#667eea",
        "#f093fb",
        "#fa709a",
        "#28a745",
        "#dc3545",
        "#ffc107",
        "#17a2b8"
      ],
      categories: [
        { id: "all", name: "全部文字", icon: "📝" },
        { id: "custom", name: "我的创作", icon: "✨" },
        { id: "motivational", name: "励志格言", icon: "💪" },
        { id: "philosophy", name: "人生哲理", icon: "🤔" },
        { id: "poetry", name: "诗词名句", icon: "📜" },
        { id: "love", name: "爱情语录", icon: "💕" },
        { id: "success", name: "成功格言", icon: "🏆" },
        { id: "life", name: "生活感悟", icon: "🌱" }
      ],
      wallpapers: [
        {
          id: "motivational1",
          title: "永不放弃",
          text: "成功路上没有捷径\n只有坚持与努力",
          category: "motivational",
          bgStyle: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          textColor: "#ffffff",
          fontSize: "24px"
        },
        {
          id: "philosophy1",
          title: "人生智慧",
          text: "生活不是等待风暴过去\n而是学会在雨中起舞",
          category: "philosophy",
          author: "未知",
          bgStyle: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
          textColor: "#ffffff",
          fontSize: "22px"
        },
        {
          id: "poetry1",
          title: "古诗名句",
          text: "山重水复疑无路\n柳暗花明又一村",
          category: "poetry",
          author: "陆游",
          bgStyle: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
          textColor: "#ffffff",
          fontSize: "26px"
        },
        {
          id: "love1",
          title: "爱情箴言",
          text: "爱不是寻找完美的人\n而是学会用完美的眼光看待不完美的人",
          category: "love",
          bgStyle: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
          textColor: "#ffffff",
          fontSize: "20px"
        },
        {
          id: "success1",
          title: "成功秘诀",
          text: "机会总是留给有准备的人",
          category: "success",
          bgStyle: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
          textColor: "#333333",
          fontSize: "28px"
        },
        {
          id: "life1",
          title: "生活感悟",
          text: "每一个不曾起舞的日子\n都是对生命的辜负",
          category: "life",
          author: "尼采",
          bgStyle: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)",
          textColor: "#8B4513",
          fontSize: "22px"
        },
        {
          id: "motivational2",
          title: "追梦路上",
          text: "梦想不会逃跑\n逃跑的永远是自己",
          category: "motivational",
          bgStyle: "linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)",
          textColor: "#ffffff",
          fontSize: "24px"
        },
        {
          id: "philosophy2",
          title: "智慧人生",
          text: "知足者常乐\n能忍者自安",
          category: "philosophy",
          bgStyle: "linear-gradient(135deg, #c471f5 0%, #fa71cd 100%)",
          textColor: "#ffffff",
          fontSize: "26px"
        }
      ],
      usageTips: [
        "精选励志格言和人生哲理文字",
        "优美的渐变背景设计",
        "支持高清壁纸下载",
        "多种文字分类，满足不同需求",
        "收藏功能，管理喜欢的文字"
      ]
    };
  },
  computed: {
    filteredWallpapers() {
      return this.selectedCategory === "all" ? this.wallpapers : this.wallpapers.filter((wall) => wall.category === this.selectedCategory);
    },
    favoriteWallpapers() {
      return this.wallpapers.filter((w) => this.favorites.includes(w.id));
    },
    customBgStyle() {
      if (this.selectedBgType === "solid") {
        const color = this.customSolidColor;
        const opacity = this.customOpacity;
        const hex = color.replace("#", "");
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);
        return `rgba(${r}, ${g}, ${b}, ${opacity})`;
      } else {
        return this.customGradient;
      }
    }
  },
  onLoad() {
    this.loadWallpapers();
  },
  methods: {
    loadWallpapers() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 500);
    },
    downloadWallpaper(wallpaper) {
      common_vendor.index.showLoading({
        title: "生成中..."
      });
      const ctx = common_vendor.index.createCanvasContext(this.canvasId, this);
      const canvasWidth = 600;
      const canvasHeight = 336;
      const grd = ctx.createLinearGradient(0, 0, canvasWidth, canvasHeight);
      this.parseGradient(wallpaper.bgStyle, grd);
      ctx.fillStyle = grd;
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);
      ctx.setFillStyle(wallpaper.textColor);
      ctx.setTextAlign("center");
      ctx.setTextBaseline("middle");
      const fontSize = parseInt(wallpaper.fontSize) * 0.8;
      ctx.setFontSize(fontSize);
      const lines = wallpaper.text.split("\n");
      const lineHeight = fontSize * 1.5;
      const startY = canvasHeight / 2 - (lines.length - 1) * lineHeight / 2;
      lines.forEach((line, index) => {
        ctx.fillText(line, canvasWidth / 2, startY + index * lineHeight);
      });
      if (wallpaper.author) {
        ctx.setFontSize(fontSize * 0.7);
        ctx.fillText(`— ${wallpaper.author}`, canvasWidth / 2, startY + lines.length * lineHeight + 40);
      }
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: this.canvasId,
            success: (res) => {
              common_vendor.index.hideLoading();
              common_vendor.index.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: () => {
                  common_vendor.index.showToast({
                    title: "保存成功",
                    icon: "success",
                    duration: 2e3
                  });
                },
                fail: (err) => {
                  common_vendor.index.__f__("error", "at pages/tools/text-wallpaper.vue:631", "保存失败", err);
                  common_vendor.index.showToast({
                    title: "保存失败",
                    icon: "none",
                    duration: 2e3
                  });
                }
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/text-wallpaper.vue:641", "生成图片失败", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "生成失败",
                icon: "none",
                duration: 2e3
              });
            }
          }, this);
        }, 300);
      });
    },
    parseGradient(bgStyle, gradient) {
      if (bgStyle.includes("667eea")) {
        gradient.addColorStop(0, "#667eea");
        gradient.addColorStop(1, "#764ba2");
      } else if (bgStyle.includes("f093fb")) {
        gradient.addColorStop(0, "#f093fb");
        gradient.addColorStop(1, "#f5576c");
      } else if (bgStyle.includes("4facfe")) {
        gradient.addColorStop(0, "#4facfe");
        gradient.addColorStop(1, "#00f2fe");
      } else if (bgStyle.includes("fa709a")) {
        gradient.addColorStop(0, "#fa709a");
        gradient.addColorStop(1, "#fee140");
      } else if (bgStyle.includes("a8edea")) {
        gradient.addColorStop(0, "#a8edea");
        gradient.addColorStop(1, "#fed6e3");
      } else if (bgStyle.includes("ffecd2")) {
        gradient.addColorStop(0, "#ffecd2");
        gradient.addColorStop(1, "#fcb69f");
      } else if (bgStyle.includes("89f7fe")) {
        gradient.addColorStop(0, "#89f7fe");
        gradient.addColorStop(1, "#66a6ff");
      } else if (bgStyle.includes("c471f5")) {
        gradient.addColorStop(0, "#c471f5");
        gradient.addColorStop(1, "#fa71cd");
      } else {
        gradient.addColorStop(0, "#4facfe");
        gradient.addColorStop(1, "#00f2fe");
      }
    },
    toggleFavorite(wallpaperId) {
      if (this.favorites.includes(wallpaperId)) {
        this.favorites = this.favorites.filter((id) => id !== wallpaperId);
      } else {
        this.favorites = [...this.favorites, wallpaperId];
      }
    },
    getRandomWallpapers() {
      if (this.loading)
        return;
      this.loading = true;
      setTimeout(() => {
        const shuffled = [...this.wallpapers].sort(() => Math.random() - 0.5);
        this.wallpapers = shuffled;
        this.loading = false;
        common_vendor.index.showToast({
          title: "内容更新",
          icon: "success",
          duration: 2e3
        });
      }, 500);
    },
    previewWallpaper(wallpaper) {
      this.currentWallpaper = wallpaper;
      this.previewVisible = true;
    },
    closePreview() {
      this.previewVisible = false;
    },
    createCustomWallpaper() {
      if (!this.customText.trim()) {
        common_vendor.index.showToast({
          title: "请输入文字内容",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      const customWallpaper = {
        id: "custom_" + Date.now(),
        title: this.customText.split("\n")[0].substring(0, 10) + "...",
        text: this.customText,
        author: this.customAuthor || void 0,
        category: "custom",
        bgStyle: this.customBgStyle,
        textColor: this.customTextColor,
        fontSize: this.customFontSize + "px"
      };
      this.wallpapers.unshift(customWallpaper);
      this.selectedCategory = "custom";
      common_vendor.index.showToast({
        title: "创建成功",
        icon: "success",
        duration: 2e3
      });
      this.resetCustomSettings();
    },
    resetCustomSettings() {
      this.customText = "";
      this.customAuthor = "";
      this.customTextColor = "#ffffff";
      this.customFontSize = 24;
      this.selectedBgType = "gradient";
      this.customSolidColor = "#4facfe";
      this.customOpacity = 1;
      this.customGradient = "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)";
    }
  }
};
if (!Array) {
  const _component_path = common_vendor.resolveComponent("path");
  const _component_polyline = common_vendor.resolveComponent("polyline");
  const _component_svg = common_vendor.resolveComponent("svg");
  const _component_line = common_vendor.resolveComponent("line");
  (_component_path + _component_polyline + _component_svg + _component_line)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i;
  return common_vendor.e({
    a: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: category.id,
        d: $data.selectedCategory === category.id ? 1 : "",
        e: common_vendor.o(($event) => $data.selectedCategory = category.id, category.id)
      };
    }),
    b: $data.selectedCategory === "custom"
  }, $data.selectedCategory === "custom" ? common_vendor.e({
    c: $data.customText,
    d: common_vendor.o(($event) => $data.customText = $event.detail.value),
    e: common_vendor.t($data.customText.length),
    f: $data.customAuthor,
    g: common_vendor.o(($event) => $data.customAuthor = $event.detail.value),
    h: common_vendor.t($data.customText || "请输入文字内容"),
    i: $data.customTextColor,
    j: $data.customFontSize + "px",
    k: $data.customAuthor
  }, $data.customAuthor ? {
    l: common_vendor.t($data.customAuthor),
    m: $data.customTextColor,
    n: $data.customFontSize * 0.7 + "px"
  } : {}, {
    o: $options.customBgStyle,
    p: common_vendor.f($data.bgTypes, (type, k0, i0) => {
      return {
        a: common_vendor.t(type.icon),
        b: common_vendor.t(type.name),
        c: type.id,
        d: $data.selectedBgType === type.id ? 1 : "",
        e: common_vendor.o(($event) => $data.selectedBgType = type.id, type.id)
      };
    }),
    q: $data.selectedBgType === "solid"
  }, $data.selectedBgType === "solid" ? {
    r: common_vendor.f($data.solidColors, (color, k0, i0) => {
      return common_vendor.e({
        a: $data.customSolidColor === color
      }, $data.customSolidColor === color ? {} : {}, {
        b: color,
        c: $data.customSolidColor === color ? 1 : "",
        d: color,
        e: common_vendor.o(($event) => $data.customSolidColor = color, color)
      });
    }),
    s: common_vendor.t(Math.round($data.customOpacity * 100)),
    t: $data.customOpacity,
    v: common_vendor.o((e) => $data.customOpacity = e.detail.value)
  } : {}, {
    w: $data.selectedBgType === "gradient"
  }, $data.selectedBgType === "gradient" ? {
    x: common_vendor.f($data.gradientPresets, (gradient, k0, i0) => {
      return common_vendor.e({
        a: $data.customGradient === gradient.style
      }, $data.customGradient === gradient.style ? {} : {}, {
        b: gradient.id,
        c: $data.customGradient === gradient.style ? 1 : "",
        d: gradient.style,
        e: common_vendor.o(($event) => $data.customGradient = gradient.style, gradient.id)
      });
    })
  } : {}, {
    y: common_vendor.f($data.textColors, (color, k0, i0) => {
      return common_vendor.e({
        a: $data.customTextColor === color
      }, $data.customTextColor === color ? {} : {}, {
        b: color,
        c: $data.customTextColor === color ? 1 : "",
        d: color,
        e: common_vendor.o(($event) => $data.customTextColor = color, color)
      });
    }),
    z: common_vendor.t($data.customFontSize),
    A: $data.customFontSize,
    B: common_vendor.o((e) => $data.customFontSize = e.detail.value),
    C: common_vendor.o((...args) => $options.createCustomWallpaper && $options.createCustomWallpaper(...args)),
    D: !$data.customText.trim(),
    E: common_vendor.o((...args) => $options.resetCustomSettings && $options.resetCustomSettings(...args))
  }) : {}, {
    F: common_vendor.t($data.selectedCategory === "all" ? "全部文字" : (_a = $data.categories.find((c) => c.id === $data.selectedCategory)) == null ? void 0 : _a.name),
    G: common_vendor.t($options.filteredWallpapers.length),
    H: $data.selectedCategory !== "custom"
  }, $data.selectedCategory !== "custom" ? {
    I: common_vendor.o((...args) => $options.getRandomWallpapers && $options.getRandomWallpapers(...args)),
    J: $data.loading
  } : {}, {
    K: $data.loading
  }, $data.loading ? {
    L: common_vendor.p({
      d: "M4.93 4.93a10 10 0 1 1-1.32 11.95"
    }),
    M: common_vendor.p({
      points: "4 13 4 19 10 19"
    }),
    N: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "48",
      height: "48",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    })
  } : {
    O: common_vendor.f($options.filteredWallpapers, (wallpaper, k0, i0) => {
      var _a2;
      return common_vendor.e({
        a: common_vendor.t(wallpaper.text),
        b: wallpaper.textColor,
        c: wallpaper.fontSize,
        d: wallpaper.author
      }, wallpaper.author ? {
        e: common_vendor.t(wallpaper.author),
        f: wallpaper.textColor,
        g: `${parseInt(wallpaper.fontSize) * 0.7}px`
      } : {}, {
        h: wallpaper.bgStyle,
        i: common_vendor.t(wallpaper.title),
        j: common_vendor.t((_a2 = $data.categories.find((c) => c.id === wallpaper.category)) == null ? void 0 : _a2.name),
        k: wallpaper.id,
        l: common_vendor.o(($event) => $options.previewWallpaper(wallpaper), wallpaper.id)
      });
    })
  }, {
    P: $options.favoriteWallpapers.length > 0
  }, $options.favoriteWallpapers.length > 0 ? {
    Q: common_vendor.t($options.favoriteWallpapers.length),
    R: common_vendor.f($options.favoriteWallpapers, (wallpaper, k0, i0) => {
      return {
        a: common_vendor.t(wallpaper.title),
        b: common_vendor.t(wallpaper.text.split("\n")[0]),
        c: "6c0592d9-4-" + i0 + "," + ("6c0592d9-3-" + i0),
        d: "6c0592d9-5-" + i0 + "," + ("6c0592d9-3-" + i0),
        e: "6c0592d9-6-" + i0 + "," + ("6c0592d9-3-" + i0),
        f: "6c0592d9-3-" + i0,
        g: common_vendor.o(($event) => $options.downloadWallpaper(wallpaper), wallpaper.id),
        h: wallpaper.id
      };
    }),
    S: common_vendor.p({
      d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
    }),
    T: common_vendor.p({
      points: "7 10 12 15 17 10"
    }),
    U: common_vendor.p({
      x1: "12",
      y1: "15",
      x2: "12",
      y2: "3"
    }),
    V: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "32",
      height: "32",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    })
  } : {}, {
    W: common_vendor.f($data.usageTips, (tip, index, i0) => {
      return {
        a: common_vendor.t(tip),
        b: index
      };
    }),
    X: $data.previewVisible
  }, $data.previewVisible ? common_vendor.e({
    Y: common_vendor.p({
      x1: "18",
      y1: "6",
      x2: "6",
      y2: "18"
    }),
    Z: common_vendor.p({
      x1: "6",
      y1: "6",
      x2: "18",
      y2: "18"
    }),
    aa: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ab: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    ac: common_vendor.t(((_b = $data.currentWallpaper) == null ? void 0 : _b.text) || ""),
    ad: ((_c = $data.currentWallpaper) == null ? void 0 : _c.textColor) || "#ffffff",
    ae: ((_d = $data.currentWallpaper) == null ? void 0 : _d.fontSize) ? parseInt($data.currentWallpaper.fontSize) * 1.2 + "px" : "28px",
    af: (_e = $data.currentWallpaper) == null ? void 0 : _e.author
  }, ((_f = $data.currentWallpaper) == null ? void 0 : _f.author) ? {
    ag: common_vendor.t($data.currentWallpaper.author),
    ah: ((_g = $data.currentWallpaper) == null ? void 0 : _g.textColor) || "#ffffff",
    ai: ((_h = $data.currentWallpaper) == null ? void 0 : _h.fontSize) ? parseInt($data.currentWallpaper.fontSize) * 0.8 + "px" : "20px"
  } : {}, {
    aj: ((_i = $data.currentWallpaper) == null ? void 0 : _i.bgStyle) || "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
    ak: common_vendor.p({
      d: "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"
    }),
    al: common_vendor.p({
      points: "7 10 12 15 17 10"
    }),
    am: common_vendor.p({
      x1: "12",
      y1: "15",
      x2: "12",
      y2: "3"
    }),
    an: common_vendor.p({
      viewBox: "0 0 24 24",
      width: "24",
      height: "24",
      fill: "none",
      stroke: "currentColor",
      ["stroke-width"]: "2",
      ["stroke-linecap"]: "round",
      ["stroke-linejoin"]: "round"
    }),
    ao: common_vendor.o(($event) => $options.downloadWallpaper($data.currentWallpaper)),
    ap: common_vendor.o(() => {
    }),
    aq: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-6c0592d9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/text-wallpaper.js.map
