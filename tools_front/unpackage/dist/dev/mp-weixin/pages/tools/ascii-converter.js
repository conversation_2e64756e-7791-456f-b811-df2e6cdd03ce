"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "AsciiConverter",
  data() {
    return {
      inputText: "",
      outputText: "",
      convertType: "toAscii",
      // 'toAscii' | 'toText'
      showFullTable: false,
      asciiTable: []
    };
  },
  computed: {
    displayedAsciiTable() {
      if (this.showFullTable) {
        return this.asciiTable;
      } else {
        return this.asciiTable.filter((item) => item.code >= 32 && item.code <= 126);
      }
    }
  },
  mounted() {
    this.generateAsciiTable();
  },
  methods: {
    generateAsciiTable() {
      this.asciiTable = [];
      const controlChars = [
        "NUL",
        "SOH",
        "STX",
        "ETX",
        "EOT",
        "ENQ",
        "ACK",
        "BEL",
        "BS",
        "TAB",
        "LF",
        "VT",
        "FF",
        "CR",
        "SO",
        "SI",
        "DLE",
        "DC1",
        "DC2",
        "DC3",
        "DC4",
        "NAK",
        "SYN",
        "ETB",
        "CAN",
        "EM",
        "SUB",
        "ESC",
        "FS",
        "GS",
        "RS",
        "US"
      ];
      for (let i = 0; i <= 127; i++) {
        let char, description;
        if (i <= 31) {
          char = controlChars[i] || "";
          description = "控制字符";
        } else if (i === 32) {
          char = "空格";
          description = "空格字符";
        } else if (i === 127) {
          char = "DEL";
          description = "删除字符";
        } else {
          char = String.fromCharCode(i);
          if (i >= 48 && i <= 57) {
            description = "数字";
          } else if (i >= 65 && i <= 90) {
            description = "大写字母";
          } else if (i >= 97 && i <= 122) {
            description = "小写字母";
          } else {
            description = "符号";
          }
        }
        this.asciiTable.push({
          code: i,
          char,
          hex: "0x" + i.toString(16).toUpperCase().padStart(2, "0"),
          description
        });
      }
    },
    setConvertType(type) {
      this.convertType = type;
      this.inputText = "";
      this.outputText = "";
      common_vendor.index.showToast({
        title: type === "toAscii" ? "切换到文本转ASCII模式" : "切换到ASCII转文本模式",
        icon: "success"
      });
    },
    swapConvertType() {
      const newType = this.convertType === "toAscii" ? "toText" : "toAscii";
      this.setConvertType(newType);
    },
    insertSample(sample) {
      this.inputText = sample;
      common_vendor.index.showToast({
        title: "示例文本已插入",
        icon: "success"
      });
    },
    convertText() {
      if (!this.inputText.trim()) {
        common_vendor.index.showToast({
          title: "请输入要转换的内容",
          icon: "error"
        });
        return;
      }
      try {
        if (this.convertType === "toAscii") {
          this.outputText = this.inputText.split("").map((char) => {
            const code = char.charCodeAt(0);
            if (code > 127) {
              throw new Error(`字符 "${char}" 不是标准ASCII字符`);
            }
            return code;
          }).join(" ");
        } else {
          const asciiCodes = this.inputText.trim().split(/\s+/).filter((code) => code.trim() !== "");
          this.outputText = asciiCodes.map((code) => {
            const num = parseInt(code.trim());
            if (isNaN(num)) {
              throw new Error(`"${code}" 不是有效的数字`);
            }
            if (num < 0 || num > 127) {
              throw new Error(`ASCII码 ${num} 超出范围（0-127）`);
            }
            return String.fromCharCode(num);
          }).join("");
        }
        common_vendor.index.showToast({
          title: "转换成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: error.message,
          icon: "error"
        });
      }
    },
    clearInput() {
      this.inputText = "";
      this.outputText = "";
      common_vendor.index.showToast({
        title: "已清空",
        icon: "success"
      });
    },
    copyToClipboard() {
      if (!this.outputText)
        return;
      common_vendor.index.setClipboardData({
        data: this.outputText,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    shareResult() {
      if (!this.outputText)
        return;
      const content = `ASCII转换结果:
原文: ${this.inputText}
结果: ${this.outputText}`;
      common_vendor.index.share({
        title: "ASCII转换结果",
        content,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        }
      });
    },
    getCharCount() {
      return this.outputText.length;
    },
    getByteCount() {
      if (!this.outputText)
        return 0;
      let byteCount = 0;
      for (let i = 0; i < this.outputText.length; i++) {
        const charCode = this.outputText.charCodeAt(i);
        if (charCode <= 127) {
          byteCount += 1;
        } else if (charCode <= 2047) {
          byteCount += 2;
        } else if (charCode <= 65535) {
          byteCount += 3;
        } else {
          byteCount += 4;
        }
      }
      return byteCount;
    },
    getAsciiRange() {
      if (this.convertType === "toText") {
        const codes = this.inputText.trim().split(/\s+/).map((code) => parseInt(code)).filter((num) => !isNaN(num));
        if (codes.length === 0)
          return "无";
        return `${Math.min(...codes)}-${Math.max(...codes)}`;
      } else {
        const codes = this.inputText.split("").map((char) => char.charCodeAt(0));
        if (codes.length === 0)
          return "无";
        return `${Math.min(...codes)}-${Math.max(...codes)}`;
      }
    },
    isHighlighted(code) {
      if (!this.outputText || this.convertType === "toText")
        return false;
      const codes = this.inputText.split("").map((char) => char.charCodeAt(0));
      return codes.includes(code);
    },
    useAsciiCode(item) {
      if (this.convertType === "toAscii") {
        this.inputText += item.char;
      } else {
        this.inputText += (this.inputText ? " " : "") + item.code;
      }
      common_vendor.index.showToast({
        title: `已添加 ${item.char}(${item.code})`,
        icon: "success"
      });
    },
    toggleTableView() {
      this.showFullTable = !this.showFullTable;
      common_vendor.index.showToast({
        title: this.showFullTable ? "显示完整表格" : "显示简化表格",
        icon: "success"
      });
    },
    generateRandomText() {
      const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()";
      const length = Math.floor(Math.random() * 20) + 5;
      let result = "";
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      this.inputText = result;
      common_vendor.index.showToast({
        title: "随机文本已生成",
        icon: "success"
      });
    },
    analyzeText() {
      if (!this.inputText) {
        common_vendor.index.showToast({
          title: "请先输入文本",
          icon: "error"
        });
        return;
      }
      const analysis = {
        length: this.inputText.length,
        letters: (this.inputText.match(/[a-zA-Z]/g) || []).length,
        digits: (this.inputText.match(/[0-9]/g) || []).length,
        symbols: (this.inputText.match(/[^a-zA-Z0-9\s]/g) || []).length,
        spaces: (this.inputText.match(/\s/g) || []).length
      };
      const content = `文本分析结果:
总字符数: ${analysis.length}
字母: ${analysis.letters}
数字: ${analysis.digits}
符号: ${analysis.symbols}
空格: ${analysis.spaces}`;
      common_vendor.index.showModal({
        title: "文本分析",
        content,
        showCancel: false
      });
    },
    batchConvert() {
      common_vendor.index.showModal({
        title: "批量转换",
        content: "批量转换功能开发中，敬请期待！",
        showCancel: false
      });
    },
    exportTable() {
      const tableData = this.displayedAsciiTable.map(
        (item) => `${item.char}	${item.code}	${item.hex}	${item.description}`
      ).join("\n");
      const header = "字符	ASCII	十六进制	说明\n";
      const exportData = header + tableData;
      common_vendor.index.setClipboardData({
        data: exportData,
        success: () => {
          common_vendor.index.showToast({
            title: "ASCII表格已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.n($data.convertType === "toAscii" ? "active" : ""),
    b: common_vendor.o(($event) => $options.setConvertType("toAscii")),
    c: common_vendor.o((...args) => $options.swapConvertType && $options.swapConvertType(...args)),
    d: common_vendor.n($data.convertType === "toText" ? "active" : ""),
    e: common_vendor.o(($event) => $options.setConvertType("toText")),
    f: $data.convertType === "toAscii" ? "输入要转换的字符..." : "输入ASCII码（用空格分隔），例如：72 101 108 108 111",
    g: $data.convertType === "toAscii" ? 2e3 : 1e4,
    h: $data.inputText,
    i: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    j: common_vendor.o((...args) => $options.convertText && $options.convertText(...args)),
    k: !$data.inputText.trim(),
    l: common_vendor.n(!$data.inputText.trim() ? "disabled" : ""),
    m: $data.outputText
  }, $data.outputText ? {
    n: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    o: common_vendor.t($data.outputText)
  } : {}, {
    p: common_vendor.t($data.showFullTable ? "📋 简化" : "📖 完整"),
    q: common_vendor.o((...args) => $options.toggleTableView && $options.toggleTableView(...args)),
    r: common_vendor.f($options.displayedAsciiTable, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.char),
        b: common_vendor.t(item.code),
        c: common_vendor.t(item.hex),
        d: common_vendor.t(item.description),
        e: item.code,
        f: common_vendor.n({
          "highlight": $options.isHighlighted(item.code)
        }),
        g: common_vendor.o(($event) => $options.useAsciiCode(item), item.code)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b72d3fdb"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/ascii-converter.js.map
