"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  name: "NationalWeatherQuery",
  data() {
    return {
      searchCity: "",
      selectedCity: "北京",
      weatherData: null,
      loading: false,
      toolService: new utils_toolService.ToolService(),
      cities: [
        "北京",
        "上海",
        "广州",
        "深圳",
        "杭州",
        "南京",
        "苏州",
        "成都",
        "重庆",
        "武汉",
        "西安",
        "天津",
        "青岛",
        "大连",
        "厦门",
        "宁波",
        "长沙",
        "郑州",
        "沈阳",
        "哈尔滨",
        "长春",
        "石家庄",
        "太原",
        "济南"
      ]
    };
  },
  computed: {
    randomAQI() {
      var _a;
      return ((_a = this.weatherData) == null ? void 0 : _a.aqi) || Math.floor(Math.random() * 150) + 50;
    },
    aqiInfo() {
      if (!this.weatherData || !this.weatherData.aqi) {
        return { level: "未知", class: "unknown" };
      }
      const aqi = this.weatherData.aqi;
      if (aqi <= 50)
        return { level: "优", class: "good" };
      if (aqi <= 100)
        return { level: "良", class: "moderate" };
      if (aqi <= 150)
        return { level: "轻度污染", class: "unhealthy-sensitive" };
      if (aqi <= 200)
        return { level: "中度污染", class: "unhealthy" };
      if (aqi <= 300)
        return { level: "重度污染", class: "very-unhealthy" };
      return { level: "严重污染", class: "hazardous" };
    }
  },
  onLoad() {
    common_vendor.index.setNavigationBarTitle({
      title: "全国天气查询"
    });
    this.queryWeather("北京");
  },
  methods: {
    async handleSearch() {
      if (!this.searchCity.trim()) {
        utils_index.showError("请输入城市名称");
        return;
      }
      this.selectedCity = this.searchCity.trim();
      await this.queryWeather(this.selectedCity);
    },
    async setSelectedCity(city) {
      this.selectedCity = city;
      this.searchCity = city;
      await this.queryWeather(city);
    },
    async queryWeather(city) {
      if (!city)
        return;
      this.loading = true;
      try {
        const params = {
          city,
          type: "current"
        };
        const result = await this.toolService.queryWeather(params);
        common_vendor.index.__f__("log", "at pages/tools/national-weather-query.vue:226", "天气查询API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.current) {
            this.weatherData = {
              current: {
                weather: apiData.current.weather,
                temp: apiData.current.maxTemp,
                humidity: apiData.current.humidity,
                windSpeed: apiData.current.wind,
                pressure: "1013",
                // 模拟气压数据
                uv: Math.floor(Math.random() * 10) + 1
                // 模拟紫外线指数
              },
              aqi: apiData.current.aqi || Math.floor(Math.random() * 150) + 50,
              forecast: apiData.forecast || []
            };
            utils_index.showSuccess(`${city}天气信息获取成功`);
          } else {
            this.weatherData = null;
            utils_index.showError(apiData.message || "天气查询失败，请重试");
          }
        } else {
          this.weatherData = null;
          utils_index.showError(result.message || "天气查询失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/national-weather-query.vue:256", "天气查询失败:", error);
        this.weatherData = null;
        utils_index.showError(error.message || "天气查询失败，请重试");
      } finally {
        this.loading = false;
      }
    },
    getWeatherIcon(weather) {
      const iconMap = {
        "晴": "☀️",
        "多云": "⛅",
        "阴": "☁️",
        "小雨": "🌦️",
        "中雨": "🌧️",
        "大雨": "⛈️",
        "雷阵雨": "⛈️",
        "雪": "❄️",
        "雾": "🌫️",
        "霾": "😷"
      };
      return iconMap[weather] || "🌤️";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    b: $data.searchCity,
    c: common_vendor.o(($event) => $data.searchCity = $event.detail.value),
    d: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    e: common_vendor.f($data.cities.slice(0, 8), (city, k0, i0) => {
      return {
        a: common_vendor.t(city),
        b: city,
        c: $data.selectedCity === city ? 1 : "",
        d: common_vendor.o(($event) => $options.setSelectedCity(city), city)
      };
    }),
    f: $data.weatherData
  }, $data.weatherData ? {
    g: common_vendor.t($options.getWeatherIcon($data.weatherData.current.weather)),
    h: common_vendor.t($data.selectedCity),
    i: common_vendor.t($data.weatherData.current.temp),
    j: common_vendor.t($data.weatherData.current.weather),
    k: common_vendor.t($data.weatherData.current.humidity),
    l: common_vendor.t($data.weatherData.current.windSpeed),
    m: common_vendor.t($data.weatherData.current.pressure),
    n: common_vendor.t($data.weatherData.current.uv)
  } : {}, {
    o: $data.weatherData
  }, $data.weatherData ? {
    p: common_vendor.t($options.randomAQI),
    q: common_vendor.t($options.aqiInfo.level),
    r: common_vendor.n($options.aqiInfo.class)
  } : {}, {
    s: $data.weatherData
  }, $data.weatherData ? {
    t: common_vendor.f($data.weatherData.forecast, (forecast, k0, i0) => {
      return {
        a: common_vendor.t(forecast.date),
        b: common_vendor.t(forecast.weather),
        c: common_vendor.t(forecast.icon),
        d: common_vendor.t(forecast.high),
        e: common_vendor.t(forecast.low),
        f: forecast.date
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-34a20241"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/national-weather-query.js.map
