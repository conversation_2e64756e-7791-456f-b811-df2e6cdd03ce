"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "EatingGifAvatar",
  data() {
    return {
      uploadedImage: "",
      selectedTemplate: "eating",
      processing: false,
      generatedGif: "",
      animationSpeed: 1,
      selectedQuality: "high",
      canvasContext: null,
      imageInfo: null,
      frames: [],
      // 存储动画帧
      templates: [
        { id: "eating", name: "吃掉头像", preview: "🍽️", description: "一口一口被吃掉", frames: 8 },
        { id: "kissing", name: "亲亲头像", preview: "😘", description: "可爱的亲吻动画", frames: 6 },
        { id: "hugging", name: "抱抱头像", preview: "🤗", description: "温馨的拥抱效果", frames: 6 },
        { id: "spinning", name: "旋转头像", preview: "🌀", description: "头像快速旋转", frames: 8 },
        { id: "bouncing", name: "跳跃头像", preview: "⬆️", description: "头像上下跳跃", frames: 6 },
        { id: "shaking", name: "摇摆头像", preview: "📳", description: "左右摇摆动画", frames: 6 }
      ],
      qualityOptions: [
        { value: "low", label: "标准", quality: 0.6 },
        { value: "high", label: "高清", quality: 0.8 },
        { value: "ultra", label: "超清", quality: 1 }
      ]
    };
  },
  computed: {
    currentTemplate() {
      return this.templates.find((t) => t.id === this.selectedTemplate) || this.templates[0];
    },
    animationClass() {
      if (!this.processing)
        return "";
      const animationMap = {
        eating: "eating-animation",
        kissing: "kissing-animation",
        hugging: "hugging-animation",
        spinning: "spinning-animation",
        bouncing: "bouncing-animation",
        shaking: "shaking-animation"
      };
      return animationMap[this.selectedTemplate] || "";
    }
  },
  onReady() {
    const context = common_vendor.index.createCanvasContext("animationCanvas", this);
    if (!context) {
      common_vendor.index.__f__("error", "at pages/tools/eating-gif-avatar.vue:230", "Canvas上下文创建失败");
      return;
    }
    this.canvasContext = context;
  },
  methods: {
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const filePath = res.tempFilePaths[0];
          common_vendor.index.getFileInfo({
            filePath,
            success: (fileInfo) => {
              if (fileInfo.size > 5 * 1024 * 1024) {
                common_vendor.index.showToast({
                  title: "图片过大，请选择小于5MB的图片",
                  icon: "none",
                  duration: 2e3
                });
                return;
              }
              this.uploadedImage = filePath;
              common_vendor.index.showToast({
                title: "头像上传成功",
                icon: "success",
                duration: 1500
              });
            }
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "图片选择失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    // 移除图片
    removeImage() {
      this.uploadedImage = "";
      this.generatedGif = "";
      common_vendor.index.showToast({
        title: "已移除头像",
        icon: "success",
        duration: 1500
      });
    },
    // 选择模板
    selectTemplate(templateId) {
      this.selectedTemplate = templateId;
      const template = this.templates.find((t) => t.id === templateId);
      common_vendor.index.showToast({
        title: `已选择${template.name}`,
        icon: "success",
        duration: 1500
      });
    },
    // 速度变化
    onSpeedChange(e) {
      this.animationSpeed = parseFloat(e.detail.value.toFixed(1));
    },
    // 质量变化
    onQualityChange(e) {
      this.selectedQuality = e.detail.value;
    },
    // 生成GIF
    async generateGif() {
      if (!this.uploadedImage) {
        common_vendor.index.showToast({
          title: "请先上传头像图片",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (!this.canvasContext) {
        common_vendor.index.showToast({
          title: "Canvas初始化失败，请重试",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      try {
        this.processing = true;
        this.frames = [];
        common_vendor.index.showLoading({
          title: "正在生成动画...",
          mask: true
        });
        const template = this.templates.find((t) => t.id === this.selectedTemplate);
        const quality = this.qualityOptions.find((q) => q.value === this.selectedQuality);
        this.imageInfo = await new Promise((resolve, reject) => {
          common_vendor.index.getImageInfo({
            src: this.uploadedImage,
            success: resolve,
            fail: reject
          });
        });
        this.canvasContext.clearRect(0, 0, 240, 240);
        this.drawFrame(this.canvasContext, this.imageInfo, template.id, 0, template.frames);
        await new Promise((resolve) => {
          this.canvasContext.draw(false, () => {
            setTimeout(() => {
              common_vendor.index.canvasToTempFilePath({
                canvasId: "animationCanvas",
                quality: quality.quality,
                success: (res) => {
                  this.generatedGif = res.tempFilePath;
                  resolve();
                },
                fail: (error) => {
                  common_vendor.index.__f__("error", "at pages/tools/eating-gif-avatar.vue:367", "保存帧失败:", error);
                  resolve();
                }
              }, this);
            }, 100);
          });
        });
        this.processing = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "动画生成完成！",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/eating-gif-avatar.vue:385", "生成动画失败:", error);
        this.processing = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: error.message || "生成失败，请重试",
          icon: "none",
          duration: 2e3
        });
      }
    },
    // 绘制动画帧
    drawFrame(ctx, imageInfo, templateId, frameIndex, totalFrames) {
      const progress = frameIndex / totalFrames;
      ctx.save();
      ctx.beginPath();
      ctx.arc(120, 120, 120, 0, Math.PI * 2);
      ctx.clip();
      switch (templateId) {
        case "eating":
          const scale = 1 - progress * 0.3;
          ctx.drawImage(
            imageInfo.path,
            120 - 120 * scale,
            120 - 120 * scale,
            240 * scale,
            240 * scale
          );
          break;
        case "spinning":
          ctx.translate(120, 120);
          ctx.rotate(progress * Math.PI * 2);
          ctx.drawImage(imageInfo.path, -120, -120, 240, 240);
          break;
        case "bouncing":
          const offset = Math.sin(progress * Math.PI * 2) * 20;
          ctx.drawImage(imageInfo.path, 0, offset, 240, 240);
          break;
        case "shaking":
          const shake = Math.sin(progress * Math.PI * 2) * 15;
          ctx.drawImage(imageInfo.path, shake, 0, 240, 240);
          break;
        case "kissing":
          const kissScale = 1 + Math.sin(progress * Math.PI) * 0.1;
          ctx.translate(120, 120);
          ctx.rotate(Math.sin(progress * Math.PI) * 0.1);
          ctx.scale(kissScale, kissScale);
          ctx.drawImage(imageInfo.path, -120, -120, 240, 240);
          break;
        case "hugging":
          const hugScale = 1 + Math.sin(progress * Math.PI) * 0.15;
          ctx.scale(hugScale, 1);
          ctx.drawImage(
            imageInfo.path,
            120 - 120 * hugScale,
            0,
            240 * hugScale,
            240
          );
          break;
        default:
          ctx.drawImage(imageInfo.path, 0, 0, 240, 240);
      }
      ctx.restore();
    },
    // 下载GIF
    async downloadGif() {
      var _a;
      if (!this.generatedGif) {
        common_vendor.index.showToast({
          title: "请先生成动画",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "保存中...",
          mask: true
        });
        await common_vendor.index.saveImageToPhotosAlbum({
          filePath: this.generatedGif
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已保存到相册",
          icon: "success",
          duration: 2e3
        });
      } catch (error) {
        common_vendor.index.hideLoading();
        if ((_a = error.errMsg) == null ? void 0 : _a.includes("auth deny")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "需要相册权限才能保存图片，是否前往设置开启权限？",
            success: (res) => {
              if (res.confirm) {
                common_vendor.index.openSetting();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "保存失败，请重试",
            icon: "none",
            duration: 2e3
          });
        }
      }
    },
    // 停止动画
    stopAnimation() {
      this.processing = false;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.uploadedImage
  }, $data.uploadedImage ? {
    b: $data.uploadedImage,
    c: common_vendor.o((...args) => $options.removeImage && $options.removeImage(...args))
  } : {}, {
    d: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    e: common_vendor.f($data.templates, (template, k0, i0) => {
      return {
        a: common_vendor.t(template.preview),
        b: common_vendor.t(template.name),
        c: common_vendor.t(template.description),
        d: template.id,
        e: common_vendor.n({
          active: $data.selectedTemplate === template.id
        }),
        f: common_vendor.o(($event) => $options.selectTemplate(template.id), template.id)
      };
    }),
    f: $data.animationSpeed,
    g: common_vendor.o((...args) => $options.onSpeedChange && $options.onSpeedChange(...args)),
    h: common_vendor.t($data.animationSpeed),
    i: common_vendor.f($data.qualityOptions, (quality, k0, i0) => {
      return {
        a: quality.value,
        b: $data.selectedQuality === quality.value,
        c: common_vendor.t(quality.label),
        d: quality.value
      };
    }),
    j: common_vendor.o((...args) => $options.onQualityChange && $options.onQualityChange(...args)),
    k: $data.processing
  }, $data.processing ? {} : {}, {
    l: common_vendor.t($data.processing ? "⏳" : "🎬"),
    m: common_vendor.t($data.processing ? "生成中..." : "生成GIF"),
    n: common_vendor.o((...args) => $options.generateGif && $options.generateGif(...args)),
    o: !$data.uploadedImage || $data.processing,
    p: common_vendor.n({
      disabled: !$data.uploadedImage || $data.processing
    }),
    q: $data.generatedGif
  }, $data.generatedGif ? {
    r: common_vendor.o((...args) => $options.downloadGif && $options.downloadGif(...args))
  } : {}, {
    s: $data.uploadedImage
  }, $data.uploadedImage ? common_vendor.e({
    t: $data.uploadedImage,
    v: common_vendor.n({
      "preview-animation": $data.processing || $data.generatedGif
    }),
    w: $data.processing || $data.generatedGif
  }, $data.processing || $data.generatedGif ? {
    x: common_vendor.t($options.currentTemplate.preview)
  } : {}) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e2d77a92"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/eating-gif-avatar.js.map
