<view class="uuid-generator data-v-0c5e3227"><view class="content data-v-0c5e3227"><view class="card data-v-0c5e3227"><view class="card-header data-v-0c5e3227"><text class="hash-icon data-v-0c5e3227">#</text><text class="header-title data-v-0c5e3227">生成设置</text></view><view class="card-content data-v-0c5e3227"><view class="setting-item data-v-0c5e3227"><text class="setting-label data-v-0c5e3227">版本:</text><picker value="{{b}}" range="{{c}}" range-key="name" bindchange="{{d}}" class="version-picker data-v-0c5e3227"><view class="picker-text data-v-0c5e3227">{{a}}</view></picker></view><view class="setting-item data-v-0c5e3227"><text class="setting-label data-v-0c5e3227">数量:</text><input class="count-input data-v-0c5e3227" type="number" min="{{1}}" max="{{100}}" placeholder="5" value="{{e}}" bindinput="{{f}}"/></view><view class="format-section data-v-0c5e3227"><text class="format-label data-v-0c5e3227">输出格式:</text><view class="format-list data-v-0c5e3227"><view wx:for="{{g}}" wx:for-item="fmt" wx:key="d" class="format-item data-v-0c5e3227" bindtap="{{fmt.e}}"><view class="format-radio data-v-0c5e3227"><view class="{{['radio-dot', 'data-v-0c5e3227', fmt.a && 'selected']}}"></view></view><view class="format-info data-v-0c5e3227"><text class="format-name data-v-0c5e3227">{{fmt.b}}</text><text class="format-example data-v-0c5e3227">{{fmt.c}}</text></view></view></view></view></view></view><view class="generate-btn data-v-0c5e3227" bindtap="{{h}}"><text class="btn-icon data-v-0c5e3227">🔄</text><text class="btn-text data-v-0c5e3227">生成UUID</text></view><view wx:if="{{i}}" class="card data-v-0c5e3227"><view class="card-header data-v-0c5e3227"><text class="header-title data-v-0c5e3227">生成的UUID</text><view class="copy-all-btn data-v-0c5e3227" bindtap="{{j}}"><text class="copy-icon data-v-0c5e3227">📋</text><text class="copy-text data-v-0c5e3227">复制全部</text></view></view><view class="card-content data-v-0c5e3227"><view class="uuid-list data-v-0c5e3227"><view wx:for="{{k}}" wx:for-item="uuid" wx:key="c" class="uuid-item data-v-0c5e3227"><text class="uuid-text data-v-0c5e3227">{{uuid.a}}</text><view class="copy-btn data-v-0c5e3227" bindtap="{{uuid.b}}"><text class="copy-icon data-v-0c5e3227">📋</text></view></view></view></view></view><view class="card data-v-0c5e3227"><view class="card-header data-v-0c5e3227"><text class="header-title data-v-0c5e3227">UUID说明</text></view><view class="card-content data-v-0c5e3227"><view class="description-list data-v-0c5e3227"><text class="description-item data-v-0c5e3227">• UUID (Universally Unique Identifier) 是全球唯一标识符</text><text class="description-item data-v-0c5e3227">• UUID v4 基于随机数生成，重复概率极低</text><text class="description-item data-v-0c5e3227">• UUID v1 基于时间戳和MAC地址生成</text><text class="description-item data-v-0c5e3227">• 广泛用于数据库主键、分布式系统等场景</text><text class="description-item data-v-0c5e3227">• 标准格式为 8-4-4-4-12 的十六进制字符</text></view></view></view></view></view>