"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "MockCall",
  data() {
    return {
      isCallActive: false,
      callStatus: "来电预览",
      // 来电预览, 通话中
      callDuration: 0,
      callerName: "重要客户",
      callerNumber: "138****8888",
      callerLocation: "重庆市 重庆市",
      callerCarrier: "中国移动",
      ringtoneIndex: 0,
      ringtones: [
        { name: "默认铃声", value: "default" },
        { name: "经典铃声", value: "classic" },
        { name: "现代铃声", value: "modern" },
        { name: "音乐铃声", value: "music" }
      ],
      currentTime: "00:00",
      callTimer: null,
      timeTimer: null,
      isMuted: false,
      isSpeakerOn: false
    };
  },
  onLoad() {
    this.updateTime();
    this.timeTimer = setInterval(this.updateTime, 1e3);
  },
  onUnload() {
    this.clearTimers();
  },
  methods: {
    startCall() {
      this.isCallActive = true;
      this.callStatus = "来电预览";
      this.callDuration = 0;
    },
    answerCall() {
      this.callStatus = "通话中";
      this.startCallTimer();
    },
    rejectCall() {
      this.endCall();
    },
    endCall() {
      this.isCallActive = false;
      this.callStatus = "来电预览";
      this.callDuration = 0;
      this.clearTimers();
    },
    startCallTimer() {
      this.callTimer = setInterval(() => {
        this.callDuration++;
      }, 1e3);
    },
    clearTimers() {
      if (this.callTimer) {
        clearInterval(this.callTimer);
        this.callTimer = null;
      }
      if (this.timeTimer) {
        clearInterval(this.timeTimer);
        this.timeTimer = null;
      }
    },
    updateTime() {
      const now = /* @__PURE__ */ new Date();
      const hours = now.getHours().toString().padStart(2, "0");
      const minutes = now.getMinutes().toString().padStart(2, "0");
      this.currentTime = `${hours}:${minutes}`;
    },
    formatDuration(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    },
    onRingtoneChange(e) {
      this.ringtoneIndex = e.detail.value;
    },
    toggleMute() {
      this.isMuted = !this.isMuted;
    },
    toggleSpeaker() {
      this.isSpeakerOn = !this.isSpeakerOn;
    },
    showKeypad() {
      common_vendor.index.showToast({
        title: "拨号键盘",
        icon: "none"
      });
    },
    addCall() {
      common_vendor.index.showToast({
        title: "添加通话",
        icon: "none"
      });
    },
    showFaceTime() {
      common_vendor.index.showToast({
        title: "FaceTime",
        icon: "none"
      });
    },
    showContacts() {
      common_vendor.index.showToast({
        title: "通讯录",
        icon: "none"
      });
    },
    remindMe() {
      common_vendor.index.showToast({
        title: "提醒我",
        icon: "none"
      });
    },
    sendMessage() {
      common_vendor.index.showToast({
        title: "发送信息",
        icon: "none"
      });
    },
    onBack() {
      const pages = getCurrentPages();
      if (pages.length > 1) {
        common_vendor.index.navigateBack();
      } else {
        common_vendor.index.switchTab({ url: "/pages/index/index" });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.isCallActive
  }, $data.isCallActive ? common_vendor.e({
    b: common_vendor.t($data.callerName),
    c: $data.callStatus !== "通话中"
  }, $data.callStatus !== "通话中" ? {
    d: common_vendor.t($data.callerNumber)
  } : {}, {
    e: $data.callStatus !== "通话中"
  }, $data.callStatus !== "通话中" ? {
    f: common_vendor.t($data.callerLocation)
  } : {}, {
    g: $data.callStatus !== "通话中"
  }, $data.callStatus !== "通话中" ? {
    h: common_vendor.t($data.callerCarrier)
  } : {
    i: common_vendor.t($options.formatDuration($data.callDuration))
  }, {
    j: $data.callStatus === "通话中"
  }, $data.callStatus === "通话中" ? {
    k: $data.isMuted ? 1 : "",
    l: common_vendor.o((...args) => $options.toggleMute && $options.toggleMute(...args)),
    m: common_vendor.o((...args) => $options.showKeypad && $options.showKeypad(...args)),
    n: $data.isSpeakerOn ? 1 : "",
    o: common_vendor.o((...args) => $options.toggleSpeaker && $options.toggleSpeaker(...args)),
    p: common_vendor.o((...args) => $options.addCall && $options.addCall(...args)),
    q: common_vendor.o((...args) => $options.showFaceTime && $options.showFaceTime(...args)),
    r: common_vendor.o((...args) => $options.showContacts && $options.showContacts(...args)),
    s: common_vendor.o((...args) => $options.endCall && $options.endCall(...args))
  } : {
    t: common_vendor.o((...args) => $options.remindMe && $options.remindMe(...args)),
    v: common_vendor.o((...args) => $options.sendMessage && $options.sendMessage(...args)),
    w: common_vendor.o((...args) => $options.rejectCall && $options.rejectCall(...args)),
    x: common_vendor.o((...args) => $options.answerCall && $options.answerCall(...args))
  }) : {
    y: common_vendor.o((...args) => $options.onBack && $options.onBack(...args)),
    z: common_vendor.t($data.callerName.charAt(0)),
    A: common_vendor.t($data.callerName),
    B: common_vendor.t($data.callerNumber),
    C: common_vendor.t($data.callerLocation),
    D: common_vendor.t($data.callerCarrier),
    E: $data.callerName,
    F: common_vendor.o(($event) => $data.callerName = $event.detail.value),
    G: $data.callerNumber,
    H: common_vendor.o(($event) => $data.callerNumber = $event.detail.value),
    I: $data.callerLocation,
    J: common_vendor.o(($event) => $data.callerLocation = $event.detail.value),
    K: $data.callerCarrier,
    L: common_vendor.o(($event) => $data.callerCarrier = $event.detail.value),
    M: common_vendor.t($data.ringtones[$data.ringtoneIndex].name),
    N: $data.ringtoneIndex,
    O: $data.ringtones,
    P: common_vendor.o((...args) => $options.onRingtoneChange && $options.onRingtoneChange(...args)),
    Q: common_vendor.o((...args) => $options.startCall && $options.startCall(...args))
  }, {
    R: $data.isCallActive ? 1 : ""
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-ffd35138"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/mock-call.js.map
