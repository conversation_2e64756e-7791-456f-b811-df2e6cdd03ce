"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      baseColor: "#3B82F6",
      selectedScheme: "complementary",
      colorCount: 5,
      saturationLevel: 70,
      brightnessLevel: 50,
      showColorPanel: false,
      customColorInput: "",
      colorPalette: [
        "#3B82F6",
        "#F59E0B",
        "#EF4444",
        "#10B981",
        "#8B5CF6"
      ],
      likedPalettes: [],
      presetColors: [
        "#FF6B6B",
        "#4ECDC4",
        "#45B7D1",
        "#96CEB4",
        "#FFEAA7",
        "#DDA0DD",
        "#98D8C8",
        "#F39C12",
        "#E74C3C",
        "#9B59B6",
        "#3498DB",
        "#2ECC71",
        "#F1C40F",
        "#E67E22",
        "#1ABC9C",
        "#34495E",
        "#95A5A6",
        "#D35400",
        "#C0392B",
        "#8E44AD",
        "#FF9F43",
        "#10AC84",
        "#5F27CD",
        "#00D2D3",
        "#FF3838",
        "#FD79A8",
        "#6C5CE7",
        "#A29BFE",
        "#74B9FF",
        "#00B894"
      ],
      colorSchemes: [
        { id: "complementary", name: "互补色", desc: "对比强烈" },
        { id: "analogous", name: "类似色", desc: "和谐统一" },
        { id: "triadic", name: "三角色", desc: "活力平衡" },
        { id: "monochromatic", name: "单色调", desc: "渐变层次" },
        { id: "tetradic", name: "四角色", desc: "丰富多彩" }
      ],
      // 颜色选择器相关
      showColorPicker: false,
      selectedColor: "#3B82F6",
      currentHue: "#FF0000",
      canvasPosition: { x: 150, y: 100 },
      huePosition: 0,
      rgbValues: { r: 59, g: 130, b: 246 },
      canvasSize: { width: 300, height: 200 }
    };
  },
  computed: {
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "px",
        top: this.canvasPosition.y + "px"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "px"
      };
    }
  },
  mounted() {
    this.generatePalette();
  },
  methods: {
    openColorPicker() {
      this.selectedColor = this.baseColor;
      this.updateRgbFromHex(this.baseColor);
      this.showColorPicker = true;
      this.$nextTick(() => {
        this.initColorCanvas();
        this.initHueSlider();
      });
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      this.baseColor = this.selectedColor;
      this.generatePalette();
      this.closeColorPicker();
    },
    initColorCanvas() {
      const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
      const { width, height } = this.canvasSize;
      const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
      gradient1.addColorStop(0, "#FFFFFF");
      gradient1.addColorStop(1, this.currentHue);
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, width, height);
      const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
      gradient2.addColorStop(0, "rgba(0,0,0,0)");
      gradient2.addColorStop(1, "#000000");
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    initHueSlider() {
      const ctx = common_vendor.index.createCanvasContext("hueCanvas", this);
      const width = 300;
      const height = 30;
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17, "#FFFF00");
      gradient.addColorStop(0.33, "#00FF00");
      gradient.addColorStop(0.5, "#00FFFF");
      gradient.addColorStop(0.67, "#0000FF");
      gradient.addColorStop(0.83, "#FF00FF");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    onCanvasTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;
          this.canvasPosition.x = Math.max(0, Math.min(rect.width, x));
          this.canvasPosition.y = Math.max(0, Math.min(rect.height, y));
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    onHueTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          this.huePosition = Math.max(0, Math.min(rect.width, x));
          this.updateHueFromSlider();
        }
      }).exec();
    },
    updateColorFromCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = this.canvasPosition.x / rect.width;
          const y = this.canvasPosition.y / rect.height;
          const hue = this.huePosition / rect.width * 360;
          const saturation = x;
          const value = 1 - y;
          const rgb = this.hsvToRgb(hue, saturation, value);
          this.rgbValues = rgb;
          this.selectedColor = this.rgbToHex(rgb);
        }
      }).exec();
    },
    updateHueFromSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const hue = this.huePosition / rect.width * 360;
          this.currentHue = this.hsvToRgb(hue, 1, 1);
          this.currentHue = this.rgbToHex(this.currentHue);
          this.initColorCanvas();
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    updateRgbFromHex(hex) {
      const rgb = this.hexToRgb(hex);
      if (rgb) {
        this.rgbValues = rgb;
        const hsv = this.rgbToHsv(rgb);
        this.huePosition = hsv.h / 360 * 300;
        this.canvasPosition.x = hsv.s * this.canvasSize.width;
        this.canvasPosition.y = (1 - hsv.v) * this.canvasSize.height;
        this.currentHue = this.rgbToHex(this.hsvToRgb(hsv.h, 1, 1));
      }
    },
    onRgbChange() {
      this.selectedColor = this.rgbToHex(this.rgbValues);
      this.updateRgbFromHex(this.selectedColor);
    },
    // 颜色转换工具方法
    hsvToRgb(h, s, v) {
      const c = v * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = v - c;
      let r, g, b;
      if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255)
      };
    },
    rgbToHsv(rgb) {
      const r = rgb.r / 255;
      const g = rgb.g / 255;
      const b = rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;
      let h = 0;
      if (diff !== 0) {
        if (max === r) {
          h = (g - b) / diff % 6;
        } else if (max === g) {
          h = (b - r) / diff + 2;
        } else {
          h = (r - g) / diff + 4;
        }
      }
      h = h * 60;
      if (h < 0)
        h += 360;
      const s = max === 0 ? 0 : diff / max;
      const v = max;
      return { h, s, v };
    },
    rgbToHex(rgb) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    showColorOptions() {
      this.showColorPanel = true;
      this.customColorInput = this.baseColor;
    },
    hideColorOptions() {
      this.showColorPanel = false;
      this.customColorInput = "";
    },
    selectColor(color) {
      this.baseColor = color;
      this.generatePalette();
      this.hideColorOptions();
    },
    confirmColor() {
      if (this.isValidColor(this.customColorInput)) {
        this.baseColor = this.customColorInput.toUpperCase();
        this.generatePalette();
        this.hideColorOptions();
      }
    },
    isValidColor(color) {
      if (!color)
        return false;
      const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      return hexRegex.test(color);
    },
    onCustomColorInput(e) {
      this.customColorInput = e.detail.value;
    },
    onColorCountChange(e) {
      this.colorCount = e.detail.value;
      this.generatePalette();
    },
    onSaturationChange(e) {
      this.saturationLevel = e.detail.value;
      this.generatePalette();
    },
    onBrightnessChange(e) {
      this.brightnessLevel = e.detail.value;
      this.generatePalette();
    },
    selectScheme(schemeId) {
      this.selectedScheme = schemeId;
      this.generatePalette();
    },
    generatePalette() {
      const baseHsl = this.hexToHsl(this.baseColor);
      let newPalette = [];
      switch (this.selectedScheme) {
        case "complementary":
          newPalette = this.generateComplementary(baseHsl);
          break;
        case "analogous":
          newPalette = this.generateAnalogous(baseHsl);
          break;
        case "triadic":
          newPalette = this.generateTriadic(baseHsl);
          break;
        case "monochromatic":
          newPalette = this.generateMonochromatic(baseHsl);
          break;
        case "tetradic":
          newPalette = this.generateTetradic(baseHsl);
          break;
      }
      this.colorPalette = newPalette.slice(0, this.colorCount);
      common_vendor.index.showToast({
        title: "配色方案已生成",
        icon: "success",
        duration: 2e3
      });
    },
    generateComplementary(hsl) {
      const colors = [this.baseColor];
      const complementaryHue = (hsl.h + 180) % 360;
      for (let i = 1; i < this.colorCount; i++) {
        const hue = i % 2 === 1 ? complementaryHue : hsl.h;
        const saturation = this.saturationLevel + (Math.random() - 0.5) * 20;
        const lightness = this.brightnessLevel + (Math.random() - 0.5) * 30;
        colors.push(this.hslToHex(hue, Math.max(0, Math.min(100, saturation)), Math.max(0, Math.min(100, lightness))));
      }
      return colors;
    },
    generateAnalogous(hsl) {
      const colors = [this.baseColor];
      const hueStep = 30;
      for (let i = 1; i < this.colorCount; i++) {
        const hue = (hsl.h + i * hueStep) % 360;
        const saturation = this.saturationLevel + (Math.random() - 0.5) * 15;
        const lightness = this.brightnessLevel + (Math.random() - 0.5) * 25;
        colors.push(this.hslToHex(hue, Math.max(0, Math.min(100, saturation)), Math.max(0, Math.min(100, lightness))));
      }
      return colors;
    },
    generateTriadic(hsl) {
      const colors = [this.baseColor];
      const hueStep = 120;
      for (let i = 1; i < this.colorCount; i++) {
        const hue = (hsl.h + i * hueStep) % 360;
        const saturation = this.saturationLevel + (Math.random() - 0.5) * 20;
        const lightness = this.brightnessLevel + (Math.random() - 0.5) * 30;
        colors.push(this.hslToHex(hue, Math.max(0, Math.min(100, saturation)), Math.max(0, Math.min(100, lightness))));
      }
      return colors;
    },
    generateMonochromatic(hsl) {
      const colors = [this.baseColor];
      for (let i = 1; i < this.colorCount; i++) {
        const saturation = this.saturationLevel + (i - this.colorCount / 2) * 10;
        const lightness = this.brightnessLevel + (i - this.colorCount / 2) * 15;
        colors.push(this.hslToHex(hsl.h, Math.max(0, Math.min(100, saturation)), Math.max(0, Math.min(100, lightness))));
      }
      return colors;
    },
    generateTetradic(hsl) {
      const colors = [this.baseColor];
      const hueSteps = [90, 180, 270];
      for (let i = 1; i < this.colorCount; i++) {
        const hue = (hsl.h + hueSteps[(i - 1) % 3]) % 360;
        const saturation = this.saturationLevel + (Math.random() - 0.5) * 20;
        const lightness = this.brightnessLevel + (Math.random() - 0.5) * 30;
        colors.push(this.hslToHex(hue, Math.max(0, Math.min(100, saturation)), Math.max(0, Math.min(100, lightness))));
      }
      return colors;
    },
    // 颜色转换工具函数
    hexToHsl(hex) {
      const r = parseInt(hex.substr(1, 2), 16) / 255;
      const g = parseInt(hex.substr(3, 2), 16) / 255;
      const b = parseInt(hex.substr(5, 2), 16) / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h, s, l = (max + min) / 2;
      if (max === min) {
        h = s = 0;
      } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r:
            h = (g - b) / d + (g < b ? 6 : 0);
            break;
          case g:
            h = (b - r) / d + 2;
            break;
          case b:
            h = (r - g) / d + 4;
            break;
        }
        h /= 6;
      }
      return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
      };
    },
    hslToHex(h, s, l) {
      l /= 100;
      const a = s * Math.min(l, 1 - l) / 100;
      const f = (n) => {
        const k = (n + h / 30) % 12;
        const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
        return Math.round(255 * color).toString(16).padStart(2, "0");
      };
      return `#${f(0)}${f(8)}${f(4)}`;
    },
    copyColor(color) {
      common_vendor.index.setClipboardData({
        data: color,
        success: () => {
          common_vendor.index.showToast({
            title: "颜色已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    copyPalette() {
      const paletteText = this.colorPalette.join(", ");
      common_vendor.index.setClipboardData({
        data: paletteText,
        success: () => {
          common_vendor.index.showToast({
            title: "调色板已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    copyLikedPalette(palette) {
      const paletteText = palette.join(", ");
      common_vendor.index.setClipboardData({
        data: paletteText,
        success: () => {
          common_vendor.index.showToast({
            title: "配色已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    likePalette() {
      this.likedPalettes.push([...this.colorPalette]);
      common_vendor.index.showToast({
        title: "配色已收藏",
        icon: "success",
        duration: 2e3
      });
    },
    formatColor(color) {
      if (color.length > 7) {
        return color.substring(0, 7);
      }
      return color;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.baseColor,
    b: common_vendor.o((...args) => $options.openColorPicker && $options.openColorPicker(...args)),
    c: common_vendor.t($data.baseColor.toUpperCase()),
    d: $data.showColorPanel
  }, $data.showColorPanel ? common_vendor.e({
    e: common_vendor.f($data.presetColors, (color, k0, i0) => {
      return {
        a: color,
        b: $data.baseColor === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => $options.selectColor(color), color)
      };
    }),
    f: common_vendor.o([($event) => $data.customColorInput = $event.detail.value, (...args) => $options.onCustomColorInput && $options.onCustomColorInput(...args)]),
    g: $data.customColorInput,
    h: $options.isValidColor($data.customColorInput) ? $data.customColorInput : "#f3f4f6",
    i: common_vendor.o((...args) => $options.hideColorOptions && $options.hideColorOptions(...args)),
    j: $options.isValidColor($data.customColorInput)
  }, $options.isValidColor($data.customColorInput) ? {
    k: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args))
  } : {}) : {}, {
    l: common_vendor.t($data.colorCount),
    m: $data.colorCount,
    n: common_vendor.o((...args) => $options.onColorCountChange && $options.onColorCountChange(...args)),
    o: common_vendor.t($data.saturationLevel),
    p: $data.saturationLevel,
    q: common_vendor.o((...args) => $options.onSaturationChange && $options.onSaturationChange(...args)),
    r: common_vendor.t($data.brightnessLevel),
    s: $data.brightnessLevel,
    t: common_vendor.o((...args) => $options.onBrightnessChange && $options.onBrightnessChange(...args)),
    v: common_vendor.f($data.colorSchemes, (scheme, k0, i0) => {
      return {
        a: common_vendor.t(scheme.name),
        b: common_vendor.t(scheme.desc),
        c: scheme.id,
        d: $data.selectedScheme === scheme.id ? 1 : "",
        e: common_vendor.o(($event) => $options.selectScheme(scheme.id), scheme.id)
      };
    }),
    w: common_vendor.o((...args) => $options.generatePalette && $options.generatePalette(...args)),
    x: common_vendor.o((...args) => $options.likePalette && $options.likePalette(...args)),
    y: common_vendor.o((...args) => $options.copyPalette && $options.copyPalette(...args)),
    z: common_vendor.f($data.colorPalette, (color, index, i0) => {
      return {
        a: color,
        b: common_vendor.t($options.formatColor(color)),
        c: index,
        d: common_vendor.o(($event) => $options.copyColor(color), index)
      };
    }),
    A: `linear-gradient(45deg, ${$data.colorPalette[0]}, ${$data.colorPalette[1]})`,
    B: `linear-gradient(135deg, ${$data.colorPalette[2]}, ${$data.colorPalette[3]})`,
    C: $data.likedPalettes.length > 0
  }, $data.likedPalettes.length > 0 ? {
    D: common_vendor.f($data.likedPalettes, (palette, index, i0) => {
      return {
        a: common_vendor.f(palette, (color, colorIndex, i1) => {
          return {
            a: colorIndex,
            b: color,
            c: common_vendor.o(($event) => $options.copyColor(color), colorIndex)
          };
        }),
        b: common_vendor.o(($event) => $options.copyLikedPalette(palette), index),
        c: index
      };
    })
  } : {}, {
    E: $data.showColorPicker
  }, $data.showColorPicker ? {
    F: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    G: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    H: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    I: common_vendor.s($options.canvasCursorStyle),
    J: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    K: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    L: common_vendor.s($options.hueCursorStyle),
    M: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    N: $data.rgbValues.r,
    O: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    P: $data.rgbValues.g,
    Q: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    R: $data.rgbValues.b,
    S: $data.selectedColor,
    T: common_vendor.t($data.selectedColor),
    U: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    V: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    W: common_vendor.o(() => {
    }),
    X: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b143980c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/color-palette.js.map
