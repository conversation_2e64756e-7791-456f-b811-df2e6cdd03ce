/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a8cf9a83 {
  display: flex;
}
.flex-1.data-v-a8cf9a83 {
  flex: 1;
}
.items-center.data-v-a8cf9a83 {
  align-items: center;
}
.justify-center.data-v-a8cf9a83 {
  justify-content: center;
}
.justify-between.data-v-a8cf9a83 {
  justify-content: space-between;
}
.text-center.data-v-a8cf9a83 {
  text-align: center;
}
.rounded.data-v-a8cf9a83 {
  border-radius: 3px;
}
.rounded-lg.data-v-a8cf9a83 {
  border-radius: 6px;
}
.shadow.data-v-a8cf9a83 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a8cf9a83 {
  padding: 16rpx;
}
.m-4.data-v-a8cf9a83 {
  margin: 16rpx;
}
.mb-4.data-v-a8cf9a83 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a8cf9a83 {
  margin-top: 16rpx;
}
.common-ports.data-v-a8cf9a83 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-a8cf9a83 {
  padding: 30rpx;
}
.card.data-v-a8cf9a83 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.card .card-header.data-v-a8cf9a83 {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.card .card-header .port-icon.data-v-a8cf9a83 {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.card .card-header .header-title.data-v-a8cf9a83 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card .card-content.data-v-a8cf9a83 {
  padding: 30rpx;
}
.search-input.data-v-a8cf9a83 {
  width: 100%;
  height: 72rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f8f9fa;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.search-input.data-v-a8cf9a83::-webkit-input-placeholder {
  color: #bdbdbd;
  font-size: 28rpx;
}
.search-input.data-v-a8cf9a83::placeholder {
  color: #bdbdbd;
  font-size: 28rpx;
}
.search-input.data-v-a8cf9a83:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 4rpx rgba(0, 123, 255, 0.08);
  outline: none;
}
.category-buttons.data-v-a8cf9a83 {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.category-buttons .category-btn.data-v-a8cf9a83 {
  padding: 20rpx 30rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 25rpx;
  background: white;
  transition: all 0.3s ease;
}
.category-buttons .category-btn.active.data-v-a8cf9a83 {
  background: #007bff;
  border-color: #007bff;
}
.category-buttons .category-btn.active .btn-text.data-v-a8cf9a83 {
  color: white;
}
.category-buttons .category-btn .btn-text.data-v-a8cf9a83 {
  font-size: 26rpx;
  color: #666;
}
.ports-list .port-item.data-v-a8cf9a83 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.ports-list .port-item .port-header.data-v-a8cf9a83 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.ports-list .port-item .port-header .port-info.data-v-a8cf9a83 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.ports-list .port-item .port-header .port-info .port-number.data-v-a8cf9a83 {
  font-size: 48rpx;
  font-weight: bold;
  color: #007bff;
}
.ports-list .port-item .port-header .port-info .protocol-tag.data-v-a8cf9a83 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}
.ports-list .port-item .port-header .port-info .protocol-tag.protocol-tcp.data-v-a8cf9a83 {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}
.ports-list .port-item .port-header .port-info .protocol-tag.protocol-udp.data-v-a8cf9a83 {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}
.ports-list .port-item .port-header .port-info .protocol-tag.protocol-both.data-v-a8cf9a83 {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}
.ports-list .port-item .port-header .port-info .protocol-tag.protocol-default.data-v-a8cf9a83 {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
}
.ports-list .port-item .port-header .port-info .protocol-tag .tag-text.data-v-a8cf9a83 {
  font-size: 22rpx;
  font-weight: 500;
}
.ports-list .port-item .port-header .copy-btn.data-v-a8cf9a83 {
  font-size: 32rpx;
  color: #ccc;
  cursor: pointer;
}
.ports-list .port-item .port-content .service-name.data-v-a8cf9a83 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}
.ports-list .port-item .port-content .service-desc.data-v-a8cf9a83 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
.empty-state.data-v-a8cf9a83 {
  background: white;
  border-radius: 16rpx;
  padding: 80rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.empty-state .empty-card .empty-text.data-v-a8cf9a83 {
  font-size: 28rpx;
  color: #999;
}
.instructions .instruction-item.data-v-a8cf9a83 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}