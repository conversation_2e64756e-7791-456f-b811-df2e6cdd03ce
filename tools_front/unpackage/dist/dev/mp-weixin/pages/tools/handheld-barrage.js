"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "HandheldBarrage",
  data() {
    return {
      text: "",
      isPlaying: false,
      speed: 50,
      fontSize: 24,
      textColor: "#ffffff",
      backgroundColor: "#000000",
      direction: "left-to-right",
      barrageCount: 1,
      displayTexts: [],
      intervalId: null,
      isFullscreen: false,
      showColorModal: false,
      colorType: "text",
      // 'text' 或 'bg'
      customColor: "",
      presetTexts: [
        "生日快乐！🎉",
        "恭喜发财 🧧",
        "新年快乐 🎊",
        "加油！💪",
        "我爱你 ❤️",
        "谢谢大家 🙏",
        "演出精彩 👏",
        "支持偶像 ⭐"
      ],
      colorPresets: [
        { bg: "#000000", text: "#ffffff", name: "经典黑白" },
        { bg: "#ff0000", text: "#ffffff", name: "热情红色" },
        { bg: "#0066ff", text: "#ffffff", name: "科技蓝色" },
        { bg: "#00aa00", text: "#ffffff", name: "活力绿色" },
        { bg: "#ffff00", text: "#000000", name: "亮眼黄色" },
        { bg: "#ff6600", text: "#ffffff", name: "温暖橙色" }
      ],
      colorOptions: [
        "#ffffff",
        "#000000",
        "#ff0000",
        "#00ff00",
        "#0000ff",
        "#ffff00",
        "#ff00ff",
        "#00ffff",
        "#ffa500",
        "#800080",
        "#008000",
        "#ffc0cb",
        "#a52a2a",
        "#808080",
        "#000080",
        "#008080",
        "#800000",
        "#808000",
        "#c0c0c0",
        "#ff6347",
        "#4682b4",
        "#d2691e",
        "#9acd32",
        "#20b2aa"
      ]
    };
  },
  watch: {
    text() {
      if (this.isPlaying) {
        this.resetBarrage();
        this.startAnimation();
      }
    },
    speed() {
      if (this.isPlaying) {
        this.stopAnimation();
        this.startAnimation();
      }
    },
    direction() {
      if (this.isPlaying) {
        this.resetBarrage();
        this.startAnimation();
      }
    },
    barrageCount() {
      if (this.isPlaying) {
        this.resetBarrage();
        this.startAnimation();
      }
    }
  },
  onUnload() {
    this.stopAnimation();
  },
  methods: {
    startBarrage() {
      if (!this.text.trim()) {
        common_vendor.index.showToast({
          title: "请输入弹幕内容",
          icon: "none"
        });
        return;
      }
      this.isPlaying = true;
      this.initDisplayTexts();
      this.startAnimation();
    },
    stopBarrage() {
      this.isPlaying = false;
      this.stopAnimation();
    },
    resetBarrage() {
      this.isPlaying = false;
      this.stopAnimation();
      this.displayTexts = [];
    },
    initDisplayTexts() {
      this.displayTexts = [];
      const containerHeight = this.isFullscreen ? common_vendor.index.getSystemInfoSync().screenHeight : 500;
      const containerWidth = this.isFullscreen ? common_vendor.index.getSystemInfoSync().screenWidth : common_vendor.index.upx2px(750);
      if (this.isFullscreen) {
        const systemInfo = common_vendor.index.getSystemInfoSync();
        const screenWidth = systemInfo.screenWidth;
        const screenHeight = systemInfo.screenHeight;
        common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:398", "横屏模式 - 屏幕尺寸:", screenWidth, "x", screenHeight);
        const textLength = this.text.length * this.fontSize * 2;
        const minX = textLength;
        const maxX = screenWidth - textLength;
        for (let i = 0; i < this.barrageCount; i++) {
          const randomX = Math.random() * (maxX - minX) + minX;
          const startY = -100 - Math.random() * 200;
          const item = {
            text: this.text,
            position: randomX,
            // 随机水平位置
            top: startY,
            // 垂直位置
            speed: (1 + Math.random() * 0.5) * (this.speed / 8),
            // 垂直移动速度
            delay: i * 500 + Math.random() * 1e3,
            // 错开出现时间
            id: i
          };
          common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:423", `横屏弹幕${i}:`, {
            randomX: item.position,
            startY: item.top,
            speed: item.speed,
            delay: item.delay,
            textLength
          });
          this.displayTexts.push(item);
        }
      } else {
        const areaHeight = containerHeight / this.barrageCount;
        for (let i = 0; i < this.barrageCount; i++) {
          const minTop = i * areaHeight + this.fontSize;
          const maxTop = (i + 1) * areaHeight - this.fontSize;
          const randomTop = Math.random() * (maxTop - minTop) + minTop;
          const item = {
            text: this.text,
            position: this.direction === "left-to-right" ? -common_vendor.index.upx2px(200) : containerWidth,
            top: randomTop,
            speed: (Math.random() * 0.5 + 0.75) * (this.speed / 10)
          };
          this.displayTexts.push(item);
        }
      }
    },
    startAnimation() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
      }
      let startTime = Date.now();
      this.intervalId = setInterval(() => {
        const currentTime = Date.now();
        const containerWidth = this.isFullscreen ? common_vendor.index.getSystemInfoSync().screenWidth : common_vendor.index.upx2px(750);
        const containerHeight = this.isFullscreen ? common_vendor.index.getSystemInfoSync().screenHeight : 500;
        const textWidth = this.text.length * this.fontSize * (this.isFullscreen ? 2 : 1);
        const textHeight = this.fontSize * (this.isFullscreen ? 2 : 1);
        this.displayTexts.forEach((item, index) => {
          if (this.isFullscreen) {
            if (item.delay > 0 && currentTime - startTime < item.delay) {
              return;
            }
            item.top += item.speed;
            if (item.top > containerHeight + textHeight) {
              const minX = textWidth;
              const maxX = containerWidth - textWidth;
              const randomX = Math.random() * (maxX - minX) + minX;
              item.position = randomX;
              item.top = -textHeight - Math.random() * 300;
              item.speed = (1 + Math.random() * 0.5) * (this.speed / 8);
              item.delay = Math.random() * 2e3;
              common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:491", `横屏弹幕${item.id}重置:`, {
                newX: item.position,
                newTop: item.top,
                newSpeed: item.speed,
                newDelay: item.delay
              });
            }
          } else {
            if (this.direction === "left-to-right") {
              if (item.position > containerWidth) {
                item.position = -textWidth;
                item.speed = (Math.random() * 0.5 + 0.75) * (this.speed / 10);
              } else {
                item.position += item.speed;
              }
            } else {
              if (item.position < -textWidth) {
                item.position = containerWidth;
                item.speed = (Math.random() * 0.5 + 0.75) * (this.speed / 10);
              } else {
                item.position -= item.speed;
              }
            }
          }
        });
      }, 16);
    },
    stopAnimation() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    onFontSizeChange(e) {
      this.fontSize = e.detail.value;
    },
    onSpeedChange(e) {
      this.speed = e.detail.value;
    },
    onBarrageCountChange(e) {
      this.barrageCount = e.detail.value;
    },
    usePresetText(presetText) {
      this.text = presetText;
      common_vendor.index.showToast({
        title: "已设置文字",
        icon: "success"
      });
    },
    useColorPreset(preset) {
      this.backgroundColor = preset.bg;
      this.textColor = preset.text;
      common_vendor.index.showToast({
        title: `已应用${preset.name}配色`,
        icon: "success"
      });
    },
    showTextColorPicker() {
      this.colorType = "text";
      this.showColorModal = true;
    },
    showBgColorPicker() {
      this.colorType = "bg";
      this.showColorModal = true;
    },
    hideColorPicker() {
      this.showColorModal = false;
      this.customColor = "";
    },
    selectColor(color) {
      if (!color || !color.startsWith("#"))
        return;
      if (this.colorType === "text") {
        this.textColor = color;
      } else {
        this.backgroundColor = color;
      }
      this.hideColorPicker();
      common_vendor.index.showToast({
        title: "颜色已更新",
        icon: "success"
      });
    },
    toggleFullscreen() {
      this.isFullscreen = true;
      common_vendor.index.setScreenBrightness({
        value: 1
      });
      common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:594", "进入全屏模式");
      common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:595", "屏幕尺寸:", common_vendor.index.getSystemInfoSync().screenWidth, "x", common_vendor.index.getSystemInfoSync().screenHeight);
      if (this.isPlaying) {
        this.stopAnimation();
        this.initDisplayTexts();
        common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:600", "弹幕初始化完成:", this.displayTexts);
        this.startAnimation();
      }
    },
    exitFullscreen() {
      this.isFullscreen = false;
      common_vendor.index.__f__("log", "at pages/tools/handheld-barrage.vue:608", "退出全屏模式");
      if (this.isPlaying) {
        this.stopAnimation();
        this.initDisplayTexts();
        this.startAnimation();
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.displayTexts, (item, index, i0) => {
      return {
        a: common_vendor.t(item.text),
        b: index,
        c: `${item.position}px`,
        d: `${item.top}px`
      };
    }),
    b: `${$data.fontSize}px`,
    c: $data.textColor,
    d: !$data.text
  }, !$data.text ? {} : {}, {
    e: common_vendor.o((...args) => $options.toggleFullscreen && $options.toggleFullscreen(...args)),
    f: $data.backgroundColor,
    g: $data.text,
    h: common_vendor.o(($event) => $data.text = $event.detail.value),
    i: common_vendor.f($data.presetTexts, (presetText, index, i0) => {
      return {
        a: common_vendor.t(presetText),
        b: index,
        c: common_vendor.o(($event) => $options.usePresetText(presetText), index)
      };
    }),
    j: !$data.isPlaying
  }, !$data.isPlaying ? {
    k: common_vendor.o((...args) => $options.startBarrage && $options.startBarrage(...args))
  } : {
    l: common_vendor.o((...args) => $options.stopBarrage && $options.stopBarrage(...args))
  }, {
    m: common_vendor.o((...args) => $options.resetBarrage && $options.resetBarrage(...args)),
    n: common_vendor.t($data.fontSize),
    o: $data.fontSize,
    p: common_vendor.o((...args) => $options.onFontSizeChange && $options.onFontSizeChange(...args)),
    q: common_vendor.t($data.speed),
    r: $data.speed,
    s: common_vendor.o((...args) => $options.onSpeedChange && $options.onSpeedChange(...args)),
    t: common_vendor.t($data.barrageCount),
    v: $data.barrageCount,
    w: common_vendor.o((...args) => $options.onBarrageCountChange && $options.onBarrageCountChange(...args)),
    x: $data.direction === "left-to-right" ? 1 : "",
    y: common_vendor.o(($event) => $data.direction = "left-to-right"),
    z: $data.direction === "right-to-left" ? 1 : "",
    A: common_vendor.o(($event) => $data.direction = "right-to-left"),
    B: common_vendor.t($data.textColor),
    C: $data.textColor,
    D: common_vendor.o((...args) => $options.showTextColorPicker && $options.showTextColorPicker(...args)),
    E: common_vendor.t($data.backgroundColor),
    F: $data.backgroundColor,
    G: common_vendor.o((...args) => $options.showBgColorPicker && $options.showBgColorPicker(...args)),
    H: common_vendor.f($data.colorPresets, (preset, index, i0) => {
      return {
        a: preset.bg,
        b: preset.text,
        c: common_vendor.t(preset.name),
        d: index,
        e: common_vendor.o(($event) => $options.useColorPreset(preset), index)
      };
    }),
    I: $data.showColorModal
  }, $data.showColorModal ? {
    J: common_vendor.t($data.colorType === "text" ? "文字" : "背景"),
    K: common_vendor.o((...args) => $options.hideColorPicker && $options.hideColorPicker(...args)),
    L: common_vendor.f($data.colorOptions, (color, k0, i0) => {
      return common_vendor.e({
        a: ($data.colorType === "text" ? $data.textColor : $data.backgroundColor) === color
      }, ($data.colorType === "text" ? $data.textColor : $data.backgroundColor) === color ? {} : {}, {
        b: color,
        c: color,
        d: common_vendor.o(($event) => $options.selectColor(color), color)
      });
    }),
    M: common_vendor.o(($event) => $options.selectColor($data.customColor)),
    N: $data.customColor,
    O: common_vendor.o(($event) => $data.customColor = $event.detail.value),
    P: common_vendor.o(() => {
    }),
    Q: common_vendor.o((...args) => $options.hideColorPicker && $options.hideColorPicker(...args))
  } : {}, {
    R: $data.isFullscreen
  }, $data.isFullscreen ? {
    S: common_vendor.f($data.displayTexts, (item, index, i0) => {
      return {
        a: common_vendor.t(item.text),
        b: index,
        c: `${item.position}px`,
        d: `${item.top}px`
      };
    }),
    T: `${$data.fontSize * 2}px`,
    U: $data.textColor,
    V: common_vendor.o((...args) => $options.exitFullscreen && $options.exitFullscreen(...args)),
    W: $data.backgroundColor
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f4ba55c6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/handheld-barrage.js.map
