"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "CSSTextEffects",
  data() {
    return {
      text: "文字效果",
      selectedEffect: "shadow",
      backgroundIndex: 0,
      selectedAnimation: "fadeIn",
      animationKey: 0,
      backgrounds: [
        "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
        "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
        "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
        "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
        "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
        "#1a1a1a",
        "#ffffff"
      ],
      randomTexts: [
        "文字效果",
        "Hello World",
        "创意设计",
        "CSS Magic",
        "炫酷特效",
        "Design",
        "前端开发",
        "Web Design",
        "视觉效果",
        "Typography",
        "字体设计",
        "Creative"
      ],
      paramValues: {
        shadowX: 2,
        shadowY: 2,
        shadowBlur: 4,
        strokeWidth: 2,
        glowSize: 10,
        fontSize: 48,
        rotation: 0
      },
      effects: {
        shadow: {
          name: "阴影效果",
          style: "color: #333; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);",
          params: {
            shadowX: { label: "水平偏移", min: -20, max: 20, step: 1, unit: "px" },
            shadowY: { label: "垂直偏移", min: -20, max: 20, step: 1, unit: "px" },
            shadowBlur: { label: "模糊半径", min: 0, max: 20, step: 1, unit: "px" }
          }
        },
        gradient: {
          name: "渐变文字",
          style: "background: linear-gradient(45deg, #ff6b6b, #4ecdc4); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"
        },
        stroke: {
          name: "描边文字",
          style: "-webkit-text-stroke: 2px #333; color: transparent;",
          params: {
            strokeWidth: { label: "描边宽度", min: 1, max: 10, step: 1, unit: "px" }
          }
        },
        neon: {
          name: "霓虹效果",
          style: "color: #fff; text-shadow: 0 0 5px #00ff00, 0 0 10px #00ff00, 0 0 15px #00ff00, 0 0 20px #00ff00;",
          params: {
            glowSize: { label: "发光大小", min: 5, max: 30, step: 1, unit: "px" }
          }
        },
        emboss: {
          name: "浮雕效果",
          style: "color: #ccc; text-shadow: -1px -1px 0 #000, 1px 1px 0 #fff;"
        },
        fire: {
          name: "火焰文字",
          style: "background: linear-gradient(45deg, #ff0000, #ffff00, #ff0000); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; text-shadow: 0 0 10px rgba(255,0,0,0.8);"
        },
        chrome: {
          name: "金属质感",
          style: "background: linear-gradient(45deg, #eee, #999, #eee); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; text-shadow: 0 1px 0 #ccc, 0 2px 0 #c9c9c9, 0 3px 0 #bbb, 0 4px 0 #b9b9b9, 0 5px 0 #aaa, 0 6px 1px rgba(0,0,0,.1);"
        },
        rainbow: {
          name: "彩虹文字",
          style: "background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; animation: rainbow 3s ease-in-out infinite;"
        },
        glitch: {
          name: "故障效果",
          style: "color: #fff; text-shadow: 0.05em 0 0 #00fffc, -0.03em -0.04em 0 #fc00ff, 0.025em 0.04em 0 #fffc00;"
        },
        retro: {
          name: "复古效果",
          style: "color: #ff6b35; text-shadow: 3px 3px 0px #be4bdb, 6px 6px 0px #7c3aed, 9px 9px 0px #3b82f6;"
        }
      },
      animationTypes: [
        { key: "fadeIn", name: "淡入", icon: "🌅", desc: "透明度从0到1" },
        { key: "fadeOut", name: "淡出", icon: "🌇", desc: "透明度从1到0" },
        { key: "slideInLeft", name: "左滑入", icon: "👈", desc: "从左侧滑入" },
        { key: "slideInRight", name: "右滑入", icon: "👉", desc: "从右侧滑入" },
        { key: "slideInUp", name: "上滑入", icon: "👆", desc: "从上方滑入" },
        { key: "slideInDown", name: "下滑入", icon: "👇", desc: "从下方滑入" },
        { key: "zoomIn", name: "放大", icon: "🔍", desc: "缩放放大效果" },
        { key: "zoomOut", name: "缩小", icon: "🔎", desc: "缩放缩小效果" },
        { key: "rotateIn", name: "旋转入", icon: "🌀", desc: "旋转进入效果" },
        { key: "bounce", name: "弹跳", icon: "⚡", desc: "弹跳动画效果" },
        { key: "pulse", name: "脉冲", icon: "💓", desc: "脉冲缩放效果" },
        { key: "shake", name: "摇摆", icon: "📳", desc: "左右摇摆效果" }
      ]
    };
  },
  computed: {
    currentBackground() {
      return this.backgrounds[this.backgroundIndex];
    },
    currentEffect() {
      return this.effects[this.selectedEffect];
    }
  },
  methods: {
    onTextInput(e) {
      this.text = e.detail.value;
    },
    randomText() {
      const randomIndex = Math.floor(Math.random() * this.randomTexts.length);
      this.text = this.randomTexts[randomIndex];
      common_vendor.index.showToast({
        title: "已生成随机文字",
        icon: "success"
      });
    },
    clearText() {
      this.text = "";
      common_vendor.index.showToast({
        title: "文字已清空",
        icon: "success"
      });
    },
    selectEffect(key) {
      this.selectedEffect = key;
      common_vendor.index.showToast({
        title: `已选择${this.effects[key].name}`,
        icon: "success"
      });
    },
    onParamChange(key, e) {
      this.paramValues[key] = e.detail.value;
    },
    changeBackground() {
      this.backgroundIndex = (this.backgroundIndex + 1) % this.backgrounds.length;
      common_vendor.index.showToast({
        title: "背景已切换",
        icon: "success"
      });
    },
    selectAnimation(key) {
      this.selectedAnimation = key;
      this.animationKey++;
      common_vendor.index.showToast({
        title: `已选择${this.animationTypes.find((a) => a.key === key).name}动画`,
        icon: "success"
      });
    },
    onAnimationEnd() {
      common_vendor.index.__f__("log", "at pages/tools/css-text-effects.vue:353", "动画播放完成");
    },
    getComputedStyle() {
      const effect = this.currentEffect;
      let style = effect.style;
      if (effect.params) {
        if (this.selectedEffect === "shadow") {
          style = `color: #333; text-shadow: ${this.paramValues.shadowX}px ${this.paramValues.shadowY}px ${this.paramValues.shadowBlur}px rgba(0,0,0,0.5);`;
        } else if (this.selectedEffect === "stroke") {
          style = `-webkit-text-stroke: ${this.paramValues.strokeWidth}px #333; color: transparent;`;
        } else if (this.selectedEffect === "neon") {
          const size = this.paramValues.glowSize;
          style = `color: #fff; text-shadow: 0 0 ${size}px #00ff00, 0 0 ${size * 2}px #00ff00, 0 0 ${size * 3}px #00ff00, 0 0 ${size * 4}px #00ff00;`;
        }
      }
      return style + `; font-size: ${this.paramValues.fontSize}rpx; transform: rotate(${this.paramValues.rotation}deg);`;
    },
    generateCSS() {
      const effect = this.currentEffect;
      let css = effect.style;
      if (effect.params) {
        if (this.selectedEffect === "shadow") {
          css = `text-shadow: ${this.paramValues.shadowX}px ${this.paramValues.shadowY}px ${this.paramValues.shadowBlur}px rgba(0,0,0,0.5);`;
        } else if (this.selectedEffect === "stroke") {
          css = `-webkit-text-stroke: ${this.paramValues.strokeWidth}px #333;
color: transparent;`;
        } else if (this.selectedEffect === "neon") {
          const size = this.paramValues.glowSize;
          css = `color: #fff;
text-shadow: 
  0 0 ${size}px #00ff00,
  0 0 ${size * 2}px #00ff00,
  0 0 ${size * 3}px #00ff00,
  0 0 ${size * 4}px #00ff00;`;
        }
      }
      let bg = "";
      if (this.currentBackground && this.currentBackground.startsWith("linear-gradient")) {
        bg = `background: ${this.currentBackground};`;
      } else if (this.currentBackground && (this.currentBackground.startsWith("#") || this.currentBackground.startsWith("rgb"))) {
        bg = `background: ${this.currentBackground};`;
      }
      return `.text-effect {
  font-size: 2rem;
  font-weight: bold;
  ${bg}
  ${css}
}

/* 可选的动画效果 */
@keyframes rainbow {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}`;
    },
    copyToClipboard() {
      const cssCode = this.generateCSS();
      common_vendor.index.setClipboardData({
        data: cssCode,
        success: () => {
          common_vendor.index.showToast({
            title: "CSS代码已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.text = $event.detail.value, (...args) => $options.onTextInput && $options.onTextInput(...args)]),
    b: $data.text,
    c: common_vendor.o((...args) => $options.randomText && $options.randomText(...args)),
    d: common_vendor.o((...args) => $options.clearText && $options.clearText(...args)),
    e: common_vendor.f($data.effects, (effect, key, i0) => {
      return {
        a: common_vendor.s(effect.style),
        b: common_vendor.t(effect.name),
        c: key,
        d: $data.selectedEffect === key ? 1 : "",
        e: common_vendor.o(($event) => $options.selectEffect(key), key)
      };
    }),
    f: $options.currentEffect.params
  }, $options.currentEffect.params ? {
    g: common_vendor.f($options.currentEffect.params, (param, key, i0) => {
      return {
        a: common_vendor.t(param.label),
        b: common_vendor.t($data.paramValues[key]),
        c: common_vendor.t(param.unit),
        d: $data.paramValues[key],
        e: common_vendor.o(($event) => $options.onParamChange(key, $event), key),
        f: param.min,
        g: param.max,
        h: param.step,
        i: key
      };
    })
  } : {}, {
    h: common_vendor.f($data.animationTypes, (item, k0, i0) => {
      return {
        a: common_vendor.t(item.icon),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.desc),
        d: item.key,
        e: $data.selectedAnimation === item.key ? 1 : "",
        f: common_vendor.o(($event) => $options.selectAnimation(item.key), item.key)
      };
    }),
    i: common_vendor.o((...args) => $options.changeBackground && $options.changeBackground(...args)),
    j: common_vendor.t($data.text),
    k: common_vendor.n($data.selectedAnimation),
    l: common_vendor.s($options.getComputedStyle()),
    m: $data.animationKey,
    n: common_vendor.o((...args) => $options.onAnimationEnd && $options.onAnimationEnd(...args)),
    o: $options.currentBackground,
    p: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    q: common_vendor.t($options.generateCSS())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-dda9f4f9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/css-text-effects.js.map
