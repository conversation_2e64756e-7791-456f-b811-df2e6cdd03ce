"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: null,
      watermarkText: "水印文字",
      watermarkPosition: "bottom-right",
      opacity: 60,
      fontSize: 24,
      watermarkColor: "#ffffff",
      isProcessing: false,
      processedImageUrl: "",
      canvasWidth: 800,
      canvasHeight: 600,
      positions: [
        { id: "top-left", name: "左上角" },
        { id: "top-center", name: "上中" },
        { id: "top-right", name: "右上角" },
        { id: "center-left", name: "左中" },
        { id: "center", name: "居中" },
        { id: "center-right", name: "右中" },
        { id: "bottom-left", name: "左下角" },
        { id: "bottom-center", name: "下中" },
        { id: "bottom-right", name: "右下角" }
      ],
      colorOptions: [
        { value: "#ffffff", name: "白色" },
        { value: "#000000", name: "黑色" },
        { value: "#3b82f6", name: "蓝色" },
        { value: "#ef4444", name: "红色" },
        { value: "#10b981", name: "绿色" },
        { value: "#f59e0b", name: "黄色" },
        { value: "#8b5cf6", name: "紫色" },
        { value: "#f97316", name: "橙色" }
      ]
    };
  },
  computed: {
    canProcess() {
      return this.watermarkText.trim().length > 0;
    }
  },
  methods: {
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getImageInfo({
            src: tempFilePath,
            success: (imageInfo) => {
              this.selectedImage = {
                url: tempFilePath,
                name: this.generateFileName(imageInfo),
                width: imageInfo.width,
                height: imageInfo.height
              };
              this.processedImageUrl = "";
              common_vendor.index.showToast({
                title: "图片选择成功",
                icon: "success"
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/image-watermarker.vue:285", "获取图片信息失败:", err);
              common_vendor.index.showToast({
                title: "图片加载失败",
                icon: "none"
              });
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-watermarker.vue:294", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 生成文件名
    generateFileName(imageInfo) {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, 19);
      const extension = imageInfo.type === "png" ? "png" : "jpg";
      return `watermarked-${timestamp}.${extension}`;
    },
    // 选择位置
    selectPosition(positionId) {
      this.watermarkPosition = positionId;
      const position = this.positions.find((p) => p.id === positionId);
      common_vendor.index.showToast({
        title: `位置：${position.name}`,
        icon: "success"
      });
    },
    // 选择颜色
    selectColor(color) {
      this.watermarkColor = color;
    },
    // 透明度变化
    onOpacityChange(e) {
      this.opacity = e.detail.value;
    },
    // 字体大小变化
    onFontSizeChange(e) {
      this.fontSize = e.detail.value;
    },
    // 开始处理
    async handleProcess() {
      if (!this.selectedImage || !this.canProcess) {
        common_vendor.index.showToast({
          title: "请完善水印设置",
          icon: "none"
        });
        return;
      }
      this.isProcessing = true;
      try {
        await this.processWatermark();
        common_vendor.index.showToast({
          title: "水印添加完成！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermarker.vue:356", "处理失败:", error);
        common_vendor.index.showToast({
          title: "处理失败，请重试",
          icon: "none"
        });
      } finally {
        this.isProcessing = false;
      }
    },
    // 水印处理核心逻辑
    async processWatermark() {
      const ctx = common_vendor.index.createCanvasContext("watermarkCanvas", this);
      const imageInfo = await this.getImageInfo(this.selectedImage.url);
      const maxSize = 1e3;
      let targetWidth = imageInfo.width;
      let targetHeight = imageInfo.height;
      if (targetWidth > maxSize || targetHeight > maxSize) {
        const scale = Math.min(maxSize / targetWidth, maxSize / targetHeight);
        targetWidth = Math.floor(targetWidth * scale);
        targetHeight = Math.floor(targetHeight * scale);
      }
      this.canvasWidth = targetWidth;
      this.canvasHeight = targetHeight;
      await this.$nextTick();
      common_vendor.index.__f__("log", "at pages/tools/image-watermarker.vue:390", "Canvas尺寸:", this.canvasWidth, "x", this.canvasHeight);
      common_vendor.index.__f__("log", "at pages/tools/image-watermarker.vue:391", "水印设置:", {
        text: this.watermarkText,
        position: this.watermarkPosition,
        opacity: this.opacity,
        fontSize: this.fontSize,
        color: this.watermarkColor
      });
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.drawImage(
        imageInfo.path,
        0,
        0,
        imageInfo.width,
        imageInfo.height,
        0,
        0,
        targetWidth,
        targetHeight
      );
      this.addTextWatermark(ctx, targetWidth, targetHeight);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "watermarkCanvas",
            success: (res) => {
              this.processedImageUrl = res.tempFilePath;
              common_vendor.index.__f__("log", "at pages/tools/image-watermarker.vue:426", "水印处理完成，图片路径:", res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/image-watermarker.vue:429", "导出图片失败:", err);
              throw new Error("导出图片失败");
            }
          }, this);
        }, 1e3);
      });
    },
    // 添加文字水印
    addTextWatermark(ctx, canvasWidth, canvasHeight) {
      common_vendor.index.__f__("log", "at pages/tools/image-watermarker.vue:439", "添加文字水印");
      ctx.font = `${this.fontSize}px Arial`;
      ctx.fillStyle = this.watermarkColor;
      ctx.globalAlpha = this.opacity / 100;
      const textMetrics = ctx.measureText ? ctx.measureText(this.watermarkText) : { width: this.watermarkText.length * this.fontSize * 0.6 };
      const textWidth = textMetrics.width || this.watermarkText.length * this.fontSize * 0.6;
      const textHeight = this.fontSize;
      const position = this.calculateWatermarkPosition(
        textWidth,
        textHeight,
        canvasWidth,
        canvasHeight
      );
      ctx.fillText(this.watermarkText, position.x, position.y);
      ctx.globalAlpha = 1;
      common_vendor.index.__f__("log", "at pages/tools/image-watermarker.vue:464", "文字水印添加完成，位置:", position);
    },
    // 计算水印位置
    calculateWatermarkPosition(watermarkWidth, watermarkHeight, canvasWidth, canvasHeight) {
      const padding = 20;
      let x = 0, y = 0;
      switch (this.watermarkPosition) {
        case "top-left":
          x = padding;
          y = padding + watermarkHeight;
          break;
        case "top-center":
          x = (canvasWidth - watermarkWidth) / 2;
          y = padding + watermarkHeight;
          break;
        case "top-right":
          x = canvasWidth - watermarkWidth - padding;
          y = padding + watermarkHeight;
          break;
        case "center-left":
          x = padding;
          y = (canvasHeight + watermarkHeight) / 2;
          break;
        case "center":
          x = (canvasWidth - watermarkWidth) / 2;
          y = (canvasHeight + watermarkHeight) / 2;
          break;
        case "center-right":
          x = canvasWidth - watermarkWidth - padding;
          y = (canvasHeight + watermarkHeight) / 2;
          break;
        case "bottom-left":
          x = padding;
          y = canvasHeight - padding;
          break;
        case "bottom-center":
          x = (canvasWidth - watermarkWidth) / 2;
          y = canvasHeight - padding;
          break;
        case "bottom-right":
          x = canvasWidth - watermarkWidth - padding;
          y = canvasHeight - padding;
          break;
      }
      return { x, y };
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 重置处理
    resetProcess() {
      this.processedImageUrl = "";
      this.isProcessing = false;
    },
    // 保存图片
    saveImage() {
      if (!this.processedImageUrl) {
        common_vendor.index.showToast({
          title: "没有可保存的图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.processedImageUrl,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功！",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/image-watermarker.vue:556", "保存失败:", err);
          if (err.errMsg.includes("auth")) {
            common_vendor.index.showModal({
              title: "需要授权",
              content: "需要您授权保存图片到相册",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedImage
  }, $data.selectedImage ? {
    b: common_vendor.t($data.selectedImage.name),
    c: $data.selectedImage.url
  } : {}, {
    d: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    e: $data.selectedImage
  }, $data.selectedImage ? {
    f: $data.selectedImage.url
  } : {}, {
    g: $data.selectedImage
  }, $data.selectedImage ? {
    h: $data.watermarkText,
    i: common_vendor.o(($event) => $data.watermarkText = $event.detail.value),
    j: common_vendor.f($data.positions, (position, k0, i0) => {
      return {
        a: common_vendor.t(position.name),
        b: position.id,
        c: $data.watermarkPosition === position.id ? 1 : "",
        d: common_vendor.o(($event) => $options.selectPosition(position.id), position.id)
      };
    }),
    k: common_vendor.t($data.opacity),
    l: $data.opacity,
    m: common_vendor.o((...args) => $options.onOpacityChange && $options.onOpacityChange(...args)),
    n: common_vendor.t($data.fontSize),
    o: $data.fontSize,
    p: common_vendor.o((...args) => $options.onFontSizeChange && $options.onFontSizeChange(...args)),
    q: common_vendor.f($data.colorOptions, (color, k0, i0) => {
      return common_vendor.e({
        a: $data.watermarkColor === color.value
      }, $data.watermarkColor === color.value ? {} : {}, {
        b: color.value,
        c: $data.watermarkColor === color.value ? 1 : "",
        d: color.value,
        e: common_vendor.o(($event) => $options.selectColor(color.value), color.value)
      });
    })
  } : {}, {
    r: $data.selectedImage && $options.canProcess
  }, $data.selectedImage && $options.canProcess ? common_vendor.e({
    s: common_vendor.t($data.isProcessing ? "⏳" : "✨"),
    t: common_vendor.t($data.isProcessing ? "处理中..." : "添加水印"),
    v: $data.isProcessing
  }, $data.isProcessing ? {} : {}, {
    w: $data.isProcessing ? 1 : "",
    x: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    y: $data.isProcessing
  }) : {}, {
    z: $data.processedImageUrl
  }, $data.processedImageUrl ? {
    A: $data.processedImageUrl,
    B: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args)),
    C: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args))
  } : {}, {
    D: $data.canvasWidth + "px",
    E: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-da0a0d3e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-watermarker.js.map
