"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      gridResult: "",
      examples: ["LOVE", "你好世界", "123456789", "ABCDEFGHI"]
    };
  },
  watch: {
    inputText: {
      handler(newVal) {
        this.generateNineGrid(newVal);
      },
      immediate: true
    }
  },
  methods: {
    generateNineGrid(text) {
      if (!text.trim()) {
        this.gridResult = "";
        return;
      }
      const chars = text.split("");
      const grid = Array(9).fill("");
      chars.forEach((char, index) => {
        if (index < 9) {
          grid[index] = char;
        }
      });
      for (let i = chars.length; i < 9; i++) {
        grid[i] = chars[i % chars.length] || " ";
      }
      const result = `
┌─────┬─────┬─────┐
│  ${grid[0]}  │  ${grid[1]}  │  ${grid[2]}  │
├─────┼─────┼─────┤
│  ${grid[3]}  │  ${grid[4]}  │  ${grid[5]}  │
├─────┼─────┼─────┤
│  ${grid[6]}  │  ${grid[7]}  │  ${grid[8]}  │
└─────┴─────┴─────┘`.trim();
      this.gridResult = result;
    },
    setExample(text) {
      this.inputText = text;
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort();
      }
    },
    copyResult() {
      if (!this.gridResult)
        return;
      common_vendor.index.setClipboardData({
        data: this.gridResult,
        success: () => {
          common_vendor.index.showToast({
            title: "九宫格已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    shareResult() {
      if (!this.gridResult) {
        common_vendor.index.showToast({
          title: "请先生成九宫格",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      common_vendor.index.setClipboardData({
        data: this.gridResult,
        success: () => {
          common_vendor.index.showToast({
            title: "九宫格已复制，可直接分享",
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
  },
  // 配置页面的分享行为
  onShareAppMessage() {
    return {
      title: "文字九宫格 - 创造独特的文字视觉效果",
      path: "/pages/tools/text-nine-grid",
      imageUrl: "/static/share-cover.png"
    };
  },
  // 开启分享到朋友圈
  onShareTimeline() {
    return {
      title: "文字九宫格 - 创造独特的文字视觉效果",
      path: "/pages/tools/text-nine-grid",
      imageUrl: "/static/share-cover.png"
    };
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.inputText,
    b: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    c: common_vendor.t($data.inputText.length),
    d: $data.gridResult
  }, $data.gridResult ? {
    e: common_vendor.t($data.gridResult),
    f: common_vendor.o((...args) => $options.copyResult && $options.copyResult(...args)),
    g: common_vendor.o((...args) => $options.shareResult && $options.shareResult(...args))
  } : {}, {
    h: common_vendor.f($data.examples, (example, index, i0) => {
      return {
        a: common_vendor.t(example),
        b: index,
        c: common_vendor.o(($event) => $options.setExample(example), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-061ecc7a"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/text-nine-grid.js.map
