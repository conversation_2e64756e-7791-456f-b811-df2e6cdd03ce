"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      heartResults: [],
      loveWords: ["LOVE", "爱你", "心心", "KIS<PERSON>", "宝贝", "亲爱的", "想你", "甜心"]
    };
  },
  methods: {
    generateHeartText() {
      if (!this.inputText.trim()) {
        this.heartResults = [];
        return;
      }
      const text = this.inputText.trim();
      const results = [];
      const chars = text.split("");
      results.push(`💖${text}💖`);
      results.push(`♥️${text}♥️`);
      results.push(`💕${text}💕`);
      results.push(`💞${text}💞`);
      const heartShape1 = `  💖 💖
💖 ${text} 💖
  💖💖💖
    💖`;
      results.push(heartShape1);
      const heartShape2 = `♥ ♥ ♥
♥${text}♥
 ♥♥♥
  ♥`;
      results.push(heartShape2);
      if (chars.length > 1) {
        results.push(chars.join("💕"));
        results.push(chars.join("♥"));
        results.push(chars.join("💖"));
      }
      results.push(`💝💖${text}💖💝`);
      results.push(`💞💕${text}💕💞`);
      results.push(`💗💘${text}💘💗`);
      const heartShape3 = `💖💖💖💖💖
💖  ${text}  💖
💖       💖
  💖   💖
    💖`;
      results.push(heartShape3);
      this.heartResults = results;
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "爱心文字已复制",
            icon: "success",
            duration: 1500
          });
          if (common_vendor.index.vibrateShort) {
            common_vendor.index.vibrateShort({
              type: "light"
            });
          }
        }
      });
    },
    setExample(word) {
      this.inputText = word;
      this.generateHeartText();
      if (common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort({
          type: "light"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputText = $event.detail.value, (...args) => $options.generateHeartText && $options.generateHeartText(...args)]),
    b: $data.inputText,
    c: $data.heartResults.length > 0
  }, $data.heartResults.length > 0 ? {
    d: common_vendor.o((...args) => $options.generateHeartText && $options.generateHeartText(...args)),
    e: common_vendor.f($data.heartResults, (result, index, i0) => {
      return {
        a: common_vendor.t(result),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(result), index)
      };
    })
  } : {}, {
    f: common_vendor.f($data.loveWords, (word, index, i0) => {
      return {
        a: common_vendor.t(word),
        b: index,
        c: common_vendor.o(($event) => $options.setExample(word), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e8cea889"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/heart-text.js.map
