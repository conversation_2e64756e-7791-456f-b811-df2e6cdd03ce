/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-33a126d0 {
  display: flex;
}
.flex-1.data-v-33a126d0 {
  flex: 1;
}
.items-center.data-v-33a126d0 {
  align-items: center;
}
.justify-center.data-v-33a126d0 {
  justify-content: center;
}
.justify-between.data-v-33a126d0 {
  justify-content: space-between;
}
.text-center.data-v-33a126d0 {
  text-align: center;
}
.rounded.data-v-33a126d0 {
  border-radius: 3px;
}
.rounded-lg.data-v-33a126d0 {
  border-radius: 6px;
}
.shadow.data-v-33a126d0 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-33a126d0 {
  padding: 16rpx;
}
.m-4.data-v-33a126d0 {
  margin: 16rpx;
}
.mb-4.data-v-33a126d0 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-33a126d0 {
  margin-top: 16rpx;
}
.sad-text-library.data-v-33a126d0 {
  min-height: 100vh;
  background: #ffffff;
  overflow-x: hidden;
  width: 100%;
}
html.data-v-33a126d0, body.data-v-33a126d0, #app.data-v-33a126d0 {
  overflow-x: hidden !important;
  width: 100%;
  max-width: 100%;
}
.container.data-v-33a126d0 {
  width: 100%;
  box-sizing: border-box;
  padding: 40rpx 30rpx;
}
.search-section.data-v-33a126d0 {
  margin-bottom: 50rpx;
  width: 100%;
}
.search-box.data-v-33a126d0 {
  position: relative;
  background: linear-gradient(to right, #f9f9f9, #ffffff);
  border-radius: 50rpx;
  border: 1.5rpx solid #e9ecef;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  width: 100%;
  height: 90rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
}
.search-box.data-v-33a126d0:focus-within {
  border-color: #6366f1;
  box-shadow: 0 6rpx 24rpx rgba(99, 102, 241, 0.15);
  transform: translateY(-2rpx);
}
.search-icon.data-v-33a126d0 {
  position: absolute;
  left: 30rpx;
  font-size: 36rpx;
  color: #6366f1;
  z-index: 2;
}
.clear-icon.data-v-33a126d0 {
  position: absolute;
  right: 30rpx;
  font-size: 28rpx;
  color: #9ca3af;
  background: #f3f4f6;
  height: 40rpx;
  width: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: all 0.2s ease;
}
.clear-icon.data-v-33a126d0:active {
  background: #e5e7eb;
  transform: scale(0.95);
}
.search-input.data-v-33a126d0 {
  width: 100%;
  height: 100%;
  padding: 0 80rpx 0 80rpx;
  font-size: 32rpx;
  color: #374151;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
}
.search-input.data-v-33a126d0::-webkit-input-placeholder {
  color: #9ca3af;
  font-weight: 400;
}
.search-input.data-v-33a126d0::placeholder {
  color: #9ca3af;
  font-weight: 400;
}
.category-section.data-v-33a126d0, .help-section.data-v-33a126d0 {
  margin-bottom: 40rpx;
  width: 100%;
}
.section-header.data-v-33a126d0 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-33a126d0 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-33a126d0 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.category-scroll.data-v-33a126d0 {
  overflow-x: auto;
  overflow-y: hidden;
  white-space: nowrap;
  margin-bottom: 24rpx;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  position: relative;
  padding: 4rpx 0;
  height: auto;
}
.category-scroll.data-v-33a126d0::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -4rpx;
  width: 100%;
  height: 3rpx;
  background: #f3f4f6;
  border-radius: 4rpx;
}
.category-row.data-v-33a126d0 {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  position: relative;
  height: auto;
  padding: 2rpx 0;
}
.category-row.data-v-33a126d0::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: -4rpx;
  width: var(--6a3e43cc);
  height: 3rpx;
  background: #6366f1;
  border-radius: 4rpx;
  transition: all 0.2s ease;
}
.category-item.data-v-33a126d0 {
  min-width: 100rpx;
  padding: 16rpx 36rpx;
  background: #fff;
  border-radius: 16rpx;
  border: 1.5rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  -webkit-user-select: none;
          user-select: none;
  margin: 2rpx;
}
.category-item.data-v-33a126d0:hover {
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.08);
  border-color: #bdbdbd;
  color: #6366f1;
  transform: translateY(-2rpx);
}
.category-item.active.data-v-33a126d0 {
  border-color: #6366f1;
  box-shadow: 0 6rpx 16rpx rgba(99, 102, 241, 0.1);
  color: #6366f1;
  background: #fff;
}
.category-name.data-v-33a126d0 {
  font-size: 28rpx;
  font-weight: 500;
  color: inherit;
}
.text-list.data-v-33a126d0 {
  margin-bottom: 40rpx;
  width: 100%;
}
.text-card.data-v-33a126d0 {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  border: 1.5rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  position: relative;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
}
.text-card.data-v-33a126d0:hover, .text-card.card-hover.data-v-33a126d0 {
  transform: scale(1.03);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border-color: #e5e7eb;
}
.text-content.data-v-33a126d0 {
  margin-bottom: 0;
  width: 100%;
}
.text-body.data-v-33a126d0 {
  font-size: 30rpx;
  line-height: 1.6;
  color: #374151;
  margin-bottom: 16rpx;
  display: block;
  word-break: break-word;
}
.tag-row.data-v-33a126d0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  flex-wrap: wrap;
}
.tag-list.data-v-33a126d0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  flex: 1;
}
.tag.data-v-33a126d0 {
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  color: #8b8b8b;
  font-size: 22rpx;
  border-radius: 20rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.tag.data-v-33a126d0:hover {
  background: #ededed;
  color: #636363;
  transform: scale(1.05);
}
.category-label.data-v-33a126d0 {
  font-size: 22rpx;
  color: #bdbdbd;
  font-weight: 500;
  margin-left: 16rpx;
  white-space: nowrap;
}
.stats-section.data-v-33a126d0 {
  margin-bottom: 40rpx;
  width: 100%;
}
.stats-card.data-v-33a126d0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32rpx;
  background: linear-gradient(135deg, #f3e8ff 0%, #fce7f3 100%);
  border-radius: 20rpx;
  border: 1rpx solid #e9d5ff;
  width: 100%;
  box-sizing: border-box;
}
.stats-icon.data-v-33a126d0 {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}
.stats-number.data-v-33a126d0 {
  font-size: 48rpx;
  font-weight: 700;
  color: #7c3aed;
  margin-bottom: 4rpx;
}
.stats-label.data-v-33a126d0 {
  font-size: 26rpx;
  color: #6b7280;
  font-weight: 500;
}
.help-content.data-v-33a126d0 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
  width: 100%;
  box-sizing: border-box;
}
.help-item.data-v-33a126d0 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 12rpx;
}
.help-item.data-v-33a126d0:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-33a126d0 {
    padding: 30rpx 20rpx;
}
.category-row.data-v-33a126d0 {
    gap: 12rpx;
}
.category-item.data-v-33a126d0 {
    min-width: 80rpx;
    padding: 14rpx 20rpx;
    font-size: 24rpx;
}
.text-card.data-v-33a126d0 {
    padding: 24rpx;
}
.category-label.data-v-33a126d0 {
    font-size: 18rpx;
    margin-left: 8rpx;
}
}