/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-432de3b9 {
  display: flex;
}
.flex-1.data-v-432de3b9 {
  flex: 1;
}
.items-center.data-v-432de3b9 {
  align-items: center;
}
.justify-center.data-v-432de3b9 {
  justify-content: center;
}
.justify-between.data-v-432de3b9 {
  justify-content: space-between;
}
.text-center.data-v-432de3b9 {
  text-align: center;
}
.rounded.data-v-432de3b9 {
  border-radius: 3px;
}
.rounded-lg.data-v-432de3b9 {
  border-radius: 6px;
}
.shadow.data-v-432de3b9 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-432de3b9 {
  padding: 16rpx;
}
.m-4.data-v-432de3b9 {
  margin: 16rpx;
}
.mb-4.data-v-432de3b9 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-432de3b9 {
  margin-top: 16rpx;
}
.timestamp-converter.data-v-432de3b9 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-card.data-v-432de3b9,
.current-time-card.data-v-432de3b9,
.converter-card.data-v-432de3b9,
.results-card.data-v-432de3b9,
.common-timestamps-card.data-v-432de3b9,
.tips-card.data-v-432de3b9 {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-432de3b9 {
  display: flex;
  align-items: center;
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
}
.card-title.data-v-432de3b9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-432de3b9 {
  padding: 30rpx;
}
.header-info.data-v-432de3b9 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.header-icon.data-v-432de3b9 {
  font-size: 60rpx;
}
.header-text.data-v-432de3b9 {
  flex: 1;
}
.header-title.data-v-432de3b9 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-desc.data-v-432de3b9 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.time-display.data-v-432de3b9 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 30rpx;
}
.time-info.data-v-432de3b9,
.timestamp-info.data-v-432de3b9 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}
.time-label.data-v-432de3b9,
.timestamp-label.data-v-432de3b9 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.time-value.data-v-432de3b9 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  font-family: monospace;
}
.timestamp-value.data-v-432de3b9 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2563eb;
  font-family: monospace;
}
.time-actions.data-v-432de3b9 {
  display: flex;
  gap: 20rpx;
}
.use-current-btn.data-v-432de3b9,
.copy-btn.data-v-432de3b9 {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f8f9fa;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  text-align: center;
}
.input-section.data-v-432de3b9,
.date-section.data-v-432de3b9 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.input-label.data-v-432de3b9 {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8rpx;
}
.input-row.data-v-432de3b9 {
  display: flex;
  gap: 20rpx;
}
.timestamp-input.data-v-432de3b9 {
  flex: 1;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  text-align: center;
  font-family: monospace;
}
.unit-picker.data-v-432de3b9 {
  min-width: 140rpx;
}
.picker-display.data-v-432de3b9 {
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f8f9fa;
  text-align: center;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
}
.picker-arrow.data-v-432de3b9 {
  margin-left: 8rpx;
  color: #6b7280;
  font-size: 20rpx;
}
.date-inputs.data-v-432de3b9 {
  display: flex;
  gap: 20rpx;
}
.date-picker.data-v-432de3b9,
.time-picker.data-v-432de3b9 {
  flex: 1;
}
.convert-btn.data-v-432de3b9 {
  padding: 24rpx;
  background: #2563eb;
  color: white;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(37, 99, 235, 0.15);
  margin: 24rpx 0 0 0;
  transition: background 0.2s, transform 0.15s;
}
.convert-btn.data-v-432de3b9:active {
  background: #1746a2;
  transform: scale(0.97);
}
.convert-btn.disabled.data-v-432de3b9 {
  opacity: 0.5;
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.copy-all-btn-icon.data-v-432de3b9 {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #2563eb;
  background: none;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
  min-width: 120rpx;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-left: 0;
  justify-content: flex-end;
}
.results-grid.data-v-432de3b9 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.result-item.data-v-432de3b9 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.result-info.data-v-432de3b9 {
  flex: 1;
}
.result-label.data-v-432de3b9 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.result-value.data-v-432de3b9 {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  font-family: monospace;
  word-break: break-all;
}
.copy-icon.data-v-432de3b9 {
  font-size: 24rpx;
  color: #6b7280;
}
.timestamps-grid.data-v-432de3b9 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.timestamp-item.data-v-432de3b9 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.timestamp-item.data-v-432de3b9::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #3b82f6, #60a5fa);
  opacity: 0;
  transition: opacity 0.3s ease;
}
.timestamp-item.data-v-432de3b9:active {
  transform: scale(0.98);
  background: #f3f4f6;
}
.timestamp-item.data-v-432de3b9:active::after {
  opacity: 1;
}
.timestamp-info.data-v-432de3b9 {
  flex: 1;
  margin-right: 30rpx;
}
.timestamp-name.data-v-432de3b9 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}
.timestamp-desc.data-v-432de3b9 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
}
.timestamp-values.data-v-432de3b9 {
  text-align: right;
  position: relative;
}
.timestamp-ts.data-v-432de3b9 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 8rpx;
  font-family: monospace;
}
.timestamp-date.data-v-432de3b9 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.copy-hint.data-v-432de3b9 {
  font-size: 20rpx;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}
.timestamp-item-hover.data-v-432de3b9 {
  border-color: #3b82f6;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.1);
}
.tips-list.data-v-432de3b9 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.tip-item.data-v-432de3b9 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.tip-bullet.data-v-432de3b9 {
  font-size: 28rpx;
  color: #2563eb;
  margin-top: 4rpx;
}
.tip-text.data-v-432de3b9 {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
}
.tip-bold.data-v-432de3b9 {
  font-weight: 600;
  color: #1a1a1a;
}