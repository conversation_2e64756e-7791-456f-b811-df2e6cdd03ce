/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-958e0a5c {
  display: flex;
}
.flex-1.data-v-958e0a5c {
  flex: 1;
}
.items-center.data-v-958e0a5c {
  align-items: center;
}
.justify-center.data-v-958e0a5c {
  justify-content: center;
}
.justify-between.data-v-958e0a5c {
  justify-content: space-between;
}
.text-center.data-v-958e0a5c {
  text-align: center;
}
.rounded.data-v-958e0a5c {
  border-radius: 3px;
}
.rounded-lg.data-v-958e0a5c {
  border-radius: 6px;
}
.shadow.data-v-958e0a5c {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-958e0a5c {
  padding: 16rpx;
}
.m-4.data-v-958e0a5c {
  margin: 16rpx;
}
.mb-4.data-v-958e0a5c {
  margin-bottom: 16rpx;
}
.mt-4.data-v-958e0a5c {
  margin-top: 16rpx;
}
.emoji-collection.data-v-958e0a5c {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-958e0a5c {
  padding: 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.category-card.data-v-958e0a5c, .emoji-card.data-v-958e0a5c, .recommend-card.data-v-958e0a5c, .info-card.data-v-958e0a5c {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.category-card .card-header.data-v-958e0a5c, .emoji-card .card-header.data-v-958e0a5c, .recommend-card .card-header.data-v-958e0a5c, .info-card .card-header.data-v-958e0a5c {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx 20rpx;
}
.category-card .card-header .header-icon.data-v-958e0a5c, .emoji-card .card-header .header-icon.data-v-958e0a5c, .recommend-card .card-header .header-icon.data-v-958e0a5c, .info-card .card-header .header-icon.data-v-958e0a5c {
  font-size: 28rpx;
  margin-right: 15rpx;
}
.category-card .card-header .header-title.data-v-958e0a5c, .emoji-card .card-header .header-title.data-v-958e0a5c, .recommend-card .card-header .header-title.data-v-958e0a5c, .info-card .card-header .header-title.data-v-958e0a5c {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.category-card .card-content.data-v-958e0a5c, .emoji-card .card-content.data-v-958e0a5c, .recommend-card .card-content.data-v-958e0a5c, .info-card .card-content.data-v-958e0a5c {
  padding: 0 30rpx 30rpx;
}
.category-grid.data-v-958e0a5c {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.category-grid .category-btn.data-v-958e0a5c {
  padding: 20rpx 32rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 25rpx;
  background: white;
  transition: all 0.3s ease;
}
.category-grid .category-btn.active.data-v-958e0a5c {
  background: #3b82f6;
  border-color: #3b82f6;
}
.category-grid .category-btn.active .category-text.data-v-958e0a5c {
  color: white;
}
.category-grid .category-btn .category-text.data-v-958e0a5c {
  font-size: 26rpx;
  color: #666;
}
.emoji-grid.data-v-958e0a5c {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
}
.emoji-grid .emoji-item.data-v-958e0a5c {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}
.emoji-grid .emoji-item .emoji-text.data-v-958e0a5c {
  font-size: 36rpx;
  font-family: monospace;
}
.emoji-grid .emoji-item .copy-icon.data-v-958e0a5c {
  font-size: 24rpx;
  color: #9ca3af;
}
.recommend-grid.data-v-958e0a5c {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.recommend-grid .recommend-item.data-v-958e0a5c {
  padding: 24rpx;
  background: #fef3c7;
  border: 2rpx solid #fde68a;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.recommend-grid .recommend-item .recommend-text.data-v-958e0a5c {
  font-size: 32rpx;
  font-family: monospace;
}
.info-list .info-item.data-v-958e0a5c {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16rpx;
}