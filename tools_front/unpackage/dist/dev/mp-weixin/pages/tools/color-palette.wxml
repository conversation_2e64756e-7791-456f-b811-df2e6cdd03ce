<view class="container data-v-b143980c"><view class="color-generator data-v-b143980c"><view class="color-picker-card data-v-b143980c"><view class="card-header data-v-b143980c"><text class="card-title data-v-b143980c">🎨 选择基础颜色</text></view><view class="color-picker-content data-v-b143980c"><view class="color-preview data-v-b143980c" style="{{'background-color:' + a}}" bindtap="{{b}}"/><view class="color-info data-v-b143980c"><text class="color-value data-v-b143980c">{{c}}</text><text class="color-hint data-v-b143980c">点击选择颜色</text></view></view><view wx:if="{{d}}" class="color-selection-panel data-v-b143980c"><view class="preset-colors-section data-v-b143980c"><text class="section-title data-v-b143980c">预设颜色</text><view class="preset-colors-grid data-v-b143980c"><view wx:for="{{e}}" wx:for-item="color" wx:key="a" class="{{['preset-color-item', 'data-v-b143980c', color.b && 'active']}}" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"/></view></view><view class="color-input-section data-v-b143980c"><text class="section-title data-v-b143980c">自定义颜色</text><view class="color-input-group data-v-b143980c"><input type="text" class="color-input data-v-b143980c" placeholder="输入颜色代码，如：#FF6B6B" bindinput="{{f}}" value="{{g}}"/><view class="color-input-preview data-v-b143980c" style="{{'background-color:' + h}}"/></view></view><view class="panel-actions data-v-b143980c"><view class="action-btn secondary data-v-b143980c" bindtap="{{i}}"><text class="data-v-b143980c">取消</text></view><view wx:if="{{j}}" class="action-btn primary data-v-b143980c" bindtap="{{k}}"><text class="data-v-b143980c">使用自定义颜色</text></view></view></view></view><view class="parameters-card data-v-b143980c"><view class="card-header data-v-b143980c"><text class="card-title data-v-b143980c">🔧 调色参数</text></view><view class="parameters-content data-v-b143980c"><view class="parameter-item data-v-b143980c"><view class="parameter-header data-v-b143980c"><text class="parameter-label data-v-b143980c">色彩数量</text><text class="parameter-value data-v-b143980c">{{l}}个</text></view><view class="slider-container data-v-b143980c"><slider class="data-v-b143980c" value="{{m}}" min="{{3}}" max="{{12}}" step="{{1}}" activeColor="#8B5CF6" backgroundColor="#e5e7eb" block-size="24" bindchange="{{n}}"/></view></view><view class="parameter-item data-v-b143980c"><view class="parameter-header data-v-b143980c"><text class="parameter-label data-v-b143980c">饱和度</text><text class="parameter-value data-v-b143980c">{{o}}%</text></view><view class="slider-container data-v-b143980c"><slider class="data-v-b143980c" value="{{p}}" min="{{20}}" max="{{100}}" step="{{5}}" activeColor="#8B5CF6" backgroundColor="#e5e7eb" block-size="24" bindchange="{{q}}"/></view></view><view class="parameter-item data-v-b143980c"><view class="parameter-header data-v-b143980c"><text class="parameter-label data-v-b143980c">亮度</text><text class="parameter-value data-v-b143980c">{{r}}%</text></view><view class="slider-container data-v-b143980c"><slider class="data-v-b143980c" value="{{s}}" min="{{30}}" max="{{90}}" step="{{5}}" activeColor="#8B5CF6" backgroundColor="#e5e7eb" block-size="24" bindchange="{{t}}"/></view></view></view></view><view class="scheme-card data-v-b143980c"><view class="card-header data-v-b143980c"><text class="card-title data-v-b143980c">配色方案</text></view><view class="scheme-grid data-v-b143980c"><view wx:for="{{v}}" wx:for-item="scheme" wx:key="c" class="{{['scheme-item', 'data-v-b143980c', scheme.d && 'active']}}" bindtap="{{scheme.e}}"><text class="scheme-name data-v-b143980c">{{scheme.a}}</text><text class="scheme-desc data-v-b143980c">{{scheme.b}}</text></view></view></view><view class="generate-btn data-v-b143980c" bindtap="{{w}}"><text class="generate-icon data-v-b143980c">🔄</text><text class="generate-text data-v-b143980c">生成配色方案</text></view><view class="palette-card data-v-b143980c"><view class="palette-header data-v-b143980c"><text class="card-title data-v-b143980c">调色板</text><view class="palette-actions data-v-b143980c"><view class="action-btn data-v-b143980c" bindtap="{{x}}"><text class="action-icon data-v-b143980c">❤️</text></view><view class="action-btn data-v-b143980c" bindtap="{{y}}"><text class="action-icon data-v-b143980c">📋</text></view></view></view><view class="palette-colors data-v-b143980c"><view wx:for="{{z}}" wx:for-item="color" wx:key="c" class="color-item data-v-b143980c" bindtap="{{color.d}}"><view class="color-block data-v-b143980c" style="{{'background-color:' + color.a}}"/><text class="color-code data-v-b143980c">{{color.b}}</text></view></view><view class="preview-section data-v-b143980c"><text class="preview-title data-v-b143980c">👁️ 预览效果</text><view class="preview-grid data-v-b143980c"><view class="preview-item data-v-b143980c" style="{{'background:' + A}}"><text class="preview-text data-v-b143980c">渐变背景</text></view><view class="preview-item data-v-b143980c" style="{{'background:' + B}}"><text class="preview-text data-v-b143980c">UI界面</text></view></view></view></view><view wx:if="{{C}}" class="liked-card data-v-b143980c"><view class="card-header data-v-b143980c"><text class="card-title data-v-b143980c">收藏的配色</text></view><view class="liked-list data-v-b143980c"><view wx:for="{{D}}" wx:for-item="palette" wx:key="c" class="liked-item data-v-b143980c"><view class="liked-colors data-v-b143980c"><view wx:for="{{palette.a}}" wx:for-item="color" wx:key="a" class="liked-color data-v-b143980c" style="{{'background-color:' + color.b}}" bindtap="{{color.c}}"/></view><view class="liked-copy data-v-b143980c" bindtap="{{palette.b}}"><text class="copy-icon data-v-b143980c">📋</text></view></view></view></view></view><view wx:if="{{E}}" class="color-picker-modal data-v-b143980c" bindtap="{{X}}"><view class="color-picker-container data-v-b143980c" catchtap="{{W}}"><view class="picker-header data-v-b143980c"><text class="picker-title data-v-b143980c">选择颜色</text><view class="picker-close data-v-b143980c" bindtap="{{F}}"><text class="close-icon data-v-b143980c">×</text></view></view><view class="color-canvas-container data-v-b143980c"><block wx:if="{{r0}}"><canvas class="color-canvas data-v-b143980c" canvas-id="colorCanvas" bindtouchstart="{{G}}" bindtouchmove="{{H}}"></canvas></block><view class="canvas-cursor data-v-b143980c" style="{{I}}"></view></view><view class="hue-slider-container data-v-b143980c"><block wx:if="{{r0}}"><canvas class="hue-slider data-v-b143980c" canvas-id="hueCanvas" bindtouchstart="{{J}}" bindtouchmove="{{K}}"></canvas></block><view class="hue-cursor data-v-b143980c" style="{{L}}"></view></view><view class="rgb-inputs data-v-b143980c"><view class="rgb-group data-v-b143980c"><text class="rgb-label data-v-b143980c">R</text><input class="rgb-input data-v-b143980c" type="number" bindinput="{{M}}" min="0" max="255" value="{{N}}"/></view><view class="rgb-group data-v-b143980c"><text class="rgb-label data-v-b143980c">G</text><input class="rgb-input data-v-b143980c" type="number" bindinput="{{O}}" min="0" max="255" value="{{P}}"/></view><view class="rgb-group data-v-b143980c"><text class="rgb-label data-v-b143980c">B</text><input class="rgb-input data-v-b143980c" type="number" bindinput="{{Q}}" min="0" max="255" value="{{R}}"/></view></view><view class="picker-preview data-v-b143980c"><view class="preview-color data-v-b143980c" style="{{'background-color:' + S}}"></view><text class="hex-value data-v-b143980c">{{T}}</text></view><view class="picker-actions data-v-b143980c"><view class="picker-btn cancel data-v-b143980c" bindtap="{{U}}">取消</view><view class="picker-btn confirm data-v-b143980c" bindtap="{{V}}">确认</view></view></view></view></view>