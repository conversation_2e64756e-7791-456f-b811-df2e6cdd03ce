"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "HueGenerator",
  data() {
    return {
      baseColor: "#3b82f6",
      hueCount: 10,
      saturation: 70,
      lightness: 50,
      hueVariations: [],
      showColorPicker: false,
      selectedColor: "#3b82f6",
      currentHue: "#FF0000",
      canvasPosition: { x: 150, y: 100 },
      huePosition: 0,
      rgbValues: { r: 59, g: 130, b: 246 },
      canvasSize: { width: 300, height: 200 }
    };
  },
  computed: {
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "px",
        top: this.canvasPosition.y + "px"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "px"
      };
    }
  },
  watch: {
    baseColor() {
      this.generateHueVariations();
    },
    hueCount() {
      this.generateHueVariations();
    },
    saturation() {
      this.generateHueVariations();
    },
    lightness() {
      this.generateHueVariations();
    }
  },
  mounted() {
    this.generateHueVariations();
  },
  methods: {
    hexToHsl(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      if (!result)
        return null;
      let r = parseInt(result[1], 16) / 255;
      let g = parseInt(result[2], 16) / 255;
      let b = parseInt(result[3], 16) / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h = 0, s = 0, l = (max + min) / 2;
      if (max !== min) {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r:
            h = (g - b) / d + (g < b ? 6 : 0);
            break;
          case g:
            h = (b - r) / d + 2;
            break;
          case b:
            h = (r - g) / d + 4;
            break;
        }
        h /= 6;
      }
      return { h: h * 360, s: s * 100, l: l * 100 };
    },
    hslToHex(h, s, l) {
      h = h % 360;
      s = s / 100;
      l = l / 100;
      const c = (1 - Math.abs(2 * l - 1)) * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = l - c / 2;
      let r = 0, g = 0, b = 0;
      if (0 <= h && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (60 <= h && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (120 <= h && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (180 <= h && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (240 <= h && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else if (300 <= h && h < 360) {
        r = c;
        g = 0;
        b = x;
      }
      r = Math.round((r + m) * 255);
      g = Math.round((g + m) * 255);
      b = Math.round((b + m) * 255);
      return "#" + [r, g, b].map((x2) => x2.toString(16).padStart(2, "0")).join("");
    },
    generateHueVariations() {
      const baseHsl = this.hexToHsl(this.baseColor);
      if (!baseHsl)
        return;
      const variations = [];
      const hueStep = 360 / this.hueCount;
      for (let i = 0; i < this.hueCount; i++) {
        const newHue = (baseHsl.h + i * hueStep) % 360;
        variations.push(this.hslToHex(newHue, this.saturation, this.lightness));
      }
      this.hueVariations = variations;
    },
    copyColor(color) {
      common_vendor.index.setClipboardData({
        data: color,
        success: () => {
          common_vendor.index.showToast({
            title: "颜色已复制",
            icon: "success"
          });
        }
      });
    },
    copyAllColors() {
      const colorsText = this.hueVariations.join(", ");
      common_vendor.index.setClipboardData({
        data: colorsText,
        success: () => {
          common_vendor.index.showToast({
            title: "全部颜色已复制",
            icon: "success"
          });
        }
      });
    },
    openColorPicker() {
      this.selectedColor = this.baseColor;
      this.updateRgbFromHex(this.baseColor);
      this.showColorPicker = true;
      this.$nextTick(() => {
        this.initColorCanvas();
        this.initHueSlider();
      });
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      this.baseColor = this.selectedColor;
      this.generateHueVariations();
      this.closeColorPicker();
    },
    initColorCanvas() {
      const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
      const { width, height } = this.canvasSize;
      const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
      gradient1.addColorStop(0, "#FFFFFF");
      gradient1.addColorStop(1, this.currentHue);
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, width, height);
      const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
      gradient2.addColorStop(0, "rgba(0,0,0,0)");
      gradient2.addColorStop(1, "#000000");
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    initHueSlider() {
      const ctx = common_vendor.index.createCanvasContext("hueCanvas", this);
      const width = 300;
      const height = 30;
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17, "#FFFF00");
      gradient.addColorStop(0.33, "#00FF00");
      gradient.addColorStop(0.5, "#00FFFF");
      gradient.addColorStop(0.67, "#0000FF");
      gradient.addColorStop(0.83, "#FF00FF");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    onCanvasTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;
          this.canvasPosition.x = Math.max(0, Math.min(rect.width, x));
          this.canvasPosition.y = Math.max(0, Math.min(rect.height, y));
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    onHueTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          this.huePosition = Math.max(0, Math.min(rect.width, x));
          this.updateHueFromSlider();
        }
      }).exec();
    },
    updateColorFromCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = this.canvasPosition.x / rect.width;
          const y = this.canvasPosition.y / rect.height;
          const hue = this.huePosition / rect.width * 360;
          const saturation = x;
          const value = 1 - y;
          const rgb = this.hsvToRgb(hue, saturation, value);
          this.rgbValues = rgb;
          this.selectedColor = this.rgbToHex(rgb);
        }
      }).exec();
    },
    updateHueFromSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const hue = this.huePosition / rect.width * 360;
          this.currentHue = this.hsvToRgb(hue, 1, 1);
          this.currentHue = this.rgbToHex(this.currentHue);
          this.initColorCanvas();
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    updateRgbFromHex(hex) {
      const rgb = this.hexToRgb(hex);
      if (rgb) {
        this.rgbValues = rgb;
        const hsv = this.rgbToHsv(rgb);
        this.huePosition = hsv.h / 360 * 300;
        this.canvasPosition.x = hsv.s * this.canvasSize.width;
        this.canvasPosition.y = (1 - hsv.v) * this.canvasSize.height;
        this.currentHue = this.rgbToHex(this.hsvToRgb(hsv.h, 1, 1));
      }
    },
    onRgbChange() {
      this.selectedColor = this.rgbToHex(this.rgbValues);
      this.updateRgbFromHex(this.selectedColor);
    },
    hsvToRgb(h, s, v) {
      const c = v * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = v - c;
      let r, g, b;
      if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255)
      };
    },
    rgbToHsv(rgb) {
      const r = rgb.r / 255;
      const g = rgb.g / 255;
      const b = rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;
      let h = 0;
      if (diff !== 0) {
        if (max === r) {
          h = (g - b) / diff % 6;
        } else if (max === g) {
          h = (b - r) / diff + 2;
        } else {
          h = (r - g) / diff + 4;
        }
      }
      h = h * 60;
      if (h < 0)
        h += 360;
      const s = max === 0 ? 0 : diff / max;
      const v = max;
      return { h, s, v };
    },
    rgbToHex(rgb) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    generateHueColors() {
      const colors = [];
      const hueStep = 360 / this.hueCount;
      const baseHsl = this.hexToHsl(this.baseColor);
      for (let i = 0; i < this.hueCount; i++) {
        const hue = (baseHsl.h + i * hueStep) % 360;
        const color = this.hslToHex(hue, this.saturation, this.lightness);
        colors.push({
          color,
          hue: Math.round(hue),
          name: this.getColorName(hue)
        });
      }
      this.hueVariations = colors.map((c) => c.color);
    },
    updateHueCount(e) {
      this.hueCount = parseInt(e.detail.value);
      this.generateHueColors();
    },
    updateSaturation(e) {
      this.saturation = parseInt(e.detail.value);
      this.generateHueColors();
    },
    updateLightness(e) {
      this.lightness = parseInt(e.detail.value);
      this.generateHueColors();
    },
    getColorName(hue) {
      const colorNames = [
        { range: [0, 15], name: "红色" },
        { range: [15, 45], name: "橙色" },
        { range: [45, 75], name: "黄色" },
        { range: [75, 105], name: "黄绿色" },
        { range: [105, 135], name: "绿色" },
        { range: [135, 165], name: "青绿色" },
        { range: [165, 195], name: "青色" },
        { range: [195, 225], name: "蓝色" },
        { range: [225, 255], name: "蓝紫色" },
        { range: [255, 285], name: "紫色" },
        { range: [285, 315], name: "红紫色" },
        { range: [315, 345], name: "粉红色" },
        { range: [345, 360], name: "红色" }
      ];
      for (let colorName of colorNames) {
        if (hue >= colorName.range[0] && hue < colorName.range[1]) {
          return colorName.name;
        }
      }
      return "红色";
    },
    saveColors() {
      const savedColors = common_vendor.index.getStorageSync("savedHueColors") || [];
      const newPalette = {
        id: Date.now(),
        baseColor: this.baseColor,
        colors: this.hueVariations,
        timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
      };
      savedColors.unshift(newPalette);
      if (savedColors.length > 10) {
        savedColors.pop();
      }
      common_vendor.index.setStorageSync("savedHueColors", savedColors);
      common_vendor.index.showToast({
        title: "色相组合已保存",
        icon: "success"
      });
    },
    resetSettings() {
      this.baseColor = "#3b82f6";
      this.hueCount = 10;
      this.saturation = 70;
      this.lightness = 50;
      this.generateHueColors();
    },
    onColorInput() {
      if (/^#[0-9A-F]{6}$/i.test(this.baseColor)) {
        this.generateHueVariations();
      }
    },
    initCanvas() {
      this.updateCursorPosition();
    },
    initHueSlider() {
      this.updateHueCursorPosition();
    },
    clickColor(color) {
      this.baseColor = color;
      this.generateHueVariations();
    },
    updateCursorPosition() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          this.canvasPosition.x = Math.min(rect.width - 2, this.canvasPosition.x);
          this.canvasPosition.y = Math.min(rect.height - 2, this.canvasPosition.y);
        }
      }).exec();
    },
    updateHueCursorPosition() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          this.huePosition = Math.min(rect.width - 2, this.huePosition);
        }
      }).exec();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.baseColor,
    b: common_vendor.o((...args) => $options.openColorPicker && $options.openColorPicker(...args)),
    c: common_vendor.o([($event) => $data.baseColor = $event.detail.value, (...args) => $options.onColorInput && $options.onColorInput(...args)]),
    d: $data.baseColor,
    e: common_vendor.t($data.hueCount),
    f: $data.hueCount,
    g: common_vendor.o((...args) => $options.updateHueCount && $options.updateHueCount(...args)),
    h: common_vendor.t($data.saturation),
    i: $data.saturation,
    j: common_vendor.o((...args) => $options.updateSaturation && $options.updateSaturation(...args)),
    k: common_vendor.t($data.lightness),
    l: $data.lightness,
    m: common_vendor.o((...args) => $options.updateLightness && $options.updateLightness(...args)),
    n: common_vendor.o((...args) => $options.copyAllColors && $options.copyAllColors(...args)),
    o: common_vendor.t($data.hueVariations.length),
    p: common_vendor.f($data.hueVariations, (color, index, i0) => {
      return {
        a: color,
        b: common_vendor.t(color),
        c: index,
        d: common_vendor.o(($event) => $options.copyColor(color), index)
      };
    }),
    q: $data.showColorPicker
  }, $data.showColorPicker ? {
    r: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    s: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    t: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    v: common_vendor.s($options.canvasCursorStyle),
    w: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    x: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    y: common_vendor.s($options.hueCursorStyle),
    z: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    A: $data.rgbValues.r,
    B: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    C: $data.rgbValues.g,
    D: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    E: $data.rgbValues.b,
    F: $data.selectedColor,
    G: common_vendor.t($data.selectedColor),
    H: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    I: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    J: common_vendor.o(() => {
    }),
    K: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1bb15d32"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/hue-generator.js.map
