/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c0a3279e {
  display: flex;
}
.flex-1.data-v-c0a3279e {
  flex: 1;
}
.items-center.data-v-c0a3279e {
  align-items: center;
}
.justify-center.data-v-c0a3279e {
  justify-content: center;
}
.justify-between.data-v-c0a3279e {
  justify-content: space-between;
}
.text-center.data-v-c0a3279e {
  text-align: center;
}
.rounded.data-v-c0a3279e {
  border-radius: 3px;
}
.rounded-lg.data-v-c0a3279e {
  border-radius: 6px;
}
.shadow.data-v-c0a3279e {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c0a3279e {
  padding: 16rpx;
}
.m-4.data-v-c0a3279e {
  margin: 16rpx;
}
.mb-4.data-v-c0a3279e {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c0a3279e {
  margin-top: 16rpx;
}
.snapshot-container.data-v-c0a3279e {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-c0a3279e, .input-card.data-v-c0a3279e, .device-card.data-v-c0a3279e, .options-card.data-v-c0a3279e, .generate-card.data-v-c0a3279e, .preview-card.data-v-c0a3279e, .tips-card.data-v-c0a3279e {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-c0a3279e {
  display: flex;
  align-items: center;
}
.header-icon.data-v-c0a3279e {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-title.data-v-c0a3279e {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-c0a3279e {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-c0a3279e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-c0a3279e {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.action-btn.data-v-c0a3279e {
  height: 60rpx;
  padding: 0 28rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #007AFF;
  color: #fff;
  border: none;
  transition: background 0.2s, box-shadow 0.2s;
}
.action-btn.data-v-c0a3279e:active {
  background: #2563eb;
}
.action-btn.data-v-c0a3279e:disabled {
  background: #e5e7eb;
  color: #bdbdbd;
  cursor: not-allowed;
}
.input-content.data-v-c0a3279e {
  display: flex;
  align-items: center;
  gap: 8rpx;
}
.url-input.data-v-c0a3279e {
  flex: 1;
  min-width: 0;
  height: 60rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  padding: 0 24rpx;
  background: #f8f9fa;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}
.url-input.data-v-c0a3279e:focus {
  border-color: #007AFF;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.08);
  outline: none;
}
.url-input.data-v-c0a3279e::-webkit-input-placeholder {
  color: #bdbdbd;
}
.url-input.data-v-c0a3279e::placeholder {
  color: #bdbdbd;
}
.url-actions.data-v-c0a3279e {
  display: flex;
  flex-direction: row;
  gap: 8rpx;
}
.device-grid.data-v-c0a3279e {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.device-item.data-v-c0a3279e {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: white;
  transition: all 0.3s ease;
}
.device-item.active.data-v-c0a3279e {
  border-color: #007AFF;
  background: #f0f8ff;
}
.device-item.data-v-c0a3279e:active {
  transform: scale(0.98);
}
.device-icon.data-v-c0a3279e {
  font-size: 36rpx;
  margin-right: 24rpx;
}
.device-info.data-v-c0a3279e {
  flex: 1;
}
.device-name.data-v-c0a3279e {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.device-size.data-v-c0a3279e {
  font-size: 24rpx;
  color: #666;
}
.options-content.data-v-c0a3279e {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.option-group.data-v-c0a3279e {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.option-label.data-v-c0a3279e {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.format-buttons.data-v-c0a3279e {
  display: flex;
  gap: 12rpx;
}
.format-btn.data-v-c0a3279e {
  padding: 12rpx 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background: white;
  font-size: 22rpx;
  color: #666;
  transition: all 0.3s ease;
}
.format-btn.active.data-v-c0a3279e {
  border-color: #007AFF;
  background: #007AFF;
  color: white;
}
.option-switches.data-v-c0a3279e {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.switch-item.data-v-c0a3279e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-label.data-v-c0a3279e {
  font-size: 26rpx;
  color: #333;
}
.generate-btn.data-v-c0a3279e {
  width: 100%;
  padding: 24rpx;
  border-radius: 12rpx;
  background: #007AFF;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}
.generate-btn.generating.data-v-c0a3279e {
  background: #4CAF50;
}
.generate-btn.disabled.data-v-c0a3279e {
  background: #ccc;
  color: #999;
}
.generate-btn.data-v-c0a3279e:active:not(.disabled) {
  transform: scale(0.98);
}
.btn-content.data-v-c0a3279e {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.btn-progress.data-v-c0a3279e {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease;
}
.preview-content.data-v-c0a3279e {
  min-height: 400rpx;
}
.preview-container.data-v-c0a3279e {
  height: 400rpx;
  border: 2rpx dashed #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}
.preview-placeholder.data-v-c0a3279e {
  text-align: center;
  color: #999;
}
.placeholder-icon.data-v-c0a3279e {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}
.placeholder-text.data-v-c0a3279e {
  font-size: 28rpx;
  display: block;
  margin-bottom: 8rpx;
}
.placeholder-desc.data-v-c0a3279e {
  font-size: 24rpx;
  color: #ccc;
}
.preview-image.data-v-c0a3279e {
  position: relative;
}
.snapshot-image.data-v-c0a3279e {
  width: 100%;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.image-info.data-v-c0a3279e {
  margin-top: 16rpx;
  text-align: center;
}
.info-text.data-v-c0a3279e {
  font-size: 24rpx;
  color: #666;
}
.preview-actions.data-v-c0a3279e {
  display: flex;
  gap: 8rpx;
}
.tips-content.data-v-c0a3279e {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-c0a3279e {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-c0a3279e {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-c0a3279e {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}