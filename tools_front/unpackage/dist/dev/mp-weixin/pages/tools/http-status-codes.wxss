
.container.data-v-eeca929f {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.search-card.data-v-eeca929f,
.info-card.data-v-eeca929f {
  background-color: #fff;
  border-radius: 25rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
}
.card-header.data-v-eeca929f {
  display: flex;
  align-items: center;
  gap: 15rpx;
  padding: 30rpx 30rpx 20rpx;
}
.search-icon.data-v-eeca929f,
.info-icon.data-v-eeca929f {
  font-size: 32rpx;
}
.card-title.data-v-eeca929f {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.search-section.data-v-eeca929f {
  padding: 0 30rpx 20rpx;
}
.search-box.data-v-eeca929f {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  padding: 20rpx 25rpx;
}
.search-icon-small.data-v-eeca929f {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #666;
}
.search-input.data-v-eeca929f {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}
.filter-section.data-v-eeca929f {
  padding: 0 30rpx 30rpx;
}
.category-scroll.data-v-eeca929f {
  width: 100%;
}
.category-list.data-v-eeca929f {
  display: flex;
  gap: 15rpx;
  white-space: nowrap;
  padding-bottom: 8rpx;
}
.category-item.data-v-eeca929f {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}
.category-item.active.data-v-eeca929f {
  background: linear-gradient(135deg, #007AFF 0%, #0056D6 100%);
  border-color: transparent;
  box-shadow: 0 2rpx 8rpx rgba(0,122,255,0.2);
}
.category-item.active .category-name.data-v-eeca929f {
  color: #fff;
}
.category-name.data-v-eeca929f {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}
.filter-section.data-v-eeca929f::after,
.filter-section.data-v-eeca929f::before {
  content: none;
}
.status-list.data-v-eeca929f {
  margin-bottom: 20rpx;
}
.status-card.data-v-eeca929f {
  background-color: #fff;
  border-radius: 20rpx;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.06);
  overflow: hidden;
}
.status-info.data-v-eeca929f {
  padding: 30rpx;
}
.status-header.data-v-eeca929f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.status-main.data-v-eeca929f {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}
.status-code.data-v-eeca929f {
  font-size: 48rpx;
  font-weight: 700;
  color: #007AFF;
}
.status-name.data-v-eeca929f {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.category-badge.data-v-eeca929f {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  flex-shrink: 0;
}
.category-info.data-v-eeca929f {
  background-color: #e3f2fd;
  color: #1976d2;
}
.category-success.data-v-eeca929f {
  background-color: #e8f5e8;
  color: #388e3c;
}
.category-redirect.data-v-eeca929f {
  background-color: #fff3e0;
  color: #f57c00;
}
.category-client-error.data-v-eeca929f {
  background-color: #ffebee;
  color: #d32f2f;
}
.category-server-error.data-v-eeca929f {
  background-color: #f3e5f5;
  color: #7b1fa2;
}
.category-text.data-v-eeca929f {
  font-size: 20rpx;
  font-weight: 500;
}
.status-description.data-v-eeca929f {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: block;
}
.status-extra.data-v-eeca929f {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 15rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
}
.info-icon.data-v-eeca929f {
  font-size: 24rpx;
}
.extra-info.data-v-eeca929f {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}
.empty-state.data-v-eeca929f {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 80rpx 30rpx;
  text-align: center;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0,0,0,0.06);
}
.empty-icon.data-v-eeca929f {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
  color: #ccc;
}
.empty-text.data-v-eeca929f {
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}
.empty-hint.data-v-eeca929f {
  font-size: 26rpx;
  color: #999;
}
.info-content.data-v-eeca929f {
  padding: 0 30rpx 30rpx;
}
.info-item.data-v-eeca929f {
  font-size: 26rpx;
  color: #666;
  line-height: 1.8;
  display: block;
  margin-bottom: 15rpx;
}
.info-bold.data-v-eeca929f {
  font-weight: 600;
  color: #333;
}
