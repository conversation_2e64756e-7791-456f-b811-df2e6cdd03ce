"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedQuote: "",
      crazyQuotes: [
        "我已经开始发疯了，快来阻止我！",
        "今天的我，疯狂得很有规律。",
        "发疯不需要理由，清醒才需要借口。",
        "我不是疯了，我只是换了个角度看世界。",
        "疯狂是天才和蠢货的分界线。",
        "今天适合发疯，明天适合装疯。",
        "我的疯狂是有底线的，但底线在哪我也不知道。",
        "发疯是一种生活态度，不疯不成魔。",
        "别拦着我，让我疯一会儿。",
        "疯狂的人不是失去理智，而是失去了一切除了理智以外的东西。",
        "我疯故我在。",
        "发疯是为了保持清醒。",
        "今日份的疯癫已上线。",
        "疯狂模式：已激活。",
        "警告：前方有疯子出没。",
        "我不是在发疯，我是在释放内心的野兽。",
        "疯狂让我快乐，快乐让我疯狂。",
        "今天的疯狂指数：爆表。",
        "发疯不是病，不疯要人命。",
        "我的疯狂是独一无二的艺术品。"
      ],
      emojiQuotes: [
        "🤪 今天适合发疯！",
        "😵‍💫 脑子短路中...",
        "🤯 理智已下线！",
        "😵 疯狂模式启动！",
        "🥴 今日份的混乱",
        "😤 愤怒的小疯子",
        "🤬 疯狂吐槽时间",
        "😈 邪恶的疯狂",
        "👹 疯魔附体",
        "🤡 疯狂小丑上线"
      ],
      actionQuotes: [
        "冲冲冲！！！",
        "啊啊啊啊啊！",
        "我要开始表演了！",
        "疯狂三连击！",
        "让暴风雨来得更猛烈些吧！",
        "今天我就是疯批本批！",
        "谁能阻止我？没人！",
        "疯狂到底！",
        "不疯不成活！",
        "疯子的世界你不懂！"
      ]
    };
  },
  mounted() {
    this.getRandomQuote();
  },
  methods: {
    getRandomQuote() {
      const allQuotes = [...this.crazyQuotes, ...this.emojiQuotes, ...this.actionQuotes];
      const randomQuote = allQuotes[Math.floor(Math.random() * allQuotes.length)];
      this.selectedQuote = randomQuote;
    },
    copyQuote(quote) {
      if (!quote) {
        common_vendor.index.showToast({
          title: "没有语录可复制",
          icon: "none"
        });
        return;
      }
      const text = `${quote} —— 未知作者`;
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          this.selectedQuote = quote;
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    shareQuote() {
      if (!this.selectedQuote) {
        common_vendor.index.showToast({
          title: "没有语录可分享",
          icon: "none"
        });
        return;
      }
      const text = `${this.selectedQuote} —— 未知作者`;
      common_vendor.index.share({
        provider: "weixin",
        type: 0,
        title: "疯狂语录",
        summary: text,
        success: () => {
          common_vendor.index.showToast({
            title: "分享成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "分享失败",
            icon: "error"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedQuote
  }, $data.selectedQuote ? {
    b: common_vendor.o((...args) => $options.getRandomQuote && $options.getRandomQuote(...args)),
    c: common_vendor.t($data.selectedQuote),
    d: common_vendor.o(($event) => $options.copyQuote($data.selectedQuote))
  } : {}, {
    e: common_vendor.f($data.crazyQuotes.slice(0, 10), (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: index,
        c: common_vendor.o(($event) => $options.copyQuote(quote), index)
      };
    }),
    f: common_vendor.f($data.emojiQuotes, (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: index,
        c: common_vendor.o(($event) => $options.copyQuote(quote), index)
      };
    }),
    g: common_vendor.f($data.actionQuotes, (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: index,
        c: common_vendor.o(($event) => $options.copyQuote(quote), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4978cc18"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/crazy-quotes.js.map
