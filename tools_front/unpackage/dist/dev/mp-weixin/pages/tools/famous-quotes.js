"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "famous-quotes",
  setup(__props) {
    const currentQuote = common_vendor.ref({ text: "", author: "" });
    const selectedCategory = common_vendor.ref("励志");
    const categories = ["励志", "人生", "爱情", "智慧", "成功", "友情"];
    const quotes = {
      励志: [
        { text: "成功不是将来才有的，而是从决定去做的那一刻起，持续累积而成。", author: "俞敏洪" },
        { text: "不要等待机会，而要创造机会。", author: "乔治·赫伯特" },
        { text: "只有不断找寻机会的人才会及时把握机会。", author: "郭台铭" },
        { text: "困难是人的教科书。", author: "朝鲜谚语" },
        { text: "天行健，君子以自强不息。", author: "周易" },
        { text: "路漫漫其修远兮，吾将上下而求索。", author: "屈原" }
      ],
      人生: [
        { text: "人生就像一盒巧克力，你永远不知道下一颗是什么味道。", author: "阿甘正传" },
        { text: "生活不是等待暴风雨过去，而是要学会在雨中跳舞。", author: "维维安·格林" },
        { text: "人生的价值，并不是用时间，而是用深度去衡量的。", author: "列夫·托尔斯泰" },
        { text: "人生如梦，一尊还酹江月。", author: "苏轼" },
        { text: "人生得意须尽欢，莫使金樽空对月。", author: "李白" },
        { text: "山重水复疑无路，柳暗花明又一村。", author: "陆游" }
      ],
      爱情: [
        { text: "爱情不是寻找一个完美的人，而是学会用完美的眼光去看一个不完美的人。", author: "佚名" },
        { text: "真正的爱情是专一的，爱情的领域是非常的狭小。", author: "席勒" },
        { text: "爱情使人忘记时间，时间也使人忘记爱情。", author: "张小娴" },
        { text: "两情若是久长时，又岂在朝朝暮暮。", author: "秦观" },
        { text: "在天愿作比翼鸟，在地愿为连理枝。", author: "白居易" },
        { text: "身无彩凤双飞翼，心有灵犀一点通。", author: "李商隐" }
      ],
      智慧: [
        { text: "知己知彼，百战不殆。", author: "孙武" },
        { text: "学而不思则罔，思而不学则殆。", author: "孔子" },
        { text: "智者千虑，必有一失；愚者千虑，必有一得。", author: "史记" },
        { text: "博学之，审问之，慎思之，明辨之，笃行之。", author: "礼记" },
        { text: "温故而知新，可以为师矣。", author: "孔子" },
        { text: "三人行，必有我师焉。", author: "孔子" }
      ],
      成功: [
        { text: "成功就是把复杂的问题简单化，然后狠狠去做。", author: "约翰·麦克斯韦尔" },
        { text: "成功的秘诀在于坚持自己的目标和信念。", author: "迪斯雷利" },
        { text: "成功不是偶然，而是必然。", author: "陈安之" },
        { text: "宝剑锋从磨砺出，梅花香自苦寒来。", author: "古诗" },
        { text: "千磨万击还坚劲，任尔东西南北风。", author: "郑燮" },
        { text: "不经一番寒彻骨，怎得梅花扑鼻香。", author: "黄蘖禅师" }
      ],
      友情: [
        { text: "友谊是一棵可以庇荫的树。", author: "柯尔律治" },
        { text: "海内存知己，天涯若比邻。", author: "王勃" },
        { text: "友情在我过去的生活里就像一盏明灯，照彻了我的灵魂。", author: "巴金" },
        { text: "真正的友谊是一种缓慢生长的植物。", author: "华盛顿" },
        { text: "桃花潭水深千尺，不及汪伦送我情。", author: "李白" },
        { text: "莫愁前路无知己，天下谁人不识君。", author: "高适" }
      ]
    };
    const recommendedQuotes = common_vendor.computed(() => quotes[selectedCategory.value].slice(0, 3));
    common_vendor.onMounted(() => {
      getRandomQuote();
    });
    const selectCategory = (category) => {
      selectedCategory.value = category;
      getRandomQuote();
    };
    const getRandomQuote = () => {
      const categoryQuotes = quotes[selectedCategory.value];
      const randomQuote = categoryQuotes[Math.floor(Math.random() * categoryQuotes.length)];
      currentQuote.value = randomQuote;
    };
    const selectQuote = (quote) => {
      currentQuote.value = quote;
    };
    const copyQuote = async () => {
      if (!currentQuote.value.text)
        return;
      const fullQuote = `${currentQuote.value.text} —— ${currentQuote.value.author}`;
      try {
        await common_vendor.index.setClipboardData({
          data: fullQuote
        });
        common_vendor.index.showToast({
          title: "复制成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "error"
        });
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(categories, (category, k0, i0) => {
          return {
            a: common_vendor.t(category),
            b: category,
            c: selectedCategory.value === category ? 1 : "",
            d: common_vendor.o(($event) => selectCategory(category), category)
          };
        }),
        b: common_vendor.t(currentQuote.value.text),
        c: common_vendor.t(currentQuote.value.author),
        d: common_vendor.o(getRandomQuote),
        e: common_vendor.o(copyQuote),
        f: common_vendor.t(selectedCategory.value),
        g: common_vendor.f(recommendedQuotes.value, (quote, index, i0) => {
          return {
            a: common_vendor.t(quote.text),
            b: common_vendor.t(quote.author),
            c: index,
            d: common_vendor.o(($event) => selectQuote(quote), index)
          };
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-abd25efd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/famous-quotes.js.map
