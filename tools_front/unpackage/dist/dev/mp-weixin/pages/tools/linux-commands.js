"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "linux-commands",
  props: {
    // Add any necessary props here
  },
  setup(__props) {
    const searchQuery = common_vendor.ref("");
    const selectedCategory = common_vendor.ref("全部");
    const commands = [
      // 文件操作
      { name: "ls", category: "文件操作", description: "列出目录内容", usage: "ls [选项] [目录]", example: "ls -la /home" },
      { name: "cd", category: "文件操作", description: "切换目录", usage: "cd [目录]", example: "cd /home/<USER>" },
      { name: "pwd", category: "文件操作", description: "显示当前目录", usage: "pwd", example: "pwd" },
      { name: "mkdir", category: "文件操作", description: "创建目录", usage: "mkdir [选项] 目录名", example: "mkdir -p /path/to/dir" },
      { name: "rmdir", category: "文件操作", description: "删除空目录", usage: "rmdir 目录名", example: "rmdir empty_dir" },
      { name: "rm", category: "文件操作", description: "删除文件或目录", usage: "rm [选项] 文件名", example: "rm -rf directory" },
      { name: "cp", category: "文件操作", description: "复制文件或目录", usage: "cp [选项] 源文件 目标文件", example: "cp -r source dest" },
      { name: "mv", category: "文件操作", description: "移动或重命名文件", usage: "mv 源文件 目标文件", example: "mv old.txt new.txt" },
      { name: "touch", category: "文件操作", description: "创建空文件或修改文件时间戳", usage: "touch 文件名", example: "touch newfile.txt" },
      { name: "find", category: "文件操作", description: "查找文件", usage: "find 路径 [条件]", example: 'find /etc -name "*.conf"' },
      { name: "ln", category: "文件操作", description: "创建链接文件", usage: "ln [选项] 源文件 目标链接", example: "ln -s /path/file linkfile" },
      { name: "chmod", category: "文件操作", description: "更改文件权限", usage: "chmod [选项] 模式 文件", example: "chmod 755 script.sh" },
      { name: "chown", category: "文件操作", description: "更改文件所有者", usage: "chown 用户:组 文件", example: "chown root:root /etc/passwd" },
      { name: "stat", category: "文件操作", description: "显示文件详细信息", usage: "stat 文件", example: "stat /etc/passwd" },
      // 文本处理
      { name: "cat", category: "文本处理", description: "显示文件内容", usage: "cat 文件名", example: "cat file.txt" },
      { name: "less", category: "文本处理", description: "分页显示文件内容", usage: "less 文件名", example: "less large_file.txt" },
      { name: "head", category: "文本处理", description: "显示文件头部", usage: "head [选项] 文件名", example: "head -n 10 file.txt" },
      { name: "tail", category: "文本处理", description: "显示文件尾部", usage: "tail [选项] 文件名", example: "tail -f log.txt" },
      { name: "grep", category: "文本处理", description: "搜索文本", usage: "grep [选项] 模式 文件名", example: 'grep -r "pattern" .' },
      { name: "sed", category: "文本处理", description: "流编辑器", usage: "sed [选项] 脚本 文件名", example: "sed s/old/new/g file.txt" },
      { name: "awk", category: "文本处理", description: "文本处理工具", usage: "awk 程序 文件名", example: "awk {print $1} file.txt" },
      { name: "sort", category: "文本处理", description: "排序文本行", usage: "sort [选项] 文件", example: "sort data.txt" },
      { name: "uniq", category: "文本处理", description: "去除重复行", usage: "uniq [选项] 文件", example: "uniq sorted.txt" },
      { name: "wc", category: "文本处理", description: "统计字数/行数/字节数", usage: "wc [选项] 文件", example: "wc -l file.txt" },
      { name: "cut", category: "文本处理", description: "按列提取文本", usage: "cut [选项] 文件", example: 'cut -d":" -f1 /etc/passwd' },
      { name: "tr", category: "文本处理", description: "字符替换/删除/压缩", usage: "tr [选项] SET1 [SET2]", example: "tr a-z A-Z < file.txt" },
      { name: "diff", category: "文本处理", description: "比较文件差异", usage: "diff 文件1 文件2", example: "diff old.txt new.txt" },
      { name: "patch", category: "文本处理", description: "打补丁", usage: "patch < patchfile", example: "patch < fix.patch" },
      { name: "split", category: "文本处理", description: "分割文件", usage: "split [选项] 文件", example: "split -l 1000 big.txt small_" },
      { name: "paste", category: "文本处理", description: "合并文件列", usage: "paste 文件1 文件2", example: "paste a.txt b.txt" },
      // 系统信息
      { name: "ps", category: "系统信息", description: "显示进程信息", usage: "ps [选项]", example: "ps aux" },
      { name: "top", category: "系统信息", description: "显示系统进程", usage: "top", example: "top" },
      { name: "htop", category: "系统信息", description: "交互式进程查看器", usage: "htop", example: "htop" },
      { name: "df", category: "系统信息", description: "显示磁盘使用情况", usage: "df [选项]", example: "df -h" },
      { name: "du", category: "系统信息", description: "显示目录大小", usage: "du [选项] 目录", example: "du -sh *" },
      { name: "free", category: "系统信息", description: "显示内存使用情况", usage: "free [选项]", example: "free -h" },
      { name: "uname", category: "系统信息", description: "显示系统信息", usage: "uname [选项]", example: "uname -a" },
      { name: "uptime", category: "系统信息", description: "显示系统运行时间", usage: "uptime", example: "uptime" },
      { name: "who", category: "系统信息", description: "显示已登录用户", usage: "who", example: "who" },
      { name: "date", category: "系统信息", description: "显示/设置系统日期时间", usage: "date", example: "date +%Y-%m-%d" },
      { name: "cal", category: "系统信息", description: "显示日历", usage: "cal [月] [年]", example: "cal 2024" },
      { name: "env", category: "系统信息", description: "显示环境变量", usage: "env", example: "env" },
      { name: "id", category: "系统信息", description: "显示用户和组ID", usage: "id [用户名]", example: "id root" },
      // 网络工具
      { name: "ping", category: "网络工具", description: "测试网络连通性", usage: "ping 主机", example: "ping google.com" },
      { name: "wget", category: "网络工具", description: "下载文件", usage: "wget [选项] URL", example: "wget https://example.com/file.zip" },
      { name: "curl", category: "网络工具", description: "HTTP客户端工具", usage: "curl [选项] URL", example: "curl -X POST https://api.com" },
      { name: "ssh", category: "网络工具", description: "远程登录", usage: "ssh 用户@主机", example: "ssh <EMAIL>" },
      { name: "scp", category: "网络工具", description: "远程复制文件", usage: "scp 源文件 用户@主机:目标路径", example: "scp file.txt user@server:/path/" },
      { name: "ifconfig", category: "网络工具", description: "配置/显示网络接口", usage: "ifconfig [接口]", example: "ifconfig eth0" },
      { name: "ip", category: "网络工具", description: "显示/管理网络配置", usage: "ip [对象] [命令]", example: "ip addr show" },
      { name: "netstat", category: "网络工具", description: "显示网络连接", usage: "netstat [选项]", example: "netstat -tulnp" },
      { name: "ss", category: "网络工具", description: "查看socket状态", usage: "ss [选项]", example: "ss -lntp" },
      { name: "nslookup", category: "网络工具", description: "DNS查询工具", usage: "nslookup 域名", example: "nslookup www.baidu.com" },
      { name: "dig", category: "网络工具", description: "DNS查询工具", usage: "dig 域名", example: "dig google.com" },
      { name: "traceroute", category: "网络工具", description: "路由跟踪", usage: "traceroute 主机", example: "traceroute www.baidu.com" },
      { name: "route", category: "网络工具", description: "显示/设置路由表", usage: "route [选项]", example: "route -n" },
      { name: "ftp", category: "网络工具", description: "FTP客户端", usage: "ftp [主机]", example: "ftp ftp.gnu.org" },
      { name: "telnet", category: "网络工具", description: "远程终端协议", usage: "telnet 主机 端口", example: "telnet localhost 80" },
      { name: "nc", category: "网络工具", description: "网络调试工具", usage: "nc [选项] 主机 端口", example: "nc -zv 127.0.0.1 22" },
      // 权限管理
      { name: "sudo", category: "权限管理", description: "以超级用户权限执行命令", usage: "sudo 命令", example: "sudo apt update" },
      { name: "su", category: "权限管理", description: "切换用户", usage: "su [用户名]", example: "su root" },
      { name: "passwd", category: "权限管理", description: "更改用户密码", usage: "passwd [用户名]", example: "passwd user" },
      { name: "visudo", category: "权限管理", description: "安全编辑sudoers文件", usage: "visudo", example: "visudo" },
      // 进程管理
      { name: "kill", category: "进程管理", description: "终止进程", usage: "kill [选项] 进程号", example: "kill -9 1234" },
      { name: "killall", category: "进程管理", description: "按名称终止进程", usage: "killall 进程名", example: "killall nginx" },
      { name: "pkill", category: "进程管理", description: "按模式终止进程", usage: "pkill 模式", example: "pkill ssh" },
      { name: "bg", category: "进程管理", description: "后台运行作业", usage: "bg [作业号]", example: "bg %1" },
      { name: "fg", category: "进程管理", description: "前台运行作业", usage: "fg [作业号]", example: "fg %1" },
      { name: "jobs", category: "进程管理", description: "显示作业列表", usage: "jobs", example: "jobs" },
      { name: "nice", category: "进程管理", description: "设置进程优先级", usage: "nice [优先级] 命令", example: "nice -n 10 command" },
      { name: "renice", category: "进程管理", description: "重新设置进程优先级", usage: "renice 优先级 -p 进程号", example: "renice 5 -p 1234" },
      // 压缩解压
      { name: "tar", category: "压缩解压", description: "打包/解包文件", usage: "tar [选项] 文件", example: "tar -czvf archive.tar.gz dir/" },
      { name: "gzip", category: "压缩解压", description: "压缩文件", usage: "gzip 文件", example: "gzip file.txt" },
      { name: "gunzip", category: "压缩解压", description: "解压gzip文件", usage: "gunzip 文件.gz", example: "gunzip file.txt.gz" },
      { name: "zip", category: "压缩解压", description: "压缩为zip文件", usage: "zip 压缩包名 文件", example: "zip archive.zip file.txt" },
      { name: "unzip", category: "压缩解压", description: "解压zip文件", usage: "unzip 压缩包名", example: "unzip archive.zip" },
      { name: "bzip2", category: "压缩解压", description: "压缩为bzip2格式", usage: "bzip2 文件", example: "bzip2 file.txt" },
      { name: "xz", category: "压缩解压", description: "压缩为xz格式", usage: "xz 文件", example: "xz file.txt" },
      // 包管理（Debian/Ubuntu）
      { name: "apt", category: "包管理", description: "包管理工具", usage: "apt [命令]", example: "apt install vim" },
      { name: "apt-get", category: "包管理", description: "包管理工具", usage: "apt-get [命令]", example: "apt-get update" },
      { name: "dpkg", category: "包管理", description: "Debian包管理", usage: "dpkg [选项]", example: "dpkg -i package.deb" },
      // 包管理（RedHat/CentOS）
      { name: "yum", category: "包管理", description: "包管理工具", usage: "yum [命令]", example: "yum install httpd" },
      { name: "dnf", category: "包管理", description: "包管理工具", usage: "dnf [命令]", example: "dnf update" },
      { name: "rpm", category: "包管理", description: "RPM包管理", usage: "rpm [选项]", example: "rpm -ivh package.rpm" },
      // 磁盘管理
      { name: "mount", category: "磁盘管理", description: "挂载文件系统", usage: "mount [选项] 设备 目录", example: "mount /dev/sda1 /mnt" },
      { name: "umount", category: "磁盘管理", description: "卸载文件系统", usage: "umount 目录", example: "umount /mnt" },
      { name: "fdisk", category: "磁盘管理", description: "磁盘分区工具", usage: "fdisk 设备", example: "fdisk /dev/sda" },
      { name: "mkfs", category: "磁盘管理", description: "创建文件系统", usage: "mkfs [类型] 设备", example: "mkfs.ext4 /dev/sdb1" },
      { name: "fsck", category: "磁盘管理", description: "文件系统检查", usage: "fsck 设备", example: "fsck /dev/sda1" },
      { name: "lsblk", category: "磁盘管理", description: "列出块设备", usage: "lsblk", example: "lsblk" },
      { name: "blkid", category: "磁盘管理", description: "显示块设备信息", usage: "blkid", example: "blkid" },
      // 其他
      { name: "echo", category: "其他", description: "输出字符串", usage: "echo [字符串]", example: "echo Hello World" },
      { name: "history", category: "其他", description: "显示命令历史", usage: "history", example: "history | grep ssh" },
      { name: "alias", category: "其他", description: "定义命令别名", usage: "alias 名=命令", example: 'alias ll="ls -l"' },
      { name: "crontab", category: "其他", description: "定时任务管理", usage: "crontab -e", example: "crontab -l" },
      { name: "at", category: "其他", description: "定时执行命令", usage: "at 时间", example: 'echo "reboot" | at 23:00' },
      { name: "shutdown", category: "其他", description: "关机", usage: "shutdown [选项]", example: "shutdown -h now" },
      { name: "reboot", category: "其他", description: "重启", usage: "reboot", example: "reboot" },
      { name: "man", category: "其他", description: "查看命令手册", usage: "man 命令", example: "man ls" },
      { name: "whatis", category: "其他", description: "显示命令简要说明", usage: "whatis 命令", example: "whatis grep" },
      { name: "whereis", category: "其他", description: "查找命令位置", usage: "whereis 命令", example: "whereis python" },
      { name: "which", category: "其他", description: "显示命令路径", usage: "which 命令", example: "which bash" },
      { name: "locate", category: "其他", description: "快速查找文件", usage: "locate 文件名", example: "locate passwd" }
    ];
    const categories = ["全部", ...Array.from(new Set(commands.map((cmd) => cmd.category)))];
    const filteredCommands = common_vendor.computed(() => {
      return commands.filter((cmd) => {
        const matchesSearch = searchQuery.value === "" || cmd.name.toLowerCase().includes(searchQuery.value.toLowerCase()) || cmd.description.toLowerCase().includes(searchQuery.value.toLowerCase());
        const matchesCategory = selectedCategory.value === "全部" || cmd.category === selectedCategory.value;
        return matchesSearch && matchesCategory;
      });
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: searchQuery.value,
        b: common_vendor.o(($event) => searchQuery.value = $event.detail.value),
        c: common_vendor.f(categories, (category, k0, i0) => {
          return {
            a: common_vendor.t(category),
            b: category,
            c: selectedCategory.value === category ? 1 : "",
            d: common_vendor.o(($event) => selectedCategory.value = category, category)
          };
        }),
        d: common_vendor.f(filteredCommands.value, (cmd, k0, i0) => {
          return {
            a: common_vendor.t(cmd.name),
            b: common_vendor.t(cmd.category),
            c: common_vendor.t(cmd.description),
            d: common_vendor.t(cmd.usage),
            e: common_vendor.t(cmd.example),
            f: cmd.name
          };
        }),
        e: filteredCommands.value.length === 0
      }, filteredCommands.value.length === 0 ? {} : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fae3112e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/linux-commands.js.map
