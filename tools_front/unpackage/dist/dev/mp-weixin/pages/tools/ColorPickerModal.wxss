
.color-picker-modal.data-v-19c31dd0 {
  position: fixed;
  z-index: 9999;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn-19c31dd0 0.25s cubic-bezier(0.2,0,0.1,1);
  -webkit-backdrop-filter: blur(8px);
          backdrop-filter: blur(8px);
}
@keyframes fadeIn-19c31dd0 {
from { opacity: 0;
}
to { opacity: 1;
}
}
.color-picker-container.data-v-19c31dd0 {
  background: linear-gradient(135deg, #fff 0%, #f5f5f5 100%);
  border-radius: 24rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  min-width: 520rpx;
  max-width: 90vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.10);
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  position: relative;
}
.picker-header.data-v-19c31dd0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}
.picker-title.data-v-19c31dd0 {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
}
.picker-close.data-v-19c31dd0 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background 0.2s;
}
.picker-close.data-v-19c31dd0:active {
  background: #e5e7eb;
}
.close-icon.data-v-19c31dd0 {
  font-size: 32rpx;
  color: #666;
}
.preset-colors.data-v-19c31dd0 {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 8rpx;
}
.preset-color.data-v-19c31dd0 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e5e5;
  box-sizing: border-box;
  cursor: pointer;
  transition: border 0.2s, transform 0.18s cubic-bezier(0.2,0,0.1,1);
}
.preset-color.data-v-19c31dd0:active {
  transform: scale(0.95);
}
.hex-input-group.data-v-19c31dd0 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}
.hex-label.data-v-19c31dd0 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
.hex-input.data-v-19c31dd0 {
  width: 160rpx;
  height: 48rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #1a1a1a;
  padding: 0 12rpx;
  background: #fff;
  font-family: 'SF Mono', 'Monaco', monospace;
}
.preview-color.data-v-19c31dd0 {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e5e5;
  box-sizing: border-box;
}
.rgb-inputs.data-v-19c31dd0 {
  display: flex;
  gap: 12rpx;
  margin-bottom: 8rpx;
}
.rgb-group.data-v-19c31dd0 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}
.rgb-label.data-v-19c31dd0 {
  font-size: 20rpx;
  color: #666;
  font-weight: 500;
}
.rgb-input.data-v-19c31dd0 {
  width: 80rpx;
  height: 48rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #1a1a1a;
  text-align: center;
  background: #fff;
  font-family: 'SF Mono', 'Monaco', monospace;
}
.picker-actions.data-v-19c31dd0 {
  display: flex;
  gap: 20rpx;
  margin-top: 8rpx;
}
.picker-btn.data-v-19c31dd0 {
  flex: 1;
  height: 56rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  transition: all 0.18s cubic-bezier(0.2,0,0.1,1);
  cursor: pointer;
}
.picker-btn.cancel.data-v-19c31dd0 {
  background: #f3f4f6;
  color: #666;
}
.picker-btn.cancel.data-v-19c31dd0:active {
  background: #e5e7eb;
}
.picker-btn.confirm.data-v-19c31dd0 {
  background: linear-gradient(135deg, #007AFF 0%, #4ecdc4 100%);
  color: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,122,255,0.10);
}
.picker-btn.confirm.data-v-19c31dd0:active {
  background: linear-gradient(135deg, #0056D6 0%, #43e97b 100%);
}
