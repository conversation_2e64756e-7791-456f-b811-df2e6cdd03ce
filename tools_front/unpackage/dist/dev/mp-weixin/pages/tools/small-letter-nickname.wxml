<view class="small-letter-nickname data-v-da2d62e1"><view class="container data-v-da2d62e1"><view class="header-section data-v-da2d62e1"><view class="title-container data-v-da2d62e1"><text class="title-icon data-v-da2d62e1">🔤</text><text class="title-text data-v-da2d62e1">小字母昵称</text></view><text class="subtitle data-v-da2d62e1">生成各种风格的小字母昵称，让你的名字更有个性</text></view><view class="input-section data-v-da2d62e1"><view class="section-header data-v-da2d62e1"><text class="section-icon data-v-da2d62e1">✏️</text><text class="section-title data-v-da2d62e1">输入文字</text></view><view class="input-wrapper data-v-da2d62e1"><input class="text-input data-v-da2d62e1" placeholder="请输入英文字母或数字..." maxlength="20" bindinput="{{a}}" value="{{b}}"/></view></view><view class="style-section data-v-da2d62e1"><view class="section-header data-v-da2d62e1"><text class="section-icon data-v-da2d62e1">🎨</text><text class="section-title data-v-da2d62e1">选择样式</text></view><view class="style-tabs data-v-da2d62e1"><view wx:for="{{c}}" wx:for-item="style" wx:key="c" bindtap="{{style.d}}" class="{{['data-v-da2d62e1', 'style-tab', style.e]}}"><text class="tab-icon data-v-da2d62e1">{{style.a}}</text><text class="tab-name data-v-da2d62e1">{{style.b}}</text></view></view></view><view wx:if="{{d}}" class="result-section data-v-da2d62e1"><view class="section-header data-v-da2d62e1"><text class="section-icon data-v-da2d62e1">✨</text><text class="section-title data-v-da2d62e1">生成结果</text></view><view class="result-grid data-v-da2d62e1"><view wx:for="{{e}}" wx:for-item="result" wx:key="b" class="result-item data-v-da2d62e1" bindtap="{{result.c}}"><text class="result-text data-v-da2d62e1">{{result.a}}</text><view class="copy-btn data-v-da2d62e1"><text class="copy-icon data-v-da2d62e1">📋</text></view></view></view></view><view class="example-section data-v-da2d62e1"><view class="section-header data-v-da2d62e1"><text class="section-icon data-v-da2d62e1">💡</text><text class="section-title data-v-da2d62e1">快速示例</text></view><view class="example-grid data-v-da2d62e1"><view wx:for="{{f}}" wx:for-item="example" wx:key="b" bindtap="{{example.c}}" class="example-item data-v-da2d62e1"><text class="example-text data-v-da2d62e1">{{example.a}}</text></view></view></view><view class="help-section data-v-da2d62e1"><view class="section-header data-v-da2d62e1"><text class="section-icon data-v-da2d62e1">💡</text><text class="section-title data-v-da2d62e1">使用说明</text></view><view class="help-content data-v-da2d62e1"><text class="help-item data-v-da2d62e1">• 输入英文字母或数字，支持大小写</text><text class="help-item data-v-da2d62e1">• 选择不同样式生成多种风格的小字母</text><text class="help-item data-v-da2d62e1">• 点击结果即可复制</text><text class="help-item data-v-da2d62e1">• 适用于社交媒体、游戏昵称等场景</text></view></view></view></view>