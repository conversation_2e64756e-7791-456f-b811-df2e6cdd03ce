/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-24b589b8 {
  display: flex;
}
.flex-1.data-v-24b589b8 {
  flex: 1;
}
.items-center.data-v-24b589b8 {
  align-items: center;
}
.justify-center.data-v-24b589b8 {
  justify-content: center;
}
.justify-between.data-v-24b589b8 {
  justify-content: space-between;
}
.text-center.data-v-24b589b8 {
  text-align: center;
}
.rounded.data-v-24b589b8 {
  border-radius: 3px;
}
.rounded-lg.data-v-24b589b8 {
  border-radius: 6px;
}
.shadow.data-v-24b589b8 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-24b589b8 {
  padding: 16rpx;
}
.m-4.data-v-24b589b8 {
  margin: 16rpx;
}
.mb-4.data-v-24b589b8 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-24b589b8 {
  margin-top: 16rpx;
}
.phone-cleaner.data-v-24b589b8 {
  min-height: 100vh;
  background: #ffffff;
}
.content.data-v-24b589b8 {
  padding: 30rpx;
  max-width: 800rpx;
  margin: 0 auto;
}
.card.data-v-24b589b8 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.card-header.data-v-24b589b8 {
  text-align: center;
  margin-bottom: 50rpx;
}
.card-header .header-icon.data-v-24b589b8 {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
}
.card-header .header-icon .speaker-icon.data-v-24b589b8 {
  font-size: 60rpx;
}
.card-header .header-title.data-v-24b589b8 {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 16rpx;
}
.card-header .header-subtitle.data-v-24b589b8 {
  display: block;
  font-size: 28rpx;
  color: #666;
}
.main-content.data-v-24b589b8 {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}
.status-section .status-display.data-v-24b589b8 {
  background: #f8fafc;
  border: 3rpx solid #e2e8f0;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.status-section .status-display .status-icon.data-v-24b589b8 {
  display: block;
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.status-section .status-display .status-text.data-v-24b589b8 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #64748b;
}
.status-section .status-display.active.data-v-24b589b8 {
  background: #dbeafe;
  border-color: #3b82f6;
}
.status-section .status-display.active .status-text.data-v-24b589b8 {
  color: #1e40af;
}
.control-section .control-item.data-v-24b589b8 {
  margin-bottom: 40rpx;
  padding: 0 20rpx;
}
.control-section .control-item .control-label.data-v-24b589b8 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 20rpx;
}
.control-section .control-item .frequency-slider.data-v-24b589b8, .control-section .control-item .duration-slider.data-v-24b589b8 {
  width: 92%;
  margin: 0 auto 16rpx;
}
.control-section .control-item .frequency-range.data-v-24b589b8, .control-section .control-item .duration-range.data-v-24b589b8 {
  width: 92%;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
}
.control-section .control-item .frequency-range .range-text.data-v-24b589b8, .control-section .control-item .duration-range .range-text.data-v-24b589b8 {
  font-size: 24rpx;
  color: #9ca3af;
}
.action-section .clean-btn.data-v-24b589b8 {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 48rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}
.action-section .clean-btn .btn-icon.data-v-24b589b8 {
  font-size: 40rpx;
}
.action-section .clean-btn .btn-text.data-v-24b589b8 {
  font-size: 36rpx;
  font-weight: 600;
}
.action-section .clean-btn.active.data-v-24b589b8 {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 8rpx 32rpx rgba(239, 68, 68, 0.3);
}
.action-section .clean-btn.data-v-24b589b8:active {
  transform: translateY(2rpx);
}
.progress-section .progress-info.data-v-24b589b8 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}
.progress-section .progress-info .progress-text.data-v-24b589b8 {
  font-size: 28rpx;
  font-weight: 500;
  color: #1e40af;
}
.progress-section .progress-info .time-remaining.data-v-24b589b8 {
  font-size: 26rpx;
  color: #64748b;
}
.progress-section .progress-bar.data-v-24b589b8 {
  width: 100%;
  height: 12rpx;
  background: #e2e8f0;
  border-radius: 6rpx;
  overflow: hidden;
}
.progress-section .progress-bar .progress-fill.data-v-24b589b8 {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}
.presets-grid.data-v-24b589b8 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.presets-grid .preset-item.data-v-24b589b8 {
  background: #f8fafc;
  border: 3rpx solid #e2e8f0;
  border-radius: 16rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.presets-grid .preset-item .preset-icon.data-v-24b589b8 {
  display: block;
  font-size: 40rpx;
  margin-bottom: 12rpx;
}
.presets-grid .preset-item .preset-name.data-v-24b589b8 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8rpx;
}
.presets-grid .preset-item .preset-desc.data-v-24b589b8 {
  display: block;
  font-size: 24rpx;
  color: #64748b;
}
.presets-grid .preset-item.data-v-24b589b8:active {
  background: #dbeafe;
  border-color: #3b82f6;
  transform: translateY(2rpx);
}
.instructions .instruction-item.data-v-24b589b8 {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
  margin-top: 24rpx;
}
.instructions .instruction-item .tip-icon.data-v-24b589b8 {
  font-size: 28rpx;
}
.instructions .instruction-item .tip-text.data-v-24b589b8 {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
}
.instructions .instruction-desc.data-v-24b589b8 {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 12rpx;
  padding-left: 44rpx;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
.presets-grid.data-v-24b589b8 {
    grid-template-columns: 1fr;
}
}