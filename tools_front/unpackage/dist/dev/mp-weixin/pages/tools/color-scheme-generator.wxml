<view class="container"><view class="main-content"><view class="section color-picker-section"><view class="section-title"><text class="title-icon">🎨</text><text class="title-text">选择主色</text></view><view class="color-display" bindtap="{{c}}"><view class="color-preview" style="{{'background-color:' + a}}"></view><view class="color-value"><text class="value-label">HEX</text><text class="value-text">{{b}}</text></view></view><view wx:if="{{d}}" class="color-picker-modal" catchtap="{{v}}"><view class="color-picker-container" catchtap="{{t}}"><view class="picker-header"><text class="picker-title">选择颜色</text><text class="close-btn" bindtap="{{e}}">×</text></view><view class="picker-content"><view class="color-canvas-container" bindtouchstart="{{g}}" bindtouchmove="{{h}}"><canvas type="2d" id="colorCanvas" canvas-id="colorCanvas" class="color-canvas"></canvas><view class="canvas-cursor" style="{{f}}"></view></view><view class="hue-slider-container" bindtouchstart="{{j}}" bindtouchmove="{{k}}"><canvas type="2d" id="hueCanvas" canvas-id="hueCanvas" class="hue-slider"></canvas><view class="hue-cursor" style="{{i}}"></view></view><view class="rgb-inputs"><view class="input-group"><text class="input-label">R</text><input type="number" class="rgb-input" bindinput="{{l}}" value="{{m}}"/></view><view class="input-group"><text class="input-label">G</text><input type="number" class="rgb-input" bindinput="{{n}}" value="{{o}}"/></view><view class="input-group"><text class="input-label">B</text><input type="number" class="rgb-input" bindinput="{{p}}" value="{{q}}"/></view></view><view class="preview-actions"><view class="color-preview-large" style="{{'background-color:' + r}}"></view><button class="confirm-btn" bindtap="{{s}}">确认</button></view></view></view></view></view><view class="section scheme-section"><view class="section-title"><text class="title-icon">🌈</text><text class="title-text">配色方案</text></view><view class="scheme-types"><view wx:for="{{w}}" wx:for-item="type" wx:key="c" class="{{['scheme-type', type.d && 'active']}}" bindtap="{{type.e}}"><text class="type-icon">{{type.a}}</text><text class="type-text">{{type.b}}</text></view></view><view class="color-list"><view wx:for="{{x}}" wx:for-item="color" wx:key="b" class="color-item" style="{{'background-color:' + color.c}}" bindtap="{{color.d}}"><text class="color-hex">{{color.a}}</text></view></view></view><view class="section instructions-section"><view class="section-title"><text class="title-icon">📝</text><text class="title-text">使用说明</text></view><view class="instruction-list"><view class="instruction-item"><text class="item-number">1</text><text class="item-text">点击色块选择主色</text></view><view class="instruction-item"><text class="item-number">2</text><text class="item-text">选择配色方案类型</text></view><view class="instruction-item"><text class="item-number">3</text><text class="item-text">点击色块复制颜色代码</text></view></view></view></view></view>