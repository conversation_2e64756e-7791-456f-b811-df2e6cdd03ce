"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "<PERSON><PERSON>t<PERSON><PERSON><PERSON>",
  data() {
    return {
      foregroundColor: "#000000",
      backgroundColor: "#FFFFFF",
      contrastRatio: "21:1",
      checks: {
        normalAA: true,
        normalAAA: true,
        largeAA: true,
        largeAAA: true
      },
      suggestions: [],
      // 颜色选择器相关
      showColorPicker: false,
      currentPickerTarget: "foreground",
      selectedColor: "#FF0000",
      currentHue: "#FF0000",
      canvasPosition: { x: 150, y: 100 },
      huePosition: 0,
      rgbValues: { r: 255, g: 0, b: 0 },
      canvasSize: { width: 300, height: 200 }
    };
  },
  computed: {
    sampleStyle() {
      return {
        backgroundColor: this.backgroundColor,
        color: this.foregroundColor,
        border: `2rpx solid ${this.foregroundColor}20`
      };
    },
    reverseSampleStyle() {
      return {
        backgroundColor: this.foregroundColor,
        color: this.backgroundColor,
        border: `2rpx solid ${this.backgroundColor}20`
      };
    },
    canvasCursorStyle() {
      return {
        left: this.canvasPosition.x + "px",
        top: this.canvasPosition.y + "px"
      };
    },
    hueCursorStyle() {
      return {
        left: this.huePosition + "px"
      };
    }
  },
  mounted() {
    this.updateContrast();
  },
  methods: {
    setForegroundColor(color) {
      this.foregroundColor = color;
      this.updateContrast();
    },
    setBackgroundColor(color) {
      this.backgroundColor = color;
      this.updateContrast();
    },
    updateContrast() {
      const ratio = this.calculateContrastRatio(this.foregroundColor, this.backgroundColor);
      this.contrastRatio = ratio.toFixed(2) + ":1";
      this.checks = {
        normalAA: ratio >= 4.5,
        normalAAA: ratio >= 7,
        largeAA: ratio >= 3,
        largeAAA: ratio >= 4.5
      };
      this.generateSuggestions(ratio);
    },
    calculateContrastRatio(color1, color2) {
      const rgb1 = this.hexToRgb(color1);
      const rgb2 = this.hexToRgb(color2);
      if (!rgb1 || !rgb2)
        return 1;
      const l1 = this.getLuminance(rgb1);
      const l2 = this.getLuminance(rgb2);
      const lighter = Math.max(l1, l2);
      const darker = Math.min(l1, l2);
      return (lighter + 0.05) / (darker + 0.05);
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : null;
    },
    getLuminance(rgb) {
      const { r, g, b } = rgb;
      const [rs, gs, bs] = [r, g, b].map((c) => {
        c = c / 255;
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
      });
      return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    },
    generateSuggestions(ratio) {
      this.suggestions = [];
      if (!this.checks.normalAA) {
        this.suggestions.push({
          text: "当前对比度不符合WCAG AA标准，建议调整颜色以提高可读性",
          colors: this.getSuggestedColors()
        });
      }
      if (ratio < 3) {
        this.suggestions.push({
          text: "对比度过低，可能导致视觉障碍用户无法正常阅读"
        });
      }
      if (this.checks.normalAAA) {
        this.suggestions.push({
          text: "恭喜！当前配色符合最高级别的WCAG AAA标准"
        });
      }
    },
    getSuggestedColors() {
      return ["#000000", "#FFFFFF", "#333333", "#F5F5F5"];
    },
    applySuggestedColor(color) {
      const currentBgLuminance = this.getLuminance(this.hexToRgb(this.backgroundColor));
      const suggestedLuminance = this.getLuminance(this.hexToRgb(color));
      if (Math.abs(currentBgLuminance - suggestedLuminance) > 0.5) {
        this.foregroundColor = color;
      } else {
        this.backgroundColor = color;
      }
      this.updateContrast();
    },
    saveColorPair() {
      const colorPair = {
        foreground: this.foregroundColor,
        background: this.backgroundColor,
        ratio: this.contrastRatio,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      };
      common_vendor.index.setStorageSync("color-pair-" + Date.now(), colorPair);
      common_vendor.index.showToast({
        title: "配色已保存",
        icon: "success",
        duration: 2e3
      });
    },
    shareResult() {
      const shareText = `颜色对比度检查结果：
前景色：${this.foregroundColor}
背景色：${this.backgroundColor}
对比度：${this.contrastRatio}
WCAG AA：${this.checks.normalAA ? "通过" : "未通过"}`;
      common_vendor.index.setClipboardData({
        data: shareText,
        success: () => {
          common_vendor.index.showToast({
            title: "结果已复制",
            icon: "success",
            duration: 2e3
          });
        }
      });
    },
    resetColors() {
      this.foregroundColor = "#000000";
      this.backgroundColor = "#FFFFFF";
      this.updateContrast();
      common_vendor.index.showToast({
        title: "已重置",
        icon: "success",
        duration: 1500
      });
    },
    openColorPicker(target) {
      this.currentPickerTarget = target;
      this.showColorPicker = true;
      this.selectedColor = target === "foreground" ? this.foregroundColor : this.backgroundColor;
      this.updateRgbFromHex(this.selectedColor);
      this.$nextTick(() => {
        this.initColorCanvas();
        this.initHueSlider();
      });
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    confirmColor() {
      if (this.currentPickerTarget === "foreground") {
        this.foregroundColor = this.selectedColor;
      } else {
        this.backgroundColor = this.selectedColor;
      }
      this.updateContrast();
      this.closeColorPicker();
    },
    initColorCanvas() {
      const ctx = common_vendor.index.createCanvasContext("colorCanvas", this);
      const { width, height } = this.canvasSize;
      const gradient1 = ctx.createLinearGradient(0, 0, width, 0);
      gradient1.addColorStop(0, "#FFFFFF");
      gradient1.addColorStop(1, this.currentHue);
      ctx.fillStyle = gradient1;
      ctx.fillRect(0, 0, width, height);
      const gradient2 = ctx.createLinearGradient(0, 0, 0, height);
      gradient2.addColorStop(0, "rgba(0,0,0,0)");
      gradient2.addColorStop(1, "#000000");
      ctx.fillStyle = gradient2;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    initHueSlider() {
      const ctx = common_vendor.index.createCanvasContext("hueCanvas", this);
      const width = 300;
      const height = 30;
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, "#FF0000");
      gradient.addColorStop(0.17, "#FFFF00");
      gradient.addColorStop(0.33, "#00FF00");
      gradient.addColorStop(0.5, "#00FFFF");
      gradient.addColorStop(0.67, "#0000FF");
      gradient.addColorStop(0.83, "#FF00FF");
      gradient.addColorStop(1, "#FF0000");
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
      ctx.draw();
    },
    onCanvasTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          const y = touch.clientY - rect.top;
          this.canvasPosition.x = Math.max(0, Math.min(rect.width, x));
          this.canvasPosition.y = Math.max(0, Math.min(rect.height, y));
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    onHueTouch(e) {
      const touch = e.touches[0];
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const x = touch.clientX - rect.left;
          this.huePosition = Math.max(0, Math.min(rect.width, x));
          this.updateHueFromSlider();
        }
      }).exec();
    },
    updateColorFromCanvas() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".color-canvas").boundingClientRect((rect) => {
        if (rect) {
          const x = this.canvasPosition.x / rect.width;
          const y = this.canvasPosition.y / rect.height;
          const hue = this.huePosition / rect.width * 360;
          const saturation = x;
          const value = 1 - y;
          const rgb = this.hsvToRgb(hue, saturation, value);
          this.rgbValues = rgb;
          this.selectedColor = this.rgbToHex(rgb);
        }
      }).exec();
    },
    updateHueFromSlider() {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".hue-slider").boundingClientRect((rect) => {
        if (rect) {
          const hue = this.huePosition / rect.width * 360;
          this.currentHue = this.hsvToRgb(hue, 1, 1);
          this.currentHue = this.rgbToHex(this.currentHue);
          this.initColorCanvas();
          this.updateColorFromCanvas();
        }
      }).exec();
    },
    updateRgbFromHex(hex) {
      const rgb = this.hexToRgb(hex);
      if (rgb) {
        this.rgbValues = rgb;
        const hsv = this.rgbToHsv(rgb);
        this.huePosition = hsv.h / 360 * 300;
        this.canvasPosition.x = hsv.s * this.canvasSize.width;
        this.canvasPosition.y = (1 - hsv.v) * this.canvasSize.height;
        this.currentHue = this.rgbToHex(this.hsvToRgb(hsv.h, 1, 1));
      }
    },
    onRgbChange() {
      this.selectedColor = this.rgbToHex(this.rgbValues);
      this.updateRgbFromHex(this.selectedColor);
    },
    // 颜色转换工具方法
    hsvToRgb(h, s, v) {
      const c = v * s;
      const x = c * (1 - Math.abs(h / 60 % 2 - 1));
      const m = v - c;
      let r, g, b;
      if (h >= 0 && h < 60) {
        r = c;
        g = x;
        b = 0;
      } else if (h >= 60 && h < 120) {
        r = x;
        g = c;
        b = 0;
      } else if (h >= 120 && h < 180) {
        r = 0;
        g = c;
        b = x;
      } else if (h >= 180 && h < 240) {
        r = 0;
        g = x;
        b = c;
      } else if (h >= 240 && h < 300) {
        r = x;
        g = 0;
        b = c;
      } else {
        r = c;
        g = 0;
        b = x;
      }
      return {
        r: Math.round((r + m) * 255),
        g: Math.round((g + m) * 255),
        b: Math.round((b + m) * 255)
      };
    },
    rgbToHsv(rgb) {
      const r = rgb.r / 255;
      const g = rgb.g / 255;
      const b = rgb.b / 255;
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;
      let h = 0;
      if (diff !== 0) {
        if (max === r) {
          h = (g - b) / diff % 6;
        } else if (max === g) {
          h = (b - r) / diff + 2;
        } else {
          h = (r - g) / diff + 4;
        }
      }
      h = h * 60;
      if (h < 0)
        h += 360;
      const s = max === 0 ? 0 : diff / max;
      const v = max;
      return { h, s, v };
    },
    rgbToHex(rgb) {
      const toHex = (n) => {
        const hex = Math.round(n).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`.toUpperCase();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.foregroundColor,
    b: common_vendor.o(($event) => $options.openColorPicker("foreground")),
    c: common_vendor.o([($event) => $data.foregroundColor = $event.detail.value, (...args) => $options.updateContrast && $options.updateContrast(...args)]),
    d: $data.foregroundColor,
    e: $data.backgroundColor,
    f: common_vendor.o(($event) => $options.openColorPicker("background")),
    g: common_vendor.o([($event) => $data.backgroundColor = $event.detail.value, (...args) => $options.updateContrast && $options.updateContrast(...args)]),
    h: $data.backgroundColor,
    i: common_vendor.t($data.contrastRatio),
    j: common_vendor.t($data.checks.normalAA ? "✓" : "✗"),
    k: $data.checks.normalAA ? 1 : "",
    l: common_vendor.t($data.checks.normalAAA ? "✓" : "✗"),
    m: $data.checks.normalAAA ? 1 : "",
    n: common_vendor.t($data.checks.largeAA ? "✓" : "✗"),
    o: $data.checks.largeAA ? 1 : "",
    p: common_vendor.t($data.checks.largeAAA ? "✓" : "✗"),
    q: $data.checks.largeAAA ? 1 : "",
    r: common_vendor.s($options.sampleStyle),
    s: common_vendor.s($options.reverseSampleStyle),
    t: $data.suggestions.length > 0
  }, $data.suggestions.length > 0 ? {
    v: common_vendor.f($data.suggestions, (suggestion, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(suggestion.text),
        b: suggestion.colors
      }, suggestion.colors ? {
        c: common_vendor.f(suggestion.colors, (color, k1, i1) => {
          return {
            a: common_vendor.t(color),
            b: color,
            c: color,
            d: common_vendor.o(($event) => $options.applySuggestedColor(color), color)
          };
        })
      } : {}, {
        d: index
      });
    })
  } : {}, {
    w: common_vendor.o((...args) => $options.saveColorPair && $options.saveColorPair(...args)),
    x: common_vendor.o((...args) => $options.shareResult && $options.shareResult(...args)),
    y: common_vendor.o((...args) => $options.resetColors && $options.resetColors(...args)),
    z: $data.showColorPicker
  }, $data.showColorPicker ? {
    A: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    B: $data.currentHue,
    C: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    D: common_vendor.o((...args) => $options.onCanvasTouch && $options.onCanvasTouch(...args)),
    E: common_vendor.s($options.canvasCursorStyle),
    F: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    G: common_vendor.o((...args) => $options.onHueTouch && $options.onHueTouch(...args)),
    H: common_vendor.s($options.hueCursorStyle),
    I: common_vendor.o([($event) => $data.rgbValues.r = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    J: $data.rgbValues.r,
    K: common_vendor.o([($event) => $data.rgbValues.g = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    L: $data.rgbValues.g,
    M: common_vendor.o([($event) => $data.rgbValues.b = $event.detail.value, (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    N: $data.rgbValues.b,
    O: $data.selectedColor,
    P: common_vendor.t($data.selectedColor),
    Q: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    R: common_vendor.o((...args) => $options.confirmColor && $options.confirmColor(...args)),
    S: common_vendor.o(() => {
    }),
    T: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-7ce08563"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/contrast-checker.js.map
