"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: null,
      selectedMode: "grayscale",
      isConverting: false,
      convertedImageUrl: "",
      canvasWidth: 800,
      canvasHeight: 600,
      conversionModes: [
        {
          id: "grayscale",
          name: "灰度转换",
          icon: "⚫",
          description: "保留图片细节的经典灰度效果"
        },
        {
          id: "blackwhite",
          name: "纯黑白",
          icon: "◐",
          description: "高对比度的纯黑白效果"
        },
        {
          id: "sepia",
          name: "怀旧棕褐",
          icon: "🟤",
          description: "复古风格的棕褐色调"
        }
      ]
    };
  },
  methods: {
    // 选择图片
    selectImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getImageInfo({
            src: tempFilePath,
            success: (imageInfo) => {
              this.selectedImage = {
                url: tempFilePath,
                name: this.generateFileName(imageInfo),
                width: imageInfo.width,
                height: imageInfo.height
              };
              this.convertedImageUrl = "";
              common_vendor.index.showToast({
                title: "图片选择成功",
                icon: "success"
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:195", "获取图片信息失败:", err);
              common_vendor.index.showToast({
                title: "图片加载失败",
                icon: "none"
              });
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:204", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 生成文件名
    generateFileName(imageInfo) {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, 19);
      const extension = imageInfo.type === "png" ? "png" : "jpg";
      return `${timestamp}.${extension}`;
    },
    // 选择转换模式
    selectMode(modeId) {
      this.selectedMode = modeId;
      if (this.convertedImageUrl) {
        this.convertedImageUrl = "";
      }
      const mode = this.conversionModes.find((m) => m.id === modeId);
      common_vendor.index.showToast({
        title: `已选择${mode.name}`,
        icon: "success"
      });
    },
    // 开始转换
    async handleConvert() {
      if (!this.selectedImage) {
        common_vendor.index.showToast({
          title: "请先选择图片",
          icon: "none"
        });
        return;
      }
      this.isConverting = true;
      try {
        await this.convertImage();
        common_vendor.index.showToast({
          title: "转换完成！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:256", "转换失败:", error);
        common_vendor.index.showToast({
          title: "转换失败，请重试",
          icon: "none"
        });
      } finally {
        this.isConverting = false;
      }
    },
    // 图片转换核心逻辑
    async convertImage() {
      const ctx = common_vendor.index.createCanvasContext("convertCanvas", this);
      const imageInfo = await this.getImageInfo(this.selectedImage.url);
      const maxSize = 1200;
      let targetWidth = imageInfo.width;
      let targetHeight = imageInfo.height;
      if (targetWidth > maxSize || targetHeight > maxSize) {
        const scale = Math.min(maxSize / targetWidth, maxSize / targetHeight);
        targetWidth = Math.floor(targetWidth * scale);
        targetHeight = Math.floor(targetHeight * scale);
      }
      this.canvasWidth = targetWidth;
      this.canvasHeight = targetHeight;
      await this.$nextTick();
      ctx.setFillStyle("#ffffff");
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.drawImage(
        this.selectedImage.url,
        0,
        0,
        imageInfo.width,
        imageInfo.height,
        0,
        0,
        targetWidth,
        targetHeight
      );
      switch (this.selectedMode) {
        case "grayscale":
          ctx.setGlobalAlpha(0.2989);
          ctx.setFillStyle("#ff0000");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.587);
          ctx.setFillStyle("#00ff00");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.114);
          ctx.setFillStyle("#0000ff");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          break;
        case "blackwhite":
          ctx.setGlobalAlpha(0.2989);
          ctx.setFillStyle("#ff0000");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.587);
          ctx.setFillStyle("#00ff00");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.114);
          ctx.setFillStyle("#0000ff");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.2);
          ctx.setFillStyle("#ffffff");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.3);
          ctx.setFillStyle("#000000");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.4);
          ctx.setFillStyle("#ffffff");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.35);
          ctx.setFillStyle("#000000");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          break;
        case "sepia":
          ctx.setGlobalAlpha(0.35);
          ctx.setFillStyle("#704214");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.25);
          ctx.setFillStyle("#B87A3D");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.15);
          ctx.setFillStyle("#FFE5CC");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          ctx.setGlobalAlpha(0.15);
          ctx.setFillStyle("#4A2800");
          ctx.fillRect(0, 0, targetWidth, targetHeight);
          break;
      }
      ctx.setGlobalAlpha(1);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "convertCanvas",
            x: 0,
            y: 0,
            width: targetWidth,
            height: targetHeight,
            destWidth: targetWidth * 2,
            // 输出2倍大小以提高质量
            destHeight: targetHeight * 2,
            fileType: "jpg",
            quality: 0.9,
            success: (res) => {
              this.convertedImageUrl = res.tempFilePath;
              common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:398", "转换完成，图片路径:", res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:401", "导出图片失败:", err);
              throw new Error("导出图片失败");
            }
          }, this);
        }, 500);
      });
    },
    // 简单的滤镜转换方法
    async convertImageSimple() {
      const ctx = common_vendor.index.createCanvasContext("convertCanvas", this);
      const imageInfo = await this.getImageInfo(this.selectedImage.url);
      const maxSize = 600;
      let targetWidth = imageInfo.width;
      let targetHeight = imageInfo.height;
      if (targetWidth > maxSize || targetHeight > maxSize) {
        const scale = Math.min(maxSize / targetWidth, maxSize / targetHeight);
        targetWidth = Math.floor(targetWidth * scale);
        targetHeight = Math.floor(targetHeight * scale);
      }
      this.canvasWidth = targetWidth;
      this.canvasHeight = targetHeight;
      await this.$nextTick();
      common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:433", "Canvas尺寸:", this.canvasWidth, "x", this.canvasHeight);
      common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:434", "转换模式:", this.selectedMode);
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.drawImage(
        imageInfo.path,
        0,
        0,
        imageInfo.width,
        imageInfo.height,
        0,
        0,
        targetWidth,
        targetHeight
      );
      this.applySimpleFilter(ctx);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "convertCanvas",
            success: (res) => {
              this.convertedImageUrl = res.tempFilePath;
              common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:463", "转换完成，图片路径:", res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:466", "导出图片失败:", err);
              throw new Error("导出图片失败");
            }
          }, this);
        }, 1e3);
      });
    },
    // 应用简单滤镜效果
    applySimpleFilter(ctx) {
      common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:476", "开始应用滤镜:", this.selectedMode);
      try {
        switch (this.selectedMode) {
          case "grayscale":
            ctx.save();
            ctx.globalAlpha = 0.299;
            ctx.fillStyle = "#ff0000";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.587;
            ctx.fillStyle = "#00ff00";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.114;
            ctx.fillStyle = "#0000ff";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = "#808080";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.restore();
            break;
          case "blackwhite":
            ctx.save();
            ctx.globalAlpha = 0.6;
            ctx.fillStyle = "#000000";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.4;
            ctx.fillStyle = "#ffffff";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = "#000000";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.restore();
            break;
          case "sepia":
            ctx.save();
            ctx.globalAlpha = 0.4;
            ctx.fillStyle = "#704214";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = "#D2B48C";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.2;
            ctx.fillStyle = "#F4A460";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.globalAlpha = 0.15;
            ctx.fillStyle = "#8B4513";
            ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
            ctx.restore();
            break;
        }
        common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:548", "高级滤镜应用完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:550", "应用滤镜时出错:", error);
        this.applyBasicFilter(ctx);
      }
    },
    // 基础滤镜（降级方案）
    applyBasicFilter(ctx) {
      common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:558", "应用基础滤镜");
      try {
        ctx.save();
        ctx.globalAlpha = 0.5;
        switch (this.selectedMode) {
          case "grayscale":
            ctx.fillStyle = "#808080";
            break;
          case "blackwhite":
            ctx.fillStyle = "#000000";
            break;
          case "sepia":
            ctx.fillStyle = "#8B4513";
            break;
        }
        ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
        ctx.restore();
        common_vendor.index.__f__("log", "at pages/tools/black-white-converter.vue:577", "基础滤镜应用完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:579", "基础滤镜也失败了:", error);
      }
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 重置转换
    resetConvert() {
      this.convertedImageUrl = "";
      this.isConverting = false;
    },
    // 保存图片
    saveImage() {
      if (!this.convertedImageUrl) {
        common_vendor.index.showToast({
          title: "没有可保存的图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.convertedImageUrl,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功！",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/black-white-converter.vue:625", "保存失败:", err);
          if (err.errMsg.includes("auth")) {
            common_vendor.index.showModal({
              title: "需要授权",
              content: "需要您授权保存图片到相册",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedImage
  }, $data.selectedImage ? {
    b: common_vendor.t($data.selectedImage.name)
  } : {}, {
    c: common_vendor.o((...args) => $options.selectImage && $options.selectImage(...args)),
    d: $data.selectedImage
  }, $data.selectedImage ? {
    e: $data.selectedImage.url
  } : {}, {
    f: $data.selectedImage
  }, $data.selectedImage ? {
    g: common_vendor.f($data.conversionModes, (mode, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: common_vendor.t(mode.description),
        d: $data.selectedMode === mode.id
      }, $data.selectedMode === mode.id ? {} : {}, {
        e: mode.id,
        f: $data.selectedMode === mode.id ? 1 : "",
        g: common_vendor.o(($event) => $options.selectMode(mode.id), mode.id)
      });
    })
  } : {}, {
    h: $data.selectedImage
  }, $data.selectedImage ? {
    i: common_vendor.t($data.isConverting ? "转换中..." : "开始转换"),
    j: $data.isConverting ? 1 : "",
    k: common_vendor.o((...args) => $options.handleConvert && $options.handleConvert(...args)),
    l: $data.isConverting
  } : {}, {
    m: $data.convertedImageUrl
  }, $data.convertedImageUrl ? {
    n: $data.convertedImageUrl,
    o: common_vendor.o((...args) => $options.resetConvert && $options.resetConvert(...args)),
    p: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args))
  } : {}, {
    q: $data.canvasWidth + "px",
    r: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-4b8efca6"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/black-white-converter.js.map
