<view class="screenshot-frame data-v-6baee0b2"><view class="content data-v-6baee0b2"><view class="upload-section data-v-6baee0b2"><view class="section-card data-v-6baee0b2"><text class="section-title data-v-6baee0b2">上传截图</text><view class="upload-area data-v-6baee0b2" bindtap="{{a}}"><text class="upload-icon data-v-6baee0b2">📱</text><text class="upload-title data-v-6baee0b2">选择截图</text><text class="upload-desc data-v-6baee0b2">支持 JPG、PNG 格式</text><text class="upload-tip data-v-6baee0b2">点击选择文件</text></view><view wx:if="{{b}}" class="file-info data-v-6baee0b2"><image src="{{c}}" class="preview-thumb data-v-6baee0b2" mode="aspectFit"/><view class="file-details data-v-6baee0b2"><text class="file-name data-v-6baee0b2">{{d}}</text><text class="file-size data-v-6baee0b2">{{e}}</text></view><view class="clear-button data-v-6baee0b2" bindtap="{{f}}"><text class="clear-icon data-v-6baee0b2">×</text></view></view></view></view><view wx:if="{{g}}" class="frame-section data-v-6baee0b2"><view class="section-card data-v-6baee0b2"><text class="section-title data-v-6baee0b2">选择设备框架</text><view class="frame-tabs data-v-6baee0b2"><view wx:for="{{h}}" wx:for-item="category" wx:key="b" class="{{['frame-tab', 'data-v-6baee0b2', category.c && 'active']}}" bindtap="{{category.d}}"><text class="tab-text data-v-6baee0b2">{{category.a}}</text></view></view><view class="frames-grid data-v-6baee0b2"><view wx:for="{{i}}" wx:for-item="frame" wx:key="c" class="{{['frame-option', 'data-v-6baee0b2', frame.d && 'active']}}" bindtap="{{frame.e}}"><view class="frame-preview data-v-6baee0b2"><image src="{{frame.a}}" class="frame-thumb data-v-6baee0b2" mode="aspectFit"/></view><text class="frame-name data-v-6baee0b2">{{frame.b}}</text></view></view></view></view><view wx:if="{{j}}" class="preview-section data-v-6baee0b2"><view class="section-card data-v-6baee0b2"><text class="section-title data-v-6baee0b2">效果预览</text><view class="preview-container data-v-6baee0b2"><view class="device-preview data-v-6baee0b2"><canvas canvas-id="previewCanvas" class="preview-canvas data-v-6baee0b2" style="{{'width:' + k + ';' + ('height:' + l) + ';' + ('display:' + m)}}"></canvas><view wx:if="{{n}}" class="preview-loading data-v-6baee0b2"><text class="loading-text data-v-6baee0b2">正在生成预览...</text></view></view></view></view></view><view wx:if="{{o}}" class="action-section data-v-6baee0b2"><view class="section-card data-v-6baee0b2"><button class="{{['process-button', 'data-v-6baee0b2', q && 'disabled']}}" bindtap="{{r}}" disabled="{{s}}">{{p}}</button></view></view><view class="info-section data-v-6baee0b2"><view class="section-card data-v-6baee0b2"><text class="section-title data-v-6baee0b2">使用说明</text><view class="usage-list data-v-6baee0b2"><text class="usage-item data-v-6baee0b2">• 上传需要加框的截图文件</text><text class="usage-item data-v-6baee0b2">• 选择合适的设备框架样式</text><text class="usage-item data-v-6baee0b2">• 实时预览加框效果</text><text class="usage-item data-v-6baee0b2">• 生成高质量的带框截图</text><text class="usage-item data-v-6baee0b2">• 使用SVG设备模板，背景透明效果更佳</text><text class="usage-item data-v-6baee0b2">• 支持手机、平板、电脑等多种设备样式</text></view></view></view><canvas canvas-id="finalCanvas" class="hidden-canvas data-v-6baee0b2" style="{{'width:' + '1px' + ';' + ('height:' + '1px')}}"></canvas></view></view>