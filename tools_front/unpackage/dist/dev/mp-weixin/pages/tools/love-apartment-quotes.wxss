/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-2de8ce91 {
  display: flex;
}
.flex-1.data-v-2de8ce91 {
  flex: 1;
}
.items-center.data-v-2de8ce91 {
  align-items: center;
}
.justify-center.data-v-2de8ce91 {
  justify-content: center;
}
.justify-between.data-v-2de8ce91 {
  justify-content: space-between;
}
.text-center.data-v-2de8ce91 {
  text-align: center;
}
.rounded.data-v-2de8ce91 {
  border-radius: 3px;
}
.rounded-lg.data-v-2de8ce91 {
  border-radius: 6px;
}
.shadow.data-v-2de8ce91 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-2de8ce91 {
  padding: 16rpx;
}
.m-4.data-v-2de8ce91 {
  margin: 16rpx;
}
.mb-4.data-v-2de8ce91 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-2de8ce91 {
  margin-top: 16rpx;
}
.love-apartment-quotes.data-v-2de8ce91 {
  min-height: 100vh;
  background: linear-gradient(135deg, #fdf2f8 0%, #faf5ff 100%);
}
.content.data-v-2de8ce91 {
  padding: 30rpx;
}
.quote-card.data-v-2de8ce91 {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(236, 72, 153, 0.1);
  border-left: 8rpx solid #ec4899;
}
.quote-card .quote-header.data-v-2de8ce91 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}
.quote-card .quote-header .quote-title.data-v-2de8ce91 {
  font-size: 32rpx;
  font-weight: 600;
  color: #ec4899;
}
.quote-card .quote-header .refresh-btn.data-v-2de8ce91 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #ec4899;
  border-radius: 25rpx;
}
.quote-card .quote-header .refresh-btn .refresh-icon.data-v-2de8ce91 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.quote-card .quote-header .refresh-btn .refresh-text.data-v-2de8ce91 {
  font-size: 24rpx;
  color: white;
}
.quote-card .current-quote.data-v-2de8ce91 {
  padding: 30rpx;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 16rpx;
  border: 2rpx solid #bfdbfe;
}
.quote-card .current-quote .quote-text.data-v-2de8ce91 {
  font-size: 28rpx;
  color: #1d4ed8;
  line-height: 1.6;
}
.character-section.data-v-2de8ce91, .quotes-section.data-v-2de8ce91 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.character-section .section-title.data-v-2de8ce91, .quotes-section .section-title.data-v-2de8ce91 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}
.character-grid.data-v-2de8ce91 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.character-grid .character-btn.data-v-2de8ce91 {
  flex: 1;
  padding: 25rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}
.character-grid .character-btn.active.data-v-2de8ce91 {
  background: #ec4899;
  border-color: #ec4899;
}
.character-grid .character-btn.active .character-name.data-v-2de8ce91 {
  color: white;
}
.character-grid .character-btn .character-name.data-v-2de8ce91 {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}
.quotes-list .quote-item.data-v-2de8ce91 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 25rpx;
  background: #eff6ff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #bfdbfe;
  transition: all 0.3s ease;
}
.quotes-list .quote-item .quote-content.data-v-2de8ce91 {
  flex: 1;
  font-size: 26rpx;
  color: #1d4ed8;
  line-height: 1.5;
  margin-right: 20rpx;
}
.quotes-list .quote-item .copy-btn.data-v-2de8ce91 {
  padding: 8rpx;
  background: #bfdbfe;
  border-radius: 8rpx;
}
.quotes-list .quote-item .copy-btn .copy-icon.data-v-2de8ce91 {
  font-size: 24rpx;
  color: #2563eb;
}
.info-card.data-v-2de8ce91 {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.info-card .info-header.data-v-2de8ce91 {
  padding: 30rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-card .info-header .info-title.data-v-2de8ce91 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.info-card .info-content.data-v-2de8ce91 {
  padding: 30rpx;
}
.info-card .info-content .info-item.data-v-2de8ce91 {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}