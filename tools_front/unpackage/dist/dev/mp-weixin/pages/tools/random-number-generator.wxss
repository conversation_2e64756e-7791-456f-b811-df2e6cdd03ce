/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-1b354db9 {
  display: flex;
}
.flex-1.data-v-1b354db9 {
  flex: 1;
}
.items-center.data-v-1b354db9 {
  align-items: center;
}
.justify-center.data-v-1b354db9 {
  justify-content: center;
}
.justify-between.data-v-1b354db9 {
  justify-content: space-between;
}
.text-center.data-v-1b354db9 {
  text-align: center;
}
.rounded.data-v-1b354db9 {
  border-radius: 3px;
}
.rounded-lg.data-v-1b354db9 {
  border-radius: 6px;
}
.shadow.data-v-1b354db9 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-1b354db9 {
  padding: 16rpx;
}
.m-4.data-v-1b354db9 {
  margin: 16rpx;
}
.mb-4.data-v-1b354db9 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-1b354db9 {
  margin-top: 16rpx;
}
.random-generator-page.data-v-1b354db9 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-section.data-v-1b354db9 {
  margin-bottom: 40rpx;
}
.header-section .header-content.data-v-1b354db9 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
  border-radius: 20rpx;
}
.header-section .header-content .header-icon.data-v-1b354db9 {
  font-size: 60rpx;
  margin-right: 20rpx;
}
.header-section .header-content .header-text.data-v-1b354db9 {
  flex: 1;
}
.header-section .header-content .header-text .header-title.data-v-1b354db9 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-section .header-content .header-text .header-subtitle.data-v-1b354db9 {
  font-size: 26rpx;
  color: #666666;
}
.card.data-v-1b354db9 {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}
.card .card-header.data-v-1b354db9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}
.card .card-header .card-title.data-v-1b354db9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card .card-header .card-actions.data-v-1b354db9 {
  display: flex;
  gap: 20rpx;
}
.card .card-header .action-btn.data-v-1b354db9 {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
}
.card .card-header .action-btn .action-icon.data-v-1b354db9 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.card .card-header .action-btn .action-text.data-v-1b354db9 {
  font-size: 24rpx;
  color: #666666;
}
.card .card-header .action-btn.data-v-1b354db9:active {
  background: #e9ecef;
}
.card .card-content.data-v-1b354db9 {
  padding: 30rpx;
}
.input-row.data-v-1b354db9 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.input-row.data-v-1b354db9:last-child {
  margin-bottom: 0;
}
.input-group.data-v-1b354db9 {
  flex: 1;
}
.input-group .input-label.data-v-1b354db9 {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}
.input-group .input-field.data-v-1b354db9 {
  width: 100%;
  height: 88rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 88rpx;
}
.input-group .input-field.data-v-1b354db9:focus {
  border-color: #007AFF;
  background: #ffffff;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}
.switch-group.data-v-1b354db9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.switch-group .switch-label.data-v-1b354db9 {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}
.preset-grid.data-v-1b354db9 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.preset-item.data-v-1b354db9 {
  padding: 20rpx;
  background: #f8f9ff;
  border: 2rpx solid #e6e8ff;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
}
.preset-item .preset-name.data-v-1b354db9 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.preset-item .preset-range.data-v-1b354db9 {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 4rpx;
}
.preset-item .preset-count.data-v-1b354db9 {
  font-size: 24rpx;
  color: #007AFF;
}
.preset-item.data-v-1b354db9:active {
  background: #e6e8ff;
  border-color: #007AFF;
  transform: scale(0.98);
}
.generate-section.data-v-1b354db9 {
  margin: 40rpx 0;
}
.generate-section .generate-btn.data-v-1b354db9 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  border-radius: 50rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 122, 255, 0.3);
}
.generate-section .generate-btn .btn-icon.data-v-1b354db9 {
  font-size: 36rpx;
  margin-right: 12rpx;
}
.generate-section .generate-btn .btn-text.data-v-1b354db9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
.generate-section .generate-btn.data-v-1b354db9:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 122, 255, 0.2);
}
.result-card .result-display .result-numbers.data-v-1b354db9 {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #007AFF;
  text-align: center;
  padding: 30rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  word-break: break-all;
}
.result-card .result-display .result-info.data-v-1b354db9 {
  display: flex;
  justify-content: space-between;
}
.result-card .result-display .result-info .info-item.data-v-1b354db9 {
  font-size: 26rpx;
  color: #666666;
}
.history-list .history-item.data-v-1b354db9 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 12rpx;
}
.history-list .history-item.data-v-1b354db9:last-child {
  margin-bottom: 0;
}
.history-list .history-item .history-numbers.data-v-1b354db9 {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  word-break: break-all;
}
.history-list .history-item .history-icon.data-v-1b354db9 {
  font-size: 24rpx;
  color: #666666;
  margin-left: 20rpx;
}
.history-list .history-item.data-v-1b354db9:active {
  background: #e9ecef;
}
.instruction-list .instruction-item.data-v-1b354db9 {
  display: block;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.instruction-list .instruction-item.data-v-1b354db9:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 750rpx) {
.preset-grid.data-v-1b354db9 {
    grid-template-columns: 1fr;
}
.input-row.data-v-1b354db9 {
    flex-direction: column;
    gap: 20rpx;
}
.switch-group.data-v-1b354db9 {
    flex-direction: row;
    justify-content: space-between;
}
.switch-group .switch-label.data-v-1b354db9 {
    margin-bottom: 0;
}
}