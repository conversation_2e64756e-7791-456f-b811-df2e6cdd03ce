/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b56fba27 {
  display: flex;
}
.flex-1.data-v-b56fba27 {
  flex: 1;
}
.items-center.data-v-b56fba27 {
  align-items: center;
}
.justify-center.data-v-b56fba27 {
  justify-content: center;
}
.justify-between.data-v-b56fba27 {
  justify-content: space-between;
}
.text-center.data-v-b56fba27 {
  text-align: center;
}
.rounded.data-v-b56fba27 {
  border-radius: 3px;
}
.rounded-lg.data-v-b56fba27 {
  border-radius: 6px;
}
.shadow.data-v-b56fba27 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b56fba27 {
  padding: 16rpx;
}
.m-4.data-v-b56fba27 {
  margin: 16rpx;
}
.mb-4.data-v-b56fba27 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b56fba27 {
  margin-top: 16rpx;
}
.love-emoji.data-v-b56fba27 {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-b56fba27 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-b56fba27 {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 0;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 24rpx;
  border: 1rpx solid #fbcfe8;
}
.title-container.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
}
.icon-wrapper.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16rpx;
  background: white;
  padding: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.1);
  border: 1rpx solid #fce7f3;
}
.icon-text.data-v-b56fba27 {
  font-size: 36rpx;
  line-height: 1.2;
  color: #ec4899;
  font-family: system-ui, -apple-system, sans-serif;
}
.icon-text.data-v-b56fba27:first-child {
  font-size: 52rpx;
  margin-bottom: 12rpx;
}
.title-text.data-v-b56fba27 {
  font-size: 36rpx;
  font-weight: 600;
  color: #be185d;
  margin-top: 16rpx;
}
.subtitle.data-v-b56fba27 {
  font-size: 26rpx;
  color: #9d174d;
  line-height: 1.5;
  opacity: 0.8;
}
.category-section.data-v-b56fba27, .emoji-section.data-v-b56fba27, .selected-section.data-v-b56fba27, .favorites-section.data-v-b56fba27, .combination-section.data-v-b56fba27, .help-section.data-v-b56fba27 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-b56fba27 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-b56fba27 {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-b56fba27 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.category-tabs.data-v-b56fba27 {
  display: flex;
  gap: 12rpx;
  overflow-x: auto;
  padding-bottom: 8rpx;
}
.category-tab.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120rpx;
}
.category-tab.data-v-b56fba27:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.category-tab.active.data-v-b56fba27 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-color: #f9a8d4;
}
.category-tab.active .tab-icon.data-v-b56fba27, .category-tab.active .tab-text.data-v-b56fba27 {
  color: #be185d;
}
.tab-icon.data-v-b56fba27 {
  font-size: 36rpx;
  margin-bottom: 8rpx;
  color: #64748b;
}
.tab-text.data-v-b56fba27 {
  font-size: 24rpx;
  font-weight: 500;
  color: #64748b;
}
.emoji-count.data-v-b56fba27 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.emoji-grid.data-v-b56fba27 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.emoji-item.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  border: 1rpx solid #f9a8d4;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.emoji-item.data-v-b56fba27:hover {
  background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
  border-color: #f472b6;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.15);
}
.emoji-text.data-v-b56fba27 {
  font-size: 40rpx;
  margin-bottom: 8rpx;
}
.emoji-name.data-v-b56fba27 {
  font-size: 24rpx;
  color: #be185d;
  font-weight: 500;
  text-align: center;
}
.selected-card.data-v-b56fba27 {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #f9a8d4;
  text-align: center;
}
.selected-emoji.data-v-b56fba27 {
  font-size: 80rpx;
  margin-bottom: 16rpx;
  display: block;
}
.selected-name.data-v-b56fba27 {
  font-size: 32rpx;
  font-weight: 700;
  color: #be185d;
  margin-bottom: 12rpx;
  display: block;
}
.selected-desc.data-v-b56fba27 {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 24rpx;
  display: block;
  line-height: 1.5;
}
.selected-actions.data-v-b56fba27 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-b56fba27 {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.primary.data-v-b56fba27 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(236, 72, 153, 0.25);
}
.action-btn.primary.data-v-b56fba27:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(236, 72, 153, 0.35);
}
.action-btn.secondary.data-v-b56fba27 {
  background: #f3f4f6;
  color: #374151;
  border: 1rpx solid #e5e7eb;
}
.action-btn.secondary.data-v-b56fba27:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.btn-icon.data-v-b56fba27 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-b56fba27 {
  font-size: 26rpx;
}
.count-badge.data-v-b56fba27 {
  background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  min-width: 40rpx;
  text-align: center;
}
.favorites-grid.data-v-b56fba27 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.favorite-item.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}
.favorite-item.data-v-b56fba27:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.favorite-emoji.data-v-b56fba27 {
  font-size: 36rpx;
  margin-bottom: 8rpx;
}
.favorite-name.data-v-b56fba27 {
  font-size: 24rpx;
  color: #374151;
  font-weight: 500;
  margin-bottom: 12rpx;
}
.favorite-actions.data-v-b56fba27 {
  display: flex;
  gap: 8rpx;
}
.mini-btn.data-v-b56fba27 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mini-btn.data-v-b56fba27:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.mini-btn.remove.data-v-b56fba27 {
  background: #fecaca;
}
.mini-btn.remove.data-v-b56fba27:hover {
  background: #fca5a5;
}
.mini-icon.data-v-b56fba27 {
  font-size: 20rpx;
}
.combination-grid.data-v-b56fba27 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.combination-item.data-v-b56fba27 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
  border-radius: 16rpx;
  border: 1rpx solid #bbf7d0;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.combination-item.data-v-b56fba27:hover {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  border-color: #86efac;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(5, 150, 105, 0.15);
}
.combination-text.data-v-b56fba27 {
  font-size: 32rpx;
  margin-bottom: 12rpx;
  letter-spacing: 4rpx;
}
.combination-name.data-v-b56fba27 {
  font-size: 24rpx;
  color: #059669;
  font-weight: 500;
}
.help-content.data-v-b56fba27 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-b56fba27 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-b56fba27:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-b56fba27 {
    padding: 30rpx 20rpx;
}
.emoji-grid.data-v-b56fba27 {
    grid-template-columns: repeat(3, 1fr);
}
.favorites-grid.data-v-b56fba27 {
    grid-template-columns: repeat(2, 1fr);
}
.combination-grid.data-v-b56fba27 {
    grid-template-columns: 1fr;
}
.selected-actions.data-v-b56fba27 {
    flex-direction: column;
    gap: 12rpx;
}
}