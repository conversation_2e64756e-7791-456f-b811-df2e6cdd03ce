<view class="flex-generator-container data-v-e3128b2a"><view class="header-card data-v-e3128b2a"><view class="header-content data-v-e3128b2a"><view class="header-icon data-v-e3128b2a">🔧</view><view class="header-info data-v-e3128b2a"><text class="header-title data-v-e3128b2a">Flex布局生成器</text><text class="header-subtitle data-v-e3128b2a">快速生成专业的Flexbox布局代码</text></view></view></view><view class="basic-card data-v-e3128b2a"><view class="card-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">📐 基础设置</text></view><view class="basic-settings data-v-e3128b2a"><view class="setting-row data-v-e3128b2a"><text class="setting-label data-v-e3128b2a">排列方向</text><view class="option-buttons data-v-e3128b2a"><view wx:for="{{a}}" wx:for-item="direction" wx:key="c" class="{{['option-btn', 'data-v-e3128b2a', direction.d && 'active']}}" bindtap="{{direction.e}}"><view class="btn-icon data-v-e3128b2a">{{direction.a}}</view><text class="btn-text data-v-e3128b2a">{{direction.b}}</text></view></view></view><view class="setting-row data-v-e3128b2a"><text class="setting-label data-v-e3128b2a">主轴对齐</text><view class="option-grid data-v-e3128b2a"><view wx:for="{{b}}" wx:for-item="justify" wx:key="b" class="{{['option-btn', 'small', 'data-v-e3128b2a', justify.c && 'active']}}" bindtap="{{justify.d}}"><text class="btn-text data-v-e3128b2a">{{justify.a}}</text></view></view></view><view class="setting-row data-v-e3128b2a"><text class="setting-label data-v-e3128b2a">交叉轴对齐</text><view class="option-grid data-v-e3128b2a"><view wx:for="{{c}}" wx:for-item="align" wx:key="b" class="{{['option-btn', 'small', 'data-v-e3128b2a', align.c && 'active']}}" bindtap="{{align.d}}"><text class="btn-text data-v-e3128b2a">{{align.a}}</text></view></view></view><view class="setting-row data-v-e3128b2a"><text class="setting-label data-v-e3128b2a">换行方式</text><view class="option-buttons data-v-e3128b2a"><view wx:for="{{d}}" wx:for-item="wrap" wx:key="b" class="{{['option-btn', 'data-v-e3128b2a', wrap.c && 'active']}}" bindtap="{{wrap.d}}"><text class="btn-text data-v-e3128b2a">{{wrap.a}}</text></view></view></view></view></view><view class="advanced-card data-v-e3128b2a"><view class="card-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">⚡ 高级设置</text></view><view class="advanced-settings data-v-e3128b2a"><view class="value-group data-v-e3128b2a"><text class="value-label data-v-e3128b2a">间距 ({{e}}px)</text><slider value="{{f}}" bindchange="{{g}}" min="0" max="25" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="value-slider data-v-e3128b2a"/></view><view class="value-group data-v-e3128b2a"><text class="value-label data-v-e3128b2a">子元素数量 ({{h}})</text><slider value="{{i}}" bindchange="{{j}}" min="1" max="8" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="value-slider data-v-e3128b2a"/></view><view class="value-group data-v-e3128b2a"><text class="value-label data-v-e3128b2a">容器高度 ({{k}}px)</text><slider value="{{l}}" bindchange="{{m}}" min="0" max="30" step="1" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="value-slider data-v-e3128b2a"/></view></view></view><view class="templates-card data-v-e3128b2a"><view class="card-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">🎨 预设模板</text></view><view class="template-grid data-v-e3128b2a"><view wx:for="{{n}}" wx:for-item="template" wx:key="c" class="template-item data-v-e3128b2a" bindtap="{{template.d}}"><view class="template-preview data-v-e3128b2a"><view wx:for="{{template.a}}" wx:for-item="item" wx:key="a" class="template-block data-v-e3128b2a" style="{{item.b}}"></view></view><text class="template-name data-v-e3128b2a">{{template.b}}</text></view></view></view><view class="preview-card data-v-e3128b2a"><view class="card-header preview-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">👀 布局预览</text><button class="action-btn right-btn data-v-e3128b2a" bindtap="{{o}}"><text class="btn-icon data-v-e3128b2a">🔄</text><text class="btn-text data-v-e3128b2a">刷新</text></button></view><view class="preview-container data-v-e3128b2a"><view class="flex-preview data-v-e3128b2a" style="{{q}}"><view wx:for="{{p}}" wx:for-item="i" wx:key="b" bindtap="{{i.c}}" class="{{['flex-item', 'data-v-e3128b2a', i.d && 'highlight']}}"><text class="item-text data-v-e3128b2a">Item {{i.a}}</text></view></view></view></view><view class="code-card data-v-e3128b2a"><view class="card-header code-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">📄 生成的CSS代码</text><button class="copy-btn right-btn data-v-e3128b2a" bindtap="{{r}}"><text class="copy-icon data-v-e3128b2a">📋</text><text class="copy-text data-v-e3128b2a">复制代码</text></button></view><view class="code-content data-v-e3128b2a"><view class="code-block data-v-e3128b2a"><text class="code-text data-v-e3128b2a">{{s}}</text></view></view></view><view class="tips-card data-v-e3128b2a"><view class="card-header data-v-e3128b2a"><text class="card-title data-v-e3128b2a">💡 使用说明</text></view><view class="tips-content data-v-e3128b2a"><view class="tip-item data-v-e3128b2a"><text class="tip-title data-v-e3128b2a">🎯 设置方向</text><text class="tip-desc data-v-e3128b2a">选择flex-direction控制主轴方向</text></view><view class="tip-item data-v-e3128b2a"><text class="tip-title data-v-e3128b2a">🎮 调整对齐</text><text class="tip-desc data-v-e3128b2a">使用justify-content和align-items调整对齐方式</text></view><view class="tip-item data-v-e3128b2a"><text class="tip-title data-v-e3128b2a">🌊 控制换行</text><text class="tip-desc data-v-e3128b2a">设置flex-wrap控制子元素是否换行</text></view><view class="tip-item data-v-e3128b2a"><text class="tip-title data-v-e3128b2a">📱 响应式布局</text><text class="tip-desc data-v-e3128b2a">生成的代码支持各种屏幕尺寸和设备</text></view></view></view></view>