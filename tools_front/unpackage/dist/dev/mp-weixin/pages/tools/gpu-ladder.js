"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const _sfc_main = {
  name: "GPULadder",
  data() {
    return {
      searchTerm: "",
      selectedTier: "all",
      selectedGPU: null,
      sortAsc: false,
      loading: false,
      tiers: ["all", "旗舰", "高端", "主流", "入门"],
      gpuData: [],
      toolService: new utils_toolService.ToolService()
    };
  },
  computed: {
    filteredGPUs() {
      let filtered = this.gpuData.filter((gpu) => {
        const matchesSearch = gpu.name.toLowerCase().includes(this.searchTerm.toLowerCase());
        const matchesTier = this.selectedTier === "all" || gpu.tier === this.selectedTier;
        return matchesSearch && matchesTier;
      });
      if (this.sortAsc) {
        filtered.sort((a, b) => a.score - b.score);
      } else {
        filtered.sort((a, b) => b.score - a.score);
      }
      return filtered;
    }
  },
  mounted() {
    this.loadGpuData();
  },
  methods: {
    async loadGpuData() {
      this.loading = true;
      try {
        common_vendor.index.__f__("log", "at pages/tools/gpu-ladder.vue:303", "GPU天梯图查询请求");
        const result = await this.toolService.getGpuLadder();
        common_vendor.index.__f__("log", "at pages/tools/gpu-ladder.vue:305", "GPU天梯图API返回:", result);
        if (result.code === 200 && result.data) {
          const apiData = result.data;
          if (apiData.success && apiData.gpus) {
            this.gpuData = apiData.gpus.map((gpu) => {
              return {
                rank: gpu.rank,
                name: gpu.name,
                score: gpu.score,
                price: gpu.price,
                power: gpu.power,
                tier: gpu.tier,
                brand: gpu.brand,
                streamProcessors: gpu.streamProcessors,
                coreClock: gpu.coreClock,
                memoryType: gpu.memoryType,
                memorySize: gpu.memorySize,
                publishDate: gpu.publishDate,
                leader: gpu.leader,
                dataSource: gpu.dataSource
              };
            });
            if (this.gpuData.length === 0) {
              common_vendor.index.showToast({
                title: "暂无GPU数据",
                icon: "none",
                duration: 2e3
              });
            } else {
              common_vendor.index.showToast({
                title: `找到${this.gpuData.length}个GPU`,
                icon: "success",
                duration: 1500
              });
            }
          } else {
            throw new Error(apiData.message || "获取GPU数据失败");
          }
        } else {
          throw new Error(result.message || "获取GPU数据失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/gpu-ladder.vue:351", "获取GPU数据失败:", error);
        common_vendor.index.showToast({
          title: error.message || "获取GPU数据失败",
          icon: "error",
          duration: 2e3
        });
        this.gpuData = this.getBackupData();
      } finally {
        this.loading = false;
      }
    },
    getBackupData() {
      return [
        { rank: 1, name: "RTX 4090", score: 100, price: "¥12000+", power: "450W", tier: "旗舰", brand: "NVIDIA" },
        { rank: 2, name: "RTX 4080 Super", score: 88, price: "¥9500+", power: "320W", tier: "高端", brand: "NVIDIA" },
        { rank: 3, name: "RTX 4080", score: 85, price: "¥9000+", power: "320W", tier: "高端", brand: "NVIDIA" },
        { rank: 4, name: "RTX 4070 Ti Super", score: 78, price: "¥7200+", power: "285W", tier: "高端", brand: "NVIDIA" },
        { rank: 5, name: "RTX 4070 Ti", score: 75, price: "¥6500+", power: "285W", tier: "高端", brand: "NVIDIA" },
        { rank: 6, name: "RTX 4070 Super", score: 71, price: "¥5200+", power: "220W", tier: "主流", brand: "NVIDIA" },
        { rank: 7, name: "RTX 4070", score: 68, price: "¥4800+", power: "200W", tier: "主流", brand: "NVIDIA" },
        { rank: 8, name: "RTX 4060 Ti 16GB", score: 61, price: "¥3600+", power: "165W", tier: "主流", brand: "NVIDIA" },
        { rank: 9, name: "RTX 4060 Ti", score: 58, price: "¥3200+", power: "165W", tier: "主流", brand: "NVIDIA" },
        { rank: 10, name: "RTX 4060", score: 48, price: "¥2400+", power: "115W", tier: "主流", brand: "NVIDIA" }
      ];
    },
    onSearch() {
    },
    clearSearch() {
      this.searchTerm = "";
    },
    selectTier(tier) {
      this.selectedTier = tier;
    },
    toggleSortOrder() {
      this.sortAsc = !this.sortAsc;
    },
    selectGPU(gpu) {
      var _a;
      this.selectedGPU = ((_a = this.selectedGPU) == null ? void 0 : _a.rank) === gpu.rank ? null : gpu;
    },
    closeDetail() {
      this.selectedGPU = null;
    },
    getTierCount(tier) {
      if (tier === "all")
        return this.gpuData.length;
      return this.gpuData.filter((gpu) => gpu.tier === tier).length;
    },
    getTierClass(tier) {
      const classes = {
        "旗舰": "tier-flagship",
        "高端": "tier-high",
        "主流": "tier-mainstream",
        "入门": "tier-entry"
      };
      return classes[tier] || "tier-default";
    },
    getScoreClass(score) {
      if (score >= 8e3)
        return "score-flagship";
      if (score >= 4e3)
        return "score-high";
      if (score >= 2e3)
        return "score-mainstream";
      return "score-entry";
    },
    getProgressClass(score) {
      if (score >= 8e3)
        return "progress-flagship";
      if (score >= 4e3)
        return "progress-high";
      if (score >= 2e3)
        return "progress-mainstream";
      return "progress-entry";
    },
    getRankBadgeClass(rank) {
      if (rank <= 3)
        return "rank-top";
      if (rank <= 10)
        return "rank-high";
      return "rank-normal";
    },
    getBrand(name) {
      if (name.includes("RTX") || name.includes("GTX"))
        return "NVIDIA";
      if (name.includes("RX"))
        return "AMD";
      return "其他";
    },
    getValueRating(gpu) {
      const priceValue = parseInt(gpu.price.replace(/[^\d]/g, "")) || 1e4;
      const ratio = gpu.score / (priceValue / 1e3);
      if (ratio >= 15)
        return 5;
      if (ratio >= 12)
        return 4;
      if (ratio >= 9)
        return 3;
      if (ratio >= 6)
        return 2;
      return 1;
    },
    getRecommendation(gpu) {
      const rating = this.getValueRating(gpu);
      const recommendations = ["不推荐", "一般", "推荐", "强烈推荐", "超值选择"];
      return recommendations[rating - 1] || "一般";
    },
    getGamePerformance(score) {
      if (score >= 8e3)
        return "4K 高画质";
      if (score >= 4e3)
        return "2K 高画质";
      if (score >= 2e3)
        return "1080p 高画质";
      return "1080p 中画质";
    },
    getUseCase(score) {
      if (score >= 8e3)
        return "专业渲染、4K游戏";
      if (score >= 4e3)
        return "2K游戏、内容创作";
      if (score >= 2e3)
        return "1080p游戏、直播";
      return "轻度游戏、办公";
    },
    // 计算性能百分比（基于最高分数）
    getPerformancePercentage(score) {
      const maxScore = this.gpuData.length > 0 ? Math.max(...this.gpuData.map((gpu) => gpu.score)) : 18e3;
      return Math.round(score / maxScore * 100);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.filteredGPUs.length),
    b: common_vendor.o([($event) => $data.searchTerm = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    c: $data.searchTerm,
    d: $data.searchTerm
  }, $data.searchTerm ? {
    e: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    f: common_vendor.f($data.tiers, (tier, k0, i0) => {
      return {
        a: common_vendor.t(tier === "all" ? "全部" : tier),
        b: common_vendor.t($options.getTierCount(tier)),
        c: tier,
        d: $data.selectedTier === tier ? 1 : "",
        e: common_vendor.o(($event) => $options.selectTier(tier), tier)
      };
    }),
    g: common_vendor.t($data.sortAsc ? "↑" : "↓"),
    h: common_vendor.t($data.sortAsc ? "升序" : "降序"),
    i: common_vendor.o((...args) => $options.toggleSortOrder && $options.toggleSortOrder(...args)),
    j: $data.loading
  }, $data.loading ? {} : common_vendor.e({
    k: common_vendor.f($options.filteredGPUs, (gpu, index, i0) => {
      return {
        a: common_vendor.t(gpu.rank),
        b: common_vendor.n($options.getRankBadgeClass(gpu.rank)),
        c: common_vendor.t(gpu.name),
        d: common_vendor.t(gpu.tier),
        e: common_vendor.n($options.getTierClass(gpu.tier)),
        f: common_vendor.t($options.getBrand(gpu.name)),
        g: common_vendor.t(gpu.score),
        h: common_vendor.n($options.getScoreClass(gpu.score)),
        i: common_vendor.t(gpu.price),
        j: common_vendor.t(gpu.power),
        k: common_vendor.t($options.getPerformancePercentage(gpu.score)),
        l: common_vendor.n($options.getProgressClass(gpu.score)),
        m: $options.getPerformancePercentage(gpu.score) + "%",
        n: common_vendor.f(5, (star, k1, i1) => {
          return {
            a: star,
            b: star <= $options.getValueRating(gpu) ? 1 : ""
          };
        }),
        o: common_vendor.t($options.getRecommendation(gpu)),
        p: gpu.rank,
        q: common_vendor.o(($event) => $options.selectGPU(gpu), gpu.rank),
        r: $data.selectedGPU && $data.selectedGPU.rank === gpu.rank ? 1 : ""
      };
    }),
    l: !$data.loading && $options.filteredGPUs.length === 0
  }, !$data.loading && $options.filteredGPUs.length === 0 ? {} : {}), {
    m: $data.selectedGPU
  }, $data.selectedGPU ? {
    n: common_vendor.t($data.selectedGPU.name),
    o: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    p: common_vendor.t($data.selectedGPU.rank),
    q: common_vendor.t($data.selectedGPU.tier),
    r: common_vendor.t($options.getGamePerformance($data.selectedGPU.score)),
    s: common_vendor.t($options.getUseCase($data.selectedGPU.score))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0574ede5"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/gpu-ladder.js.map
