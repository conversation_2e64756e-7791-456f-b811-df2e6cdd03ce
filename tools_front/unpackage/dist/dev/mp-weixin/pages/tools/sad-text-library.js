"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedCategory: "all",
      searchText: "",
      favorites: [],
      scrollLeft: 0,
      scrollViewId: "categoryScroll",
      scrollProgress: 0,
      categories: [
        { id: "all", name: "全部" },
        { id: "love", name: "爱情" },
        { id: "life", name: "人生" },
        { id: "friendship", name: "友情" },
        { id: "loneliness", name: "孤独" },
        { id: "memory", name: "回忆" },
        { id: "growth", name: "成长" }
      ],
      sadTexts: [
        {
          id: 1,
          category: "love",
          text: "有些人，一旦错过就不再。有些事，一旦失去就永远找不回来了。",
          tags: ["错过", "失去", "永远"]
        },
        {
          id: 2,
          category: "loneliness",
          text: "深夜的时候，总是会想起很多人，想起很多事，然后就睡不着了。",
          tags: ["深夜", "想念", "失眠"]
        },
        {
          id: 3,
          category: "life",
          text: "成年人的世界，连崩溃都要选择时间和地点。",
          tags: ["成年人", "崩溃", "现实"]
        },
        {
          id: 4,
          category: "memory",
          text: "时间是最好的良药，但也是最狠的毒药，它能治愈一切，也能带走一切。",
          tags: ["时间", "良药", "毒药"]
        },
        {
          id: 5,
          category: "friendship",
          text: "最怕的不是朋友突然的关心，而是朋友突然的疏远。",
          tags: ["朋友", "疏远", "害怕"]
        },
        {
          id: 6,
          category: "love",
          text: "我们总是在错误的时间遇见对的人，在正确的时间遇见错误的人。",
          tags: ["时间", "遇见", "错过"]
        },
        {
          id: 7,
          category: "growth",
          text: "长大后才发现，原来小时候的那些烦恼，真的都是小事。",
          tags: ["长大", "烦恼", "小事"]
        },
        {
          id: 8,
          category: "loneliness",
          text: "有时候，一个人挺好，除了孤独点。",
          tags: ["一个人", "孤独", "自在"]
        }
      ]
    };
  },
  mounted() {
    this.initScrollWidth();
  },
  computed: {
    filteredTexts() {
      return this.sadTexts.filter((item) => {
        const matchCategory = this.selectedCategory === "all" || item.category === this.selectedCategory;
        const search = this.searchText.trim().toLowerCase();
        const matchSearch = !search || item.text.toLowerCase().includes(search) || item.tags.some((tag) => tag.toLowerCase().includes(search));
        return matchCategory && matchSearch;
      });
    }
  },
  methods: {
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    handleScroll(e) {
      const { scrollLeft, scrollWidth } = e.detail;
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(`#${this.scrollViewId}`).boundingClientRect((data) => {
        if (data) {
          const containerWidth = data.width;
          const scrollableWidth = scrollWidth - containerWidth;
          if (scrollableWidth > 0) {
            this.scrollProgress = scrollLeft / scrollableWidth * 100;
          }
        }
      }).exec();
    },
    initScrollWidth() {
      this.scrollProgress = 0;
    }
  }
};
const __injectCSSVars__ = () => {
  common_vendor.useCssVars((_ctx) => ({
    "6a3e43cc": _ctx.scrollProgress + "%"
  }));
};
const __setup__ = _sfc_main.setup;
_sfc_main.setup = __setup__ ? (props, ctx) => {
  __injectCSSVars__();
  return __setup__(props, ctx);
} : __injectCSSVars__;
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.searchText,
    b: common_vendor.o(($event) => $data.searchText = $event.detail.value),
    c: $data.searchText
  }, $data.searchText ? {
    d: common_vendor.o(($event) => $data.searchText = "")
  } : {}, {
    e: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: category.id,
        c: common_vendor.o(($event) => $data.selectedCategory = category.id, category.id),
        d: common_vendor.n({
          active: $data.selectedCategory === category.id
        })
      };
    }),
    f: common_vendor.o((...args) => $options.handleScroll && $options.handleScroll(...args)),
    g: $data.scrollLeft,
    h: $data.scrollViewId,
    i: common_vendor.f($options.filteredTexts, (item, k0, i0) => {
      var _a;
      return {
        a: common_vendor.t(item.text),
        b: common_vendor.f(item.tags, (tag, index, i1) => {
          return {
            a: common_vendor.t(tag),
            b: index
          };
        }),
        c: common_vendor.t((_a = $data.categories.find((cat) => cat.id === item.category)) == null ? void 0 : _a.name),
        d: item.id,
        e: common_vendor.o(($event) => $options.copyText(item.text), item.id)
      };
    }),
    j: $data.favorites.length > 0
  }, $data.favorites.length > 0 ? {
    k: common_vendor.t($data.favorites.length)
  } : {}, {
    l: common_vendor.s(_ctx.__cssVars())
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-33a126d0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/sad-text-library.js.map
