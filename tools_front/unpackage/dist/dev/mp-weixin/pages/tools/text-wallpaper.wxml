<view class="text-wallpaper-page data-v-6c0592d9"><view class="content data-v-6c0592d9"><view class="section-card data-v-6c0592d9"><view class="section-header data-v-6c0592d9"><view class="section-icon data-v-6c0592d9">🏷️</view><text class="section-title data-v-6c0592d9">文字分类</text></view><view class="category-grid data-v-6c0592d9"><view wx:for="{{a}}" wx:for-item="category" wx:key="c" class="{{['category-item', 'data-v-6c0592d9', category.d && 'active']}}" bindtap="{{category.e}}"><text class="category-icon data-v-6c0592d9">{{category.a}}</text><text class="category-name data-v-6c0592d9">{{category.b}}</text></view></view></view><view wx:if="{{b}}" class="section-card data-v-6c0592d9"><view class="section-header data-v-6c0592d9"><view class="section-icon data-v-6c0592d9">✨</view><text class="section-title data-v-6c0592d9">创建自定义文字</text></view><view class="custom-creator data-v-6c0592d9"><view class="creator-section data-v-6c0592d9"><text class="creator-label data-v-6c0592d9">输入文字内容</text><block wx:if="{{r0}}"><textarea class="custom-textarea data-v-6c0592d9" placeholder="输入您想要的文字内容，支持换行" maxlength="200" auto-height value="{{c}}" bindinput="{{d}}"/></block><view class="char-count data-v-6c0592d9">{{e}}/200</view></view><view class="creator-section data-v-6c0592d9"><text class="creator-label data-v-6c0592d9">作者署名（可选）</text><input class="custom-input data-v-6c0592d9" placeholder="输入作者名称" maxlength="20" value="{{f}}" bindinput="{{g}}"/></view><view class="creator-section data-v-6c0592d9"><text class="creator-label data-v-6c0592d9">预览效果</text><view class="custom-preview data-v-6c0592d9" style="{{'background:' + o}}"><view class="wallpaper-text-container data-v-6c0592d9"><text class="wallpaper-text data-v-6c0592d9" style="{{'color:' + i + ';' + ('font-size:' + j)}}">{{h}}</text><text wx:if="{{k}}" class="wallpaper-author data-v-6c0592d9" style="{{'color:' + m + ';' + ('font-size:' + n)}}"> — {{l}}</text></view></view></view><view class="creator-section data-v-6c0592d9"><text class="creator-label data-v-6c0592d9">背景设置</text><view class="bg-type-tabs data-v-6c0592d9"><view wx:for="{{p}}" wx:for-item="type" wx:key="c" class="{{['bg-type-tab', 'data-v-6c0592d9', type.d && 'active']}}" bindtap="{{type.e}}"><text class="bg-type-icon data-v-6c0592d9">{{type.a}}</text><text class="bg-type-name data-v-6c0592d9">{{type.b}}</text></view></view><view wx:if="{{q}}" class="bg-options data-v-6c0592d9"><view class="color-picker-section data-v-6c0592d9"><text class="color-label data-v-6c0592d9">选择背景颜色</text><view class="color-grid data-v-6c0592d9"><view wx:for="{{r}}" wx:for-item="color" wx:key="b" class="{{['color-item', 'data-v-6c0592d9', color.c && 'active']}}" style="{{'background-color:' + color.d}}" bindtap="{{color.e}}"><view wx:if="{{color.a}}" class="color-check data-v-6c0592d9">✓</view></view></view></view><view class="opacity-section data-v-6c0592d9"><text class="opacity-label data-v-6c0592d9">透明度: {{s}}%</text><slider value="{{t}}" bindchange="{{v}}" min="{{0.1}}" max="{{1}}" step="{{0.1}}" class="opacity-slider data-v-6c0592d9" activeColor="#495057" backgroundColor="#e9ecef"/></view></view><view wx:if="{{w}}" class="bg-options data-v-6c0592d9"><view class="gradient-presets data-v-6c0592d9"><text class="gradient-label data-v-6c0592d9">选择渐变样式</text><view class="gradient-grid data-v-6c0592d9"><view wx:for="{{x}}" wx:for-item="gradient" wx:key="b" class="{{['gradient-item', 'data-v-6c0592d9', gradient.c && 'active']}}" style="{{'background:' + gradient.d}}" bindtap="{{gradient.e}}"><view wx:if="{{gradient.a}}" class="gradient-check data-v-6c0592d9">✓</view></view></view></view></view></view><view class="creator-section data-v-6c0592d9"><text class="creator-label data-v-6c0592d9">文字设置</text><view class="text-settings data-v-6c0592d9"><view class="text-color-section data-v-6c0592d9"><text class="color-label data-v-6c0592d9">文字颜色</text><view class="color-grid data-v-6c0592d9"><view wx:for="{{y}}" wx:for-item="color" wx:key="b" class="{{['color-item', 'data-v-6c0592d9', color.c && 'active']}}" style="{{'background-color:' + color.d}}" bindtap="{{color.e}}"><view wx:if="{{color.a}}" class="color-check data-v-6c0592d9">✓</view></view></view></view><view class="font-size-section data-v-6c0592d9"><text class="font-size-label data-v-6c0592d9">字体大小: {{z}}px</text><slider value="{{A}}" bindchange="{{B}}" min="{{16}}" max="{{32}}" step="{{2}}" class="font-size-slider data-v-6c0592d9" activeColor="#495057" backgroundColor="#e9ecef"/></view></view></view><view class="creator-actions data-v-6c0592d9"><button class="create-btn data-v-6c0592d9" bindtap="{{C}}" disabled="{{D}}"><text class="create-btn-text data-v-6c0592d9">创建壁纸</text></button><button class="reset-btn data-v-6c0592d9" bindtap="{{E}}"><text class="reset-btn-text data-v-6c0592d9">重置设置</text></button></view></view></view><view class="section-card data-v-6c0592d9"><view class="section-header-wrapper data-v-6c0592d9"><view class="section-header data-v-6c0592d9"><text class="section-title data-v-6c0592d9">{{F}} ({{G}}) </text></view><view wx:if="{{H}}" class="header-actions data-v-6c0592d9"><button class="refresh-btn data-v-6c0592d9" bindtap="{{I}}" disabled="{{J}}"><text class="refresh-text data-v-6c0592d9">换一批</text></button></view></view><view class="wallpaper-content data-v-6c0592d9"><view wx:if="{{K}}" class="loading-state data-v-6c0592d9"><view class="loading-icon rotating data-v-6c0592d9"><svg wx:if="{{N}}" class="data-v-6c0592d9" u-s="{{['d']}}" u-i="6c0592d9-0" bind:__l="__l" u-p="{{N}}"><path wx:if="{{L}}" class="data-v-6c0592d9" u-i="6c0592d9-1,6c0592d9-0" bind:__l="__l" u-p="{{L}}"/><polyline wx:if="{{M}}" class="data-v-6c0592d9" u-i="6c0592d9-2,6c0592d9-0" bind:__l="__l" u-p="{{M}}"/></svg></view><text class="loading-text data-v-6c0592d9">正在加载文字壁纸...</text></view><view wx:else class="wallpaper-grid data-v-6c0592d9"><view wx:for="{{O}}" wx:for-item="wallpaper" wx:key="k" class="wallpaper-item data-v-6c0592d9" bindtap="{{wallpaper.l}}"><view class="wallpaper-preview data-v-6c0592d9" style="{{'background:' + wallpaper.h}}"><view class="wallpaper-text-container data-v-6c0592d9"><text class="wallpaper-text data-v-6c0592d9" style="{{'color:' + wallpaper.b + ';' + ('font-size:' + wallpaper.c)}}">{{wallpaper.a}}</text><text wx:if="{{wallpaper.d}}" class="wallpaper-author data-v-6c0592d9" style="{{'color:' + wallpaper.f + ';' + ('font-size:' + wallpaper.g)}}"> — {{wallpaper.e}}</text></view></view><view class="wallpaper-info data-v-6c0592d9"><text class="wallpaper-title data-v-6c0592d9">{{wallpaper.i}}</text><view class="wallpaper-meta data-v-6c0592d9"><text class="wallpaper-category data-v-6c0592d9">{{wallpaper.j}}</text></view></view></view></view></view></view><view wx:if="{{P}}" class="section-card data-v-6c0592d9"><view class="section-header data-v-6c0592d9"><view class="section-icon data-v-6c0592d9">⭐</view><text class="section-title data-v-6c0592d9">收藏的文字 ({{Q}})</text></view><view class="favorite-list data-v-6c0592d9"><view wx:for="{{R}}" wx:for-item="wallpaper" wx:key="h" class="favorite-item data-v-6c0592d9"><view class="favorite-info data-v-6c0592d9"><text class="favorite-title data-v-6c0592d9">{{wallpaper.a}}</text><text class="favorite-preview data-v-6c0592d9">{{wallpaper.b}}</text></view><button class="favorite-download-btn data-v-6c0592d9" bindtap="{{wallpaper.g}}"><svg wx:if="{{V}}" class="data-v-6c0592d9" u-s="{{['d']}}" u-i="{{wallpaper.f}}" bind:__l="__l" u-p="{{V}}"><path wx:if="{{S}}" class="data-v-6c0592d9" u-i="{{wallpaper.c}}" bind:__l="__l" u-p="{{S}}"/><polyline wx:if="{{T}}" class="data-v-6c0592d9" u-i="{{wallpaper.d}}" bind:__l="__l" u-p="{{T}}"/><line wx:if="{{U}}" class="data-v-6c0592d9" u-i="{{wallpaper.e}}" bind:__l="__l" u-p="{{U}}"/></svg></button></view></view></view><view class="section-card usage-card data-v-6c0592d9"><view class="section-header data-v-6c0592d9"><view class="section-icon data-v-6c0592d9">💡</view><text class="section-title data-v-6c0592d9">使用说明</text></view><view class="usage-list data-v-6c0592d9"><view wx:for="{{W}}" wx:for-item="tip" wx:key="b" class="usage-item data-v-6c0592d9"><text class="usage-bullet data-v-6c0592d9">•</text><text class="usage-text data-v-6c0592d9">{{tip.a}}</text></view></view></view><canvas class="data-v-6c0592d9" canvas-id="textWallpaperCanvas" style="width:600px;height:336px;position:fixed;left:-2000px;top:-2000px"></canvas></view><view wx:if="{{X}}" class="preview-modal data-v-6c0592d9" catchtap="{{aq}}"><view class="preview-content data-v-6c0592d9" catchtap="{{ap}}"><button class="preview-close-btn data-v-6c0592d9" bindtap="{{ab}}"><svg wx:if="{{aa}}" class="data-v-6c0592d9" u-s="{{['d']}}" u-i="6c0592d9-7" bind:__l="__l" u-p="{{aa}}"><line wx:if="{{Y}}" class="data-v-6c0592d9" u-i="6c0592d9-8,6c0592d9-7" bind:__l="__l" u-p="{{Y}}"></line><line wx:if="{{Z}}" class="data-v-6c0592d9" u-i="6c0592d9-9,6c0592d9-7" bind:__l="__l" u-p="{{Z}}"></line></svg></button><view class="preview-image data-v-6c0592d9" style="{{'background:' + aj}}"><view class="wallpaper-text-container data-v-6c0592d9"><text class="wallpaper-text data-v-6c0592d9" style="{{'color:' + ad + ';' + ('font-size:' + ae)}}">{{ac}}</text><text wx:if="{{af}}" class="wallpaper-author data-v-6c0592d9" style="{{'color:' + ah + ';' + ('font-size:' + ai)}}"> — {{ag}}</text></view></view><view class="preview-actions data-v-6c0592d9"><button class="preview-download-btn data-v-6c0592d9" bindtap="{{ao}}"><view class="download-icon data-v-6c0592d9"><svg wx:if="{{an}}" class="data-v-6c0592d9" u-s="{{['d']}}" u-i="6c0592d9-10" bind:__l="__l" u-p="{{an}}"><path wx:if="{{ak}}" class="data-v-6c0592d9" u-i="6c0592d9-11,6c0592d9-10" bind:__l="__l" u-p="{{ak}}"/><polyline wx:if="{{al}}" class="data-v-6c0592d9" u-i="6c0592d9-12,6c0592d9-10" bind:__l="__l" u-p="{{al}}"/><line wx:if="{{am}}" class="data-v-6c0592d9" u-i="6c0592d9-13,6c0592d9-10" bind:__l="__l" u-p="{{am}}"/></svg></view><text class="data-v-6c0592d9">保存到相册</text></button></view></view></view></view>