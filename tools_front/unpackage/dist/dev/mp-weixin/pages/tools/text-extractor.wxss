/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-86f21235 {
  display: flex;
}
.flex-1.data-v-86f21235 {
  flex: 1;
}
.items-center.data-v-86f21235 {
  align-items: center;
}
.justify-center.data-v-86f21235 {
  justify-content: center;
}
.justify-between.data-v-86f21235 {
  justify-content: space-between;
}
.text-center.data-v-86f21235 {
  text-align: center;
}
.rounded.data-v-86f21235 {
  border-radius: 3px;
}
.rounded-lg.data-v-86f21235 {
  border-radius: 6px;
}
.shadow.data-v-86f21235 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-86f21235 {
  padding: 16rpx;
}
.m-4.data-v-86f21235 {
  margin: 16rpx;
}
.mb-4.data-v-86f21235 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-86f21235 {
  margin-top: 16rpx;
}
.text-extractor.data-v-86f21235 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-86f21235 {
  padding: 30rpx;
  max-width: 1000rpx;
  margin: 0 auto;
}
.card.data-v-86f21235 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card .card-header.data-v-86f21235 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card .card-header .upload-icon.data-v-86f21235, .card .card-header .info-icon.data-v-86f21235 {
  font-size: 32rpx;
  color: #3b82f6;
  margin-right: 16rpx;
}
.card .card-header .header-title.data-v-86f21235 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card .card-content .upload-area.data-v-86f21235 {
  border: 4rpx dashed #d1d5db;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}
.card .card-content .upload-area .upload-icon-large.data-v-86f21235 {
  font-size: 96rpx;
  color: #9ca3af;
  margin-bottom: 30rpx;
}
.card .card-content .upload-area .upload-text.data-v-86f21235 {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 30rpx;
}
.card .card-content .upload-area .file-input-wrapper.data-v-86f21235 {
  position: relative;
  display: inline-block;
}
.card .card-content .upload-area .file-input-wrapper .file-label.data-v-86f21235 {
  display: inline-block;
  padding: 24rpx 48rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.card .card-content .upload-area .file-input-wrapper .file-label.data-v-86f21235:active {
  transform: scale(0.96);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.15);
}
.card .card-content .upload-area .file-input-wrapper .file-label .label-text.data-v-86f21235 {
  font-size: 28rpx;
  font-weight: 500;
}
.card .card-content .upload-area .selected-file.data-v-86f21235 {
  margin-top: 30rpx;
  padding: 24rpx;
  background: #f8fafc;
  border: 1rpx solid rgba(59, 130, 246, 0.1);
  border-radius: 16rpx;
}
.card .card-content .upload-area .selected-file .preview-container.data-v-86f21235 {
  position: relative;
  width: 400rpx;
  margin: 0 auto 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}
.card .card-content .upload-area .selected-file .preview-container.data-v-86f21235:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.card .card-content .upload-area .selected-file .preview-container .preview-image.data-v-86f21235 {
  display: block;
  width: 100%;
  object-fit: contain;
  background: #ffffff;
}
.card .card-content .upload-area .selected-file .preview-container .preview-overlay.data-v-86f21235 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  -webkit-backdrop-filter: blur(4rpx);
          backdrop-filter: blur(4rpx);
}
.card .card-content .upload-area .selected-file .preview-container .preview-overlay .preview-icon.data-v-86f21235 {
  font-size: 48rpx;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  transition: all 0.3s ease;
}
.card .card-content .upload-area .selected-file .preview-container:active .preview-overlay.data-v-86f21235 {
  opacity: 1;
  background: rgba(0, 0, 0, 0.4);
}
.card .card-content .upload-area .selected-file .preview-container:active .preview-overlay .preview-icon.data-v-86f21235 {
  transform: scale(1);
}
.card .card-content .upload-area .selected-file .file-name.data-v-86f21235 {
  font-size: 26rpx;
  color: #3b82f6;
  font-weight: 500;
  word-break: break-all;
  display: block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  padding: 0 20rpx;
}
.card .card-content .extract-btn.data-v-86f21235 {
  width: 100%;
  padding: 48rpx 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}
.card .card-content .extract-btn.data-v-86f21235:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}
.card .card-content .extract-btn.disabled.data-v-86f21235 {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.card .card-content .extract-btn.disabled.data-v-86f21235:active {
  transform: none;
  box-shadow: none;
  background: #e5e7eb;
}
.card .card-content .extract-btn .btn-text.data-v-86f21235 {
  font-size: 32rpx;
  font-weight: 600;
}
.card .card-content .result-container.data-v-86f21235 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.card .card-content .result-container .result-text.data-v-86f21235 {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  white-space: pre-wrap;
}
.card .card-content .action-buttons.data-v-86f21235 {
  margin-top: 20rpx;
}
.card .card-content .action-buttons .action-btn.data-v-86f21235 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.2);
}
.card .card-content .action-buttons .action-btn.data-v-86f21235:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.15);
}
.card .card-content .action-buttons .action-btn .btn-icon.data-v-86f21235 {
  font-size: 32rpx;
}
.card .card-content .action-buttons .action-btn .btn-text.data-v-86f21235 {
  font-size: 30rpx;
  font-weight: 600;
}
.card .card-content .instructions .instruction-item.data-v-86f21235 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  line-height: 1.6;
}