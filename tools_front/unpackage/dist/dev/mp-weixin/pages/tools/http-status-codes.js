"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchQuery: "",
      selectedCategory: "全部",
      categories: ["全部", "信息性", "成功", "重定向", "客户端错误", "服务器错误"],
      scrollLeft: 0,
      statusCodes: [
        // 1xx 信息性状态码
        { code: 100, name: "Continue", description: "继续。客户端应继续其请求", category: "信息性" },
        { code: 101, name: "Switching Protocols", description: "切换协议。服务器根据客户端的请求切换协议", category: "信息性" },
        { code: 102, name: "Processing", description: "处理中。服务器已收到并正在处理请求", category: "信息性" },
        // 2xx 成功状态码
        { code: 200, name: "OK", description: "请求成功。一般用于GET与POST请求", category: "成功" },
        { code: 201, name: "Created", description: "已创建。成功请求并创建了新的资源", category: "成功" },
        { code: 202, name: "Accepted", description: "已接受。已经接受请求，但未处理完成", category: "成功" },
        { code: 204, name: "No Content", description: "无内容。服务器成功处理，但未返回内容", category: "成功" },
        { code: 206, name: "Partial Content", description: "部分内容。服务器成功处理了部分GET请求", category: "成功" },
        // 3xx 重定向状态码
        { code: 300, name: "Multiple Choices", description: "多种选择。请求的资源可包括多个位置", category: "重定向" },
        { code: 301, name: "Moved Permanently", description: "永久移动。请求的资源已被永久移动到新URI", category: "重定向" },
        { code: 302, name: "Found", description: "临时移动。资源现在临时从不同的URI响应请求", category: "重定向" },
        { code: 304, name: "Not Modified", description: "未修改。所请求的资源未修改，可使用缓存", category: "重定向" },
        { code: 307, name: "Temporary Redirect", description: "临时重定向。与302类似，但要求保持请求方法不变", category: "重定向" },
        // 4xx 客户端错误状态码
        { code: 400, name: "Bad Request", description: "错误请求。服务器无法理解请求的格式", category: "客户端错误" },
        { code: 401, name: "Unauthorized", description: "未授权。请求要求用户的身份认证", category: "客户端错误" },
        { code: 403, name: "Forbidden", description: "禁止。服务器理解请求但拒绝执行", category: "客户端错误" },
        { code: 404, name: "Not Found", description: "未找到。服务器无法根据请求找到资源", category: "客户端错误" },
        { code: 405, name: "Method Not Allowed", description: "方法禁用。禁用请求中指定的方法", category: "客户端错误" },
        { code: 408, name: "Request Timeout", description: "请求超时。服务器等待客户端发送的请求时间过长", category: "客户端错误" },
        { code: 409, name: "Conflict", description: "冲突。服务器完成请求时发生冲突", category: "客户端错误" },
        { code: 410, name: "Gone", description: "已删除。请求的资源已被永久删除", category: "客户端错误" },
        { code: 429, name: "Too Many Requests", description: "请求过多。用户在给定的时间内发送了太多的请求", category: "客户端错误" },
        // 5xx 服务器错误状态码
        { code: 500, name: "Internal Server Error", description: "服务器内部错误。服务器遇到错误，无法完成请求", category: "服务器错误" },
        { code: 501, name: "Not Implemented", description: "尚未实施。服务器不支持请求的功能", category: "服务器错误" },
        { code: 502, name: "Bad Gateway", description: "错误网关。服务器作为网关或代理，从上游服务器收到无效响应", category: "服务器错误" },
        { code: 503, name: "Service Unavailable", description: "服务不可用。服务器目前无法使用（过载或停机维护）", category: "服务器错误" },
        { code: 504, name: "Gateway Timeout", description: "网关超时。服务器作为网关或代理，但是没有收到上游服务器的响应", category: "服务器错误" },
        { code: 505, name: "HTTP Version Not Supported", description: "HTTP版本不受支持。服务器不支持请求的HTTP协议版本", category: "服务器错误" }
      ]
    };
  },
  computed: {
    filteredCodes() {
      return this.statusCodes.filter((status) => {
        const matchesSearch = this.searchQuery === "" || status.code.toString().includes(this.searchQuery) || status.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || status.description.toLowerCase().includes(this.searchQuery.toLowerCase());
        const matchesCategory = this.selectedCategory === "全部" || status.category === this.selectedCategory;
        return matchesSearch && matchesCategory;
      });
    }
  },
  methods: {
    onSearch() {
    },
    setCategory(category) {
      this.selectedCategory = category;
    },
    getCategoryClass(category) {
      const classes = {
        "信息性": "category-info",
        "成功": "category-success",
        "重定向": "category-redirect",
        "客户端错误": "category-client-error",
        "服务器错误": "category-server-error"
      };
      return classes[category] || "category-default";
    },
    getExtraInfo(code) {
      const info = {
        200: "最常见的成功响应",
        404: "最常见的客户端错误",
        500: "最常见的服务器错误",
        301: "搜索引擎会更新索引",
        302: "搜索引擎不会更新索引",
        401: "需要身份验证",
        403: "服务器拒绝访问",
        429: "触发了速率限制"
      };
      return info[code] || "常用HTTP状态码";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchQuery = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    b: $data.searchQuery,
    c: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category),
        b: category,
        c: $data.selectedCategory === category ? 1 : "",
        d: common_vendor.o(($event) => $options.setCategory(category), category)
      };
    }),
    d: $data.scrollLeft,
    e: common_vendor.f($options.filteredCodes, (status, k0, i0) => {
      return {
        a: common_vendor.t(status.code),
        b: common_vendor.t(status.name),
        c: common_vendor.t(status.category),
        d: common_vendor.n($options.getCategoryClass(status.category)),
        e: common_vendor.t(status.description),
        f: common_vendor.t($options.getExtraInfo(status.code)),
        g: status.code
      };
    }),
    f: $options.filteredCodes.length === 0
  }, $options.filteredCodes.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-eeca929f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/http-status-codes.js.map
