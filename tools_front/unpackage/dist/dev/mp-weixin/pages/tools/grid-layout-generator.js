"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "GridLayoutGenerator",
  data() {
    return {
      rows: 3,
      columns: 3,
      gap: 16,
      gridItems: [],
      showEditModal: false,
      editingIndex: -1,
      editingAreaName: "",
      layoutTemplates: [
        {
          name: "经典布局",
          rows: 3,
          columns: 3,
          items: ["header", "nav", "aside", "main", "main", "main", "footer", "footer", "footer"],
          preview: [
            { style: "grid-column: 1/4; background: #ff6b6b;" },
            { style: "grid-column: 1/2; background: #4ecdc4;" },
            { style: "grid-column: 2/4; background: #45b7d1;" },
            { style: "grid-column: 1/4; background: #96ceb4;" }
          ]
        },
        {
          name: "双栏布局",
          rows: 2,
          columns: 2,
          items: ["header", "header", "sidebar", "content"],
          preview: [
            { style: "grid-column: 1/3; background: #ff6b6b;" },
            { style: "grid-column: 1/2; background: #4ecdc4;" },
            { style: "grid-column: 2/3; background: #45b7d1;" }
          ]
        },
        {
          name: "卡片网格",
          rows: 2,
          columns: 2,
          items: ["card1", "card2", "card3", "card4"],
          preview: [
            { style: "background: #ff6b6b;" },
            { style: "background: #4ecdc4;" },
            { style: "background: #45b7d1;" },
            { style: "background: #96ceb4;" }
          ]
        }
      ]
    };
  },
  computed: {
    previewGridStyle() {
      return {
        display: "grid",
        gridTemplateRows: `repeat(${this.rows}, 1fr)`,
        gridTemplateColumns: `repeat(${this.columns}, 1fr)`,
        gap: `${this.gap}rpx`,
        minHeight: "400rpx"
      };
    },
    generatedCSS() {
      const containerCSS = `.grid-container {
  display: grid;
  grid-template-rows: repeat(${this.rows}, 1fr);
  grid-template-columns: repeat(${this.columns}, 1fr);
  gap: ${this.gap}px;
  min-height: 100vh;
}`;
      const itemsCSS = this.gridItems.map((item, index) => {
        const row = Math.floor(index / this.columns) + 1;
        const col = index % this.columns + 1;
        return `.${item.name} {
  grid-area: ${row} / ${col} / ${row + 1} / ${col + 1};
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}`;
      }).join("\n\n");
      return `${containerCSS}

${itemsCSS}`;
    }
  },
  watch: {
    rows: "initializeGrid",
    columns: "initializeGrid"
  },
  mounted() {
    this.initializeGrid();
  },
  methods: {
    onRowsChange(e) {
      this.rows = e.detail.value;
    },
    onColumnsChange(e) {
      this.columns = e.detail.value;
    },
    onGapChange(e) {
      this.gap = e.detail.value * 2;
    },
    initializeGrid() {
      const totalItems = this.rows * this.columns;
      this.gridItems = Array(totalItems).fill(null).map((_, index) => ({
        name: `area${index + 1}`,
        index
      }));
    },
    applyTemplate(template) {
      this.rows = template.rows;
      this.columns = template.columns;
      this.$nextTick(() => {
        this.gridItems = template.items.map((name, index) => ({
          name,
          index
        }));
        common_vendor.index.showToast({
          title: "模板已应用",
          icon: "success"
        });
      });
    },
    randomizeAreas() {
      const areaNames = ["header", "nav", "main", "aside", "footer", "content", "sidebar", "article"];
      this.gridItems = this.gridItems.map((item, index) => ({
        ...item,
        name: areaNames[Math.floor(Math.random() * areaNames.length)] + (index + 1)
      }));
      common_vendor.index.showToast({
        title: "区域名称已随机生成",
        icon: "success"
      });
    },
    editAreaName(index) {
      this.editingIndex = index;
      this.editingAreaName = this.gridItems[index].name;
      this.showEditModal = true;
    },
    closeEditModal() {
      this.showEditModal = false;
      this.editingIndex = -1;
      this.editingAreaName = "";
    },
    confirmEdit() {
      if (this.editingAreaName.trim()) {
        this.gridItems[this.editingIndex].name = this.editingAreaName.trim();
        common_vendor.index.showToast({
          title: "区域名称已更新",
          icon: "success"
        });
      }
      this.closeEditModal();
    },
    copyToClipboard() {
      common_vendor.index.setClipboardData({
        data: this.generatedCSS,
        success: () => {
          common_vendor.index.showToast({
            title: "CSS代码已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.rows),
    b: $data.rows,
    c: common_vendor.o((...args) => $options.onRowsChange && $options.onRowsChange(...args)),
    d: common_vendor.t($data.columns),
    e: $data.columns,
    f: common_vendor.o((...args) => $options.onColumnsChange && $options.onColumnsChange(...args)),
    g: common_vendor.t($data.gap),
    h: $data.gap / 2,
    i: common_vendor.o((...args) => $options.onGapChange && $options.onGapChange(...args)),
    j: common_vendor.f($data.layoutTemplates, (template, k0, i0) => {
      return {
        a: common_vendor.f(template.preview, (item, index, i1) => {
          return {
            a: index,
            b: common_vendor.s(item.style)
          };
        }),
        b: common_vendor.t(template.name),
        c: template.name,
        d: common_vendor.o(($event) => $options.applyTemplate(template), template.name)
      };
    }),
    k: common_vendor.o((...args) => $options.randomizeAreas && $options.randomizeAreas(...args)),
    l: common_vendor.f($data.gridItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: index,
        c: common_vendor.o(($event) => $options.editAreaName(index), index)
      };
    }),
    m: common_vendor.s($options.previewGridStyle),
    n: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    o: common_vendor.t($options.generatedCSS),
    p: $data.showEditModal
  }, $data.showEditModal ? {
    q: common_vendor.o((...args) => $options.confirmEdit && $options.confirmEdit(...args)),
    r: $data.editingAreaName,
    s: common_vendor.o(($event) => $data.editingAreaName = $event.detail.value),
    t: common_vendor.o((...args) => $options.closeEditModal && $options.closeEditModal(...args)),
    v: common_vendor.o((...args) => $options.confirmEdit && $options.confirmEdit(...args)),
    w: common_vendor.o(() => {
    }),
    x: common_vendor.o((...args) => $options.closeEditModal && $options.closeEditModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b660904b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/grid-layout-generator.js.map
