"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: null,
      mosaicType: "pixel",
      mosaicIntensity: 15,
      mosaicOpacity: 100,
      // 添加透明度属性
      processingMode: "full",
      isProcessing: false,
      processedImageUrl: "",
      canvasWidth: 800,
      canvasHeight: 600,
      mosaicTypes: [
        {
          id: "pixel",
          name: "像素化",
          icon: "🟫"
        },
        {
          id: "blur",
          name: "模糊化",
          icon: "🌫️"
        },
        {
          id: "colorBlock",
          name: "彩色块",
          icon: "🎨"
        }
      ],
      intensityPresets: [
        { name: "精细", value: 8 },
        { name: "中等", value: 15 },
        { name: "粗糙", value: 25 },
        { name: "超粗", value: 40 }
      ],
      opacityPresets: [
        { name: "完全遮挡", value: 0 },
        { name: "低透明", value: 25 },
        { name: "中透明", value: 50 },
        { name: "高透明", value: 75 }
      ],
      processingModes: [
        {
          id: "full",
          name: "全图处理",
          icon: "🖼️",
          desc: "对整张图片应用马赛克效果"
        },
        {
          id: "center",
          name: "中心区域",
          icon: "🎯",
          desc: "仅对图片中心区域添加马赛克"
        }
      ],
      imageDisplayWidth: 0,
      imageDisplayHeight: 0,
      isDrawing: false,
      startX: 0,
      startY: 0,
      selectedAreas: [],
      touchCtx: null,
      animationTimer: null,
      dashOffset: 0
    };
  },
  methods: {
    // 选择图片
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getImageInfo({
            src: tempFilePath,
            success: (imageInfo) => {
              this.selectedImage = {
                url: tempFilePath,
                name: this.generateFileName(imageInfo),
                width: imageInfo.width,
                height: imageInfo.height
              };
              this.processedImageUrl = "";
              common_vendor.index.showToast({
                title: "图片选择成功",
                icon: "success"
              });
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/image-mosaic-maker.vue:333", "获取图片信息失败:", err);
              common_vendor.index.showToast({
                title: "图片加载失败",
                icon: "none"
              });
            }
          });
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-mosaic-maker.vue:342", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    // 生成文件名
    generateFileName(imageInfo) {
      const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-").slice(0, 19);
      const extension = imageInfo.type === "png" ? "png" : "jpg";
      return `mosaic-${timestamp}.${extension}`;
    },
    // 选择马赛克类型
    selectMosaicType(typeId) {
      this.mosaicType = typeId;
      if (this.processedImageUrl) {
        this.processedImageUrl = "";
      }
      const type = this.mosaicTypes.find((t) => t.id === typeId);
      common_vendor.index.showToast({
        title: `已选择${type.name}`,
        icon: "success"
      });
    },
    // 选择处理模式
    selectProcessingMode(modeId) {
      this.processingMode = modeId;
      const mode = this.processingModes.find((m) => m.id === modeId);
      common_vendor.index.showToast({
        title: `模式：${mode.name}`,
        icon: "success"
      });
    },
    // 设置强度
    setIntensity(value) {
      this.mosaicIntensity = value;
    },
    // 强度变化
    onIntensityChange(e) {
      this.mosaicIntensity = e.detail.value;
    },
    // 设置透明度
    setOpacity(value) {
      this.mosaicOpacity = value;
    },
    // 透明度变化
    onOpacityChange(e) {
      this.mosaicOpacity = e.detail.value;
    },
    // 开始处理
    async handleProcess() {
      if (!this.selectedImage) {
        common_vendor.index.showToast({
          title: "请先选择图片",
          icon: "none"
        });
        return;
      }
      this.isProcessing = true;
      try {
        await this.processMosaic();
        common_vendor.index.showToast({
          title: "马赛克处理完成！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-mosaic-maker.vue:425", "处理失败:", error);
        common_vendor.index.showToast({
          title: "处理失败，请重试",
          icon: "none"
        });
      } finally {
        this.isProcessing = false;
      }
    },
    // 马赛克处理核心逻辑
    async processMosaic() {
      const ctx = common_vendor.index.createCanvasContext("mosaicCanvas", this);
      const imageInfo = await this.getImageInfo(this.selectedImage.url);
      const maxSize = 1e3;
      let targetWidth = imageInfo.width;
      let targetHeight = imageInfo.height;
      if (targetWidth > maxSize || targetHeight > maxSize) {
        const scale = Math.min(maxSize / targetWidth, maxSize / targetHeight);
        targetWidth = Math.floor(targetWidth * scale);
        targetHeight = Math.floor(targetHeight * scale);
      }
      this.canvasWidth = targetWidth;
      this.canvasHeight = targetHeight;
      await this.$nextTick();
      common_vendor.index.__f__("log", "at pages/tools/image-mosaic-maker.vue:459", "Canvas尺寸:", this.canvasWidth, "x", this.canvasHeight);
      common_vendor.index.__f__("log", "at pages/tools/image-mosaic-maker.vue:460", "马赛克设置:", {
        type: this.mosaicType,
        intensity: this.mosaicIntensity,
        opacity: this.mosaicOpacity,
        mode: this.processingMode
      });
      ctx.fillStyle = "#ffffff";
      ctx.fillRect(0, 0, this.canvasWidth, this.canvasHeight);
      ctx.drawImage(
        imageInfo.path,
        0,
        0,
        imageInfo.width,
        imageInfo.height,
        0,
        0,
        targetWidth,
        targetHeight
      );
      this.applyMosaicEffect(ctx, targetWidth, targetHeight);
      ctx.draw(false, () => {
        setTimeout(() => {
          common_vendor.index.canvasToTempFilePath({
            canvasId: "mosaicCanvas",
            success: (res) => {
              this.processedImageUrl = res.tempFilePath;
              common_vendor.index.__f__("log", "at pages/tools/image-mosaic-maker.vue:494", "马赛克处理完成，图片路径:", res.tempFilePath);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/tools/image-mosaic-maker.vue:497", "导出图片失败:", err);
              throw new Error("导出图片失败");
            }
          }, this);
        }, 1e3);
      });
    },
    // 应用马赛克效果
    applyMosaicEffect(ctx, canvasWidth, canvasHeight) {
      common_vendor.index.__f__("log", "at pages/tools/image-mosaic-maker.vue:507", "应用马赛克效果:", this.mosaicType);
      const blockSize = this.mosaicIntensity;
      const scaleX = canvasWidth / this.imageDisplayWidth;
      const scaleY = canvasHeight / this.imageDisplayHeight;
      this.selectedAreas.forEach((area) => {
        const scaledArea = {
          x: area.x * scaleX,
          y: area.y * scaleY,
          width: area.width * scaleX,
          height: area.height * scaleY
        };
        switch (this.mosaicType) {
          case "pixel":
            this.applyPixelMosaic(
              ctx,
              scaledArea.x,
              scaledArea.y,
              scaledArea.x + scaledArea.width,
              scaledArea.y + scaledArea.height,
              blockSize
            );
            break;
          case "blur":
            this.applyBlurMosaic(
              ctx,
              scaledArea.x,
              scaledArea.y,
              scaledArea.x + scaledArea.width,
              scaledArea.y + scaledArea.height,
              blockSize
            );
            break;
          case "colorBlock":
            this.applyColorBlockMosaic(
              ctx,
              scaledArea.x,
              scaledArea.y,
              scaledArea.x + scaledArea.width,
              scaledArea.y + scaledArea.height,
              blockSize
            );
            break;
        }
      });
      common_vendor.index.__f__("log", "at pages/tools/image-mosaic-maker.vue:541", "马赛克效果应用完成");
    },
    // 像素化马赛克
    applyPixelMosaic(ctx, startX, startY, endX, endY, blockSize) {
      const colors = [
        "#808080",
        // 中灰色
        "#666666",
        // 深灰色
        "#999999",
        // 浅灰色
        "#737373"
        // 中深灰色
      ];
      const opacity = (100 - this.mosaicOpacity) / 100;
      for (let x = startX; x < endX; x += blockSize) {
        for (let y = startY; y < endY; y += blockSize) {
          const colorIndex = Math.floor(Math.random() * colors.length);
          const color = colors[colorIndex];
          const r = parseInt(color.slice(1, 3), 16);
          const g = parseInt(color.slice(3, 5), 16);
          const b = parseInt(color.slice(5, 7), 16);
          ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
          ctx.fillRect(x, y, Math.min(blockSize, endX - x), Math.min(blockSize, endY - y));
        }
      }
    },
    // 模糊化马赛克
    applyBlurMosaic(ctx, startX, startY, endX, endY, blockSize) {
      const opacity = (100 - this.mosaicOpacity) / 100;
      for (let x = startX; x < endX; x += blockSize) {
        for (let y = startY; y < endY; y += blockSize) {
          const grayValue = 180 + Math.floor(Math.random() * 50);
          ctx.fillStyle = `rgba(${grayValue}, ${grayValue}, ${grayValue}, ${opacity})`;
          ctx.fillRect(x, y, Math.min(blockSize, endX - x), Math.min(blockSize, endY - y));
        }
      }
    },
    // 彩色块马赛克
    applyColorBlockMosaic(ctx, startX, startY, endX, endY, blockSize) {
      const colors = [
        "#FF6384",
        // 红色
        "#36A2EB",
        // 蓝色
        "#FFCE56",
        // 黄色
        "#4BC0C0",
        // 青色
        "#9966FF",
        // 紫色
        "#FF9F40"
        // 橙色
      ];
      const opacity = (100 - this.mosaicOpacity) / 100;
      for (let x = startX; x < endX; x += blockSize) {
        for (let y = startY; y < endY; y += blockSize) {
          const colorIndex = Math.floor(Math.random() * colors.length);
          const color = colors[colorIndex];
          const r = parseInt(color.slice(1, 3), 16);
          const g = parseInt(color.slice(3, 5), 16);
          const b = parseInt(color.slice(5, 7), 16);
          ctx.fillStyle = `rgba(${r}, ${g}, ${b}, ${opacity})`;
          ctx.fillRect(x, y, Math.min(blockSize, endX - x), Math.min(blockSize, endY - y));
        }
      }
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 图片加载完成后设置画布尺寸
    onImageLoad(e) {
      const query = common_vendor.index.createSelectorQuery().in(this);
      query.select(".preview-image").boundingClientRect((data) => {
        this.imageDisplayWidth = data.width;
        this.imageDisplayHeight = data.height;
        this.initTouchCanvas();
      }).exec();
    },
    // 修改初始化触摸画布方法
    initTouchCanvas() {
      this.touchCtx = common_vendor.index.createCanvasContext("touchCanvas", this);
      this.touchCtx.lineWidth = 2;
      this.startDashAnimation();
    },
    // 添加动画控制方法
    startDashAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer);
      }
      this.animationTimer = setInterval(() => {
        this.dashOffset = (this.dashOffset + 1) % 16;
        this.redrawSelectedAreas();
      }, 100);
    },
    // 停止动画
    stopDashAnimation() {
      if (this.animationTimer) {
        clearInterval(this.animationTimer);
        this.animationTimer = null;
      }
    },
    // 开始触摸
    onTouchStart(e) {
      const touch = e.touches[0];
      this.isDrawing = true;
      this.startX = touch.x;
      this.startY = touch.y;
    },
    // 触摸移动
    onTouchMove(e) {
      if (!this.isDrawing)
        return;
      const touch = e.touches[0];
      const currentX = touch.x;
      const currentY = touch.y;
      this.touchCtx.clearRect(0, 0, this.imageDisplayWidth, this.imageDisplayHeight);
      this.drawSelectionRect(
        this.startX,
        this.startY,
        currentX - this.startX,
        currentY - this.startY
      );
      this.drawSelectedAreas();
      this.touchCtx.draw();
    },
    // 结束触摸
    onTouchEnd(e) {
      if (!this.isDrawing)
        return;
      const touch = e.changedTouches[0];
      const endX = touch.x;
      const endY = touch.y;
      const area = {
        x: Math.min(this.startX, endX),
        y: Math.min(this.startY, endY),
        width: Math.abs(endX - this.startX),
        height: Math.abs(endY - this.startY)
      };
      if (area.width > 10 && area.height > 10) {
        this.selectedAreas.push(area);
      }
      this.isDrawing = false;
      this.touchCtx.clearRect(0, 0, this.imageDisplayWidth, this.imageDisplayHeight);
      this.drawSelectedAreas();
      this.touchCtx.draw();
    },
    // 绘制选择框
    drawSelectionRect(x, y, width, height) {
      this.touchCtx.save();
      this.touchCtx.setLineDash([4, 4]);
      this.touchCtx.lineDashOffset = -this.dashOffset;
      this.touchCtx.strokeStyle = "#ff0000";
      this.touchCtx.beginPath();
      this.touchCtx.rect(x, y, width, height);
      this.touchCtx.stroke();
      this.touchCtx.restore();
    },
    // 绘制已选择的区域
    drawSelectedAreas() {
      this.selectedAreas.forEach((area) => {
        this.drawSelectionRect(area.x, area.y, area.width, area.height);
      });
    },
    // 重绘所有选择区域
    redrawSelectedAreas() {
      this.touchCtx.clearRect(0, 0, this.imageDisplayWidth, this.imageDisplayHeight);
      this.drawSelectedAreas();
      this.touchCtx.draw();
    },
    // 重置处理
    resetProcess() {
      this.processedImageUrl = "";
      this.isProcessing = false;
      this.selectedAreas = [];
      this.stopDashAnimation();
      if (this.touchCtx) {
        this.touchCtx.clearRect(0, 0, this.imageDisplayWidth, this.imageDisplayHeight);
        this.touchCtx.draw();
      }
    },
    // 保存图片
    saveImage() {
      if (!this.processedImageUrl) {
        common_vendor.index.showToast({
          title: "没有可保存的图片",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "保存中..."
      });
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.processedImageUrl,
        success: () => {
          common_vendor.index.hideLoading();
          common_vendor.index.showToast({
            title: "保存成功！",
            icon: "success"
          });
        },
        fail: (err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/image-mosaic-maker.vue:794", "保存失败:", err);
          if (err.errMsg.includes("auth")) {
            common_vendor.index.showModal({
              title: "需要授权",
              content: "需要您授权保存图片到相册",
              success: (res) => {
                if (res.confirm) {
                  common_vendor.index.openSetting();
                }
              }
            });
          } else {
            common_vendor.index.showToast({
              title: "保存失败",
              icon: "none"
            });
          }
        }
      });
    },
    // 组件销毁时清理
    beforeDestroy() {
      this.stopDashAnimation();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.selectedImage
  }, $data.selectedImage ? {
    b: common_vendor.t($data.selectedImage.name)
  } : {}, {
    c: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    d: $data.selectedImage
  }, $data.selectedImage ? {
    e: $data.selectedImage.url,
    f: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    g: $data.imageDisplayWidth + "px",
    h: $data.imageDisplayHeight + "px",
    i: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    j: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    k: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args))
  } : {}, {
    l: $data.selectedImage
  }, $data.selectedImage ? {
    m: common_vendor.f($data.mosaicTypes, (type, k0, i0) => {
      return {
        a: common_vendor.t(type.icon),
        b: common_vendor.t(type.name),
        c: type.id,
        d: $data.mosaicType === type.id ? 1 : "",
        e: common_vendor.o(($event) => $options.selectMosaicType(type.id), type.id)
      };
    }),
    n: $data.mosaicIntensity,
    o: common_vendor.o((...args) => $options.onIntensityChange && $options.onIntensityChange(...args)),
    p: common_vendor.t($data.mosaicIntensity),
    q: common_vendor.f($data.intensityPresets, (preset, k0, i0) => {
      return {
        a: common_vendor.t(preset.name),
        b: preset.value,
        c: $data.mosaicIntensity === preset.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setIntensity(preset.value), preset.value)
      };
    }),
    r: $data.mosaicOpacity,
    s: common_vendor.o((...args) => $options.onOpacityChange && $options.onOpacityChange(...args)),
    t: common_vendor.t($data.mosaicOpacity),
    v: common_vendor.f($data.opacityPresets, (preset, k0, i0) => {
      return {
        a: common_vendor.t(preset.name),
        b: preset.value,
        c: $data.mosaicOpacity === preset.value ? 1 : "",
        d: common_vendor.o(($event) => $options.setOpacity(preset.value), preset.value)
      };
    }),
    w: common_vendor.f($data.processingModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: common_vendor.t(mode.desc),
        d: mode.id,
        e: $data.processingMode === mode.id ? 1 : "",
        f: common_vendor.o(($event) => $options.selectProcessingMode(mode.id), mode.id)
      };
    })
  } : {}, {
    x: $data.selectedImage
  }, $data.selectedImage ? {
    y: common_vendor.t($data.isProcessing ? "处理中..." : "生成马赛克效果"),
    z: $data.isProcessing ? 1 : "",
    A: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    B: $data.isProcessing
  } : {}, {
    C: $data.processedImageUrl
  }, $data.processedImageUrl ? {
    D: $data.selectedImage.url,
    E: $data.processedImageUrl,
    F: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args)),
    G: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args))
  } : {}, {
    H: $data.canvasWidth + "px",
    I: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2d931c16"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-mosaic-maker.js.map
