"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      randomMusic: "",
      musicNotes: [
        "♪",
        "♫",
        "♬",
        "♭",
        "♮",
        "♯",
        "𝄞",
        "𝄢",
        "𝄡",
        "𝄟",
        "♩",
        "♪",
        "♫",
        "♬",
        "♭",
        "♮",
        "♯",
        "𝄞",
        "𝄢",
        "𝄡",
        "♩♪",
        "♪♫",
        "♫♬",
        "♬♪",
        "♪♩",
        "♫♪",
        "♬♫",
        "♪♬",
        "♩♪♫",
        "♪♫♬",
        "♫♬♪",
        "♬♪♩",
        "♪♩♫",
        "♫♪♬",
        "♬♫♩"
      ],
      musicDecorations: [
        "♪～♪～♪",
        "♫♪♫♪♫",
        "♬♪♬♪♬",
        "♪♫♪♫♪",
        "♩♪♫♬♪",
        "♪♬♪♬♪",
        "♫♪♬♪♫",
        "♬♫♪♫♬",
        "♪♫♬♪♫♬",
        "♬♪♫♪♬♪♫",
        "♪♬♪♫♪♬♪",
        "♫♪♬♫♪♬♫",
        "♪♫♪♬♪♫♪♬",
        "♬♪♫♬♪♫♬♪",
        "♪♬♫♪♬♫♪♬♫"
      ],
      musicEmojis: [
        "🎵",
        "🎶",
        "🎼",
        "🎤",
        "🎧",
        "🎺",
        "🎷",
        "🎸",
        "🎹",
        "🥁",
        "🎻",
        "🎺",
        "🎪",
        "🎭",
        "🎨",
        "🎬",
        "🎵🎶",
        "🎼🎤",
        "🎧🎺",
        "🎷🎸",
        "🎹🥁",
        "🎻🎺",
        "🎵🎶🎼",
        "🎤🎧🎺",
        "🎷🎸🎹",
        "🥁🎻🎺"
      ],
      musicStyles: [
        "ᴍᴜsɪᴄ",
        "ＭＵＳＩＣ",
        "ʍnsıɔ",
        "ᗰᑌᔕIᑕ",
        "ᴹᵁˢᴵᶜ",
        "ᴍᴜꜱɪᴄ",
        "ᵐᵘˢⁱᶜ",
        "ₘᵤₛᵢc",
        "ᴍᴜsɪᴄ ♪",
        "ＭＵＳＩＣ ♫",
        "ʍnsıɔ ♬",
        "ᗰᑌᔕIᑕ ♪",
        "ᴹᵁˢᴵᶜ ♫",
        "ᴍᴜꜱɪᴄ ♬",
        "ᵐᵘˢⁱᶜ ♪",
        "ₘᵤₛᵢc ♫"
      ]
    };
  },
  methods: {
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    generateRandomMusic() {
      const allMusic = [...this.musicNotes, ...this.musicDecorations, ...this.musicEmojis, ...this.musicStyles];
      const randomIndex = Math.floor(Math.random() * allMusic.length);
      this.randomMusic = allMusic[randomIndex];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.musicNotes, (note, index, i0) => {
      return {
        a: common_vendor.t(note),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(note), index)
      };
    }),
    b: common_vendor.f($data.musicDecorations, (decoration, index, i0) => {
      return {
        a: common_vendor.t(decoration),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(decoration), index)
      };
    }),
    c: common_vendor.f($data.musicEmojis, (emoji, index, i0) => {
      return {
        a: common_vendor.t(emoji),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(emoji), index)
      };
    }),
    d: common_vendor.f($data.musicStyles, (style, index, i0) => {
      return {
        a: common_vendor.t(style),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(style), index)
      };
    }),
    e: common_vendor.o((...args) => $options.generateRandomMusic && $options.generateRandomMusic(...args)),
    f: $data.randomMusic
  }, $data.randomMusic ? {
    g: common_vendor.t($data.randomMusic),
    h: common_vendor.o(($event) => $options.copyText($data.randomMusic))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a4931e09"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/music-text.js.map
