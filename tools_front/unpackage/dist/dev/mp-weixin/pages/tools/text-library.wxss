/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-fc4ba18f {
  display: flex;
}
.flex-1.data-v-fc4ba18f {
  flex: 1;
}
.items-center.data-v-fc4ba18f {
  align-items: center;
}
.justify-center.data-v-fc4ba18f {
  justify-content: center;
}
.justify-between.data-v-fc4ba18f {
  justify-content: space-between;
}
.text-center.data-v-fc4ba18f {
  text-align: center;
}
.rounded.data-v-fc4ba18f {
  border-radius: 3px;
}
.rounded-lg.data-v-fc4ba18f {
  border-radius: 6px;
}
.shadow.data-v-fc4ba18f {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-fc4ba18f {
  padding: 16rpx;
}
.m-4.data-v-fc4ba18f {
  margin: 16rpx;
}
.mb-4.data-v-fc4ba18f {
  margin-bottom: 16rpx;
}
.mt-4.data-v-fc4ba18f {
  margin-top: 16rpx;
}
.text-library.data-v-fc4ba18f {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-fc4ba18f {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-fc4ba18f {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-fc4ba18f {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-fc4ba18f {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-fc4ba18f {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-fc4ba18f {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

/* 分类标签横向滚动样式 */
.category-tabs.data-v-fc4ba18f {
  margin-bottom: 32rpx;
  border-radius: 20rpx;
  background: #f8fafc;
  padding: 4rpx;
}
.tabs-scroll-view.data-v-fc4ba18f {
  width: 100%;
  white-space: nowrap;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.tabs-scroll-view.data-v-fc4ba18f ::-webkit-scrollbar {
  display: none;
}
.tabs-container.data-v-fc4ba18f {
  display: inline-flex;
  padding: 12rpx 8rpx;
}
.tab-item.data-v-fc4ba18f {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin: 0 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
  background: transparent;
  border-radius: 50rpx;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.tab-item.active.data-v-fc4ba18f {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  color: #0284c7;
  box-shadow: 0 2rpx 12rpx rgba(2, 132, 199, 0.15);
  transform: translateY(-2rpx);
}
.tab-icon.data-v-fc4ba18f {
  font-size: 28rpx;
  margin-right: 8rpx;
}
.tab-label.data-v-fc4ba18f {
  font-size: 28rpx;
}
.content-section.data-v-fc4ba18f, .selected-section.data-v-fc4ba18f, .help-section.data-v-fc4ba18f {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-fc4ba18f {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-fc4ba18f {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-fc4ba18f {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.random-button.data-v-fc4ba18f {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-radius: 50rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.2);
}
.random-button.data-v-fc4ba18f:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(16, 185, 129, 0.3);
}
.random-icon.data-v-fc4ba18f {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.random-text.data-v-fc4ba18f {
  font-size: 24rpx;
}
.text-grid.data-v-fc4ba18f {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.text-item.data-v-fc4ba18f {
  position: relative;
  padding: 24rpx;
  background: linear-gradient(to right, #f8fafc, #ffffff);
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.text-item.data-v-fc4ba18f:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}
.text-content.data-v-fc4ba18f {
  font-size: 28rpx;
  line-height: 1.6;
  color: #334155;
  flex: 1;
  padding-right: 16rpx;
}
.text-actions.data-v-fc4ba18f {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.copy-btn.data-v-fc4ba18f, .share-btn.data-v-fc4ba18f {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}
.copy-btn.data-v-fc4ba18f:hover, .share-btn.data-v-fc4ba18f:hover {
  transform: scale(1.05) translateY(-2rpx);
}
.copy-btn.data-v-fc4ba18f {
  background: linear-gradient(135deg, #e0f2fe, #bae6fd);
  box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.15);
}
.copy-btn.data-v-fc4ba18f:hover {
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.25);
}
.share-btn.data-v-fc4ba18f {
  background: linear-gradient(135deg, #ddd6fe, #c4b5fd);
  box-shadow: 0 2rpx 8rpx rgba(124, 58, 237, 0.15);
}
.share-btn.data-v-fc4ba18f:hover {
  box-shadow: 0 4rpx 12rpx rgba(124, 58, 237, 0.25);
}
.copy-icon.data-v-fc4ba18f {
  font-size: 28rpx;
  color: #0284c7;
}
.share-icon.data-v-fc4ba18f {
  font-size: 28rpx;
  color: #6d28d9;
}

/* 优化当前选中文案样式 */
.selected-card.data-v-fc4ba18f {
  background: linear-gradient(135deg, #f5f3ff, #ede9fe);
  border-radius: 20rpx;
  padding: 40rpx;
  border: 1.5rpx solid #c4b5fd;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(124, 58, 237, 0.1);
  position: relative;
  overflow: hidden;
}
.selected-card.data-v-fc4ba18f::before {
  content: "";
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, rgba(167, 139, 250, 0.3), rgba(196, 181, 253, 0.1));
  border-radius: 50%;
  z-index: 0;
}
.selected-text.data-v-fc4ba18f {
  font-size: 32rpx;
  line-height: 1.6;
  color: #5b21b6;
  margin-bottom: 32rpx;
  display: block;
  font-weight: 500;
  position: relative;
  z-index: 1;
}
.selected-actions.data-v-fc4ba18f {
  display: flex;
  gap: 20rpx;
  position: relative;
  z-index: 1;
  flex-wrap: wrap;
}
.action-btn.data-v-fc4ba18f {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 32rpx;
  border-radius: 50rpx;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
  max-width: 90%;
  flex: 1;
  min-width: 200rpx;
}
.action-btn.primary.data-v-fc4ba18f {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  color: white;
  box-shadow: 0 6rpx 16rpx rgba(124, 58, 237, 0.25);
}
.action-btn.primary.data-v-fc4ba18f:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(124, 58, 237, 0.35);
}
.action-btn.secondary.data-v-fc4ba18f {
  background: rgba(255, 255, 255, 0.8);
  color: #6d28d9;
  border: 1.5rpx solid #c4b5fd;
}
.action-btn.secondary.data-v-fc4ba18f:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(124, 58, 237, 0.1);
}
.btn-icon.data-v-fc4ba18f {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.btn-text.data-v-fc4ba18f {
  font-size: 28rpx;
}
.help-content.data-v-fc4ba18f {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-fc4ba18f {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-fc4ba18f:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-fc4ba18f {
    padding: 30rpx 20rpx;
}
.tab-item.data-v-fc4ba18f {
    padding: 14rpx 20rpx;
    margin: 0 6rpx;
}
.selected-card.data-v-fc4ba18f {
    padding: 32rpx 24rpx;
}
.selected-actions.data-v-fc4ba18f {
    flex-direction: column;
    gap: 16rpx;
}
.action-btn.data-v-fc4ba18f {
    width: 100%;
    padding: 16rpx 24rpx;
}
.btn-text.data-v-fc4ba18f {
    font-size: 26rpx;
}
}