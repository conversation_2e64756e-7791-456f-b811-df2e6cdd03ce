/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-a2cc706b {
  display: flex;
}
.flex-1.data-v-a2cc706b {
  flex: 1;
}
.items-center.data-v-a2cc706b {
  align-items: center;
}
.justify-center.data-v-a2cc706b {
  justify-content: center;
}
.justify-between.data-v-a2cc706b {
  justify-content: space-between;
}
.text-center.data-v-a2cc706b {
  text-align: center;
}
.rounded.data-v-a2cc706b {
  border-radius: 3px;
}
.rounded-lg.data-v-a2cc706b {
  border-radius: 6px;
}
.shadow.data-v-a2cc706b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-a2cc706b {
  padding: 16rpx;
}
.m-4.data-v-a2cc706b {
  margin: 16rpx;
}
.mb-4.data-v-a2cc706b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-a2cc706b {
  margin-top: 16rpx;
}
.image-stitcher.data-v-a2cc706b {
  min-height: 100vh;
  background: #f8f9fa;
}
.image-stitcher .content.data-v-a2cc706b {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.image-stitcher .section-card.data-v-a2cc706b {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.image-stitcher .section-card .card-header.data-v-a2cc706b {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.image-stitcher .section-card .card-header .header-icon.data-v-a2cc706b {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.image-stitcher .section-card .card-header .header-title.data-v-a2cc706b {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.image-stitcher .section-card .card-content .upload-area.data-v-a2cc706b {
  border: 4rpx dashed #e5e7eb;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: #f9fafb;
  transition: all 0.3s;
}
.image-stitcher .section-card .card-content .upload-area.data-v-a2cc706b:active {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}
.image-stitcher .section-card .card-content .upload-area .upload-icon.data-v-a2cc706b {
  margin-bottom: 24rpx;
}
.image-stitcher .section-card .card-content .upload-area .upload-icon .icon-text.data-v-a2cc706b {
  font-size: 80rpx;
  opacity: 0.6;
}
.image-stitcher .section-card .card-content .upload-area .upload-title.data-v-a2cc706b {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}
.image-stitcher .section-card .card-content .upload-area .upload-desc.data-v-a2cc706b {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 24rpx;
}
.image-stitcher .section-card .card-content .upload-area .selected-count .count-text.data-v-a2cc706b {
  font-size: 28rpx;
  color: #3b82f6;
  font-weight: 600;
}
.image-stitcher .section-card .card-content .image-preview-grid.data-v-a2cc706b {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 32rpx;
}
.image-stitcher .section-card .card-content .image-preview-grid .preview-item.data-v-a2cc706b {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
}
.image-stitcher .section-card .card-content .image-preview-grid .preview-item .preview-image.data-v-a2cc706b {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}
.image-stitcher .section-card .card-content .image-preview-grid .preview-item .remove-btn.data-v-a2cc706b {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.image-stitcher .section-card .card-content .image-preview-grid .preview-item .remove-btn .remove-icon.data-v-a2cc706b {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}
.image-stitcher .section-card .card-content .image-preview-grid .preview-item .image-index.data-v-a2cc706b {
  position: absolute;
  bottom: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.image-stitcher .mode-grid.data-v-a2cc706b {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}
.image-stitcher .mode-grid .mode-item.data-v-a2cc706b {
  padding: 32rpx 24rpx;
  border: 3rpx solid #e5e7eb;
  border-radius: 20rpx;
  text-align: center;
  background: white;
  transition: all 0.2s;
}
.image-stitcher .mode-grid .mode-item.active.data-v-a2cc706b {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.15);
}
.image-stitcher .mode-grid .mode-item .mode-icon.data-v-a2cc706b {
  margin-bottom: 16rpx;
}
.image-stitcher .mode-grid .mode-item .mode-icon .icon-text.data-v-a2cc706b {
  font-size: 48rpx;
}
.image-stitcher .mode-grid .mode-item .mode-name.data-v-a2cc706b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}
.image-stitcher .mode-grid .mode-item .mode-desc.data-v-a2cc706b {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}
.image-stitcher .grid-settings .setting-item.data-v-a2cc706b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
}
.image-stitcher .grid-settings .setting-item .setting-label.data-v-a2cc706b {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}
.image-stitcher .grid-settings .setting-item .setting-control.data-v-a2cc706b {
  display: flex;
  align-items: center;
  gap: 24rpx;
}
.image-stitcher .grid-settings .setting-item .setting-control .control-btn.data-v-a2cc706b {
  width: 60rpx;
  height: 60rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.image-stitcher .grid-settings .setting-item .setting-control .control-btn.data-v-a2cc706b:active {
  background: #e5e7eb;
  transform: scale(0.95);
}
.image-stitcher .grid-settings .setting-item .setting-control .control-btn .control-icon.data-v-a2cc706b {
  font-size: 32rpx;
  font-weight: bold;
  color: #374151;
}
.image-stitcher .grid-settings .setting-item .setting-control .control-value.data-v-a2cc706b {
  font-size: 32rpx;
  font-weight: 600;
  color: #3b82f6;
  min-width: 60rpx;
  text-align: center;
}
.image-stitcher .stitch-button.data-v-a2cc706b {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.image-stitcher .stitch-button.data-v-a2cc706b:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
}
.image-stitcher .stitch-button.disabled.data-v-a2cc706b {
  opacity: 0.6;
  background: #d1d5db;
  box-shadow: none;
}
.image-stitcher .result-container .result-image.data-v-a2cc706b {
  width: 100%;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.image-stitcher .result-container .result-actions.data-v-a2cc706b {
  display: flex;
  gap: 16rpx;
}
.image-stitcher .result-container .result-actions .action-button.data-v-a2cc706b {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 32rpx 24rpx;
  border-radius: 16rpx;
  border: none;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s;
}
.image-stitcher .result-container .result-actions .action-button.secondary.data-v-a2cc706b {
  background: #f3f4f6;
  color: #374151;
}
.image-stitcher .result-container .result-actions .action-button.secondary.data-v-a2cc706b:active {
  background: #e5e7eb;
  transform: scale(0.98);
}
.image-stitcher .result-container .result-actions .action-button.primary.data-v-a2cc706b {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}
.image-stitcher .result-container .result-actions .action-button.primary.data-v-a2cc706b:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.2);
}
.image-stitcher .result-container .result-actions .action-button .button-icon.data-v-a2cc706b {
  font-size: 24rpx;
}
.image-stitcher .result-container .result-actions .action-button .button-text.data-v-a2cc706b {
  font-size: 28rpx;
}
.image-stitcher .usage-list .usage-item.data-v-a2cc706b {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.image-stitcher .usage-list .usage-item.data-v-a2cc706b:last-child {
  margin-bottom: 0;
}
.image-stitcher .hidden-canvas.data-v-a2cc706b {
  position: fixed;
  top: -9999rpx;
  left: -9999rpx;
  z-index: -1;
}