/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-796a2d06 {
  display: flex;
}
.flex-1.data-v-796a2d06 {
  flex: 1;
}
.items-center.data-v-796a2d06 {
  align-items: center;
}
.justify-center.data-v-796a2d06 {
  justify-content: center;
}
.justify-between.data-v-796a2d06 {
  justify-content: space-between;
}
.text-center.data-v-796a2d06 {
  text-align: center;
}
.rounded.data-v-796a2d06 {
  border-radius: 3px;
}
.rounded-lg.data-v-796a2d06 {
  border-radius: 6px;
}
.shadow.data-v-796a2d06 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-796a2d06 {
  padding: 16rpx;
}
.m-4.data-v-796a2d06 {
  margin: 16rpx;
}
.mb-4.data-v-796a2d06 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-796a2d06 {
  margin-top: 16rpx;
}
.nine-grid-cutter.data-v-796a2d06 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-796a2d06 {
  padding: 30rpx;
  max-width: 1000rpx;
  margin: 0 auto;
}
.card.data-v-796a2d06 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card .card-header.data-v-796a2d06 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card .card-header .upload-icon.data-v-796a2d06, .card .card-header .info-icon.data-v-796a2d06 {
  font-size: 32rpx;
  color: #3b82f6;
  margin-right: 16rpx;
}
.card .card-header .header-title.data-v-796a2d06 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card .card-content .upload-area.data-v-796a2d06 {
  border: 4rpx dashed #d1d5db;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}
.card .card-content .upload-area .grid-icon.data-v-796a2d06 {
  font-size: 96rpx;
  color: #9ca3af;
  margin-bottom: 30rpx;
}
.card .card-content .upload-area .upload-text.data-v-796a2d06 {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 30rpx;
}
.card .card-content .upload-area .file-input-wrapper.data-v-796a2d06 {
  position: relative;
}
.card .card-content .upload-area .file-label.data-v-796a2d06 {
  display: inline-block;
  padding: 24rpx 48rpx;
  background: #3b82f6;
  color: white;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}
.card .card-content .upload-area .file-label.data-v-796a2d06:active {
  transform: scale(0.96);
  background: #2563eb;
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.3);
}
.card .card-content .upload-area .file-label .label-text.data-v-796a2d06 {
  font-size: 28rpx;
  font-weight: 500;
}
.card .card-content .preview-container.data-v-796a2d06 {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}
.card .card-content .preview-container .preview-image.data-v-796a2d06 {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.card .card-content .preview-container .grid-overlay.data-v-796a2d06 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  pointer-events: none;
}
.card .card-content .preview-container .grid-overlay .grid-cell.data-v-796a2d06 {
  border: 1rpx solid rgba(255, 255, 255, 0.7);
}
.card .card-content .cut-btn.data-v-796a2d06 {
  width: 100%;
  padding: 48rpx 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 16rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}
.card .card-content .cut-btn.data-v-796a2d06:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}
.card .card-content .cut-btn.disabled.data-v-796a2d06 {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.card .card-content .cut-btn.disabled.data-v-796a2d06:active {
  transform: none;
  box-shadow: none;
  background: #e5e7eb;
}
.card .card-content .cut-btn .btn-text.data-v-796a2d06 {
  font-size: 32rpx;
  font-weight: 600;
}
.card .card-content .result-grid.data-v-796a2d06 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-bottom: 30rpx;
}
.card .card-content .result-grid .result-item.data-v-796a2d06 {
  aspect-ratio: 1;
  background: #f3f4f6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}
.card .card-content .result-grid .result-item.data-v-796a2d06:active {
  transform: scale(0.98);
  box-shadow: 0 0 0 2rpx #3b82f6;
}
.card .card-content .result-grid .result-item .img-container.data-v-796a2d06 {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}
.card .card-content .result-grid .result-item .result-preview.data-v-796a2d06 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: center center;
}
.card .card-content .result-grid .result-item .item-number.data-v-796a2d06 {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  z-index: 10;
}
.card .card-content .download-all-btn.data-v-796a2d06 {
  width: 100%;
  padding: 32rpx 0;
  background: #3b82f6;
  color: white;
  border-radius: 16rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  transition: all 0.2s ease;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
}
.card .card-content .download-all-btn.data-v-796a2d06:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
  background: #2563eb;
}
.card .card-content .download-all-btn .btn-icon.data-v-796a2d06 {
  font-size: 28rpx;
}
.card .card-content .download-all-btn .btn-text.data-v-796a2d06 {
  font-size: 28rpx;
  font-weight: 500;
}
.card .card-content .instructions .instruction-item.data-v-796a2d06 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  line-height: 1.6;
}