
.bg-white.data-v-85ea2c4b {
  background: #fff;
}
.rounded-lg.data-v-85ea2c4b {
  border-radius: 20rpx;
}
.shadow-sm.data-v-85ea2c4b {
  box-shadow: 0 6rpx 24rpx rgba(59,130,246,0.06);
}
.p-4.data-v-85ea2c4b {
  padding: 32rpx;
}
.p-6.data-v-85ea2c4b {
  padding: 48rpx;
}
.mb-3.data-v-85ea2c4b {
  margin-bottom: 24rpx;
}
.space-y-4.data-v-85ea2c4b {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.flex.data-v-85ea2c4b {
  display: flex;
}
.items-center.data-v-85ea2c4b {
  align-items: center;
}
.justify-between.data-v-85ea2c4b {
  justify-content: space-between;
}
.gap-2.data-v-85ea2c4b {
  gap: 16rpx;
}
.grid.data-v-85ea2c4b {
  display: grid;
}
.grid-cols-1.data-v-85ea2c4b {
  grid-template-columns: 1fr;
}
.grid-cols-2.data-v-85ea2c4b {
  grid-template-columns: repeat(2, 1fr);
}
.text-base.data-v-85ea2c4b {
  font-size: 32rpx;
  line-height: 1.5;
}
.font-semibold.data-v-85ea2c4b {
  font-weight: 600;
}
.font-medium.data-v-85ea2c4b {
  font-weight: 500;
}
.text-blue-500.data-v-85ea2c4b {
  color: #3b82f6;
}
.text-purple-500.data-v-85ea2c4b {
  color: #a78bfa;
}
.text-green-500.data-v-85ea2c4b {
  color: #10b981;
}
.text-orange-500.data-v-85ea2c4b {
  color: #f59e42;
}
.text-sm.data-v-85ea2c4b {
  font-size: 26rpx;
}
.text-gray-600.data-v-85ea2c4b {
  color: #6b7280;
}
.text-gray-400.data-v-85ea2c4b {
  color: #9ca3af;
}
.bg-gray-50.data-v-85ea2c4b {
  background: #f8f9fa;
}
.bg-gray-100.data-v-85ea2c4b {
  background: #f3f4f6;
}
.bg-gray-200.data-v-85ea2c4b {
  background: #e5e7eb;
}
.bg-green-50.data-v-85ea2c4b {
  background: #ecfdf5;
}
.bg-red-50.data-v-85ea2c4b {
  background: #fef2f2;
}
.bg-green-500.data-v-85ea2c4b {
  background: #10b981;
}
.bg-red-500.data-v-85ea2c4b {
  background: #ef4444;
}
.bg-orange-500.data-v-85ea2c4b {
  background: #f59e42;
}
.bg-purple-500.data-v-85ea2c4b {
  background: #a78bfa;
}
.bg-gray-400.data-v-85ea2c4b {
  background: #9ca3af;
}
.border.data-v-85ea2c4b {
  border-width: 1px;
}
.border-gray-300.data-v-85ea2c4b {
  border-color: #e5e7eb;
}
.border-green-200.data-v-85ea2c4b {
  border-color: #bbf7d0;
}
.border-red-200.data-v-85ea2c4b {
  border-color: #fecaca;
}
.rounded-full.data-v-85ea2c4b {
  border-radius: 9999px;
}
.px-3.data-v-85ea2c4b {
  padding-left: 24rpx;
  padding-right: 24rpx;
}
.px-4.data-v-85ea2c4b {
  padding-left: 32rpx;
  padding-right: 32rpx;
}
.py-1.data-v-85ea2c4b {
  padding-top: 8rpx;
  padding-bottom: 8rpx;
}
.py-2.data-v-85ea2c4b {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.text-white.data-v-85ea2c4b {
  color: #fff;
}
.hover-bg-blue-600.data-v-85ea2c4b:active {
  background: #2563eb;
}
.hover-bg-purple-600.data-v-85ea2c4b:active {
  background: #7c3aed;
}
.hover-bg-orange-600.data-v-85ea2c4b:active {
  background: #ea580c;
}
.disabled-opacity-50.data-v-85ea2c4b:disabled {
  opacity: 0.5;
}
input[type="text"].data-v-85ea2c4b, input[type="number"].data-v-85ea2c4b {
  width: 100%;
  height: 60rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  padding: 0 24rpx;
  background: #f8f9fa;
  color: #333;
  box-sizing: border-box;
  transition: border-color 0.2s, box-shadow 0.2s;
}
input[type="text"].data-v-85ea2c4b:focus, input[type="number"].data-v-85ea2c4b:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4rpx rgba(59,130,246,0.08);
  outline: none;
}
button.data-v-85ea2c4b {
  height: 60rpx;
  padding: 0 32rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
  outline: none;
  cursor: pointer;
  border-radius: 12rpx;
  color: #fff;
  background: #3b82f6;
  transition: background 0.2s, box-shadow 0.2s, color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}
button.data-v-85ea2c4b:disabled, .disabled-opacity-50.data-v-85ea2c4b:disabled {
  color: #bdbdbd !important;
  background: #f3f4f6 !important;
  cursor: not-allowed;
  box-shadow: none;
}
.scan-btn.data-v-85ea2c4b {
  min-width: 160rpx;
  height: 56rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, #a78bfa 0%, #7c3aed 100%);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(124,58,237,0.2);
  float: right;
}
.scan-btn.data-v-85ea2c4b:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(124,58,237,0.15);
}
.scan-btn.data-v-85ea2c4b:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.text-right.data-v-85ea2c4b {
  text-align: right;
}
.port-item.data-v-85ea2c4b {
  padding: 0;
  margin: 0;
}
.status-width.data-v-85ea2c4b {
  min-width: 120rpx;
  padding-right: 0;
}
.status-text.data-v-85ea2c4b {
  color: #666;
  padding-right: 32rpx;
}
.scan-range-btn.data-v-85ea2c4b {
  min-width: 160rpx;
  height: 60rpx;
  padding: 0 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #fff;
  background: linear-gradient(135deg, #f59e42 0%, #ea580c 100%);
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2rpx 12rpx rgba(234,88,12,0.2);
}
.scan-range-btn.data-v-85ea2c4b:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(234,88,12,0.15);
}
.scan-range-btn.data-v-85ea2c4b:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
