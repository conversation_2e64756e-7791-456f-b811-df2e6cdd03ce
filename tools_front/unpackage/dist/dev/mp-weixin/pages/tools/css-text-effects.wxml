<view class="text-effects-container data-v-dda9f4f9"><view class="header-card data-v-dda9f4f9"><view class="header-content data-v-dda9f4f9"><view class="header-icon data-v-dda9f4f9">✨</view><view class="header-info data-v-dda9f4f9"><text class="header-title data-v-dda9f4f9">CSS文本效果生成器</text><text class="header-subtitle data-v-dda9f4f9">创建炫酷的文字特效和样式</text></view></view></view><view class="input-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">📝 输入文字</text></view><view class="input-content data-v-dda9f4f9"><input class="text-input data-v-dda9f4f9" placeholder="输入要添加效果的文字" bindinput="{{a}}" value="{{b}}"/><view class="input-actions data-v-dda9f4f9"><button class="action-btn data-v-dda9f4f9" bindtap="{{c}}"><text class="btn-icon data-v-dda9f4f9">🎲</text><text class="btn-text data-v-dda9f4f9">随机文字</text></button><button class="action-btn data-v-dda9f4f9" bindtap="{{d}}"><text class="btn-icon data-v-dda9f4f9">🗑️</text><text class="btn-text data-v-dda9f4f9">清空</text></button></view></view></view><view class="effects-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">🎨 效果选择</text></view><view class="effects-grid data-v-dda9f4f9"><view wx:for="{{e}}" wx:for-item="effect" wx:key="c" class="{{['effect-item', 'data-v-dda9f4f9', effect.d && 'active']}}" bindtap="{{effect.e}}"><view class="effect-preview data-v-dda9f4f9"><text class="effect-text data-v-dda9f4f9" style="{{effect.a}}">Aa</text></view><text class="effect-name data-v-dda9f4f9">{{effect.b}}</text></view></view></view><view wx:if="{{f}}" class="params-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">⚙️ 参数调整</text></view><view class="params-content data-v-dda9f4f9"><view wx:for="{{g}}" wx:for-item="param" wx:key="i" class="param-group data-v-dda9f4f9"><text class="param-label data-v-dda9f4f9">{{param.a}} ({{param.b}}{{param.c}})</text><slider value="{{param.d}}" bindchange="{{param.e}}" min="{{param.f}}" max="{{param.g}}" step="{{param.h}}" activeColor="#007AFF" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-dda9f4f9"/></view></view></view><view class="animation-type-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">🪄 动画类型</text></view><view class="animation-type-grid data-v-dda9f4f9"><view wx:for="{{h}}" wx:for-item="item" wx:key="d" class="{{['animation-type-item', 'data-v-dda9f4f9', item.e && 'active']}}" bindtap="{{item.f}}"><view class="animation-type-icon data-v-dda9f4f9">{{item.a}}</view><view class="animation-type-name data-v-dda9f4f9">{{item.b}}</view><view class="animation-type-desc data-v-dda9f4f9">{{item.c}}</view></view></view></view><view class="preview-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">👀 效果预览</text><view class="preview-actions data-v-dda9f4f9"><button class="preview-btn data-v-dda9f4f9" bindtap="{{i}}"><text class="btn-icon data-v-dda9f4f9">🎭</text><text class="btn-text data-v-dda9f4f9">换背景</text></button></view></view><view class="preview-container data-v-dda9f4f9" style="{{'background-image:' + o}}"><text class="{{['preview-text', 'data-v-dda9f4f9', 'animated-text', k]}}" style="{{l}}" key="{{m}}" bindanimationend="{{n}}">{{j}}</text></view></view><view class="code-card data-v-dda9f4f9"><view class="card-header code-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">📄 生成的CSS代码</text><button class="copy-btn data-v-dda9f4f9" bindtap="{{p}}"><text class="copy-icon data-v-dda9f4f9">📋</text><text class="copy-text data-v-dda9f4f9">复制代码</text></button></view><view class="code-content data-v-dda9f4f9"><view class="code-block data-v-dda9f4f9"><text class="code-text data-v-dda9f4f9">{{q}}</text></view></view></view><view class="tips-card data-v-dda9f4f9"><view class="card-header data-v-dda9f4f9"><text class="card-title data-v-dda9f4f9">💡 使用说明</text></view><view class="tips-content data-v-dda9f4f9"><view class="tip-item data-v-dda9f4f9"><text class="tip-title data-v-dda9f4f9">✏️ 输入文字</text><text class="tip-desc data-v-dda9f4f9">在输入框中输入要添加效果的文字内容</text></view><view class="tip-item data-v-dda9f4f9"><text class="tip-title data-v-dda9f4f9">🎨 选择效果</text><text class="tip-desc data-v-dda9f4f9">点击不同的效果卡片来应用文字特效</text></view><view class="tip-item data-v-dda9f4f9"><text class="tip-title data-v-dda9f4f9">⚙️ 调整参数</text><text class="tip-desc data-v-dda9f4f9">使用滑块调整效果的颜色、大小等参数</text></view><view class="tip-item data-v-dda9f4f9"><text class="tip-title data-v-dda9f4f9">⚠️ 兼容性提示</text><text class="tip-desc data-v-dda9f4f9">部分效果需要现代浏览器支持，建议测试兼容性</text></view></view></view></view>