<view class="animation-generator-container data-v-4506e66d"><view class="header-card data-v-4506e66d"><view class="header-content data-v-4506e66d"><view class="header-icon data-v-4506e66d">✨</view><view class="header-info data-v-4506e66d"><text class="header-title data-v-4506e66d">CSS动画生成器</text><text class="header-subtitle data-v-4506e66d">快速生成专业的CSS动画效果</text></view></view></view><view class="type-card data-v-4506e66d"><view class="card-header data-v-4506e66d"><text class="card-title data-v-4506e66d">🎭 动画类型</text></view><view class="animation-types data-v-4506e66d"><view wx:for="{{a}}" wx:for-item="template" wx:key="d" class="{{['type-item', 'data-v-4506e66d', template.e && 'active']}}" bindtap="{{template.f}}"><view class="type-icon data-v-4506e66d">{{template.a}}</view><text class="type-name data-v-4506e66d">{{template.b}}</text><text class="type-desc data-v-4506e66d">{{template.c}}</text></view></view></view><view class="params-card data-v-4506e66d"><view class="card-header data-v-4506e66d"><text class="card-title data-v-4506e66d">⚙️ 动画参数</text></view><view class="param-groups data-v-4506e66d"><view class="param-group data-v-4506e66d"><text class="param-label data-v-4506e66d">持续时间: {{b}}s</text><slider value="{{c}}" bindchange="{{d}}" min="1" max="50" step="1" activeColor="#3b82f6" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-4506e66d"/></view><view class="param-group data-v-4506e66d"><text class="param-label data-v-4506e66d">延迟时间: {{e}}s</text><slider value="{{f}}" bindchange="{{g}}" min="0" max="30" step="1" activeColor="#3b82f6" backgroundColor="#e5e7eb" block-size="20" class="param-slider data-v-4506e66d"/></view><view class="param-group data-v-4506e66d"><text class="param-label data-v-4506e66d">重复次数</text><view class="iteration-options data-v-4506e66d"><view wx:for="{{h}}" wx:for-item="option" wx:key="b" class="{{['iteration-btn', 'data-v-4506e66d', option.c && 'active']}}" bindtap="{{option.d}}"><text class="option-text data-v-4506e66d">{{option.a}}</text></view></view></view></view></view><view class="preview-card data-v-4506e66d"><view class="card-header data-v-4506e66d"><text class="card-title data-v-4506e66d">🎬 动画预览</text><view class="preview-controls data-v-4506e66d"><button class="control-btn data-v-4506e66d" bindtap="{{k}}"><text class="btn-icon data-v-4506e66d">{{i}}</text><text class="btn-text data-v-4506e66d">{{j}}</text></button><button class="control-btn data-v-4506e66d" bindtap="{{l}}"><text class="btn-icon data-v-4506e66d">🔄</text><text class="btn-text data-v-4506e66d">重播</text></button></view></view><view class="preview-stage data-v-4506e66d"><view class="{{['preview-element', 'data-v-4506e66d', m, n]}}" style="{{o}}" key="{{p}}"><text class="element-text data-v-4506e66d">示例元素</text></view></view></view><view class="code-card data-v-4506e66d"><view class="card-header code-header data-v-4506e66d"><text class="card-title data-v-4506e66d">📄 生成的CSS代码</text><button class="copy-btn right-btn data-v-4506e66d" bindtap="{{q}}"><text class="copy-icon data-v-4506e66d">📋</text><text class="copy-text data-v-4506e66d">复制代码</text></button></view><view class="code-content data-v-4506e66d"><view class="code-block data-v-4506e66d"><text class="code-text data-v-4506e66d">{{r}}</text></view></view></view><view class="tips-card data-v-4506e66d"><view class="card-header data-v-4506e66d"><text class="card-title data-v-4506e66d">💡 使用说明</text></view><view class="tips-content data-v-4506e66d"><view class="tip-item data-v-4506e66d"><text class="tip-title data-v-4506e66d">🎯 快速应用</text><text class="tip-desc data-v-4506e66d">复制CSS代码并添加到您的样式表中</text></view><view class="tip-item data-v-4506e66d"><text class="tip-title data-v-4506e66d">🏷️ 添加类名</text><text class="tip-desc data-v-4506e66d">给元素添加对应的动画类名即可生效</text></view><view class="tip-item data-v-4506e66d"><text class="tip-title data-v-4506e66d">⚡ 性能优化</text><text class="tip-desc data-v-4506e66d">建议使用transform和opacity属性获得最佳性能</text></view><view class="tip-item data-v-4506e66d"><text class="tip-title data-v-4506e66d">🔧 自定义调整</text><text class="tip-desc data-v-4506e66d">可根据实际需求调整动画参数和效果</text></view></view></view></view>