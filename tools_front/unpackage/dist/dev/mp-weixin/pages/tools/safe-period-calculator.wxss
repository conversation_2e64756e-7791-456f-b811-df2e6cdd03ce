/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-8343362b {
  display: flex;
}
.flex-1.data-v-8343362b {
  flex: 1;
}
.items-center.data-v-8343362b {
  align-items: center;
}
.justify-center.data-v-8343362b {
  justify-content: center;
}
.justify-between.data-v-8343362b {
  justify-content: space-between;
}
.text-center.data-v-8343362b {
  text-align: center;
}
.rounded.data-v-8343362b {
  border-radius: 3px;
}
.rounded-lg.data-v-8343362b {
  border-radius: 6px;
}
.shadow.data-v-8343362b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-8343362b {
  padding: 16rpx;
}
.m-4.data-v-8343362b {
  margin: 16rpx;
}
.mb-4.data-v-8343362b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-8343362b {
  margin-top: 16rpx;
}
.safe-period-page.data-v-8343362b {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-section.data-v-8343362b {
  margin-bottom: 40rpx;
}
.header-section .header-content.data-v-8343362b {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
  border-radius: 20rpx;
}
.header-section .header-content .header-icon.data-v-8343362b {
  font-size: 60rpx;
  margin-right: 20rpx;
}
.header-section .header-content .header-text.data-v-8343362b {
  flex: 1;
}
.header-section .header-content .header-text .header-title.data-v-8343362b {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-section .header-content .header-text .header-subtitle.data-v-8343362b {
  font-size: 26rpx;
  color: #666666;
}
.card.data-v-8343362b {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}
.card .card-header.data-v-8343362b {
  padding: 30rpx;
  border-bottom: 2rpx solid #f5f5f5;
}
.card .card-header .card-title.data-v-8343362b {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card .card-content.data-v-8343362b {
  padding: 30rpx;
}
.input-section.data-v-8343362b {
  margin-bottom: 30rpx;
}
.input-section .input-label.data-v-8343362b {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}
.input-section .date-picker.data-v-8343362b {
  width: 100%;
}
.input-section .date-picker .picker-display.data-v-8343362b {
  height: 88rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}
.input-section .date-picker .picker-display .picker-text.data-v-8343362b {
  font-size: 28rpx;
  color: #333333;
  line-height: 1;
}
.input-section .date-picker .picker-display .picker-icon.data-v-8343362b {
  font-size: 28rpx;
  color: #666666;
}
.input-row.data-v-8343362b {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.input-group.data-v-8343362b {
  flex: 1;
}
.input-group .input-label.data-v-8343362b {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 12rpx;
}
.input-group .input-field.data-v-8343362b {
  width: 100%;
  height: 88rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 88rpx;
}
.input-group .input-field.data-v-8343362b:focus {
  border-color: #007AFF;
  background: #ffffff;
  outline: none;
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
}
.input-group .input-hint.data-v-8343362b {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-top: 8rpx;
}
.calculate-section.data-v-8343362b {
  margin-top: 20rpx;
}
.calculate-section .calculate-btn.data-v-8343362b {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  background: linear-gradient(135deg, #EC4899 0%, #BE185D 100%);
  border-radius: 50rpx;
  box-shadow: 0 4rpx 20rpx rgba(236, 72, 153, 0.3);
}
.calculate-section .calculate-btn .btn-icon.data-v-8343362b {
  font-size: 36rpx;
  margin-right: 12rpx;
  color: #ffffff;
}
.calculate-section .calculate-btn .btn-text.data-v-8343362b {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
.calculate-section .calculate-btn.data-v-8343362b:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(236, 72, 153, 0.2);
}
.status-card.data-v-8343362b {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}
.status-card .status-content.data-v-8343362b {
  padding: 40rpx;
  text-align: center;
}
.status-card .status-content .status-badge.data-v-8343362b {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  margin-bottom: 20rpx;
}
.status-card .status-content .status-badge .status-text.data-v-8343362b {
  font-size: 32rpx;
  font-weight: 600;
}
.status-card .status-content .status-badge.danger-bg.data-v-8343362b {
  background: #fef2f2;
}
.status-card .status-content .status-badge.danger-bg .danger-color.data-v-8343362b {
  color: #dc2626;
}
.status-card .status-content .status-badge.safe-bg.data-v-8343362b {
  background: #f0fdf4;
}
.status-card .status-content .status-badge.safe-bg .safe-color.data-v-8343362b {
  color: #16a34a;
}
.status-card .status-content .status-badge.normal-bg.data-v-8343362b {
  background: #eff6ff;
}
.status-card .status-content .status-badge.normal-bg .normal-color.data-v-8343362b {
  color: #2563eb;
}
.status-card .status-content .status-desc.data-v-8343362b {
  font-size: 28rpx;
  color: #666666;
}
.result-item.data-v-8343362b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.result-item .item-left.data-v-8343362b {
  display: flex;
  align-items: center;
}
.result-item .item-left .item-dot.data-v-8343362b {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.result-item .item-left .item-label.data-v-8343362b {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.result-item .item-right.data-v-8343362b {
  text-align: right;
}
.result-item .item-right .item-date.data-v-8343362b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}
.result-item .item-right .item-days.data-v-8343362b {
  font-size: 24rpx;
  color: #666666;
}
.result-item.period-item.data-v-8343362b {
  background: #fdf2f8;
}
.result-item.period-item .period-dot.data-v-8343362b {
  background: #EC4899;
}
.result-item.ovulation-item.data-v-8343362b {
  background: #fffbeb;
}
.result-item.ovulation-item .ovulation-dot.data-v-8343362b {
  background: #F59E0B;
}
.period-section.data-v-8343362b {
  padding: 25rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}
.period-section .section-header.data-v-8343362b {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.period-section .section-header .section-dot.data-v-8343362b {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.period-section .section-header .section-title.data-v-8343362b {
  font-size: 28rpx;
  font-weight: 600;
}
.period-section .section-range.data-v-8343362b {
  font-size: 26rpx;
  margin-left: 40rpx;
}
.period-section .safe-periods.data-v-8343362b {
  margin-left: 40rpx;
}
.period-section .safe-periods .safe-period.data-v-8343362b {
  display: block;
  font-size: 26rpx;
  margin-bottom: 8rpx;
}
.period-section .safe-periods .safe-period.data-v-8343362b:last-child {
  margin-bottom: 0;
}
.period-section.danger-section.data-v-8343362b {
  background: #fef2f2;
}
.period-section.danger-section .danger-dot.data-v-8343362b {
  background: #EF4444;
}
.period-section.danger-section .section-title.data-v-8343362b,
.period-section.danger-section .section-range.data-v-8343362b {
  color: #991b1b;
}
.period-section.safe-section.data-v-8343362b {
  background: #f0fdf4;
}
.period-section.safe-section .safe-dot.data-v-8343362b {
  background: #22C55E;
}
.period-section.safe-section .section-title.data-v-8343362b,
.period-section.safe-section .safe-period.data-v-8343362b {
  color: #166534;
}
.warning-card.data-v-8343362b {
  background: #fff7ed;
  border: 2rpx solid #fed7aa;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}
.warning-card .card-header.data-v-8343362b {
  padding: 30rpx;
  border-bottom: 2rpx solid #fed7aa;
}
.warning-card .card-header .warning-title.data-v-8343362b {
  font-size: 32rpx;
  font-weight: 600;
  color: #c2410c;
}
.warning-card .card-content.data-v-8343362b {
  padding: 30rpx;
}
.warning-card .card-content .warning-list .warning-item.data-v-8343362b {
  display: block;
  font-size: 26rpx;
  color: #c2410c;
  line-height: 1.6;
  margin-bottom: 16rpx;
}
.warning-card .card-content .warning-list .warning-item.data-v-8343362b:last-child {
  margin-bottom: 0;
}
.instruction-section.data-v-8343362b {
  margin-bottom: 30rpx;
}
.instruction-section.data-v-8343362b:last-child {
  margin-bottom: 0;
}
.instruction-section .instruction-title.data-v-8343362b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}
.instruction-section .instruction-item.data-v-8343362b {
  display: block;
  font-size: 26rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 8rpx;
}
.instruction-section .instruction-item.data-v-8343362b:last-child {
  margin-bottom: 0;
}
@media screen and (max-width: 750rpx) {
.input-row.data-v-8343362b {
    flex-direction: column;
    gap: 20rpx;
}
}