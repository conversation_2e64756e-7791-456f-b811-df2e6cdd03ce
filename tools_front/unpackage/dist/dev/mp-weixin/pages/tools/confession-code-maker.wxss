/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-90a76c95 {
  display: flex;
}
.flex-1.data-v-90a76c95 {
  flex: 1;
}
.items-center.data-v-90a76c95 {
  align-items: center;
}
.justify-center.data-v-90a76c95 {
  justify-content: center;
}
.justify-between.data-v-90a76c95 {
  justify-content: space-between;
}
.text-center.data-v-90a76c95 {
  text-align: center;
}
.rounded.data-v-90a76c95 {
  border-radius: 3px;
}
.rounded-lg.data-v-90a76c95 {
  border-radius: 6px;
}
.shadow.data-v-90a76c95 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-90a76c95 {
  padding: 16rpx;
}
.m-4.data-v-90a76c95 {
  margin: 16rpx;
}
.mb-4.data-v-90a76c95 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-90a76c95 {
  margin-top: 16rpx;
}
.container.data-v-90a76c95 {
  min-height: 100vh;
  background: #ffffff;
}
.main-content.data-v-90a76c95 {
  padding: 40rpx 32rpx;
  padding-bottom: 40rpx;
}
.section-title.data-v-90a76c95 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-title .title-icon.data-v-90a76c95 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.section-title .title-text.data-v-90a76c95 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.input-section.data-v-90a76c95 {
  margin-bottom: 60rpx;
}
.input-container.data-v-90a76c95 {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f0f0;
}
.name-input.data-v-90a76c95 {
  width: 92%;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
}
.name-input.data-v-90a76c95:focus {
  border-color: #ff6b9d;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(255, 107, 157, 0.1);
}
.textarea-container.data-v-90a76c95 {
  position: relative;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}
.textarea-container.data-v-90a76c95:focus-within {
  border-color: #ff6b9d;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(255, 107, 157, 0.1);
}
.message-input.data-v-90a76c95 {
  width: 100%;
  min-height: 200rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}
.char-count.data-v-90a76c95 {
  position: absolute;
  bottom: 12rpx;
  right: 16rpx;
  font-size: 24rpx;
  color: #6c757d;
}
.language-section.data-v-90a76c95 {
  margin-bottom: 60rpx;
}
.language-grid.data-v-90a76c95 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.language-item.data-v-90a76c95 {
  background: #ffffff;
  border: 3rpx solid #f0f0f0;
  border-radius: 16rpx;
  padding: 32rpx 16rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.language-item.data-v-90a76c95:active {
  transform: scale(0.98);
}
.language-item.active.data-v-90a76c95 {
  border-color: #ff6b9d;
  background: linear-gradient(135deg, #ff6b9d, #ff8fab);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 157, 0.3);
}
.language-item.active .lang-icon.data-v-90a76c95 {
  transform: scale(1.1);
}
.language-item.active .lang-name.data-v-90a76c95 {
  color: #ffffff;
  font-weight: 600;
}
.lang-icon.data-v-90a76c95 {
  font-size: 48rpx;
  margin-bottom: 12rpx;
  transition: transform 0.3s ease;
}
.lang-name.data-v-90a76c95 {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
  transition: all 0.3s ease;
}
.result-section.data-v-90a76c95 {
  margin-bottom: 40rpx;
}
.section-header.data-v-90a76c95 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-header .section-title.data-v-90a76c95 {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
.section-header .section-title .title-icon.data-v-90a76c95 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.section-header .section-title .title-text.data-v-90a76c95 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.action-buttons.data-v-90a76c95 {
  display: flex;
  gap: 16rpx;
}
.action-btn.data-v-90a76c95 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}
.action-btn.data-v-90a76c95:active {
  transform: scale(0.98);
}
.action-btn.copy-btn.data-v-90a76c95 {
  background: #007bff;
  color: #fff;
}
.action-btn.copy-btn.data-v-90a76c95:active {
  background: #0056b3;
}
.action-btn.download-btn.data-v-90a76c95 {
  background: #28a745;
  color: #fff;
}
.action-btn.download-btn.data-v-90a76c95:active {
  background: #1e7e34;
}
.action-icon.data-v-90a76c95 {
  font-size: 20rpx;
}
.action-text.data-v-90a76c95 {
  font-size: 22rpx;
}
.result-container.data-v-90a76c95 {
  background: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f0f0;
}
.code-container.data-v-90a76c95 {
  background: #1a1a1a;
  padding: 32rpx;
  overflow-x: auto;
}
.code-container .code-text.data-v-90a76c95 {
  font-family: "SF Mono", "Monaco", "Inconsolata", "Roboto Mono", monospace;
  font-size: 24rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}
.result-tip.data-v-90a76c95 {
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  text-align: center;
}
.result-tip .tip-text.data-v-90a76c95 {
  font-size: 26rpx;
  color: #6c757d;
}
.generate-section.data-v-90a76c95 {
  margin-bottom: 40rpx;
}
.generate-btn.data-v-90a76c95 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
  border: none;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #fff;
  box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.generate-btn.data-v-90a76c95:active:not(.disabled):not(.loading) {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 10rpx rgba(255, 71, 87, 0.2);
}
.generate-btn.disabled.data-v-90a76c95 {
  background: #e9ecef;
  box-shadow: none;
  color: #6c757d;
  opacity: 0.6;
}
.generate-btn.loading.data-v-90a76c95 {
  background: linear-gradient(135deg, #ff4757 0%, #ff6b7a 100%);
  box-shadow: 0 6rpx 20rpx rgba(255, 71, 87, 0.3);
}
.generate-btn.loading .btn-icon.data-v-90a76c95 {
  animation: spin-90a76c95 1s linear infinite;
}
.btn-icon.data-v-90a76c95 {
  font-size: 32rpx;
  margin-right: 12rpx;
}
.btn-text.data-v-90a76c95 {
  font-size: 30rpx;
  font-weight: 600;
}
@keyframes spin-90a76c95 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
/* 响应式适配 */
@media (max-width: 750rpx) {
.language-grid.data-v-90a76c95 {
    grid-template-columns: repeat(2, 1fr);
}
.main-content.data-v-90a76c95 {
    padding: 32rpx 24rpx;
    padding-bottom: 140rpx;
}
}