"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      width: 300,
      height: 200,
      customText: "",
      backgroundColor: "#cccccc",
      textColor: "#333333",
      generatedImages: [],
      showPreview: false,
      previewData: null,
      isGenerating: false,
      // 颜色选择器相关
      showColorPicker: false,
      colorPickerType: "background",
      // 'background' 或 'text'
      customColorValue: "",
      presetColors: [
        "#ffffff",
        "#f8f9fa",
        "#e9ecef",
        "#dee2e6",
        "#ced4da",
        "#adb5bd",
        "#6c757d",
        "#495057",
        "#343a40",
        "#212529",
        "#000000",
        "#ff6b6b",
        "#ee5a24",
        "#feca57",
        "#48dbfb",
        "#0abde3",
        "#1dd1a1",
        "#10ac84",
        "#a55eea",
        "#8c7ae6",
        "#778ca3",
        "#2c2c54",
        "#40407a",
        "#706fd3",
        "#f7f1e3",
        "#34ace0",
        "#33d9b2",
        "#ff793f",
        "#cd6133",
        "#ffb142",
        "#cc8e35"
      ],
      presets: [
        { name: "头像", width: 150, height: 150 },
        { name: "横幅", width: 800, height: 200 },
        { name: "卡片", width: 300, height: 200 },
        { name: "缩略图", width: 200, height: 150 },
        { name: "全屏", width: 1920, height: 1080 },
        { name: "移动端", width: 375, height: 667 }
      ],
      usageTips: [
        "使用Canvas动态生成占位图片，无需网络请求",
        "支持自定义尺寸、颜色和文字内容",
        "可批量生成不同样式的占位图",
        "生成的图片可直接保存到相册",
        "提供常用尺寸的快速预设"
      ]
    };
  },
  computed: {
    canvasWidth() {
      return Math.max(this.width || 300, 1);
    },
    canvasHeight() {
      return Math.max(this.height || 200, 1);
    }
  },
  methods: {
    // 生成单张图片
    async generateSingleImage() {
      if (this.isGenerating)
        return;
      this.isGenerating = true;
      try {
        const imageData = await this.drawImage({
          width: this.canvasWidth,
          height: this.canvasHeight,
          text: this.customText || `${this.canvasWidth} × ${this.canvasHeight}`,
          backgroundColor: this.backgroundColor,
          textColor: this.textColor
        });
        this.generatedImages = [imageData, ...this.generatedImages.slice(0, 7)];
        utils_index.showSuccess("生成成功！");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/random-loading-image.vue:304", "生成失败:", error);
        utils_index.showError("生成失败，请重试");
      } finally {
        this.isGenerating = false;
      }
    },
    // 批量生成图片
    async generateMultipleImages() {
      if (this.isGenerating)
        return;
      this.isGenerating = true;
      const newImages = [];
      const texts = ["Loading...", "Placeholder", "Image", "Sample", "Demo", "Preview"];
      const colors = ["#cccccc", "#e0e0e0", "#f0f0f0", "#ddd", "#ccc", "#bbb"];
      try {
        for (let i = 0; i < 6; i++) {
          const randomText = this.customText || texts[i];
          const randomBg = colors[i];
          const imageData = await this.drawImage({
            width: this.canvasWidth,
            height: this.canvasHeight,
            text: randomText,
            backgroundColor: randomBg,
            textColor: this.textColor
          });
          newImages.push(imageData);
        }
        this.generatedImages = newImages;
        utils_index.showSuccess(`成功生成${newImages.length}张图片！`);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/random-loading-image.vue:340", "批量生成失败:", error);
        utils_index.showError("生成失败，请重试");
      } finally {
        this.isGenerating = false;
      }
    },
    // Canvas绘制图片
    drawImage(options) {
      return new Promise((resolve, reject) => {
        const { width, height, text, backgroundColor, textColor } = options;
        const ctx = common_vendor.index.createCanvasContext("placeholder-canvas", this);
        ctx.clearRect(0, 0, width, height);
        ctx.setFillStyle(backgroundColor);
        ctx.fillRect(0, 0, width, height);
        ctx.setFillStyle(textColor);
        const fontSize = Math.min(width, height) / 8;
        ctx.setFontSize(fontSize);
        ctx.setTextAlign("center");
        ctx.setTextBaseline("middle");
        const lines = this.wrapText(text, width * 0.8, fontSize);
        const lineHeight = fontSize * 1.2;
        const totalHeight = lines.length * lineHeight;
        const startY = (height - totalHeight) / 2 + lineHeight / 2;
        lines.forEach((line, index) => {
          ctx.fillText(line, width / 2, startY + index * lineHeight);
        });
        ctx.draw(false, () => {
          setTimeout(() => {
            common_vendor.index.canvasToTempFilePath({
              canvasId: "placeholder-canvas",
              success: (res) => {
                resolve({
                  url: res.tempFilePath,
                  width,
                  height,
                  text,
                  backgroundColor,
                  textColor,
                  timestamp: Date.now()
                });
              },
              fail: (error) => {
                reject(error);
              }
            }, this);
          }, 100);
        });
      });
    },
    // 文字换行处理
    wrapText(text, maxWidth, fontSize) {
      const words = text.split("");
      const lines = [];
      let currentLine = "";
      for (let i = 0; i < words.length; i++) {
        const testLine = currentLine + words[i];
        const testWidth = testLine.length * fontSize * 0.6;
        if (testWidth > maxWidth && currentLine !== "") {
          lines.push(currentLine);
          currentLine = words[i];
        } else {
          currentLine = testLine;
        }
      }
      lines.push(currentLine);
      return lines.filter((line) => line.trim() !== "");
    },
    // 选择预设
    selectPreset(preset) {
      this.width = preset.width;
      this.height = preset.height;
    },
    // 预览图片
    previewImage(imageData) {
      this.previewData = imageData;
      this.showPreview = true;
    },
    // 关闭预览
    closePreview() {
      this.showPreview = false;
      this.previewData = null;
    },
    // 保存到相册
    saveToAlbum(imageData) {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: imageData.url,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success",
            duration: 2e3
          });
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/tools/random-loading-image.vue:456", "保存失败:", error);
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error",
            duration: 2e3
          });
        }
      });
    },
    // 分享图片
    shareImage(imageData) {
      common_vendor.index.showActionSheet({
        itemList: ["保存到相册", "发送给朋友"],
        success: (res) => {
          if (res.tapIndex === 0) {
            this.saveToAlbum(imageData);
          } else if (res.tapIndex === 1) {
            common_vendor.index.showToast({
              title: "请使用右上角分享",
              icon: "none",
              duration: 2e3
            });
          }
        }
      });
    },
    // 图片加载错误处理
    onImageError(e) {
      common_vendor.index.__f__("error", "at pages/tools/random-loading-image.vue:487", "图片加载失败:", e);
    },
    // 打开颜色选择器
    openColorPicker(type) {
      this.colorPickerType = type;
      this.customColorValue = this.getCurrentColor();
      this.showColorPicker = true;
    },
    // 关闭颜色选择器
    closeColorPicker() {
      this.showColorPicker = false;
      this.colorPickerType = "background";
      this.customColorValue = "";
    },
    // 获取当前选中的颜色
    getCurrentColor() {
      return this.colorPickerType === "background" ? this.backgroundColor : this.textColor;
    },
    // 选择颜色
    selectColor(color) {
      if (!color || !color.startsWith("#")) {
        common_vendor.index.showToast({
          title: "请输入有效的颜色值",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      if (this.colorPickerType === "background") {
        this.backgroundColor = color;
      } else {
        this.textColor = color;
      }
      this.closeColorPicker();
      common_vendor.index.showToast({
        title: "颜色已更新",
        icon: "success",
        duration: 1500
      });
    },
    // 判断预设是否被选中
    isPresetSelected(preset) {
      return this.width === preset.width && this.height === preset.height;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.width,
    b: common_vendor.o(common_vendor.m(($event) => $data.width = $event.detail.value, {
      number: true
    })),
    c: $data.height,
    d: common_vendor.o(common_vendor.m(($event) => $data.height = $event.detail.value, {
      number: true
    })),
    e: $data.customText,
    f: common_vendor.o(($event) => $data.customText = $event.detail.value),
    g: common_vendor.s(`background: ${$data.backgroundColor}`),
    h: common_vendor.t($data.backgroundColor),
    i: common_vendor.o(($event) => $options.openColorPicker("background")),
    j: common_vendor.s(`background: ${$data.textColor}`),
    k: common_vendor.t($data.textColor),
    l: common_vendor.o(($event) => $options.openColorPicker("text")),
    m: common_vendor.f($data.presets, (preset, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(preset.name),
        b: common_vendor.t(preset.width),
        c: common_vendor.t(preset.height),
        d: $options.isPresetSelected(preset)
      }, $options.isPresetSelected(preset) ? {} : {}, {
        e: preset.name,
        f: common_vendor.o(($event) => $options.selectPreset(preset), preset.name),
        g: $options.isPresetSelected(preset) ? 1 : ""
      });
    }),
    n: common_vendor.t($data.isGenerating ? "生成中..." : "生成单张"),
    o: common_vendor.o((...args) => $options.generateSingleImage && $options.generateSingleImage(...args)),
    p: $data.isGenerating,
    q: common_vendor.o((...args) => $options.generateMultipleImages && $options.generateMultipleImages(...args)),
    r: $data.isGenerating,
    s: common_vendor.s(`width:${$options.canvasWidth}px;height:${$options.canvasHeight}px;position:absolute;left:-9999px;top:-9999px;opacity:0;`),
    t: $options.canvasWidth,
    v: $options.canvasHeight,
    w: $data.generatedImages.length > 0
  }, $data.generatedImages.length > 0 ? {
    x: common_vendor.t($data.generatedImages.length),
    y: common_vendor.f($data.generatedImages, (imageData, index, i0) => {
      return {
        a: imageData.url,
        b: common_vendor.o((...args) => $options.onImageError && $options.onImageError(...args), index),
        c: common_vendor.t(imageData.width),
        d: common_vendor.t(imageData.height),
        e: common_vendor.t(imageData.text),
        f: common_vendor.o(($event) => $options.saveToAlbum(imageData), index),
        g: common_vendor.o(($event) => $options.shareImage(imageData), index),
        h: index,
        i: common_vendor.o(($event) => $options.previewImage(imageData), index)
      };
    })
  } : {}, {
    z: $data.showPreview
  }, $data.showPreview ? {
    A: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    B: $data.previewData.url,
    C: common_vendor.t($data.previewData.width),
    D: common_vendor.t($data.previewData.height),
    E: common_vendor.t($data.previewData.text),
    F: common_vendor.o(($event) => $options.saveToAlbum($data.previewData)),
    G: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {}, {
    H: $data.showColorPicker
  }, $data.showColorPicker ? {
    I: common_vendor.t($data.colorPickerType === "background" ? "背景" : "文字"),
    J: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args)),
    K: common_vendor.f($data.presetColors, (color, k0, i0) => {
      return common_vendor.e({
        a: $options.getCurrentColor() === color
      }, $options.getCurrentColor() === color ? {} : {}, {
        b: color,
        c: common_vendor.s(`background: ${color}`),
        d: common_vendor.o(($event) => $options.selectColor(color), color)
      });
    }),
    L: $data.customColorValue,
    M: common_vendor.o(($event) => $data.customColorValue = $event.detail.value),
    N: common_vendor.o(($event) => $options.selectColor($data.customColorValue)),
    O: common_vendor.o((...args) => $options.closeColorPicker && $options.closeColorPicker(...args))
  } : {}, {
    P: common_vendor.f($data.usageTips, (tip, index, i0) => {
      return {
        a: common_vendor.t(tip),
        b: index
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a4f74c06"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/random-loading-image.js.map
