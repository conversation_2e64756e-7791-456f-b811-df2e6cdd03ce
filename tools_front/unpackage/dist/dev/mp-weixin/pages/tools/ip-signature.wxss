/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-099d725b {
  display: flex;
}
.flex-1.data-v-099d725b {
  flex: 1;
}
.items-center.data-v-099d725b {
  align-items: center;
}
.justify-center.data-v-099d725b {
  justify-content: center;
}
.justify-between.data-v-099d725b {
  justify-content: space-between;
}
.text-center.data-v-099d725b {
  text-align: center;
}
.rounded.data-v-099d725b {
  border-radius: 3px;
}
.rounded-lg.data-v-099d725b {
  border-radius: 6px;
}
.shadow.data-v-099d725b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-099d725b {
  padding: 16rpx;
}
.m-4.data-v-099d725b {
  margin: 16rpx;
}
.mb-4.data-v-099d725b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-099d725b {
  margin-top: 16rpx;
}
.ip-signature-tool.data-v-099d725b {
  min-height: 100vh;
  background: #f9fafb;
  padding: 30rpx;
}
.tips-card.data-v-099d725b, .settings-card.data-v-099d725b, .preview-card.data-v-099d725b, .ip-info-card.data-v-099d725b, .header-card.data-v-099d725b {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.tips-card.data-v-099d725b:hover, .settings-card.data-v-099d725b:hover, .preview-card.data-v-099d725b:hover, .ip-info-card.data-v-099d725b:hover, .header-card.data-v-099d725b:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.card-header.data-v-099d725b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1rpx solid #e8ecff;
}
.card-header .header-icon.data-v-099d725b {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.card-header .header-title.data-v-099d725b {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.header-card.data-v-099d725b {
  margin-bottom: 40rpx;
}
.header-card .header-content.data-v-099d725b {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
}
.header-card .header-content .header-icon.data-v-099d725b {
  font-size: 64rpx;
  margin-right: 30rpx;
  animation: pulse-099d725b 2s infinite;
}
.header-card .header-content .header-info.data-v-099d725b {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.header-card .header-content .header-info .header-title.data-v-099d725b {
  font-size: 48rpx;
  font-weight: 700;
  color: white;
  display: block;
}
.header-card .header-content .header-info .header-subtitle.data-v-099d725b {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.4;
  color: white;
  display: block;
}
.ip-info-card .refresh-btn.data-v-099d725b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #6366f1;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}
.ip-info-card .refresh-btn.data-v-099d725b:hover {
  background: #5856eb;
  transform: scale(1.05);
}
.ip-info-card .refresh-btn.loading.data-v-099d725b {
  opacity: 0.7;
  pointer-events: none;
}
.ip-info-card .refresh-btn .refresh-icon.data-v-099d725b {
  font-size: 24rpx;
  transition: transform 0.3s ease;
}
.ip-info-card .refresh-btn .refresh-icon.spinning.data-v-099d725b {
  animation: spin-099d725b 1s linear infinite;
}
.ip-info-card .refresh-btn .refresh-text.data-v-099d725b {
  font-weight: 500;
}
.ip-info-card .ip-content.data-v-099d725b {
  padding: 30rpx;
}
.ip-info-card .loading-state.data-v-099d725b {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200rpx;
}
.ip-info-card .loading-state .loading-spinner.data-v-099d725b {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #e5e7eb;
  border-top: 4rpx solid #6366f1;
  border-radius: 50%;
  animation: spin-099d725b 1s linear infinite;
  margin-bottom: 20rpx;
}
.ip-info-card .loading-state .loading-text.data-v-099d725b {
  font-size: 28rpx;
  color: #6b7280;
}
.ip-info-card .ip-info-grid.data-v-099d725b {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.ip-info-card .info-item.data-v-099d725b {
  display: flex;
  flex-direction: column;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1rpx solid #e8ecff;
  border-radius: 16rpx;
  position: relative;
  transition: all 0.3s ease;
}
.ip-info-card .info-item.data-v-099d725b:hover {
  border-color: #6366f1;
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.15);
}
.ip-info-card .info-item .info-label.data-v-099d725b {
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}
.ip-info-card .info-item .info-value.data-v-099d725b {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  word-break: break-all;
}
.ip-info-card .info-item .info-icon.data-v-099d725b {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 32rpx;
  opacity: 0.6;
}
.preview-card .regen-btn.data-v-099d725b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}
.preview-card .regen-btn.data-v-099d725b:hover {
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.3);
  transform: translateY(-2rpx);
}
.preview-card .regen-btn .btn-icon.data-v-099d725b {
  font-size: 24rpx;
}
.preview-card .regen-btn .btn-text.data-v-099d725b {
  font-weight: 500;
}
.preview-card .preview-content.data-v-099d725b {
  padding: 30rpx;
}
.preview-card .signature-preview.data-v-099d725b {
  padding: 40rpx;
  border-radius: 16rpx;
  border: 2rpx dashed #e5e7eb;
  min-height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.preview-card .signature-preview.data-v-099d725b:hover {
  border-color: #6366f1;
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.1);
}
.preview-card .signature-preview .signature-text.data-v-099d725b {
  white-space: pre-wrap;
  text-align: center;
  line-height: 1.6;
}
.settings-card .settings-content.data-v-099d725b {
  padding: 30rpx;
}
.settings-card .setting-group.data-v-099d725b {
  margin-bottom: 40rpx;
}
.settings-card .setting-group.data-v-099d725b:last-child {
  margin-bottom: 0;
}
.settings-card .setting-group .group-title.data-v-099d725b {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 24rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #e8ecff;
}
.settings-card .setting-item.data-v-099d725b {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.settings-card .setting-item.data-v-099d725b:last-child {
  margin-bottom: 0;
}
.settings-card .setting-item .setting-label.data-v-099d725b {
  font-size: 28rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-right: 20rpx;
  min-width: 120rpx;
  flex-shrink: 0;
}
.settings-card .setting-item .setting-value.data-v-099d725b {
  font-size: 26rpx;
  font-weight: 600;
  color: #6366f1;
  margin-left: 20rpx;
}
.settings-card .font-slider.data-v-099d725b {
  flex: 1;
}
.settings-card .font-group.data-v-099d725b {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 30rpx;
  flex-wrap: nowrap;
}
.settings-card .font-group .font-option.data-v-099d725b {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}
.settings-card .font-group .font-option.data-v-099d725b:hover {
  background: #f0f4ff;
}
.settings-card .font-group .font-option .font-text.data-v-099d725b {
  font-size: 26rpx;
  color: #1a1a1a;
  display: inline-block;
  word-break: keep-all;
}
.settings-card .color-row.data-v-099d725b {
  display: flex;
  gap: 30rpx;
  margin-bottom: 30rpx;
}
.settings-card .color-item.data-v-099d725b {
  flex: 1;
}
.settings-card .color-item .color-label.data-v-099d725b {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 12rpx;
}
.settings-card .color-item .color-picker-wrapper.data-v-099d725b {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  cursor: pointer;
}
.settings-card .color-item .color-picker-wrapper.data-v-099d725b:hover {
  background: #f0f4ff;
}
.settings-card .color-item .color-picker-wrapper .color-preview.data-v-099d725b {
  width: 40rpx;
  height: 40rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e8ecff;
}
.settings-card .color-item .color-picker-wrapper .color-value.data-v-099d725b {
  font-size: 24rpx;
  font-family: monospace;
  color: #6b7280;
}
.settings-card .preset-colors .preset-title.data-v-099d725b {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
}
.settings-card .preset-colors .preset-grid.data-v-099d725b {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.settings-card .preset-colors .preset-item.data-v-099d725b {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f8f9ff;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}
.settings-card .preset-colors .preset-item.data-v-099d725b:hover {
  background: #f0f4ff;
  transform: translateY(-2rpx);
}
.settings-card .preset-colors .preset-item .preset-colors-demo.data-v-099d725b {
  margin-bottom: 12rpx;
}
.settings-card .preset-colors .preset-item .preset-colors-demo .demo-bg.data-v-099d725b {
  width: 60rpx;
  height: 40rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid #e8ecff;
}
.settings-card .preset-colors .preset-item .preset-colors-demo .demo-bg .demo-text.data-v-099d725b {
  font-size: 24rpx;
  font-weight: 600;
}
.settings-card .preset-colors .preset-item .preset-name.data-v-099d725b {
  font-size: 22rpx;
  color: #6b7280;
}
.settings-card .template-grid.data-v-099d725b {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}
.settings-card .template-item.data-v-099d725b {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2rpx solid #e8ecff;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.settings-card .template-item.data-v-099d725b:hover {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  transform: translateY(-2rpx);
}
.settings-card .template-item.active.data-v-099d725b {
  border-color: #6366f1;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.2);
}
.settings-card .template-item .template-preview.data-v-099d725b {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}
.settings-card .template-item .template-name.data-v-099d725b {
  font-size: 26rpx;
  font-weight: 500;
  color: #1a1a1a;
}
.action-buttons.data-v-099d725b {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}
.action-buttons button.data-v-099d725b {
  flex: 1;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.action-buttons button.data-v-099d725b:active {
  transform: scale(0.98);
}
.action-buttons .copy-btn.data-v-099d725b {
  background: #ffffff;
  color: #333333;
  border: 2rpx solid #e5e7eb;
}
.action-buttons .copy-btn.data-v-099d725b:active {
  background: #f3f4f6;
}
.action-buttons .download-btn.data-v-099d725b {
  background: #1a1a1a;
  color: #ffffff;
}
.action-buttons .download-btn.data-v-099d725b:active {
  background: #333333;
}
.tips-card .tips-content.data-v-099d725b {
  padding: 30rpx;
}
.tips-card .tip-item.data-v-099d725b {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tips-card .tip-item.data-v-099d725b:last-child {
  margin-bottom: 0;
}
.tips-card .tip-item .tip-bullet.data-v-099d725b {
  font-size: 28rpx;
  color: #6366f1;
  margin-right: 16rpx;
  margin-top: 4rpx;
  font-weight: bold;
}
.tips-card .tip-item .tip-text.data-v-099d725b {
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
  flex: 1;
}
@keyframes spin-099d725b {
to {
    transform: rotate(360deg);
}
}
@keyframes pulse-099d725b {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.6;
}
}
@media (max-width: 750rpx) {
.ip-info-grid.data-v-099d725b {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.color-row.data-v-099d725b {
    flex-direction: column;
    gap: 20rpx;
}
.preset-grid.data-v-099d725b {
    grid-template-columns: repeat(2, 1fr);
}
.template-grid.data-v-099d725b {
    grid-template-columns: 1fr;
}
.action-buttons.data-v-099d725b {
    padding: 0;
}
}
.signature-canvas.data-v-099d725b {
  position: fixed;
  left: -9999rpx;
  visibility: hidden;
}