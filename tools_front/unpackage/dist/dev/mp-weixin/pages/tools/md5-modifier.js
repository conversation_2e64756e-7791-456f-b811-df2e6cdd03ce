"use strict";
const common_vendor = require("../../common/vendor.js");
function md5(data) {
  class MD5 {
    constructor() {
      this.remainder = null;
      this.length = 0;
      this.state = new Int32Array([1732584193, 4023233417, 2562383102, 271733878]);
    }
    static rotateLeft(value, shift) {
      return value << shift | value >>> 32 - shift;
    }
    static wordToBytes(word) {
      const bytes = [];
      for (let i = 0; i < 4; i++) {
        bytes.push(word >>> 8 * i & 255);
      }
      return bytes;
    }
    static bytesToWords(bytes) {
      const words = [];
      for (let i = 0; i < bytes.length; i += 4) {
        words.push(
          bytes[i] | bytes[i + 1] << 8 | bytes[i + 2] << 16 | bytes[i + 3] << 24
        );
      }
      return words;
    }
    update(data2) {
      let msg;
      if (typeof data2 === "string") {
        msg = new TextEncoder().encode(data2);
      } else {
        msg = new Uint8Array(data2);
      }
      this.length += msg.length;
      const bytes = Array.from(msg);
      if (this.remainder) {
        bytes.unshift(...this.remainder);
      }
      const chunks = Math.floor(bytes.length / 64);
      this.remainder = bytes.slice(chunks * 64);
      for (let i = 0; i < chunks; i++) {
        const chunk = bytes.slice(i * 64, (i + 1) * 64);
        this.processChunk(chunk);
      }
      return this;
    }
    processChunk(chunk) {
      const words = MD5.bytesToWords(chunk);
      const [a, b, c, d] = this.state;
      let [aa, bb, cc, dd] = [a, b, c, d];
      aa = MD5.rotateLeft(aa + (bb & cc | ~bb & dd) + words[0] + 3614090360, 7) + bb;
      dd = MD5.rotateLeft(dd + (aa & bb | ~aa & cc) + words[1] + 3905402710, 12) + aa;
      cc = MD5.rotateLeft(cc + (dd & aa | ~dd & bb) + words[2] + 606105819, 17) + dd;
      bb = MD5.rotateLeft(bb + (cc & dd | ~cc & aa) + words[3] + 3250441966, 22) + cc;
      this.state[0] = this.state[0] + aa >>> 0;
      this.state[1] = this.state[1] + bb >>> 0;
      this.state[2] = this.state[2] + cc >>> 0;
      this.state[3] = this.state[3] + dd >>> 0;
    }
    finalize() {
      const bytes = this.remainder || [];
      const padding = new Array(64 - (bytes.length + 9) % 64);
      padding[0] = 128;
      const bits = this.length * 8;
      const bitsArray = [
        bits & 255,
        bits >>> 8 & 255,
        bits >>> 16 & 255,
        bits >>> 24 & 255,
        0,
        0,
        0,
        0
      ];
      bytes.push(...padding, ...bitsArray);
      this.update(bytes);
      const result = [];
      for (const word of this.state) {
        result.push(...MD5.wordToBytes(word));
      }
      return result;
    }
  }
  const md5Instance = new MD5();
  md5Instance.update(data);
  const hash = md5Instance.finalize();
  return hash.map((byte) => byte.toString(16).padStart(2, "0")).join("");
}
const _sfc_main = {
  name: "MD5Modifier",
  data() {
    return {
      selectedFile: null,
      originalMD5: "",
      modifiedInfo: null,
      selectedMethod: "addBytes",
      isModifying: false,
      modifyMethods: [
        {
          value: "addBytes",
          label: "添加字节",
          description: "在文件末尾添加随机字节"
        },
        {
          value: "modifyMetadata",
          label: "修改元数据",
          description: "修改文件头部元数据"
        },
        {
          value: "insertBytes",
          label: "插入字节",
          description: "在文件中间插入字节"
        },
        {
          value: "replaceBytes",
          label: "替换字节",
          description: "替换文件末尾字节"
        }
      ]
    };
  },
  methods: {
    selectFile() {
      common_vendor.index.chooseMessageFile({
        count: 1,
        type: "all",
        success: (res) => {
          const file = res.tempFiles[0];
          const maxSize = 500 * 1024;
          if (file.size > maxSize) {
            common_vendor.index.showToast({
              title: "文件大小不能超过500KB",
              icon: "none"
            });
            return;
          }
          this.selectedFile = {
            name: file.name,
            size: file.size,
            path: file.path
          };
          this.calculateOriginalMD5();
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:317", "选择文件失败:", err);
          common_vendor.index.showToast({
            title: "选择文件失败",
            icon: "none"
          });
        }
      });
    },
    clearFile() {
      this.selectedFile = null;
      this.originalMD5 = "";
      this.modifiedInfo = null;
    },
    async calculateOriginalMD5() {
      if (!this.selectedFile)
        return;
      try {
        const fileData = await this.readFile(this.selectedFile.path);
        const hash = await this.calculateMD5(fileData);
        this.originalMD5 = hash;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:342", "计算MD5失败:", error);
        common_vendor.index.showToast({
          title: "计算MD5失败",
          icon: "none"
        });
      }
    },
    // 读取文件内容
    readFile(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.getFileSystemManager().readFile({
          filePath,
          success: (res) => resolve(res.data),
          fail: reject
        });
      });
    },
    // 计算MD5
    calculateMD5(data) {
      return new Promise((resolve, reject) => {
        try {
          const hash = md5(data);
          resolve(hash);
        } catch (error) {
          reject(error);
        }
      });
    },
    async handleModify() {
      if (!this.selectedFile || !this.selectedMethod || this.isModifying)
        return;
      this.isModifying = true;
      try {
        const fileData = await this.readFile(this.selectedFile.path);
        let modifiedData;
        switch (this.selectedMethod) {
          case "addBytes":
            modifiedData = this.addRandomBytes(fileData);
            break;
          case "modifyMetadata":
            modifiedData = this.modifyFileMetadata(fileData);
            break;
          case "insertBytes":
            modifiedData = this.insertRandomBytes(fileData);
            break;
          case "replaceBytes":
            modifiedData = this.replaceLastBytes(fileData);
            break;
          default:
            throw new Error("未知的修改方法");
        }
        const tempFilePath = await this.saveToTempFile(modifiedData);
        const newMD5 = await this.calculateMD5(modifiedData);
        this.modifiedInfo = {
          originalMD5: this.originalMD5,
          modifiedMD5: newMD5,
          method: this.selectedMethod,
          tempFilePath
          // 保存临时文件路径
        };
        common_vendor.index.showToast({
          title: "修改完成！",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:419", "修改失败:", error);
        common_vendor.index.showToast({
          title: "修改失败，请重试",
          icon: "none"
        });
      } finally {
        this.isModifying = false;
      }
    },
    // 修改文件元数据
    modifyFileMetadata(fileData) {
      const result = new Uint8Array(fileData.byteLength + 1);
      result[0] = 255;
      result.set(new Uint8Array(fileData), 1);
      return result.buffer;
    },
    // 添加随机字节
    addRandomBytes(fileData) {
      const result = new Uint8Array(fileData.byteLength + 1);
      result.set(new Uint8Array(fileData), 0);
      result[result.length - 1] = Math.floor(Math.random() * 256);
      return result.buffer;
    },
    // 插入随机字节
    insertRandomBytes(fileData) {
      const result = new Uint8Array(fileData.byteLength + 1);
      const insertPosition = Math.floor(fileData.byteLength / 2);
      result.set(new Uint8Array(fileData.slice(0, insertPosition)), 0);
      result[insertPosition] = Math.floor(Math.random() * 256);
      result.set(new Uint8Array(fileData.slice(insertPosition)), insertPosition + 1);
      return result.buffer;
    },
    // 替换最后的字节
    replaceLastBytes(fileData) {
      const result = new Uint8Array(fileData);
      result[result.length - 1] = Math.floor(Math.random() * 256);
      return result.buffer;
    },
    // 保存到临时文件
    async saveToTempFile(data) {
      return new Promise(async (resolve, reject) => {
        try {
          await this.clearTempFiles();
          const fs = common_vendor.index.getFileSystemManager();
          const extension = this.selectedFile.name.split(".").pop() || "";
          const tempFilePath = `${common_vendor.index.env.USER_DATA_PATH}/modified_${Date.now()}.${extension}`;
          fs.writeFile({
            filePath: tempFilePath,
            data,
            success: () => resolve(tempFilePath),
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:483", "写入文件失败:", error);
              reject(new Error("文件保存失败，请尝试更小的文件"));
            }
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:488", "保存文件失败:", error);
          reject(new Error("文件保存失败，请尝试更小的文件"));
        }
      });
    },
    // 清理临时文件
    clearTempFiles() {
      return new Promise((resolve, reject) => {
        const fs = common_vendor.index.getFileSystemManager();
        fs.readdir({
          dirPath: common_vendor.index.env.USER_DATA_PATH,
          success: (res) => {
            const promises = res.files.filter((file) => file.startsWith("modified_") || file.startsWith("chunk_")).map((file) => {
              return new Promise((res2) => {
                fs.unlink({
                  filePath: `${common_vendor.index.env.USER_DATA_PATH}/${file}`,
                  success: res2,
                  fail: res2
                });
              });
            });
            Promise.all(promises).then(resolve).catch(resolve);
          },
          fail: resolve
          // 如果读取目录失败，仍然继续
        });
      });
    },
    // 检查文件是否为图片
    isImageFile(filename) {
      const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
      return imageExtensions.some(
        (ext) => filename.toLowerCase().endsWith(ext)
      );
    },
    // 检查文件是否存在
    checkFileExists(filePath) {
      return new Promise((resolve, reject) => {
        const fs = common_vendor.index.getFileSystemManager();
        fs.access({
          path: filePath,
          success: resolve,
          fail: () => reject(new Error("文件不存在或已被删除"))
        });
      });
    },
    // 保存图片到相册
    saveImageToPhotosAlbum(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.saveImageToPhotosAlbum({
          filePath,
          success: resolve,
          fail: () => reject(new Error("保存到相册失败"))
        });
      });
    },
    // 下载修改后的文件
    async downloadModifiedFile() {
      var _a;
      if (!((_a = this.modifiedInfo) == null ? void 0 : _a.tempFilePath)) {
        common_vendor.index.showToast({
          title: "没有可下载的文件",
          icon: "none"
        });
        return;
      }
      try {
        await this.checkFileExists(this.modifiedInfo.tempFilePath);
        if (this.isImageFile(this.selectedFile.name)) {
          await this.saveImageToPhotosAlbum(this.modifiedInfo.tempFilePath);
          common_vendor.index.showToast({
            title: "图片已保存到相册",
            icon: "success"
          });
        } else {
          const extension = this.selectedFile.name.split(".").pop() || "";
          const newFileName = `modified_${Date.now()}.${extension}`;
          try {
            const fs = common_vendor.index.getFileSystemManager();
            const newPath = `${common_vendor.index.env.USER_DATA_PATH}/${newFileName}`;
            fs.copyFileSync(this.modifiedInfo.tempFilePath, newPath);
            const savedFilePath = await this.saveFile(newPath);
            common_vendor.index.showToast({
              title: "文件已保存",
              icon: "success"
            });
          } catch (error) {
            throw new Error("保存文件失败");
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/md5-modifier.vue:594", "下载失败:", error);
        common_vendor.index.showToast({
          title: error.message || "下载失败",
          icon: "none"
        });
      }
    },
    // 打开文件
    openDocument(filePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.openDocument({
          filePath,
          success: resolve,
          fail: reject
        });
      });
    },
    // 保存文件
    saveFile(tempFilePath) {
      return new Promise((resolve, reject) => {
        common_vendor.index.saveFile({
          tempFilePath,
          success: (res) => resolve(res.savedFilePath),
          fail: () => reject(new Error("保存文件失败"))
        });
      });
    },
    formatFileSize(bytes) {
      if (bytes === 0)
        return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },
    async copyToClipboard(text) {
      try {
        await common_vendor.index.setClipboardData({
          data: text,
          success: () => {
            common_vendor.index.showToast({
              title: "复制成功！",
              icon: "success"
            });
          }
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  var _a;
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.selectFile && $options.selectFile(...args)),
    b: $data.selectedFile
  }, $data.selectedFile ? {
    c: common_vendor.t($data.selectedFile.name),
    d: common_vendor.t($options.formatFileSize($data.selectedFile.size)),
    e: common_vendor.o((...args) => $options.clearFile && $options.clearFile(...args))
  } : {}, {
    f: $data.originalMD5
  }, $data.originalMD5 ? {
    g: common_vendor.t($data.originalMD5),
    h: common_vendor.o(($event) => $options.copyToClipboard($data.originalMD5)),
    i: common_vendor.t($options.formatFileSize(((_a = $data.selectedFile) == null ? void 0 : _a.size) || 0))
  } : {}, {
    j: $data.selectedFile && $data.originalMD5
  }, $data.selectedFile && $data.originalMD5 ? {
    k: common_vendor.f($data.modifyMethods, (method, k0, i0) => {
      return {
        a: common_vendor.t(method.label),
        b: common_vendor.t(method.description),
        c: method.value,
        d: $data.selectedMethod === method.value ? 1 : "",
        e: common_vendor.o(($event) => $data.selectedMethod = method.value, method.value)
      };
    }),
    l: common_vendor.t($data.isModifying ? "修改中..." : "开始修改MD5"),
    m: $data.isModifying || !$data.selectedMethod ? 1 : "",
    n: common_vendor.o((...args) => $options.handleModify && $options.handleModify(...args))
  } : {}, {
    o: $data.modifiedInfo
  }, $data.modifiedInfo ? {
    p: common_vendor.t($data.modifiedInfo.originalMD5),
    q: common_vendor.o(($event) => $options.copyToClipboard($data.modifiedInfo.originalMD5)),
    r: common_vendor.t($data.modifiedInfo.modifiedMD5),
    s: common_vendor.o(($event) => $options.copyToClipboard($data.modifiedInfo.modifiedMD5)),
    t: common_vendor.o((...args) => $options.downloadModifiedFile && $options.downloadModifiedFile(...args)),
    v: common_vendor.o((...args) => _ctx.resetAll && _ctx.resetAll(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-57fc8a40"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/md5-modifier.js.map
