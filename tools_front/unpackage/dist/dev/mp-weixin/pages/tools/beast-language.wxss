/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-6c2f8856 {
  display: flex;
}
.flex-1.data-v-6c2f8856 {
  flex: 1;
}
.items-center.data-v-6c2f8856 {
  align-items: center;
}
.justify-center.data-v-6c2f8856 {
  justify-content: center;
}
.justify-between.data-v-6c2f8856 {
  justify-content: space-between;
}
.text-center.data-v-6c2f8856 {
  text-align: center;
}
.rounded.data-v-6c2f8856 {
  border-radius: 3px;
}
.rounded-lg.data-v-6c2f8856 {
  border-radius: 6px;
}
.shadow.data-v-6c2f8856 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-6c2f8856 {
  padding: 16rpx;
}
.m-4.data-v-6c2f8856 {
  margin: 16rpx;
}
.mb-4.data-v-6c2f8856 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-6c2f8856 {
  margin-top: 16rpx;
}
.beast-language.data-v-6c2f8856 {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 30rpx;
}
.header-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}
.header-card .header-content.data-v-6c2f8856 {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}
.header-card .header-icon.data-v-6c2f8856 {
  font-size: 48rpx;
  flex-shrink: 0;
}
.header-card .header-info.data-v-6c2f8856 {
  flex: 1;
  min-width: 0;
}
.header-card .header-title.data-v-6c2f8856 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  word-wrap: break-word;
  word-break: break-all;
}
.header-card .header-desc.data-v-6c2f8856 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
  display: block;
}
.mode-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.mode-card .card-header.data-v-6c2f8856 {
  margin-bottom: 24rpx;
}
.mode-card .section-title.data-v-6c2f8856 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.mode-grid.data-v-6c2f8856 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}
.mode-item.data-v-6c2f8856 {
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s;
}
.mode-item.active.data-v-6c2f8856 {
  background: #fff;
  border-color: #3b82f6;
  box-shadow: 0 2rpx 12rpx rgba(59, 130, 246, 0.1);
}
.mode-item .mode-name.data-v-6c2f8856 {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
}
.mode-item .mode-chars.data-v-6c2f8856 {
  display: block;
  font-size: 26rpx;
  color: #666;
}
.convert-mode.data-v-6c2f8856 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.convert-mode .mode-btn.data-v-6c2f8856 {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e5e7eb;
}
.convert-mode .mode-btn.active.data-v-6c2f8856 {
  background: #3b82f6;
  color: #fff;
  border-color: transparent;
}
.input-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.input-card .input-area.data-v-6c2f8856 {
  margin-top: 20rpx;
  width: 100%;
  box-sizing: border-box;
}
.input-card .input-textarea.data-v-6c2f8856 {
  width: 100%;
  height: 240rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  background: #f8f9fa;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-y: auto;
  box-sizing: border-box;
}
.input-card .input-footer.data-v-6c2f8856 {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 20rpx;
}
.input-card .input-footer .char-count.data-v-6c2f8856 {
  font-size: 24rpx;
  color: #666;
}
.action-buttons.data-v-6c2f8856 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.action-buttons .convert-btn.data-v-6c2f8856 {
  flex: 1;
  height: 88rpx;
  background: #3b82f6;
  color: #fff;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}
.action-buttons .convert-btn.data-v-6c2f8856:disabled {
  opacity: 0.5;
}
.action-buttons .clear-btn.data-v-6c2f8856 {
  width: 88rpx;
  height: 88rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.output-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
}
.output-card .card-header.data-v-6c2f8856 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.output-card .header-actions.data-v-6c2f8856 {
  display: flex;
  gap: 16rpx;
}
.output-card .action-btn.data-v-6c2f8856 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  background: #f8f9fa;
  color: #666;
  border: 2rpx solid #e5e7eb;
}
.output-card .action-btn.primary.data-v-6c2f8856 {
  background: #ebf5ff;
  color: #3b82f6;
  border-color: transparent;
}
.output-card .output-content.data-v-6c2f8856 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  min-height: 200rpx;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  overflow-y: auto;
}
.btn-icon.data-v-6c2f8856 {
  font-size: 36rpx;
  transition: all 0.3s;
}
.history-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.history-card .card-header.data-v-6c2f8856 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}
.history-card .section-title.data-v-6c2f8856 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.history-card .clear-history-btn.data-v-6c2f8856 {
  font-size: 24rpx;
  color: #3b82f6;
  background: none;
  border: none;
  padding: 10rpx 20rpx;
}
.history-content.data-v-6c2f8856 {
  max-height: 240rpx;
  overflow-y: auto;
}
.history-item.data-v-6c2f8856 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1rpx solid #e5e7eb;
}
.history-item.data-v-6c2f8856:last-child {
  border-bottom: none;
}
.history-item .history-item-header.data-v-6c2f8856 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.history-item .history-item-mode.data-v-6c2f8856 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.history-item .history-item-time.data-v-6c2f8856 {
  font-size: 24rpx;
  color: #666;
}
.history-item .history-item-content.data-v-6c2f8856 {
  flex: 1;
  margin: 0 16rpx;
}
.history-item .history-item-input.data-v-6c2f8856 {
  display: block;
  font-size: 26rpx;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  margin-bottom: 8rpx;
  line-height: 1.5;
}
.history-item .history-item-output.data-v-6c2f8856 {
  display: block;
  font-size: 26rpx;
  color: #666;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
  margin-bottom: 8rpx;
  line-height: 1.5;
}
.history-item .load-history-btn.data-v-6c2f8856 {
  font-size: 24rpx;
  color: #3b82f6;
  background: none;
  border: none;
  padding: 10rpx 20rpx;
}
.usage-card.data-v-6c2f8856 {
  background: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.usage-card .card-header.data-v-6c2f8856 {
  margin-bottom: 24rpx;
}
.usage-card .section-title.data-v-6c2f8856 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.usage-content.data-v-6c2f8856 {
  font-size: 28rpx;
  color: #666;
}
.convert-btn.data-v-6c2f8856 {
  position: relative;
  overflow: hidden;
}
.convert-btn .btn-content.data-v-6c2f8856 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  z-index: 1;
}
.convert-btn .btn-icon.rotate.data-v-6c2f8856 {
  animation: rotate-6c2f8856 2s linear infinite;
}
.convert-btn.is-playing.data-v-6c2f8856 {
  background: #2563eb;
}
.playing-animation.data-v-6c2f8856 {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  display: flex;
  justify-content: center;
  gap: 8rpx;
  padding: 0 20rpx;
}
.playing-animation .bar.data-v-6c2f8856 {
  width: 4rpx;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  animation: soundBars-6c2f8856 1s ease-in-out infinite;
}
.playing-animation .bar.data-v-6c2f8856:nth-child(1) {
  animation-delay: 0s;
}
.playing-animation .bar.data-v-6c2f8856:nth-child(2) {
  animation-delay: 0.2s;
}
.playing-animation .bar.data-v-6c2f8856:nth-child(3) {
  animation-delay: 0.4s;
}
@keyframes rotate-6c2f8856 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
@keyframes soundBars-6c2f8856 {
0% {
    height: 2rpx;
}
50% {
    height: 16rpx;
}
100% {
    height: 2rpx;
}
}
.volume-control.data-v-6c2f8856 {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}
.volume-control .volume-icon.data-v-6c2f8856 {
  font-size: 36rpx;
}
.volume-control .volume-slider.data-v-6c2f8856 {
  flex: 1;
}
.loading-status.data-v-6c2f8856 {
  text-align: center;
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}
.loading-status .status-text.data-v-6c2f8856 {
  font-size: 24rpx;
  color: #666;
  margin-left: 20rpx;
}