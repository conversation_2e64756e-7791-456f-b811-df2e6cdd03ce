/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c7bbac36 {
  display: flex;
}
.flex-1.data-v-c7bbac36 {
  flex: 1;
}
.items-center.data-v-c7bbac36 {
  align-items: center;
}
.justify-center.data-v-c7bbac36 {
  justify-content: center;
}
.justify-between.data-v-c7bbac36 {
  justify-content: space-between;
}
.text-center.data-v-c7bbac36 {
  text-align: center;
}
.rounded.data-v-c7bbac36 {
  border-radius: 3px;
}
.rounded-lg.data-v-c7bbac36 {
  border-radius: 6px;
}
.shadow.data-v-c7bbac36 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c7bbac36 {
  padding: 16rpx;
}
.m-4.data-v-c7bbac36 {
  margin: 16rpx;
}
.mb-4.data-v-c7bbac36 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c7bbac36 {
  margin-top: 16rpx;
}
.braid-nickname.data-v-c7bbac36 {
  min-height: 100vh;
  background-color: #ffffff;
}
.content.data-v-c7bbac36 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.card.data-v-c7bbac36 {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.card.data-v-c7bbac36:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.card-header.data-v-c7bbac36 {
  padding: 32rpx 32rpx 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header-title-container.data-v-c7bbac36 {
  display: flex;
  align-items: center;
}
.header-icon.data-v-c7bbac36 {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.header-title.data-v-c7bbac36 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.card-content.data-v-c7bbac36 {
  padding: 24rpx 32rpx 32rpx;
}
.input-section.data-v-c7bbac36 {
  position: relative;
  margin: 8rpx 0;
}
.name-input.data-v-c7bbac36 {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  padding-right: 80rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  text-align: center;
  box-sizing: border-box;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.name-input.data-v-c7bbac36:focus {
  border-color: #3b82f6;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.15);
  outline: none;
}
.input-counter.data-v-c7bbac36 {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #94a3b8;
}
.refresh-btn.data-v-c7bbac36 {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.refresh-btn.data-v-c7bbac36:hover {
  transform: translateY(-2rpx) scale(1.02);
  box-shadow: 0 6rpx 16rpx rgba(59, 130, 246, 0.35);
}
.refresh-btn.data-v-c7bbac36:active {
  transform: translateY(0) scale(0.98);
}
.refresh-icon.data-v-c7bbac36 {
  font-size: 24rpx;
}
.refresh-text.data-v-c7bbac36 {
  font-size: 24rpx;
  color: white;
  font-weight: 600;
}
.names-container.data-v-c7bbac36 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.name-item.data-v-c7bbac36 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.name-item.data-v-c7bbac36:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.name-text.data-v-c7bbac36 {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
  flex: 1;
}
.copy-btn.data-v-c7bbac36 {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  border: 1rpx solid #e2e8f0;
}
.copy-btn.data-v-c7bbac36:hover {
  background: #e2e8f0;
  transform: translateY(-2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.copy-btn.data-v-c7bbac36:active {
  transform: translateY(0) scale(0.95);
}
.copy-icon.data-v-c7bbac36 {
  font-size: 24rpx;
}
.symbols-section.data-v-c7bbac36 {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.symbol-category.data-v-c7bbac36 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.category-title.data-v-c7bbac36 {
  font-size: 28rpx;
  font-weight: 500;
  color: #64748b;
  margin-bottom: 8rpx;
}
.symbols-grid.data-v-c7bbac36 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.symbol-btn.data-v-c7bbac36 {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(248, 250, 252, 0.8);
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.symbol-btn.data-v-c7bbac36:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.symbol-btn.data-v-c7bbac36:active {
  transform: translateY(0) scale(0.95);
}
.symbol-text.data-v-c7bbac36 {
  font-size: 28rpx;
  color: #374151;
}
.instructions.data-v-c7bbac36 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.instruction-item.data-v-c7bbac36 {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.6;
  position: relative;
  padding-left: 28rpx;
}
.instruction-item.data-v-c7bbac36:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #3b82f6;
  font-weight: bold;
}
@media (max-width: 400px) {
.content.data-v-c7bbac36 {
    padding: 30rpx 20rpx;
}
.symbols-grid.data-v-c7bbac36 {
    gap: 12rpx;
}
.symbol-btn.data-v-c7bbac36 {
    width: 56rpx;
    height: 56rpx;
}
}