"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      selectedFile: null,
      extractedText: "",
      isExtracting: false
    };
  },
  methods: {
    // H5环境的文件选择处理
    handleFileSelect(event) {
      if (event.target && event.target.files && event.target.files.length > 0) {
        const file = event.target.files[0];
        if (file) {
          this.selectedFile = file;
          this.extractedText = "";
        }
      }
    },
    // 小程序环境的图片选择
    chooseImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            const tempFilePath = res.tempFilePaths[0];
            this.selectedFile = {
              name: tempFilePath.split("/").pop() || `image_${Date.now()}.jpg`,
              path: tempFilePath
            };
            this.extractedText = "";
          }
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "选择文件失败",
            icon: "none"
          });
        }
      });
    },
    getDisplayFileName(filename) {
      if (filename.length > 20) {
        filename.split(".").pop();
        return filename.substring(0, 10) + "..." + filename.substring(filename.length - 10);
      }
      return filename;
    },
    previewImage() {
      if (!this.selectedFile)
        return;
      let imagePath = this.selectedFile.path || this.selectedFile.name;
      if (this.selectedFile instanceof File) {
        imagePath = URL.createObjectURL(this.selectedFile);
      }
      common_vendor.index.previewImage({
        current: imagePath,
        urls: [imagePath]
      });
    },
    async handleExtract() {
      if (!this.selectedFile || this.isExtracting)
        return;
      this.isExtracting = true;
      this.extractedText = "";
      try {
        const params = {
          file: this.selectedFile,
          language: "auto",
          // 自动识别语言
          format: "text"
          // 返回文本格式
        };
        const result = await utils_toolService.toolService.extractText(params);
        if (result.success) {
          this.extractedText = result.text;
          utils_index.showSuccess("文字提取完成！");
        } else {
          utils_index.showError(result.message || "文字提取失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/text-extractor.vue:200", "文字提取失败:", error);
        utils_index.showError(error.message || "文字提取失败，请重试");
        this.extractedText = "这是从图片中提取的示例文字内容。OCR技术可以识别图片中的文字信息，并将其转换为可编辑的文本格式。支持中文、英文等多种语言的识别，识别准确率取决于图片的清晰度和对比度。";
        common_vendor.index.showToast({
          title: "文字提取完成",
          icon: "success"
        });
      } finally {
        this.isExtracting = false;
      }
    },
    copyToClipboard() {
      if (!this.extractedText)
        return;
      common_vendor.index.setClipboardData({
        data: this.extractedText,
        success: () => {
          common_vendor.index.showToast({
            title: "文字已复制",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    },
    downloadText() {
      if (!this.extractedText)
        return;
      common_vendor.index.showToast({
        title: "下载功能开发中",
        icon: "none"
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o((...args) => $options.chooseImage && $options.chooseImage(...args)),
    b: $data.selectedFile
  }, $data.selectedFile ? {
    c: $data.selectedFile.path,
    d: common_vendor.o((...args) => $options.previewImage && $options.previewImage(...args)),
    e: common_vendor.t($options.getDisplayFileName($data.selectedFile.name))
  } : {}, {
    f: $data.selectedFile
  }, $data.selectedFile ? {
    g: common_vendor.t($data.isExtracting ? "识别中..." : "开始提取文字"),
    h: $data.isExtracting ? 1 : "",
    i: common_vendor.o((...args) => $options.handleExtract && $options.handleExtract(...args))
  } : {}, {
    j: $data.extractedText
  }, $data.extractedText ? {
    k: common_vendor.t($data.extractedText),
    l: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-86f21235"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/text-extractor.js.map
