/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-3482bacd {
  display: flex;
}
.flex-1.data-v-3482bacd {
  flex: 1;
}
.items-center.data-v-3482bacd {
  align-items: center;
}
.justify-center.data-v-3482bacd {
  justify-content: center;
}
.justify-between.data-v-3482bacd {
  justify-content: space-between;
}
.text-center.data-v-3482bacd {
  text-align: center;
}
.rounded.data-v-3482bacd {
  border-radius: 3px;
}
.rounded-lg.data-v-3482bacd {
  border-radius: 6px;
}
.shadow.data-v-3482bacd {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-3482bacd {
  padding: 16rpx;
}
.m-4.data-v-3482bacd {
  margin: 16rpx;
}
.mb-4.data-v-3482bacd {
  margin-bottom: 16rpx;
}
.mt-4.data-v-3482bacd {
  margin-top: 16rpx;
}
.answer-book.data-v-3482bacd {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-3482bacd {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-3482bacd {
  text-align: center;
  margin-bottom: 50rpx;
}
.title-container.data-v-3482bacd {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-3482bacd {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-3482bacd {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-3482bacd {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.question-section.data-v-3482bacd, .answer-section.data-v-3482bacd, .guide-section.data-v-3482bacd, .tip-section.data-v-3482bacd {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-3482bacd {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-3482bacd {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-3482bacd {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.input-container.data-v-3482bacd {
  margin-bottom: 32rpx;
}
.input-label.data-v-3482bacd {
  display: block;
  font-size: 28rpx;
  color: #374151;
  margin-bottom: 16rpx;
  font-weight: 500;
}
.question-input.data-v-3482bacd {
  width: 100%;
  height: 90rpx;
  padding: 0 80rpx 0 32rpx;
  font-size: 32rpx;
  color: #374151;
  background: linear-gradient(to right, #f9f9f9, #ffffff);
  border: 1.5rpx solid #e9ecef;
  border-radius: 50rpx;
  outline: none;
  box-sizing: border-box;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.question-input.data-v-3482bacd:focus {
  border-color: #6366f1;
  box-shadow: 0 6rpx 24rpx rgba(99, 102, 241, 0.15);
  background: #ffffff;
}
.question-input.data-v-3482bacd::-webkit-input-placeholder {
  color: #9ca3af;
  font-weight: 400;
}
.question-input.data-v-3482bacd::placeholder {
  color: #9ca3af;
  font-weight: 400;
}
.char-count.data-v-3482bacd {
  text-align: right;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #6b7280;
}
.action-button.data-v-3482bacd {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 28rpx 40rpx;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border-radius: 16rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.25);
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.action-button.data-v-3482bacd:hover:not(.disabled) {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(99, 102, 241, 0.35);
}
.action-button.disabled.data-v-3482bacd {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}
.action-icon.data-v-3482bacd {
  font-size: 28rpx;
  margin-right: 12rpx;
}
.action-text.data-v-3482bacd {
  font-size: 30rpx;
}
.answer-card.data-v-3482bacd {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  text-align: center;
  border: 1rpx solid #f59e0b;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.15);
}
.answer-quote.data-v-3482bacd {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #92400e;
  line-height: 1.5;
  margin-bottom: 16rpx;
}
.answer-source.data-v-3482bacd {
  font-size: 24rpx;
  color: #b45309;
  font-style: italic;
}
.next-button.data-v-3482bacd {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 32rpx;
  background: #f3f4f6;
  color: #374151;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  font-weight: 500;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.next-button.data-v-3482bacd:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.next-icon.data-v-3482bacd {
  font-size: 24rpx;
  margin-right: 12rpx;
}
.next-text.data-v-3482bacd {
  font-size: 28rpx;
}
.guide-content.data-v-3482bacd {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.guide-item.data-v-3482bacd {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.guide-item.data-v-3482bacd:last-child {
  margin-bottom: 0;
}
.tip-content.data-v-3482bacd {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #0ea5e9;
  text-align: center;
}
.tip-text.data-v-3482bacd {
  font-size: 28rpx;
  color: #0c4a6e;
  line-height: 1.6;
  font-style: italic;
}
@media (max-width: 400px) {
.container.data-v-3482bacd {
    padding: 30rpx 20rpx;
}
.title-icon.data-v-3482bacd {
    font-size: 40rpx;
}
.title-text.data-v-3482bacd {
    font-size: 36rpx;
}
.question-section.data-v-3482bacd, .answer-section.data-v-3482bacd, .guide-section.data-v-3482bacd, .tip-section.data-v-3482bacd {
    padding: 24rpx;
}
.answer-card.data-v-3482bacd {
    padding: 32rpx;
}
}