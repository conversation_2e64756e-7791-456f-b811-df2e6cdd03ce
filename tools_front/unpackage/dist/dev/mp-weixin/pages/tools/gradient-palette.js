"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedGradient: "",
      gradientPresets: [
        // 自然风光系列
        { name: "日出", gradient: "linear-gradient(45deg, #ff9a9e 0%, #fad0c4 99%, #fad0c4 100%)" },
        { name: "晚霞", gradient: "linear-gradient(120deg, #f6d365 0%, #fda085 100%)" },
        { name: "极光", gradient: "linear-gradient(to right, #43e97b 0%, #38f9d7 100%)" },
        { name: "深海", gradient: "linear-gradient(to right, #4facfe 0%, #00f2fe 100%)" },
        { name: "紫霞", gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)" },
        { name: "森林", gradient: "linear-gradient(to right, #00b09b, #96c93d)" },
        { name: "星空", gradient: "linear-gradient(to right, #0f2027, #203a43, #2c5364)" },
        { name: "沙漠", gradient: "linear-gradient(to right, #ff9966, #ff5e62)" },
        // 糖果色系列
        { name: "棉花糖", gradient: "linear-gradient(45deg, #e0c3fc 0%, #8ec5fc 100%)" },
        { name: "柠檬冰", gradient: "linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%)" },
        { name: "草莓奶昔", gradient: "linear-gradient(-225deg, #FFE29F 0%, #FFA99F 48%, #FF719A 100%)" },
        { name: "薄荷巧克力", gradient: "linear-gradient(to right, #43e97b 0%, #38f9d7 100%)" },
        // 现代商务系列
        { name: "科技蓝", gradient: "linear-gradient(135deg, #5B86E5, #36D1DC)" },
        { name: "商务灰", gradient: "linear-gradient(to right, #606c88, #3f4c6b)" },
        { name: "优雅紫", gradient: "linear-gradient(to right, #DA22FF, #9733EE)" },
        { name: "专业黑", gradient: "linear-gradient(60deg, #29323c 0%, #485563 100%)" },
        // 时尚系列
        { name: "珊瑚粉", gradient: "linear-gradient(-225deg, #FFE29F 0%, #FFA99F 48%, #FF719A 100%)" },
        { name: "玫瑰金", gradient: "linear-gradient(to right, #f2709c, #ff9472)" },
        { name: "青铜", gradient: "linear-gradient(-60deg, #ff5858 0%, #f09819 100%)" },
        { name: "钛金属", gradient: "linear-gradient(-60deg, #1f1c2c 0%, #928dab 100%)" },
        // 创意系列
        { name: "霓虹灯", gradient: "linear-gradient(to right, #fc00ff, #00dbde)" },
        { name: "彩虹", gradient: "linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3)" },
        { name: "极光幻彩", gradient: "linear-gradient(-225deg, #FF057C 0%, #8D0B93 50%, #321575 100%)" },
        { name: "电音律动", gradient: "linear-gradient(to right, #8E2DE2, #4A00E0)" }
      ]
    };
  },
  methods: {
    selectGradient(gradient) {
      this.selectedGradient = gradient;
    },
    async copyGradient(gradient) {
      try {
        await common_vendor.index.setClipboardData({
          data: `background: ${gradient};`
        });
        common_vendor.index.showToast({
          title: "代码已复制",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "error"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.f($data.gradientPresets, (preset, index, i0) => {
      return {
        a: preset.gradient,
        b: common_vendor.t(preset.name),
        c: common_vendor.o(($event) => $options.copyGradient(preset.gradient), index),
        d: common_vendor.t(preset.gradient.slice(0, 40)),
        e: index,
        f: common_vendor.o(($event) => $options.selectGradient(preset.gradient), index)
      };
    }),
    b: $data.selectedGradient
  }, $data.selectedGradient ? {
    c: $data.selectedGradient,
    d: common_vendor.t($data.selectedGradient),
    e: common_vendor.o(($event) => $options.copyGradient($data.selectedGradient))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e91c53a2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/gradient-palette.js.map
