/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-b77d809d {
  display: flex;
}
.flex-1.data-v-b77d809d {
  flex: 1;
}
.items-center.data-v-b77d809d {
  align-items: center;
}
.justify-center.data-v-b77d809d {
  justify-content: center;
}
.justify-between.data-v-b77d809d {
  justify-content: space-between;
}
.text-center.data-v-b77d809d {
  text-align: center;
}
.rounded.data-v-b77d809d {
  border-radius: 3px;
}
.rounded-lg.data-v-b77d809d {
  border-radius: 6px;
}
.shadow.data-v-b77d809d {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-b77d809d {
  padding: 16rpx;
}
.m-4.data-v-b77d809d {
  margin: 16rpx;
}
.mb-4.data-v-b77d809d {
  margin-bottom: 16rpx;
}
.mt-4.data-v-b77d809d {
  margin-top: 16rpx;
}
.arrow-text.data-v-b77d809d {
  min-height: 100vh;
  background: #ffffff;
}
.container.data-v-b77d809d {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-b77d809d {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-b77d809d {
  font-size: 48rpx;
  margin-right: 16rpx;
}
.title-text.data-v-b77d809d {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
}
.subtitle.data-v-b77d809d {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-b77d809d, .results-section.data-v-b77d809d, .examples-section.data-v-b77d809d, .styles-section.data-v-b77d809d, .help-section.data-v-b77d809d {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}
.section-header.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.section-icon.data-v-b77d809d {
  font-size: 36rpx;
  margin-right: 16rpx;
}
.section-title.data-v-b77d809d {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-container.data-v-b77d809d {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.text-input.data-v-b77d809d {
  width: 100%;
  min-height: 120rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #1f2937;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
}
.text-input.data-v-b77d809d::-webkit-input-placeholder {
  color: #9ca3af;
}
.text-input.data-v-b77d809d::placeholder {
  color: #9ca3af;
}
.input-footer.data-v-b77d809d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}
.char-count.data-v-b77d809d {
  font-size: 24rpx;
  color: #6b7280;
}
.input-actions.data-v-b77d809d {
  display: flex;
  gap: 12rpx;
}
.action-btn.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  border-radius: 12rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.action-btn.generate.data-v-b77d809d {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
}
.action-btn.generate.data-v-b77d809d:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(59, 130, 246, 0.35);
}
.btn-icon.data-v-b77d809d {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.btn-text.data-v-b77d809d {
  font-size: 26rpx;
}
.results-grid.data-v-b77d809d {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16rpx;
}
.result-item.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-radius: 16rpx;
  border: 1rpx solid #93c5fd;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.result-item.data-v-b77d809d:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #60a5fa;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
}
.result-text.data-v-b77d809d {
  font-size: 28rpx;
  font-weight: 500;
  color: #1d4ed8;
  flex: 1;
  word-break: break-all;
}
.result-actions.data-v-b77d809d {
  display: flex;
  gap: 8rpx;
  margin-left: 16rpx;
}
.mini-btn.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx;
  background: #f3f4f6;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}
.mini-btn.data-v-b77d809d:hover {
  background: #e5e7eb;
  transform: translateY(-1rpx);
}
.mini-icon.data-v-b77d809d {
  font-size: 20rpx;
}
.examples-grid.data-v-b77d809d {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12rpx;
}
.example-item.data-v-b77d809d {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12rpx;
  border: 1rpx solid #bae6fd;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.example-item.data-v-b77d809d:hover {
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
  border-color: #7dd3fc;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.15);
}
.example-text.data-v-b77d809d {
  font-size: 26rpx;
  font-weight: 500;
  color: #0369a1;
  text-align: center;
}
.styles-grid.data-v-b77d809d {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.style-item.data-v-b77d809d {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border-radius: 16rpx;
  border: 1rpx solid #fcd34d;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  text-align: center;
}
.style-item.data-v-b77d809d:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%);
  border-color: #f59e0b;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.15);
}
.style-symbol.data-v-b77d809d {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: #d97706;
}
.style-name.data-v-b77d809d {
  font-size: 24rpx;
  color: #92400e;
  font-weight: 500;
}
.help-content.data-v-b77d809d {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}
.help-item.data-v-b77d809d {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 12rpx;
}
.help-item.data-v-b77d809d:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-b77d809d {
    padding: 30rpx 20rpx;
}
.examples-grid.data-v-b77d809d {
    grid-template-columns: repeat(2, 1fr);
}
.styles-grid.data-v-b77d809d {
    grid-template-columns: repeat(3, 1fr);
}
}