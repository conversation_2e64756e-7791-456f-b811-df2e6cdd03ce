"use strict";
const common_vendor = require("../../common/vendor.js");
const ColorPickerModal = () => "./ColorPickerModal.js";
const _sfc_main = {
  name: "GlassEffectGenerator",
  components: { ColorPickerModal },
  data() {
    return {
      blur: 10,
      opacity: 0.15,
      borderOpacity: 0.2,
      shadowIntensity: 0.37,
      borderRadius: 12,
      backgroundColor: "#ffffff",
      backgroundIndex: 0,
      colorPresets: [
        "#ffffff",
        "#f8f9fa",
        "#e9ecef",
        "#dee2e6",
        "#ced4da",
        "#adb5bd",
        "#6c757d",
        "#495057"
      ],
      backgrounds: [
        "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
        "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
        "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
        "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
        "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
        "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
        "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)",
        "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)"
      ],
      glassPresets: [
        {
          name: "经典玻璃",
          blur: 10,
          opacity: 0.15,
          borderOpacity: 0.2,
          shadowIntensity: 0.37,
          borderRadius: 12,
          backgroundColor: "#ffffff"
        },
        {
          name: "强烈模糊",
          blur: 25,
          opacity: 0.1,
          borderOpacity: 0.3,
          shadowIntensity: 0.5,
          borderRadius: 16,
          backgroundColor: "#ffffff"
        },
        {
          name: "轻微效果",
          blur: 5,
          opacity: 0.25,
          borderOpacity: 0.15,
          shadowIntensity: 0.2,
          borderRadius: 8,
          backgroundColor: "#ffffff"
        },
        {
          name: "深色玻璃",
          blur: 15,
          opacity: 0.2,
          borderOpacity: 0.25,
          shadowIntensity: 0.4,
          borderRadius: 12,
          backgroundColor: "#1a1a1a"
        },
        {
          name: "彩色玻璃",
          blur: 12,
          opacity: 0.18,
          borderOpacity: 0.3,
          shadowIntensity: 0.35,
          borderRadius: 20,
          backgroundColor: "#3b82f6"
        },
        {
          name: "圆角卡片",
          blur: 8,
          opacity: 0.12,
          borderOpacity: 0.18,
          shadowIntensity: 0.25,
          borderRadius: 24,
          backgroundColor: "#ffffff"
        }
      ],
      showColorPicker: false
    };
  },
  computed: {
    currentBackground() {
      return this.backgrounds[this.backgroundIndex];
    },
    glassStyle() {
      const rgb = this.hexToRgb(this.backgroundColor);
      return {
        background: `rgba(${rgb}, ${this.opacity})`,
        backdropFilter: `blur(${this.blur}px)`,
        WebkitBackdropFilter: `blur(${this.blur}px)`,
        border: `1px solid rgba(255, 255, 255, ${this.borderOpacity})`,
        borderRadius: `${this.borderRadius}px`,
        boxShadow: `0 8px 32px 0 rgba(31, 38, 135, ${this.shadowIntensity})`
      };
    },
    generatedCSS() {
      const rgb = this.hexToRgb(this.backgroundColor);
      let bg = "";
      if (this.currentBackground && this.currentBackground.startsWith("linear-gradient")) {
        bg = `background: ${this.currentBackground};`;
      } else if (this.currentBackground && (this.currentBackground.startsWith("#") || this.currentBackground.startsWith("rgb"))) {
        bg = `background: ${this.currentBackground};`;
      } else {
        bg = `background: rgba(${rgb}, ${this.opacity});`;
      }
      return `.glass-effect {
  ${bg}
  backdrop-filter: blur(${this.blur}px);
  -webkit-backdrop-filter: blur(${this.blur}px);
  border: 1px solid rgba(255, 255, 255, ${this.borderOpacity});
  border-radius: ${this.borderRadius}px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, ${this.shadowIntensity});
}

/* 可选的动画效果 */
.glass-effect:hover {
  backdrop-filter: blur(${this.blur + 5}px);
  -webkit-backdrop-filter: blur(${this.blur + 5}px);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px 0 rgba(31, 38, 135, ${this.shadowIntensity + 0.1});
}`;
    }
  },
  methods: {
    onBlurChange(e) {
      this.blur = e.detail.value;
    },
    onOpacityChange(e) {
      this.opacity = e.detail.value / 100;
    },
    onBorderOpacityChange(e) {
      this.borderOpacity = e.detail.value / 100;
    },
    onShadowChange(e) {
      this.shadowIntensity = e.detail.value / 100;
    },
    onBorderRadiusChange(e) {
      this.borderRadius = e.detail.value;
    },
    onColorChange(e) {
      this.backgroundColor = e.detail.value;
    },
    setBackgroundColor(color) {
      this.backgroundColor = color;
    },
    changeBackground() {
      this.backgroundIndex = (this.backgroundIndex + 1) % this.backgrounds.length;
      common_vendor.index.showToast({
        title: "背景已切换",
        icon: "success"
      });
    },
    applyPreset(preset) {
      this.blur = preset.blur;
      this.opacity = preset.opacity;
      this.borderOpacity = preset.borderOpacity;
      this.shadowIntensity = preset.shadowIntensity;
      this.borderRadius = preset.borderRadius;
      this.backgroundColor = preset.backgroundColor;
      common_vendor.index.showToast({
        title: "预设已应用",
        icon: "success"
      });
    },
    getPresetStyle(preset) {
      const rgb = this.hexToRgb(preset.backgroundColor);
      const lightColors = ["#fff", "#ffffff", "#f8f9fa", "#e9ecef", "#dee2e6", "#ced4da", "#adb5bd"];
      const isLight = lightColors.includes(preset.backgroundColor.toLowerCase());
      return {
        background: `rgba(${rgb}, ${preset.opacity})`,
        backdropFilter: `blur(${preset.blur}px)`,
        borderRadius: `${preset.borderRadius}px`,
        border: isLight ? "1px solid #e5e5e5" : `1px solid rgba(255,255,255,${preset.borderOpacity})`,
        boxShadow: "0 2px 8px 0 rgba(31,38,135,0.08)"
      };
    },
    hexToRgb(hex) {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      if (result) {
        return `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`;
      }
      return "255, 255, 255";
    },
    copyToClipboard() {
      common_vendor.index.setClipboardData({
        data: this.generatedCSS,
        success: () => {
          common_vendor.index.showToast({
            title: "CSS代码已复制",
            icon: "success"
          });
        }
      });
    },
    openColorPicker() {
      this.showColorPicker = true;
    },
    closeColorPicker() {
      this.showColorPicker = false;
    },
    onColorPicked(color) {
      this.backgroundColor = color;
      this.showColorPicker = false;
    }
  }
};
if (!Array) {
  const _component_color_picker_modal = common_vendor.resolveComponent("color-picker-modal");
  _component_color_picker_modal();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.blur),
    b: $data.blur,
    c: common_vendor.o((...args) => $options.onBlurChange && $options.onBlurChange(...args)),
    d: common_vendor.t($data.opacity.toFixed(2)),
    e: $data.opacity * 100,
    f: common_vendor.o((...args) => $options.onOpacityChange && $options.onOpacityChange(...args)),
    g: common_vendor.t($data.borderOpacity.toFixed(2)),
    h: $data.borderOpacity * 100,
    i: common_vendor.o((...args) => $options.onBorderOpacityChange && $options.onBorderOpacityChange(...args)),
    j: common_vendor.t($data.shadowIntensity.toFixed(2)),
    k: $data.shadowIntensity * 100,
    l: common_vendor.o((...args) => $options.onShadowChange && $options.onShadowChange(...args)),
    m: common_vendor.t($data.borderRadius),
    n: $data.borderRadius,
    o: common_vendor.o((...args) => $options.onBorderRadiusChange && $options.onBorderRadiusChange(...args)),
    p: common_vendor.f($data.colorPresets, (color, idx, i0) => {
      return {
        a: color,
        b: $data.backgroundColor === color ? 1 : "",
        c: color,
        d: common_vendor.o(($event) => idx === 0 ? $options.openColorPicker() : $options.setBackgroundColor(color), color)
      };
    }),
    q: $data.showColorPicker
  }, $data.showColorPicker ? {
    r: common_vendor.o($options.onColorPicked),
    s: common_vendor.o($options.closeColorPicker),
    t: common_vendor.p({
      value: $data.backgroundColor
    })
  } : {}, {
    v: common_vendor.f($data.glassPresets, (preset, k0, i0) => {
      return {
        a: common_vendor.s($options.getPresetStyle(preset)),
        b: common_vendor.t(preset.name),
        c: preset.name,
        d: common_vendor.o(($event) => $options.applyPreset(preset), preset.name)
      };
    }),
    w: common_vendor.o((...args) => $options.changeBackground && $options.changeBackground(...args)),
    x: common_vendor.s($options.glassStyle),
    y: $options.currentBackground,
    z: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    A: common_vendor.t($options.generatedCSS)
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-bc8177dc"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/glass-effect-generator.js.map
