/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c6f9b57b {
  display: flex;
}
.flex-1.data-v-c6f9b57b {
  flex: 1;
}
.items-center.data-v-c6f9b57b {
  align-items: center;
}
.justify-center.data-v-c6f9b57b {
  justify-content: center;
}
.justify-between.data-v-c6f9b57b {
  justify-content: space-between;
}
.text-center.data-v-c6f9b57b {
  text-align: center;
}
.rounded.data-v-c6f9b57b {
  border-radius: 3px;
}
.rounded-lg.data-v-c6f9b57b {
  border-radius: 6px;
}
.shadow.data-v-c6f9b57b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c6f9b57b {
  padding: 16rpx;
}
.m-4.data-v-c6f9b57b {
  margin: 16rpx;
}
.mb-4.data-v-c6f9b57b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c6f9b57b {
  margin-top: 16rpx;
}
.px-em-converter.data-v-c6f9b57b {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 30rpx;
}
.header-card.data-v-c6f9b57b,
.settings-card.data-v-c6f9b57b,
.converter-card.data-v-c6f9b57b,
.results-card.data-v-c6f9b57b,
.reference-card.data-v-c6f9b57b,
.info-card.data-v-c6f9b57b,
.tips-card.data-v-c6f9b57b {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
}
.card-title.data-v-c6f9b57b {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-c6f9b57b {
  padding: 30rpx;
}
.header-info.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.header-icon.data-v-c6f9b57b {
  font-size: 60rpx;
}
.header-text.data-v-c6f9b57b {
  flex: 1;
}
.header-title.data-v-c6f9b57b {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-desc.data-v-c6f9b57b {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.setting-item.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
}
.setting-info.data-v-c6f9b57b {
  flex: 1;
}
.setting-title.data-v-c6f9b57b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}
.setting-desc.data-v-c6f9b57b {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.input-group.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.base-input.data-v-c6f9b57b {
  width: 120rpx;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
}
.unit-text.data-v-c6f9b57b {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}
.converter-section.data-v-c6f9b57b {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.input-section.data-v-c6f9b57b,
.output-section.data-v-c6f9b57b {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.converter-row.data-v-c6f9b57b {
  display: flex;
  gap: 12rpx;
}
.number-input.data-v-c6f9b57b,
.result-input.data-v-c6f9b57b {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
}
.result-input.data-v-c6f9b57b {
  background: #f8f9fa;
  color: #2563eb;
}
.unit-picker.data-v-c6f9b57b {
  min-width: 160rpx;
}
.picker-display.data-v-c6f9b57b {
  padding: 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: #f8f9fa;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
}
.picker-arrow.data-v-c6f9b57b {
  margin-left: 8rpx;
  color: #6b7280;
  font-size: 20rpx;
}
.convert-controls.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 0;
}
.swap-btn.data-v-c6f9b57b {
  width: 60rpx;
  height: 60rpx;
  padding: 0;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  background: #ffffff;
  color: #666;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.swap-btn.data-v-c6f9b57b:active {
  background: #f8f9fa;
  transform: scale(0.96);
}
.swap-icon.data-v-c6f9b57b {
  font-size: 28rpx;
  font-weight: normal;
}
.convert-btn-wrapper.data-v-c6f9b57b {
  margin-top: 40rpx;
}
.convert-btn.data-v-c6f9b57b {
  padding: 24rpx 60rpx;
  background: #2563eb;
  color: white;
  border-radius: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(37, 99, 235, 0.15);
  transition: background 0.2s, transform 0.15s;
}
.convert-btn.data-v-c6f9b57b:active {
  background: #1746a2;
  transform: scale(0.97);
}
.copy-all-btn.data-v-c6f9b57b {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #2563eb;
  background: none;
  border: none;
  padding: 8rpx 24rpx;
  border-radius: 8rpx;
  transition: box-shadow 0.2s, transform 0.15s;
  display: flex;
  align-items: center;
  gap: 8rpx;
  height: 48rpx;
  box-sizing: border-box;
}
.copy-all-btn.data-v-c6f9b57b:hover, .copy-all-btn.data-v-c6f9b57b:active {
  box-shadow: 0 2rpx 8rpx rgba(37, 99, 235, 0.1);
  transform: translateY(-50%) scale(1.03);
  background: #f0f6ff;
}
.results-grid.data-v-c6f9b57b {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.result-item.data-v-c6f9b57b {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
}
.result-info.data-v-c6f9b57b {
  flex: 1;
}
.result-label.data-v-c6f9b57b {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 4rpx;
}
.result-value.data-v-c6f9b57b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  font-family: monospace;
}
.copy-icon.data-v-c6f9b57b {
  font-size: 24rpx;
  color: #6b7280;
}
.reference-grid.data-v-c6f9b57b {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.reference-item.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}
.size-info.data-v-c6f9b57b {
  flex: 1;
}
.size-name.data-v-c6f9b57b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 4rpx;
}
.size-usage.data-v-c6f9b57b {
  display: block;
  font-size: 24rpx;
  color: #666;
}
.size-values.data-v-c6f9b57b {
  text-align: right;
}
.size-px.data-v-c6f9b57b {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 4rpx;
  font-family: monospace;
}
.size-em.data-v-c6f9b57b {
  display: block;
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}
.info-list.data-v-c6f9b57b {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.info-item.data-v-c6f9b57b {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
}
.unit-header.data-v-c6f9b57b {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
.unit-name.data-v-c6f9b57b {
  font-size: 28rpx;
  font-weight: 700;
  color: #2563eb;
  font-family: monospace;
}
.unit-full-name.data-v-c6f9b57b {
  font-size: 26rpx;
  font-weight: 600;
  color: #374151;
}
.unit-desc.data-v-c6f9b57b {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}
.tips-list.data-v-c6f9b57b {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.tip-item.data-v-c6f9b57b {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.tip-bullet.data-v-c6f9b57b {
  font-size: 28rpx;
  color: #2563eb;
  margin-top: 4rpx;
}
.tip-text.data-v-c6f9b57b {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
}
.tip-bold.data-v-c6f9b57b {
  font-weight: 600;
  color: #1a1a1a;
}
@media (max-width: 375px) {
.px-em-converter.data-v-c6f9b57b {
    padding: 20rpx;
}
.card-content.data-v-c6f9b57b {
    padding: 30rpx 20rpx;
}
.number-input.data-v-c6f9b57b,
.result-input.data-v-c6f9b57b,
.picker-display.data-v-c6f9b57b {
    height: 70rpx;
    font-size: 28rpx;
}
.unit-picker.data-v-c6f9b57b {
    width: 150rpx;
}
}