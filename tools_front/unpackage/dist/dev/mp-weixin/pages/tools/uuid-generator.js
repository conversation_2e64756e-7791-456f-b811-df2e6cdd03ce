"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      uuids: [],
      count: 5,
      version: "v4",
      versionIndex: 0,
      format: "default",
      versionOptions: [
        { id: "v4", name: "UUID v4 (随机)" },
        { id: "v1", name: "UUID v1 (时间戳)" }
      ],
      formats: [
        { id: "default", name: "默认格式", example: "550e8400-e29b-41d4-a716-************" },
        { id: "uppercase", name: "大写", example: "550E8400-E29B-41D4-A716-************" },
        { id: "nohyphens", name: "无连字符", example: "550e8400e29b41d4a716************" },
        { id: "braces", name: "花括号", example: "{550e8400-e29b-41d4-a716-************}" },
        { id: "quotes", name: "引号", example: '"550e8400-e29b-41d4-a716-************"' }
      ]
    };
  },
  methods: {
    onVersionChange(e) {
      this.versionIndex = e.detail.value;
      this.version = this.versionOptions[this.versionIndex].id;
    },
    selectFormat(formatId) {
      this.format = formatId;
    },
    generateUUID() {
      const uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === "x" ? r : r & 3 | 8;
        return v.toString(16);
      });
      this.uuids.unshift({
        id: Date.now(),
        uuid,
        timestamp: (/* @__PURE__ */ new Date()).toLocaleString()
      });
      if (this.uuids.length > 20) {
        this.uuids = this.uuids.slice(0, 20);
      }
    },
    formatUUID(uuid) {
      switch (this.format) {
        case "uppercase":
          return uuid.toUpperCase();
        case "nohyphens":
          return uuid.replace(/-/g, "");
        case "braces":
          return `{${uuid}}`;
        case "quotes":
          return `"${uuid}"`;
        default:
          return uuid;
      }
    },
    generateMultipleUUIDs() {
      for (let i = 0; i < this.count; i++) {
        this.generateUUID();
      }
      common_vendor.index.showToast({
        title: `已生成${this.count}个UUID`,
        icon: "success"
      });
    },
    copyToClipboard(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    },
    copyAllUUIDs() {
      const allUUIDs = this.uuids.map((item) => item.uuid).join("\n");
      this.copyToClipboard(allUUIDs);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.versionOptions[$data.versionIndex].name),
    b: $data.versionIndex,
    c: $data.versionOptions,
    d: common_vendor.o((...args) => $options.onVersionChange && $options.onVersionChange(...args)),
    e: $data.count,
    f: common_vendor.o(common_vendor.m(($event) => $data.count = $event.detail.value, {
      number: true
    })),
    g: common_vendor.f($data.formats, (fmt, k0, i0) => {
      return {
        a: $data.format === fmt.id ? 1 : "",
        b: common_vendor.t(fmt.name),
        c: common_vendor.t(fmt.example),
        d: fmt.id,
        e: common_vendor.o(($event) => $options.selectFormat(fmt.id), fmt.id)
      };
    }),
    h: common_vendor.o((...args) => $options.generateMultipleUUIDs && $options.generateMultipleUUIDs(...args)),
    i: $data.uuids.length > 0
  }, $data.uuids.length > 0 ? {
    j: common_vendor.o((...args) => $options.copyAllUUIDs && $options.copyAllUUIDs(...args)),
    k: common_vendor.f($data.uuids, (uuid, index, i0) => {
      return {
        a: common_vendor.t(uuid),
        b: common_vendor.o(($event) => $options.copyToClipboard(uuid), index),
        c: index
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-0c5e3227"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/uuid-generator.js.map
