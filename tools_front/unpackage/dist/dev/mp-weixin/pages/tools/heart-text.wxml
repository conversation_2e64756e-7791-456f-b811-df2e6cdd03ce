<view class="heart-text data-v-e8cea889"><view class="container data-v-e8cea889"><view class="header-section data-v-e8cea889"><view class="title-container data-v-e8cea889"><text class="title-icon data-v-e8cea889">💖</text><text class="title-text data-v-e8cea889">爱心文字</text></view><text class="subtitle data-v-e8cea889">将文字排列成爱心形状，表达浪漫情感</text></view><view class="input-section data-v-e8cea889"><view class="section-header data-v-e8cea889"><text class="section-icon data-v-e8cea889">✏️</text><text class="section-title data-v-e8cea889">输入文字</text></view><view class="input-wrapper data-v-e8cea889"><input class="text-input data-v-e8cea889" placeholder="输入要表达爱意的文字..." maxlength="20" bindinput="{{a}}" value="{{b}}"/></view></view><view wx:if="{{c}}" class="result-section data-v-e8cea889"><view class="section-header data-v-e8cea889"><text class="section-icon data-v-e8cea889">💕</text><text class="section-title data-v-e8cea889">爱心排列效果</text><view class="refresh-btn data-v-e8cea889" bindtap="{{d}}"><text class="refresh-icon data-v-e8cea889">🔄</text><text class="refresh-text data-v-e8cea889">刷新</text></view></view><view class="result-grid data-v-e8cea889"><view wx:for="{{e}}" wx:for-item="result" wx:key="b" class="result-item data-v-e8cea889" bindtap="{{result.c}}"><text class="result-text data-v-e8cea889">{{result.a}}</text><view class="copy-btn data-v-e8cea889"><text class="copy-icon data-v-e8cea889">📋</text></view></view></view></view><view class="example-section data-v-e8cea889"><view class="section-header data-v-e8cea889"><text class="section-icon data-v-e8cea889">🌹</text><text class="section-title data-v-e8cea889">浪漫词汇</text></view><view class="example-grid data-v-e8cea889"><view wx:for="{{f}}" wx:for-item="word" wx:key="b" bindtap="{{word.c}}" class="example-item data-v-e8cea889"><text class="example-text data-v-e8cea889">{{word.a}}</text></view></view></view><view class="preview-section data-v-e8cea889"><view class="section-header data-v-e8cea889"><text class="section-icon data-v-e8cea889">🎨</text><text class="section-title data-v-e8cea889">爱心样式</text></view><view class="preview-grid data-v-e8cea889"><view class="preview-item data-v-e8cea889"><text class="preview-label data-v-e8cea889">💖 简约爱心</text><text class="preview-desc data-v-e8cea889">用爱心符号装饰文字</text></view><view class="preview-item data-v-e8cea889"><text class="preview-label data-v-e8cea889">💕 爱心形状</text><text class="preview-desc data-v-e8cea889">将文字排列成爱心形状</text></view><view class="preview-item data-v-e8cea889"><text class="preview-label data-v-e8cea889">💞 间隔装饰</text><text class="preview-desc data-v-e8cea889">在文字间添加爱心符号</text></view><view class="preview-item data-v-e8cea889"><text class="preview-label data-v-e8cea889">💝 边框装饰</text><text class="preview-desc data-v-e8cea889">用多种爱心符号装饰</text></view></view></view><view class="help-section data-v-e8cea889"><view class="section-header data-v-e8cea889"><text class="section-icon data-v-e8cea889">💡</text><text class="section-title data-v-e8cea889">使用说明</text></view><view class="help-content data-v-e8cea889"><text class="help-item data-v-e8cea889">• 将文字排列成爱心形状</text><text class="help-item data-v-e8cea889">• 支持中英文混合输入</text><text class="help-item data-v-e8cea889">• 提供多种爱心装饰效果</text><text class="help-item data-v-e8cea889">• 适合表达爱意和浪漫情感</text><text class="help-item data-v-e8cea889">• 一键复制分享给心爱的人</text></view></view></view></view>