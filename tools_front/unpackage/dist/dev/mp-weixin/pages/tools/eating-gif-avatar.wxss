/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-e2d77a92 {
  display: flex;
}
.flex-1.data-v-e2d77a92 {
  flex: 1;
}
.items-center.data-v-e2d77a92 {
  align-items: center;
}
.justify-center.data-v-e2d77a92 {
  justify-content: center;
}
.justify-between.data-v-e2d77a92 {
  justify-content: space-between;
}
.text-center.data-v-e2d77a92 {
  text-align: center;
}
.rounded.data-v-e2d77a92 {
  border-radius: 3px;
}
.rounded-lg.data-v-e2d77a92 {
  border-radius: 6px;
}
.shadow.data-v-e2d77a92 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-e2d77a92 {
  padding: 16rpx;
}
.m-4.data-v-e2d77a92 {
  margin: 16rpx;
}
.mb-4.data-v-e2d77a92 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-e2d77a92 {
  margin-top: 16rpx;
}
.gif-avatar-tool.data-v-e2d77a92 {
  min-height: 100vh;
  background: #f9fafb;
  padding: 30rpx;
}
.tips-card.data-v-e2d77a92, .preview-card.data-v-e2d77a92, .settings-card.data-v-e2d77a92, .template-card.data-v-e2d77a92, .upload-card.data-v-e2d77a92, .header-card.data-v-e2d77a92 {
  background: #ffffff;
  border-radius: 24rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #e5e7eb;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
}
.tips-card.data-v-e2d77a92:hover, .preview-card.data-v-e2d77a92:hover, .settings-card.data-v-e2d77a92:hover, .template-card.data-v-e2d77a92:hover, .upload-card.data-v-e2d77a92:hover, .header-card.data-v-e2d77a92:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-2rpx);
}
.card-header.data-v-e2d77a92 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-bottom: 1rpx solid #e8ecff;
}
.card-header .header-icon.data-v-e2d77a92 {
  font-size: 40rpx;
  margin-right: 20rpx;
}
.card-header .header-title.data-v-e2d77a92 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  flex: 1;
}
.header-card.data-v-e2d77a92 {
  margin-bottom: 40rpx;
}
.header-card .header-content.data-v-e2d77a92 {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(139, 92, 246, 0.95) 100%);
  color: white;
  border-radius: 24rpx;
}
.header-card .header-content .header-icon.data-v-e2d77a92 {
  font-size: 48rpx;
  margin-right: 24rpx;
  margin-top: 4rpx;
  animation: bounce-e2d77a92 2s infinite;
}
.header-card .header-content .header-info.data-v-e2d77a92 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.header-card .header-content .header-info .header-title.data-v-e2d77a92 {
  font-size: 40rpx;
  font-weight: 700;
  color: rgba(255, 255, 255, 0.98);
  line-height: 1.2;
  margin: 0;
}
.header-card .header-content .header-info .header-subtitle.data-v-e2d77a92 {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.4;
  font-weight: 400;
}
.upload-card .upload-content.data-v-e2d77a92 {
  padding: 30rpx;
}
.upload-card .upload-area.data-v-e2d77a92 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 320rpx;
  border: 3rpx dashed #e5e7eb;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.upload-card .upload-area.data-v-e2d77a92:hover {
  border-color: #6366f1;
  background: #f8f9ff;
}
.upload-card .image-preview.data-v-e2d77a92 {
  position: relative;
}
.upload-card .image-preview .avatar-image.data-v-e2d77a92 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  border: 6rpx solid #6366f1;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.upload-card .image-preview .remove-btn.data-v-e2d77a92 {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 60rpx;
  height: 60rpx;
  background: #ef4444;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.upload-card .image-preview .remove-btn.data-v-e2d77a92:hover {
  background: #dc2626;
  transform: scale(1.1);
}
.upload-card .image-preview .remove-btn .remove-icon.data-v-e2d77a92 {
  color: white;
  font-size: 32rpx;
  font-weight: 700;
}
.upload-card .upload-placeholder.data-v-e2d77a92 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.upload-card .upload-placeholder .upload-icon.data-v-e2d77a92 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}
.upload-card .upload-placeholder .upload-text.data-v-e2d77a92 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}
.upload-card .upload-placeholder .upload-hint.data-v-e2d77a92 {
  font-size: 24rpx;
  color: #6b7280;
}
.template-card .template-grid.data-v-e2d77a92 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  padding: 30rpx;
}
.template-card .template-item.data-v-e2d77a92 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 2rpx solid #e8ecff;
  border-radius: 20rpx;
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  cursor: pointer;
}
.template-card .template-item.data-v-e2d77a92:hover {
  border-color: #6366f1;
  background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
  box-shadow: 0 8rpx 20rpx rgba(99, 102, 241, 0.15);
  transform: translateY(-4rpx);
}
.template-card .template-item.active.data-v-e2d77a92 {
  border-color: #6366f1;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 0 12rpx 28rpx rgba(99, 102, 241, 0.25);
}
.template-card .template-item .template-preview.data-v-e2d77a92 {
  font-size: 56rpx;
  margin-bottom: 16rpx;
  animation: pulse-e2d77a92 2s infinite;
}
.template-card .template-item .template-name.data-v-e2d77a92 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.template-card .template-item .template-desc.data-v-e2d77a92 {
  font-size: 22rpx;
  color: #6b7280;
  text-align: center;
  line-height: 1.4;
}
.settings-card .settings-content.data-v-e2d77a92 {
  padding: 30rpx;
}
.settings-card .setting-item.data-v-e2d77a92 {
  margin-bottom: 40rpx;
}
.settings-card .setting-item.data-v-e2d77a92:last-child {
  margin-bottom: 0;
}
.settings-card .setting-item .setting-label.data-v-e2d77a92 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 20rpx;
}
.settings-card .setting-item .setting-value.data-v-e2d77a92 {
  font-size: 26rpx;
  font-weight: 600;
  color: #6366f1;
  margin-left: 20rpx;
}
.settings-card .speed-slider.data-v-e2d77a92 {
  width: 100%;
}
.settings-card .quality-group.data-v-e2d77a92 {
  display: flex;
  gap: 30rpx;
}
.settings-card .quality-group .quality-option.data-v-e2d77a92 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.settings-card .quality-group .quality-option .quality-text.data-v-e2d77a92 {
  font-size: 26rpx;
  color: #1a1a1a;
}
.action-buttons.data-v-e2d77a92 {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin: 40rpx 0;
  padding: 0 30rpx;
}
.action-buttons .generate-btn.data-v-e2d77a92,
.action-buttons .download-btn.data-v-e2d77a92 {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  width: 100%;
  padding: 32rpx;
  border: none;
  border-radius: 28rpx;
  font-weight: 600;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.2, 0, 0.1, 1);
  overflow: hidden;
}
.action-buttons .generate-btn.data-v-e2d77a92::before,
.action-buttons .download-btn.data-v-e2d77a92::before {
  content: "";
  position: absolute;
  inset: 1rpx;
  border-radius: 27rpx;
  background: inherit;
  z-index: -1;
}
.action-buttons .generate-btn.data-v-e2d77a92::after,
.action-buttons .download-btn.data-v-e2d77a92::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 28rpx;
  padding: 1rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.4), rgba(255, 255, 255, 0.1));
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}
.action-buttons .generate-btn .btn-icon.data-v-e2d77a92,
.action-buttons .download-btn .btn-icon.data-v-e2d77a92 {
  font-size: 36rpx;
  line-height: 1;
}
.action-buttons .generate-btn .btn-text.data-v-e2d77a92,
.action-buttons .download-btn .btn-text.data-v-e2d77a92 {
  font-size: 32rpx;
  font-weight: 600;
  letter-spacing: 0.5rpx;
}
.action-buttons .generate-btn.data-v-e2d77a92 {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.95) 0%, rgba(139, 92, 246, 0.95) 100%);
  color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8rpx 24rpx rgba(99, 102, 241, 0.25), 0 2rpx 4rpx rgba(99, 102, 241, 0.1), inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.action-buttons .generate-btn.data-v-e2d77a92:hover {
  transform: translateY(-2rpx) scale(1.01);
  box-shadow: 0 12rpx 32rpx rgba(99, 102, 241, 0.35), 0 4rpx 8rpx rgba(99, 102, 241, 0.2), inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.action-buttons .generate-btn.data-v-e2d77a92:active {
  transform: translateY(1rpx) scale(0.99);
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.2), 0 2rpx 4rpx rgba(99, 102, 241, 0.1), inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.action-buttons .generate-btn.disabled.data-v-e2d77a92 {
  background: linear-gradient(135deg, rgba(203, 213, 225, 0.95) 0%, rgba(148, 163, 184, 0.95) 100%);
  color: rgba(255, 255, 255, 0.7);
  box-shadow: none;
  cursor: not-allowed;
}
.action-buttons .generate-btn.disabled.data-v-e2d77a92:hover {
  transform: none;
  box-shadow: none;
}
.action-buttons .generate-btn .loading-spinner.data-v-e2d77a92 {
  width: 36rpx;
  height: 36rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  animation: spin-e2d77a92 0.8s linear infinite;
}
.action-buttons .download-btn.data-v-e2d77a92 {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.95) 0%, rgba(5, 150, 105, 0.95) 100%);
  color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 8rpx 24rpx rgba(16, 185, 129, 0.25), 0 2rpx 4rpx rgba(16, 185, 129, 0.1), inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.action-buttons .download-btn.data-v-e2d77a92:hover {
  transform: translateY(-2rpx) scale(1.01);
  box-shadow: 0 12rpx 32rpx rgba(16, 185, 129, 0.35), 0 4rpx 8rpx rgba(16, 185, 129, 0.2), inset 0 -1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.action-buttons .download-btn.data-v-e2d77a92:active {
  transform: translateY(1rpx) scale(0.99);
  box-shadow: 0 6rpx 20rpx rgba(16, 185, 129, 0.2), 0 2rpx 4rpx rgba(16, 185, 129, 0.1), inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}
.preview-card .preview-content.data-v-e2d77a92 {
  padding: 30rpx;
}
.preview-card .preview-area.data-v-e2d77a92 {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300rpx;
}
.preview-card .preview-area .preview-image.data-v-e2d77a92 {
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  border: 4rpx solid #6366f1;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
}
.preview-card .preview-area .preview-image.preview-animation.data-v-e2d77a92 {
  animation: previewAnimation-e2d77a92 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
.preview-card .preview-area .animation-overlay.data-v-e2d77a92 {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.preview-card .preview-area .animation-overlay .animation-emoji.data-v-e2d77a92 {
  font-size: 80rpx;
  animation: overlayAnimation-e2d77a92 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}
.tips-card .tips-content.data-v-e2d77a92 {
  padding: 30rpx;
}
.tips-card .tip-item.data-v-e2d77a92 {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.tips-card .tip-item.data-v-e2d77a92:last-child {
  margin-bottom: 0;
}
.tips-card .tip-item .tip-bullet.data-v-e2d77a92 {
  font-size: 28rpx;
  color: #6366f1;
  margin-right: 16rpx;
  margin-top: 4rpx;
  font-weight: bold;
}
.tips-card .tip-item .tip-text.data-v-e2d77a92 {
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
  flex: 1;
}
@keyframes spin-e2d77a92 {
to {
    transform: rotate(360deg);
}
}
@keyframes bounce-e2d77a92 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-10rpx);
}
}
@keyframes pulse-e2d77a92 {
0%, 100% {
    opacity: 1;
}
50% {
    opacity: 0.6;
}
}
@keyframes previewAnimation-e2d77a92 {
0% {
    transform: scale(1) rotate(0deg);
}
25% {
    transform: scale(0.9) rotate(-5deg);
}
50% {
    transform: scale(0.85) rotate(0deg);
}
75% {
    transform: scale(0.9) rotate(5deg);
}
100% {
    transform: scale(1) rotate(0deg);
}
}
@keyframes overlayAnimation-e2d77a92 {
0% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
}
25% {
    transform: scale(1.1) translate(-10rpx, 0);
    opacity: 0.8;
}
50% {
    transform: scale(1.2) translate(0, -10rpx);
    opacity: 0.6;
}
75% {
    transform: scale(1.1) translate(10rpx, 0);
    opacity: 0.8;
}
100% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
}
}
.eating-animation.data-v-e2d77a92 {
  animation: eating-e2d77a92 2s infinite;
}
.kissing-animation.data-v-e2d77a92 {
  animation: kissing-e2d77a92 1.5s infinite;
}
.hugging-animation.data-v-e2d77a92 {
  animation: hugging-e2d77a92 2s infinite;
}
.spinning-animation.data-v-e2d77a92 {
  animation: spinning-e2d77a92 1s linear infinite;
}
.bouncing-animation.data-v-e2d77a92 {
  animation: bouncing-e2d77a92 1s infinite;
}
.shaking-animation.data-v-e2d77a92 {
  animation: shaking-e2d77a92 0.5s infinite;
}
@keyframes eating-e2d77a92 {
0%, 100% {
    transform: scale(1);
}
25% {
    transform: scale(0.9);
}
50% {
    transform: scale(0.7);
}
75% {
    transform: scale(0.9);
}
}
@keyframes kissing-e2d77a92 {
0%, 100% {
    transform: scale(1) rotate(0deg);
}
50% {
    transform: scale(1.1) rotate(5deg);
}
}
@keyframes hugging-e2d77a92 {
0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.2);
}
}
@keyframes spinning-e2d77a92 {
to {
    transform: rotate(360deg);
}
}
@keyframes bouncing-e2d77a92 {
0%, 100% {
    transform: translateY(0);
}
50% {
    transform: translateY(-20rpx);
}
}
@keyframes shaking-e2d77a92 {
0%, 100% {
    transform: translateX(0);
}
25% {
    transform: translateX(-10rpx);
}
75% {
    transform: translateX(10rpx);
}
}
@media (max-width: 750rpx) {
.template-grid.data-v-e2d77a92 {
    grid-template-columns: 1fr;
    gap: 16rpx;
}
.quality-group.data-v-e2d77a92 {
    flex-direction: column;
    gap: 20rpx;
}
}