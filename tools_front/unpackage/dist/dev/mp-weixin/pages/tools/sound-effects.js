"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "all",
      isExpanded: false,
      currentPlaying: null,
      currentAudio: null,
      isPlaying: false,
      currentTime: 0,
      playProgress: 0,
      audioContext: null,
      isDownloading: null,
      // 新增下载状态
      progressInterval: null,
      // 新增进度条定时器管理
      isLoadingAudio: false,
      // 新增加载状态
      // 分类数据
      categories: [
        { id: "all", name: "全部" },
        { id: "featured", name: "精选" },
        { id: "ironman", name: "钢铁侠" },
        { id: "doraemon2", name: "哪吒2" },
        { id: "car_lock", name: "订制锁车音" },
        { id: "ikun", name: "i<PERSON><PERSON>" },
        { id: "retro_ads", name: "复古广告" },
        { id: "doraemon", name: "哆啦A梦" },
        { id: "princess", name: "公主上车" },
        { id: "game_anime", name: "游戏日漫" },
        { id: "counter_hero", name: "反恐精英" },
        { id: "macao", name: "澳门弹珠台金币" },
        { id: "plants", name: "植物大战僵尸" },
        { id: "mario", name: "超级玛丽全集" },
        { id: "police", name: "警车" },
        { id: "movie_quotes", name: "电影名句" },
        { id: "crayon", name: "蜡笔小新" },
        { id: "famous_quotes", name: "名人语录" },
        { id: "star_wars", name: "星际争霸音效全集" },
        { id: "tang_poems", name: "卢本伟5句" },
        { id: "jay_chou", name: "周杰伦曲头" },
        { id: "star_birthday", name: "明星拜年" },
        { id: "red_army", name: "红警人物语音-盟军" },
        { id: "red_soviet", name: "红警人物语音-苏军" },
        { id: "red_vip", name: "红警人物语音-尤里VIP" },
        { id: "mcdonald", name: "麦当劳专辑" },
        { id: "harry_potter", name: "哈利波特-魔法咒语" },
        { id: "cctv", name: "CCTV常用音乐" },
        { id: "blackpink", name: "blackpink" },
        { id: "tokyo", name: "东京热" },
        { id: "mtr", name: "港铁到站提示音" },
        { id: "csgo", name: "尖叫鸡-浣熊" }
      ],
      // 音频数据
      audioList: [
        // 精选
        {
          id: 1,
          title: "钢铁侠启动音效",
          category: "featured",
          duration: "00:03",
          url: "https://cdn.example.com/audio/ironman-startup.mp3"
          // 替换为实际的音频文件URL
        },
        { id: 2, title: "哆啦A梦任意门", category: "featured", duration: "00:02", url: "/static/audio/doraemon-door.mp3" },
        { id: 3, title: "超级玛丽金币音效", category: "featured", duration: "00:01", url: "/static/audio/mario-coin.mp3" },
        // 钢铁侠
        { id: 4, title: "钢铁侠战衣启动", category: "ironman", duration: "00:05", url: "/static/audio/ironman-suit.mp3" },
        { id: 5, title: "贾维斯语音", category: "ironman", duration: "00:04", url: "/static/audio/jarvis-voice.mp3" },
        { id: 6, title: "反应堆充能", category: "ironman", duration: "00:03", url: "/static/audio/arc-reactor.mp3" },
        // 哆啦A梦
        { id: 7, title: "哆啦A梦主题曲", category: "doraemon", duration: "00:08", url: "/static/audio/doraemon-theme.mp3" },
        { id: 8, title: "竹蜻蜓飞行", category: "doraemon", duration: "00:03", url: "/static/audio/bamboo-copter.mp3" },
        { id: 9, title: "时光机启动", category: "doraemon", duration: "00:04", url: "/static/audio/time-machine.mp3" },
        // 蜡笔小新
        { id: 10, title: "小新经典台词", category: "crayon", duration: "00:02", url: "/static/audio/shinchan-voice.mp3" },
        { id: 11, title: "动感超人", category: "crayon", duration: "00:03", url: "/static/audio/action-kamen.mp3" },
        // 哪吒2
        { id: 12, title: "哪吒变身", category: "nezha", duration: "00:04", url: "/static/audio/nezha-transform.mp3" },
        { id: 13, title: "混天绫攻击", category: "nezha", duration: "00:03", url: "/static/audio/huntian-ling.mp3" },
        // 锁车音
        { id: 14, title: "奔驰锁车音", category: "car", duration: "00:02", url: "/static/audio/benz-lock.mp3" },
        { id: 15, title: "宝马锁车音", category: "car", duration: "00:02", url: "/static/audio/bmw-lock.mp3" },
        { id: 16, title: "奥迪锁车音", category: "car", duration: "00:02", url: "/static/audio/audi-lock.mp3" },
        // 植物大战僵尸
        { id: 17, title: "豌豆射手攻击", category: "plants", duration: "00:01", url: "/static/audio/peashooter.mp3" },
        { id: 18, title: "僵尸咆哮", category: "plants", duration: "00:02", url: "/static/audio/zombie-roar.mp3" },
        { id: 19, title: "向日葵收集阳光", category: "plants", duration: "00:01", url: "/static/audio/sunflower.mp3" },
        // 超级玛丽
        { id: 20, title: "玛丽跳跃", category: "mario", duration: "00:01", url: "/static/audio/mario-jump.mp3" },
        { id: 21, title: "吃蘑菇变大", category: "mario", duration: "00:02", url: "/static/audio/mario-powerup.mp3" },
        { id: 22, title: "过关音效", category: "mario", duration: "00:03", url: "/static/audio/mario-level-complete.mp3" },
        // 网络热梗
        { id: 23, title: "确实", category: "meme", duration: "00:01", url: "/static/audio/queshi.mp3" },
        { id: 24, title: "6666", category: "meme", duration: "00:02", url: "/static/audio/6666.mp3" },
        { id: 25, title: "芜湖起飞", category: "meme", duration: "00:02", url: "/static/audio/wuhu-takeoff.mp3" },
        // 通知音效
        { id: 26, title: "微信消息提示", category: "notification", duration: "00:01", url: "/static/audio/wechat-msg.mp3" },
        { id: 27, title: "QQ消息提示", category: "notification", duration: "00:01", url: "/static/audio/qq-msg.mp3" },
        { id: 28, title: "邮件提醒", category: "notification", duration: "00:02", url: "/static/audio/email-notification.mp3" },
        // 自然音效
        { id: 29, title: "鸟儿啁啾", category: "nature", duration: "00:05", url: "/static/audio/birds-chirping.mp3" },
        { id: 30, title: "流水声", category: "nature", duration: "00:08", url: "/static/audio/water-flowing.mp3" },
        { id: 31, title: "雨声", category: "nature", duration: "00:10", url: "/static/audio/rain-sound.mp3" },
        // iKun
        { id: 32, title: "鸡你太美", category: "ikun", duration: "00:03", url: "/static/audio/jntm.mp3" },
        { id: 33, title: "只因你太美", category: "ikun", duration: "00:04", url: "/static/audio/zynm.mp3" },
        { id: 34, title: "小黑子", category: "ikun", duration: "00:02", url: "/static/audio/xhz.mp3" },
        // 复古广告
        { id: 35, title: "脑白金经典", category: "retro_ads", duration: "00:05", url: "/static/audio/nbj.mp3" },
        { id: 36, title: "东鹏特饮", category: "retro_ads", duration: "00:04", url: "/static/audio/dpty.mp3" },
        { id: 37, title: "六个核桃", category: "retro_ads", duration: "00:03", url: "/static/audio/lght.mp3" },
        // 游戏日漫
        { id: 38, title: "原神启动", category: "game_anime", duration: "00:02", url: "/static/audio/ysqd.mp3" },
        { id: 39, title: "王者荣耀击杀", category: "game_anime", duration: "00:02", url: "/static/audio/wzry-kill.mp3" },
        { id: 40, title: "火影忍者", category: "game_anime", duration: "00:03", url: "/static/audio/naruto.mp3" },
        // 反恐精英
        { id: 41, title: "Fire in the hole", category: "counter_hero", duration: "00:02", url: "/static/audio/cs-fire.mp3" },
        { id: 42, title: "Bomb has been planted", category: "counter_hero", duration: "00:03", url: "/static/audio/cs-bomb.mp3" },
        { id: 43, title: "Counter Terrorists Win", category: "counter_hero", duration: "00:02", url: "/static/audio/cs-win.mp3" },
        // 澳门弹珠台
        { id: 44, title: "澳门金币声", category: "macao", duration: "00:02", url: "/static/audio/macao-coin.mp3" },
        { id: 45, title: "澳门中奖声", category: "macao", duration: "00:03", url: "/static/audio/macao-win.mp3" },
        // 红警语音
        { id: 46, title: "基地建设完成", category: "red_army", duration: "00:02", url: "/static/audio/ra-base.mp3" },
        { id: 47, title: "核弹发射", category: "red_soviet", duration: "00:02", url: "/static/audio/ra-nuke.mp3" },
        { id: 48, title: "尤里的复仇", category: "red_vip", duration: "00:03", url: "/static/audio/ra-yuri.mp3" },
        // 哈利波特
        { id: 49, title: "阿瓦达索命", category: "harry_potter", duration: "00:02", url: "/static/audio/hp-avada.mp3" },
        { id: 50, title: "神锋无影", category: "harry_potter", duration: "00:02", url: "/static/audio/hp-sectumsempra.mp3" },
        // CCTV
        { id: 51, title: "新闻联播片头", category: "cctv", duration: "00:05", url: "/static/audio/cctv-news.mp3" },
        { id: 52, title: "焦点访谈", category: "cctv", duration: "00:04", url: "/static/audio/cctv-focus.mp3" },
        // Blackpink
        { id: 53, title: "How You Like That", category: "blackpink", duration: "00:03", url: "/static/audio/bp-hylt.mp3" },
        { id: 54, title: "DDU-DU DDU-DU", category: "blackpink", duration: "00:03", url: "/static/audio/bp-dddd.mp3" },
        // 港铁提示音
        { id: 55, title: "请勿靠近车门", category: "mtr", duration: "00:03", url: "/static/audio/mtr-door.mp3" },
        { id: 56, title: "下一站", category: "mtr", duration: "00:02", url: "/static/audio/mtr-next.mp3" },
        // 周杰伦
        { id: 57, title: "听妈妈的话前奏", category: "jay_chou", duration: "00:05", url: "/static/audio/jay-tmmdh.mp3" },
        { id: 58, title: "菊花台前奏", category: "jay_chou", duration: "00:05", url: "/static/audio/jay-jht.mp3" },
        // 卢本伟
        { id: 59, title: "你币有了", category: "tang_poems", duration: "00:02", url: "/static/audio/lbw-nb.mp3" },
        { id: 60, title: "正能量", category: "tang_poems", duration: "00:02", url: "/static/audio/lbw-znl.mp3" }
      ]
    };
  },
  async onLoad() {
    await this.loadSoundEffects();
  },
  onUnload() {
    this.cleanupAudio();
  },
  computed: {
    displayCategories() {
      return this.categories;
    },
    filteredAudioList() {
      let filtered = this.audioList;
      if (this.selectedCategory !== "all") {
        filtered = filtered.filter((audio) => audio.category === this.selectedCategory);
      }
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter(
          (audio) => audio.title.toLowerCase().includes(keyword)
        );
      }
      return filtered;
    }
  },
  methods: {
    async loadSoundEffects() {
      this.isLoadingAudio = true;
      try {
        const params = {
          category: this.selectedCategory,
          keyword: this.searchKeyword,
          page: 1,
          limit: 100
        };
        const result = await utils_toolService.toolService.getSoundEffects(params);
        if (result.success) {
          this.audioList = result.data || [];
          utils_index.showSuccess("音效数据加载成功");
        } else {
          utils_index.showError(result.message || "加载失败，请重试");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:359", "音效数据加载失败:", error);
        utils_index.showError(error.message || "加载失败，请重试");
        common_vendor.index.__f__("log", "at pages/tools/sound-effects.vue:363", "使用本地音效数据作为备用方案");
      } finally {
        this.isLoadingAudio = false;
      }
    },
    handleSearch() {
      this.loadSoundEffects();
    },
    clearSearch() {
      this.searchKeyword = "";
      this.loadSoundEffects();
    },
    selectCategory(categoryId) {
      this.selectedCategory = categoryId;
      this.loadSoundEffects();
    },
    toggleCategoryExpand() {
      this.isExpanded = !this.isExpanded;
    },
    getCategoryName(categoryId) {
      const category = this.categories.find((cat) => cat.id === categoryId);
      return category ? category.name : "未知";
    },
    playAudio(audio) {
      if (this.currentPlaying === audio.id) {
        this.isPlaying = !this.isPlaying;
        if (this.isPlaying) {
          this.simulatePlayProgress();
          common_vendor.index.showToast({
            title: "继续播放",
            icon: "none"
          });
        } else {
          common_vendor.index.showToast({
            title: "暂停播放",
            icon: "none"
          });
        }
        return;
      }
      this.currentAudio = audio;
      this.currentPlaying = audio.id;
      this.isPlaying = true;
      this.currentTime = 0;
      this.playProgress = 0;
      this.simulatePlayProgress();
      common_vendor.index.showToast({
        title: "开始播放",
        icon: "none"
      });
    },
    stopPlay() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
        this.progressInterval = null;
      }
      this.currentPlaying = null;
      this.currentAudio = null;
      this.isPlaying = false;
      this.currentTime = 0;
      this.playProgress = 0;
    },
    simulatePlayProgress() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
      }
      if (!this.isPlaying)
        return;
      this.progressInterval = setInterval(() => {
        if (!this.isPlaying) {
          clearInterval(this.progressInterval);
          this.progressInterval = null;
          return;
        }
        this.currentTime += 0.1;
        this.playProgress = this.currentTime / this.getDurationInSeconds() * 100;
        if (this.playProgress >= 100) {
          clearInterval(this.progressInterval);
          this.progressInterval = null;
          this.stopPlay();
        }
      }, 100);
    },
    getDurationInSeconds() {
      if (!this.currentAudio)
        return 0;
      const duration = this.currentAudio.duration;
      const parts = duration.split(":");
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    },
    formatTime(seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    },
    async downloadAudio(audio) {
      if (this.isDownloading === audio.id) {
        return;
      }
      this.isDownloading = audio.id;
      try {
        common_vendor.index.showLoading({
          title: "准备下载...",
          mask: true
        });
        const params = {
          audioId: audio.id,
          audioUrl: audio.url,
          title: audio.title
        };
        const result = await utils_toolService.toolService.downloadSoundEffect(params);
        if (result.success) {
          const downloadUrl = result.downloadUrl || audio.url;
          common_vendor.index.downloadFile({
            url: downloadUrl,
            success: (res) => {
              if (res.statusCode === 200) {
                common_vendor.wx$1.saveFile({
                  tempFilePath: res.tempFilePath,
                  success: (saveRes) => {
                    common_vendor.index.hideLoading();
                    utils_index.showSuccess("音效下载成功！");
                  },
                  fail: (err) => {
                    common_vendor.index.hideLoading();
                    utils_index.showError("保存失败");
                    common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:528", "保存失败:", err);
                  }
                });
              } else {
                common_vendor.index.hideLoading();
                utils_index.showError("下载失败");
              }
            },
            fail: (err) => {
              common_vendor.index.hideLoading();
              utils_index.showError("下载失败");
              common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:552", "下载失败:", err);
            }
          });
        } else {
          common_vendor.index.hideLoading();
          utils_index.showError(result.message || "获取下载链接失败");
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:562", "下载音效失败:", error);
        utils_index.showError(error.message || "下载失败，请重试");
        common_vendor.index.downloadFile({
          url: audio.url,
          success: (res) => {
            if (res.statusCode === 200) {
              utils_index.showSuccess("音效下载成功！");
            } else {
              utils_index.showError("下载失败");
            }
          },
          fail: (err) => {
            utils_index.showError("下载失败");
            common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:577", "下载失败:", err);
          }
        });
      } finally {
        this.isDownloading = null;
      }
    },
    cleanupAudio() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
        this.progressInterval = null;
      }
      if (this.audioContext) {
        this.audioContext.destroy();
        this.audioContext = null;
      }
      this.currentPlaying = null;
      this.currentAudio = null;
      this.isPlaying = false;
      this.currentTime = 0;
      this.playProgress = 0;
    },
    // 新增一个检查权限的方法
    async checkPermission() {
      try {
        const res = await common_vendor.index.getSetting();
        if (!res.authSetting["scope.writePhotosAlbum"]) {
          const auth = await common_vendor.index.authorize({
            scope: "scope.writePhotosAlbum"
          });
          return auth;
        }
        return true;
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/tools/sound-effects.vue:632", "获取权限失败:", e);
        return false;
      }
    },
    // 新增一个保存到相册的方法
    saveToAlbum(filePath) {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath,
        success: () => {
          common_vendor.index.showToast({
            title: "已保存到相册",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存到相册失败",
            icon: "none"
          });
        }
      });
    }
  },
  beforeDestroy() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.handleSearch && $options.handleSearch(...args)]),
    b: $data.searchKeyword,
    c: $data.searchKeyword
  }, $data.searchKeyword ? {
    d: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    e: common_vendor.t($data.isExpanded ? "收起" : "展开全部"),
    f: common_vendor.o((...args) => $options.toggleCategoryExpand && $options.toggleCategoryExpand(...args)),
    g: !$data.isExpanded
  }, !$data.isExpanded ? {
    h: common_vendor.f($options.displayCategories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: category.id,
        c: "category-" + category.id,
        d: common_vendor.n({
          active: $data.selectedCategory === category.id
        }),
        e: common_vendor.o(($event) => $options.selectCategory(category.id), category.id)
      };
    }),
    i: "category-" + $data.selectedCategory
  } : {
    j: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: category.id,
        c: "category-" + category.id,
        d: common_vendor.n({
          active: $data.selectedCategory === category.id
        }),
        e: common_vendor.o(($event) => $options.selectCategory(category.id), category.id)
      };
    })
  }, {
    k: common_vendor.f($options.filteredAudioList, (audio, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t($data.currentPlaying === audio.id && $data.isPlaying ? "⏸" : "▶"),
        b: common_vendor.n({
          playing: $data.currentPlaying === audio.id && $data.isPlaying
        }),
        c: common_vendor.t(audio.title),
        d: $data.isDownloading === audio.id
      }, $data.isDownloading === audio.id ? {} : {}, {
        e: $data.isDownloading === audio.id ? 1 : "",
        f: common_vendor.o(($event) => $options.downloadAudio(audio), audio.id),
        g: $data.currentPlaying === audio.id
      }, $data.currentPlaying === audio.id ? {
        h: $data.playProgress + "%",
        i: $data.playProgress + "%",
        j: common_vendor.t($options.formatTime($data.currentTime)),
        k: common_vendor.t(audio.duration)
      } : {}, {
        l: audio.id,
        m: common_vendor.n({
          playing: $data.currentPlaying === audio.id
        }),
        n: common_vendor.o(($event) => $options.playAudio(audio), audio.id)
      });
    }),
    l: $options.filteredAudioList.length === 0
  }, $options.filteredAudioList.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f7b94a4b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/sound-effects.js.map
