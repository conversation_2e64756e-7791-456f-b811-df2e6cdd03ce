"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputText: "",
      outputText: "",
      conversionMode: "toUnicode",
      // 'toUnicode' | 'fromUnicode'
      commonUnicodeChars: [
        { char: "😀", code: "\\u1f600", desc: "笑脸" },
        { char: "❤️", code: "\\u2764", desc: "红心" },
        { char: "★", code: "\\u2605", desc: "实心星" },
        { char: "☆", code: "\\u2606", desc: "空心星" },
        { char: "©", code: "\\u00a9", desc: "版权符号" },
        { char: "®", code: "\\u00ae", desc: "注册商标" }
      ]
    };
  },
  methods: {
    setConversionMode(mode) {
      this.conversionMode = mode;
      this.inputText = "";
      this.outputText = "";
    },
    convertToUnicode(text) {
      return text.split("").map((char) => {
        const code = char.charCodeAt(0);
        return `\\u${code.toString(16).padStart(4, "0")}`;
      }).join("");
    },
    convertFromUnicode(text) {
      try {
        return text.replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => {
          return String.fromCharCode(parseInt(code, 16));
        });
      } catch (error) {
        return "转换失败，请检查Unicode格式";
      }
    },
    handleConvert() {
      if (!this.inputText.trim())
        return;
      if (this.conversionMode === "toUnicode") {
        this.outputText = this.convertToUnicode(this.inputText);
      } else {
        this.outputText = this.convertFromUnicode(this.inputText);
      }
    },
    swapMode() {
      this.conversionMode = this.conversionMode === "toUnicode" ? "fromUnicode" : "toUnicode";
      const temp = this.inputText;
      this.inputText = this.outputText;
      this.outputText = temp;
    },
    selectUnicodeChar(item) {
      this.inputText = this.conversionMode === "toUnicode" ? item.char : item.code;
    },
    copyToClipboard(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.conversionMode === "toUnicode" ? 1 : "",
    b: common_vendor.o(($event) => $options.setConversionMode("toUnicode")),
    c: common_vendor.o((...args) => $options.swapMode && $options.swapMode(...args)),
    d: $data.conversionMode === "fromUnicode" ? 1 : "",
    e: common_vendor.o(($event) => $options.setConversionMode("fromUnicode")),
    f: common_vendor.t($data.conversionMode === "toUnicode" ? "输入文字" : "输入Unicode编码"),
    g: $data.conversionMode === "toUnicode" ? "输入要转换为Unicode的文字..." : "输入Unicode编码，如：\\u4f60\\u597d",
    h: -1,
    i: $data.inputText,
    j: common_vendor.o(($event) => $data.inputText = $event.detail.value),
    k: common_vendor.o((...args) => $options.handleConvert && $options.handleConvert(...args)),
    l: !$data.inputText,
    m: $data.outputText
  }, $data.outputText ? {
    n: common_vendor.o(($event) => $options.copyToClipboard($data.outputText)),
    o: $data.outputText,
    p: -1
  } : {}, {
    q: common_vendor.f($data.commonUnicodeChars, (item, index, i0) => {
      return {
        a: common_vendor.t(item.char),
        b: common_vendor.t(item.desc),
        c: common_vendor.t(item.code),
        d: index,
        e: common_vendor.o(($event) => $options.selectUnicodeChar(item), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-f5ba67cb"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/unicode-converter.js.map
