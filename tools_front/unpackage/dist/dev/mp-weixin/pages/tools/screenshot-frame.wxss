/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-6baee0b2 {
  display: flex;
}
.flex-1.data-v-6baee0b2 {
  flex: 1;
}
.items-center.data-v-6baee0b2 {
  align-items: center;
}
.justify-center.data-v-6baee0b2 {
  justify-content: center;
}
.justify-between.data-v-6baee0b2 {
  justify-content: space-between;
}
.text-center.data-v-6baee0b2 {
  text-align: center;
}
.rounded.data-v-6baee0b2 {
  border-radius: 3px;
}
.rounded-lg.data-v-6baee0b2 {
  border-radius: 6px;
}
.shadow.data-v-6baee0b2 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-6baee0b2 {
  padding: 16rpx;
}
.m-4.data-v-6baee0b2 {
  margin: 16rpx;
}
.mb-4.data-v-6baee0b2 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-6baee0b2 {
  margin-top: 16rpx;
}
.screenshot-frame.data-v-6baee0b2 {
  min-height: 100vh;
  background: #f8f9fa;
}
.screenshot-frame .content.data-v-6baee0b2 {
  padding: 40rpx;
  max-width: 1200rpx;
  margin: 0 auto;
}
.screenshot-frame .section-card.data-v-6baee0b2 {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}
.screenshot-frame .section-card .section-title.data-v-6baee0b2 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 32rpx;
}
.screenshot-frame .upload-area.data-v-6baee0b2 {
  border: 4rpx dashed #3b82f6;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  transition: all 0.3s;
}
.screenshot-frame .upload-area.data-v-6baee0b2:active {
  border-color: #2563eb;
  background: rgba(59, 130, 246, 0.05);
}
.screenshot-frame .upload-area .upload-icon.data-v-6baee0b2 {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}
.screenshot-frame .upload-area .upload-title.data-v-6baee0b2 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}
.screenshot-frame .upload-area .upload-desc.data-v-6baee0b2 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 12rpx;
}
.screenshot-frame .upload-area .upload-tip.data-v-6baee0b2 {
  font-size: 24rpx;
  color: #3b82f6;
  display: block;
}
.screenshot-frame .file-info.data-v-6baee0b2 {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 16rpx;
  margin-top: 24rpx;
  border: 2rpx solid rgba(59, 130, 246, 0.1);
  position: relative;
  overflow: hidden;
}
.screenshot-frame .file-info.data-v-6baee0b2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6rpx;
  background: #3b82f6;
}
.screenshot-frame .file-info .preview-thumb.data-v-6baee0b2 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid #e5e7eb;
  background: #fff;
  object-fit: cover;
}
.screenshot-frame .file-info .file-details.data-v-6baee0b2 {
  flex: 1;
  min-width: 0;
  padding-right: 16rpx;
}
.screenshot-frame .file-info .file-details .file-name.data-v-6baee0b2 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e40af;
  display: block;
  margin-bottom: 8rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
}
.screenshot-frame .file-info .file-details .file-size.data-v-6baee0b2 {
  font-size: 24rpx;
  color: #64748b;
  display: block;
  background: rgba(59, 130, 246, 0.08);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  display: inline-block;
}
.screenshot-frame .file-info .clear-button.data-v-6baee0b2 {
  width: 56rpx;
  height: 56rpx;
  flex-shrink: 0;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: 16rpx;
}
.screenshot-frame .file-info .clear-button.data-v-6baee0b2:active {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(0.95);
}
.screenshot-frame .file-info .clear-button .clear-icon.data-v-6baee0b2 {
  font-size: 32rpx;
  color: #ef4444;
  font-weight: bold;
}
.screenshot-frame .frame-tabs.data-v-6baee0b2 {
  display: flex;
  gap: 10rpx;
  margin-bottom: 32rpx;
  padding: 8rpx;
  background: #f8fafc;
  border-radius: 60rpx;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2 {
  flex: 1;
  min-width: 70rpx;
  padding: 16rpx 10rpx;
  border-radius: 50rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: visible;
  white-space: nowrap;
  text-align: center;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2:nth-child(1) {
  flex: 0.85;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2:nth-child(2) {
  flex: 0.85;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2:nth-child(3) {
  flex: 0.85;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2:nth-child(4) {
  flex: 0.85;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2:nth-child(5) {
  flex: 1.2;
  min-width: 140rpx;
  padding: 16rpx 16rpx;
}
.screenshot-frame .frame-tabs .frame-tab.data-v-6baee0b2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  border-radius: 50rpx;
}
.screenshot-frame .frame-tabs .frame-tab.active.data-v-6baee0b2::before {
  opacity: 1;
}
.screenshot-frame .frame-tabs .frame-tab.active .tab-text.data-v-6baee0b2 {
  color: #fff;
  position: relative;
  z-index: 2;
}
.screenshot-frame .frame-tabs .frame-tab .tab-text.data-v-6baee0b2 {
  font-size: 28rpx;
  color: #64748b;
  font-weight: 500;
  text-align: center;
  display: block;
  white-space: nowrap;
  transform: scale(0.95);
  transition: all 0.3s ease;
  letter-spacing: 0;
}
.screenshot-frame .frames-grid.data-v-6baee0b2 {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 24rpx;
  padding: 8rpx;
}
.screenshot-frame .frames-grid .frame-option.data-v-6baee0b2 {
  padding: 24rpx;
  border-radius: 16rpx;
  background: #fff;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}
.screenshot-frame .frames-grid .frame-option.data-v-6baee0b2::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}
.screenshot-frame .frames-grid .frame-option.active.data-v-6baee0b2 {
  box-shadow: 0 4rpx 20rpx rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  transform: translateY(-2rpx);
}
.screenshot-frame .frames-grid .frame-option.active.data-v-6baee0b2::before {
  opacity: 1;
}
.screenshot-frame .frames-grid .frame-option.active .frame-name.data-v-6baee0b2 {
  color: #2563eb;
}
.screenshot-frame .frames-grid .frame-option .frame-preview.data-v-6baee0b2 {
  width: 120rpx;
  height: 160rpx;
  margin: 0 auto 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 16rpx;
}
.screenshot-frame .frames-grid .frame-option .frame-preview .frame-thumb.data-v-6baee0b2 {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.screenshot-frame .frames-grid .frame-option .frame-name.data-v-6baee0b2 {
  font-size: 26rpx;
  font-weight: 600;
  color: #334155;
  display: block;
  margin-bottom: 8rpx;
  text-align: center;
  line-height: 1.3;
}
.screenshot-frame .preview-container.data-v-6baee0b2 {
  text-align: center;
  padding: 48rpx;
  background: transparent;
  border-radius: 24rpx;
  border: none;
  margin-top: 32rpx;
}
.screenshot-frame .preview-container .device-preview.data-v-6baee0b2 {
  display: inline-block;
  position: relative;
}
.screenshot-frame .preview-container .device-preview .preview-canvas.data-v-6baee0b2 {
  border-radius: 0;
  box-shadow: none;
  background: transparent;
  transition: all 0.3s ease;
}
.screenshot-frame .preview-container .device-preview .preview-canvas.data-v-6baee0b2:hover {
  transform: none;
  box-shadow: none;
}
.screenshot-frame .preview-container .device-preview .preview-loading.data-v-6baee0b2 {
  width: 300rpx;
  height: 400rpx;
  background: transparent;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}
.screenshot-frame .preview-container .device-preview .preview-loading .loading-text.data-v-6baee0b2 {
  color: #64748b;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.screenshot-frame .preview-container .device-preview .preview-loading .loading-text.data-v-6baee0b2::before {
  content: "⌛️";
  margin-right: 8rpx;
  font-size: 32rpx;
}
.screenshot-frame .process-button.data-v-6baee0b2 {
  width: 100%;
  padding: 48rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 20rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
  transition: all 0.2s;
  border: none;
  font-size: 32rpx;
  font-weight: 600;
}
.screenshot-frame .process-button.data-v-6baee0b2:active {
  transform: translateY(4rpx);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.2);
}
.screenshot-frame .process-button.disabled.data-v-6baee0b2 {
  opacity: 0.5;
  background: #d1d5db;
  box-shadow: none;
}
.screenshot-frame .usage-list .usage-item.data-v-6baee0b2 {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.screenshot-frame .usage-list .usage-item.data-v-6baee0b2:last-child {
  margin-bottom: 0;
}
.hidden-canvas.data-v-6baee0b2 {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}
canvas[canvas-id=finalCanvas].data-v-6baee0b2 {
  position: fixed;
  top: -9999px;
  left: -9999px;
  opacity: 0;
  pointer-events: none;
}