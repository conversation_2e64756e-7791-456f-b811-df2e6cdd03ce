<view class="text-nine-grid data-v-061ecc7a"><view class="container data-v-061ecc7a"><view class="header-section data-v-061ecc7a"><view class="title-container data-v-061ecc7a"><text class="title-icon data-v-061ecc7a">📏</text><text class="title-text data-v-061ecc7a">文字九宫格</text></view><text class="subtitle data-v-061ecc7a">将文字排列成九宫格形式</text></view><view class="input-section data-v-061ecc7a"><view class="section-header data-v-061ecc7a"><text class="section-icon data-v-061ecc7a">✏️</text><text class="section-title data-v-061ecc7a">输入文字</text></view><view class="input-wrapper data-v-061ecc7a"><input class="text-input data-v-061ecc7a" placeholder="输入要排列的文字..." maxlength="20" type="text" value="{{a}}" bindinput="{{b}}"/><view class="input-counter data-v-061ecc7a">{{c}}/20</view></view><text class="input-tip data-v-061ecc7a">建议输入1-9个字符，超出部分会循环填充</text></view><view wx:if="{{d}}" class="grid-section data-v-061ecc7a"><view class="section-header data-v-061ecc7a"><text class="section-icon data-v-061ecc7a">🎯</text><text class="section-title data-v-061ecc7a">九宫格效果</text></view><view class="grid-display data-v-061ecc7a"><text class="grid-text data-v-061ecc7a">{{e}}</text><view class="grid-actions data-v-061ecc7a"><view class="action-btn primary data-v-061ecc7a" bindtap="{{f}}"><text class="btn-icon data-v-061ecc7a">📋</text><text class="btn-text data-v-061ecc7a">复制九宫格</text></view><view class="action-btn secondary data-v-061ecc7a" bindtap="{{g}}"><text class="btn-icon data-v-061ecc7a">📤</text><text class="btn-text data-v-061ecc7a">分享</text></view></view></view></view><view class="examples-section data-v-061ecc7a"><view class="section-header data-v-061ecc7a"><text class="section-icon data-v-061ecc7a">⚡</text><text class="section-title data-v-061ecc7a">快速示例</text></view><view class="examples-grid data-v-061ecc7a"><view wx:for="{{h}}" wx:for-item="example" wx:key="b" class="example-item data-v-061ecc7a" bindtap="{{example.c}}"><text class="example-text data-v-061ecc7a">{{example.a}}</text></view></view></view><view class="help-section data-v-061ecc7a"><view class="section-header data-v-061ecc7a"><text class="section-icon data-v-061ecc7a">💡</text><text class="section-title data-v-061ecc7a">使用说明</text></view><view class="help-content data-v-061ecc7a"><text class="help-item data-v-061ecc7a">• 将文字排列成3×3的九宫格形式</text><text class="help-item data-v-061ecc7a">• 支持中英文和数字字符</text><text class="help-item data-v-061ecc7a">• 字符不足时会循环填充</text><text class="help-item data-v-061ecc7a">• 创造独特的文字视觉效果</text><text class="help-item data-v-061ecc7a">• 适合制作个性签名和装饰</text></view></view></view></view>