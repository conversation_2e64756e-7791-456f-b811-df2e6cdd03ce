"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "anime-wallpaper",
  setup(__props) {
    const wallpapers = common_vendor.ref([]);
    const selectedType = common_vendor.ref("all");
    const searchTerm = common_vendor.ref("");
    const favorites = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const types = [
      { id: "all", name: "全部", icon: "🌸" },
      { id: "popular", name: "热门动漫", icon: "🔥" },
      { id: "classic", name: "经典作品", icon: "⭐" },
      { id: "movie", name: "动画电影", icon: "🎬" },
      { id: "character", name: "角色专辑", icon: "👤" },
      { id: "new", name: "新番动漫", icon: "🆕" }
    ];
    const baseWallpapers = [
      {
        id: "anime1",
        title: "千与千寻",
        series: "宫崎骏作品",
        character: "千寻",
        type: "movie",
        url: "https://images.unsplash.com/photo-1522383225653-ed111181a951?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 95,
        tags: ["宫崎骏", "经典", "奇幻"]
      },
      {
        id: "anime2",
        title: "龙猫森林",
        series: "龙猫",
        character: "龙猫",
        type: "classic",
        url: "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 92,
        tags: ["龙猫", "童年", "温馨"]
      },
      {
        id: "anime3",
        title: "天空之城",
        series: "天空之城",
        type: "movie",
        url: "https://images.unsplash.com/photo-1520637836862-4d197d17c27a?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 90,
        tags: ["飞行", "冒险", "城堡"]
      },
      {
        id: "anime4",
        title: "魔女宅急便",
        series: "魔女宅急便",
        character: "琪琪",
        type: "classic",
        url: "https://images.unsplash.com/photo-1446776877081-d282a0f896e2?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 88,
        tags: ["魔女", "成长", "青春"]
      },
      {
        id: "anime5",
        title: "你的名字",
        series: "你的名字",
        character: "三叶",
        type: "new",
        url: "https://images.unsplash.com/photo-1500375592092-40eb2168fd21?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 94,
        tags: ["青春", "爱情", "奇迹"]
      },
      {
        id: "anime6",
        title: "鬼灭之刃",
        series: "鬼灭之刃",
        character: "炭治郎",
        type: "popular",
        url: "https://images.unsplash.com/photo-1462331940025-496dfbfc7564?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 96,
        tags: ["热血", "战斗", "兄妹情"]
      },
      {
        id: "anime7",
        title: "进击的巨人",
        series: "进击的巨人",
        character: "艾伦",
        type: "popular",
        url: "https://images.unsplash.com/photo-1557682224-5b8590cd9ec5?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 93,
        tags: ["巨人", "战争", "自由"]
      },
      {
        id: "anime8",
        title: "海贼王",
        series: "海贼王",
        character: "路飞",
        type: "classic",
        url: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop",
        resolution: "1920x1080",
        popularity: 97,
        tags: ["冒险", "友情", "梦想"]
      }
    ];
    common_vendor.computed(() => {
      return wallpapers.value.filter((w) => favorites.value.includes(w.id));
    });
    const generateAnimeWallpapers = (type = "all") => {
      let filtered = type === "all" ? baseWallpapers : baseWallpapers.filter((wall) => wall.type === type);
      if (searchTerm.value) {
        filtered = filtered.filter(
          (wall) => {
            var _a;
            return wall.title.toLowerCase().includes(searchTerm.value.toLowerCase()) || wall.series.toLowerCase().includes(searchTerm.value.toLowerCase()) || ((_a = wall.character) == null ? void 0 : _a.toLowerCase().includes(searchTerm.value.toLowerCase())) || wall.tags.some((tag) => tag.toLowerCase().includes(searchTerm.value.toLowerCase()));
          }
        );
      }
      return filtered.sort((a, b) => b.popularity - a.popularity);
    };
    const loadWallpapers = () => {
      loading.value = true;
      setTimeout(() => {
        const newWallpapers = generateAnimeWallpapers(selectedType.value);
        wallpapers.value = newWallpapers;
        loading.value = false;
      }, 500);
    };
    const downloadWallpaper = (wallpaper) => {
      const link = document.createElement("a");
      link.href = wallpaper.url;
      link.download = `${wallpaper.series}-${wallpaper.title}-${wallpaper.resolution}.jpg`;
      link.click();
      alert(`正在下载 ${wallpaper.title}`);
    };
    const toggleFavorite = (wallpaperId) => {
      if (favorites.value.includes(wallpaperId)) {
        favorites.value = favorites.value.filter((id) => id !== wallpaperId);
      } else {
        favorites.value = [...favorites.value, wallpaperId];
      }
    };
    common_vendor.watch(() => [selectedType.value, searchTerm.value], () => {
      loadWallpapers();
    }, { deep: true });
    common_vendor.onMounted(() => {
      loadWallpapers();
    });
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: searchTerm.value,
        b: common_vendor.o(($event) => searchTerm.value = $event.detail.value),
        c: common_vendor.f(types, (type, k0, i0) => {
          return {
            a: common_vendor.t(type.icon),
            b: common_vendor.t(type.name),
            c: type.id,
            d: selectedType.value === type.id ? 1 : "",
            e: common_vendor.o(($event) => selectedType.value = type.id, type.id)
          };
        }),
        d: common_vendor.t(selectedType.value === "all" ? "全部动漫" : (_a = types.find((t) => t.id === selectedType.value)) == null ? void 0 : _a.name),
        e: common_vendor.t(wallpapers.value.length),
        f: loading.value
      }, loading.value ? {} : {
        g: common_vendor.f(wallpapers.value, (wallpaper, k0, i0) => {
          return common_vendor.e({
            a: wallpaper.url,
            b: wallpaper.title,
            c: common_vendor.t(wallpaper.title),
            d: common_vendor.t(wallpaper.series),
            e: wallpaper.character
          }, wallpaper.character ? {
            f: common_vendor.t(wallpaper.character)
          } : {}, {
            g: common_vendor.t(wallpaper.resolution),
            h: common_vendor.t(wallpaper.series),
            i: common_vendor.t(wallpaper.type),
            j: wallpaper.character
          }, wallpaper.character ? {
            k: common_vendor.t(wallpaper.character)
          } : {}, {
            l: favorites.value.includes(wallpaper.id) ? 1 : "",
            m: common_vendor.o(($event) => toggleFavorite(wallpaper.id), wallpaper.id),
            n: common_vendor.o(($event) => downloadWallpaper(wallpaper), wallpaper.id),
            o: common_vendor.t(wallpaper.popularity),
            p: wallpaper.id
          });
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7e887b58"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/anime-wallpaper.js.map
