/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-bc8177dc {
  display: flex;
}
.flex-1.data-v-bc8177dc {
  flex: 1;
}
.items-center.data-v-bc8177dc {
  align-items: center;
}
.justify-center.data-v-bc8177dc {
  justify-content: center;
}
.justify-between.data-v-bc8177dc {
  justify-content: space-between;
}
.text-center.data-v-bc8177dc {
  text-align: center;
}
.rounded.data-v-bc8177dc {
  border-radius: 3px;
}
.rounded-lg.data-v-bc8177dc {
  border-radius: 6px;
}
.shadow.data-v-bc8177dc {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-bc8177dc {
  padding: 16rpx;
}
.m-4.data-v-bc8177dc {
  margin: 16rpx;
}
.mb-4.data-v-bc8177dc {
  margin-bottom: 16rpx;
}
.mt-4.data-v-bc8177dc {
  margin-top: 16rpx;
}
.glass-generator-container.data-v-bc8177dc {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}
.header-card.data-v-bc8177dc, .params-card.data-v-bc8177dc, .presets-card.data-v-bc8177dc, .preview-card.data-v-bc8177dc, .code-card.data-v-bc8177dc, .tips-card.data-v-bc8177dc {
  background: white;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.header-content.data-v-bc8177dc {
  display: flex;
  align-items: center;
}
.header-icon.data-v-bc8177dc {
  font-size: 48rpx;
  margin-right: 24rpx;
}
.header-title.data-v-bc8177dc {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-subtitle.data-v-bc8177dc {
  font-size: 24rpx;
  color: #666;
}
.card-header.data-v-bc8177dc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}
.card-title.data-v-bc8177dc {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.params-content.data-v-bc8177dc {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.param-group.data-v-bc8177dc {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.param-label.data-v-bc8177dc {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.color-section.data-v-bc8177dc {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.color-presets.data-v-bc8177dc {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8rpx;
}
.color-preset.data-v-bc8177dc {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e5e5;
  transition: all 0.3s ease;
}
.color-preset.active.data-v-bc8177dc {
  border-color: #007AFF;
  transform: scale(1.1);
}
.presets-grid.data-v-bc8177dc {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}
.preset-item.data-v-bc8177dc {
  text-align: center;
  transition: all 0.3s ease;
}
.preset-item.data-v-bc8177dc:active {
  transform: scale(0.95);
}
.preset-preview.data-v-bc8177dc {
  width: 100%;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  /* 移除默认背景，完全由getPresetStyle控制 */
}
.preset-text.data-v-bc8177dc {
  font-size: 24rpx;
  font-weight: 600;
  color: white;
}
.preset-name.data-v-bc8177dc {
  font-size: 22rpx;
  color: #333;
}
.action-btn.data-v-bc8177dc {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.preview-container.data-v-bc8177dc {
  width: 100%;
  height: 500rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}
.glass-demo.data-v-bc8177dc {
  padding: 60rpx;
  text-align: center;
  color: white;
  position: relative;
  z-index: 1;
  max-width: 400rpx;
}
.demo-title.data-v-bc8177dc {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  margin-bottom: 16rpx;
}
.demo-desc.data-v-bc8177dc {
  display: block;
  font-size: 24rpx;
  opacity: 0.8;
  margin-bottom: 32rpx;
  line-height: 1.5;
}
.demo-buttons.data-v-bc8177dc {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}
.demo-btn.data-v-bc8177dc {
  padding: 16rpx 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 8rpx;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 22rpx;
}
.demo-btn.secondary.data-v-bc8177dc {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.2);
}
.copy-btn.data-v-bc8177dc {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
}
.code-block.data-v-bc8177dc {
  background: #1a1a1a;
  border-radius: 12rpx;
  padding: 24rpx;
}
.code-text.data-v-bc8177dc {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 22rpx;
  color: #4ade80;
  line-height: 1.6;
  white-space: pre-wrap;
}
.tips-content.data-v-bc8177dc {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.tip-item.data-v-bc8177dc {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.tip-title.data-v-bc8177dc {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.tip-desc.data-v-bc8177dc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

/* header按钮绝对定位靠右 */
.preview-header.data-v-bc8177dc, .code-header.data-v-bc8177dc {
  position: relative;
}
.preview-btn.data-v-bc8177dc {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #fff;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
  justify-content: center;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.18s cubic-bezier(0.2, 0, 0.1, 1), transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
}
.preview-btn.data-v-bc8177dc:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: scale(0.97);
}
.copy-btn.data-v-bc8177dc {
  position: absolute;
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 22rpx;
  color: #666;
  min-width: 120rpx;
  justify-content: center;
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.03);
  transition: box-shadow 0.18s cubic-bezier(0.2, 0, 0.1, 1), transform 0.18s cubic-bezier(0.2, 0, 0.1, 1);
}
.copy-btn.data-v-bc8177dc:active {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transform: scale(0.97);
}