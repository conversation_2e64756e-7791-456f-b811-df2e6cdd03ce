"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const LotteryWheel = () => "../../components/lottery-wheel/lottery-wheel.js";
const _sfc_main = {
  name: "BiasedWheel",
  components: {
    LotteryWheel
  },
  data() {
    return {
      isSpinning: false,
      result: "",
      resultWeight: 0,
      resultProbability: 0,
      soundEnabled: true,
      vibrationEnabled: true,
      colors: [
        "#FF7B54",
        // 橙红色
        "#D4B2A7",
        // 米色
        "#B8E6B8",
        // 淡绿色
        "#87CEEB",
        // 天蓝色
        "#DDA0DD",
        // 紫色
        "#F0E68C",
        // 黄色
        "#FFB6C1",
        // 粉色
        "#98FB98"
        // 薄荷绿
      ],
      options: [
        { id: 1, text: "麻辣烫", weight: 1, color: "#FF7B54" },
        { id: 2, text: "奶茶", weight: 1, color: "#D4B2A7" },
        { id: 3, text: "咖啡", weight: 1, color: "#B8E6B8" },
        { id: 4, text: "抹茶", weight: 1, color: "#87CEEB" },
        { id: 5, text: "甜点", weight: 1, color: "#DDA0DD" },
        { id: 6, text: "烧烤", weight: 1, color: "#F0E68C" }
      ],
      templates: [
        {
          name: "吃什么",
          description: "解决选择困难症",
          options: [
            { text: "中餐", weight: 2 },
            { text: "西餐", weight: 1 },
            { text: "日料", weight: 1 },
            { text: "韩餐", weight: 1 },
            { text: "快餐", weight: 3 },
            { text: "自己做", weight: 1 }
          ]
        },
        {
          name: "周末活动",
          description: "休闲娱乐选择",
          options: [
            { text: "看电影", weight: 2 },
            { text: "逛街", weight: 2 },
            { text: "在家躺平", weight: 3 },
            { text: "户外运动", weight: 1 },
            { text: "聚会", weight: 1 },
            { text: "学习", weight: 1 }
          ]
        },
        {
          name: "学习内容",
          description: "技能提升方向",
          options: [
            { text: "编程", weight: 2 },
            { text: "设计", weight: 1 },
            { text: "英语", weight: 2 },
            { text: "运动", weight: 1 },
            { text: "读书", weight: 2 },
            { text: "音乐", weight: 1 }
          ]
        },
        {
          name: "是否决策",
          description: "简单的是非判断",
          options: [
            { text: "是", weight: 1 },
            { text: "否", weight: 1 }
          ]
        }
      ]
    };
  },
  computed: {
    totalWeight() {
      return this.options.reduce((sum, option) => sum + option.weight, 0);
    },
    // 转换为转盘组件需要的数据格式
    prizeList() {
      const weightedPrizes = [];
      this.options.forEach((option) => {
        for (let i = 0; i < option.weight; i++) {
          weightedPrizes.push({
            name: option.text,
            image: "/static/prize-icon.png",
            pro: `权重:${option.weight}`,
            color: option.color,
            originalIndex: this.options.findIndex((o) => o.id === option.id)
          });
        }
      });
      return weightedPrizes;
    }
  },
  mounted() {
    this.loadSettings();
  },
  methods: {
    getOptionProbability(option) {
      return (option.weight / this.totalWeight * 100).toFixed(1);
    },
    addOption() {
      const newId = Math.max(...this.options.map((o) => o.id), 0) + 1;
      const newOption = {
        id: newId,
        text: `选项${newId}`,
        weight: 1,
        color: this.colors[this.options.length % this.colors.length]
      };
      this.options.push(newOption);
      this.updatePrizes();
    },
    removeOption(id) {
      if (this.options.length > 2) {
        const index = this.options.findIndex((o) => o.id === id);
        if (index > -1) {
          this.options.splice(index, 1);
          this.updatePrizes();
        }
      }
    },
    changeColor(option) {
      const currentIndex = this.colors.indexOf(option.color);
      const nextIndex = (currentIndex + 1) % this.colors.length;
      option.color = this.colors[nextIndex];
      this.updatePrizes();
    },
    randomizeColors() {
      this.options.forEach((option) => {
        option.color = this.colors[Math.floor(Math.random() * this.colors.length)];
      });
      this.updatePrizes();
      common_vendor.index.showToast({
        title: "颜色已随机化",
        icon: "success"
      });
    },
    loadTemplate(template) {
      common_vendor.index.showModal({
        title: "加载模板",
        content: `确定要加载"${template.name}"模板吗？这将覆盖当前设置。`,
        success: (res) => {
          if (res.confirm) {
            this.options.splice(0, this.options.length);
            template.options.forEach((opt, index) => {
              this.options.push({
                id: index + 1,
                text: opt.text,
                weight: opt.weight,
                color: this.colors[index % this.colors.length]
              });
            });
            this.updatePrizes();
            common_vendor.index.showToast({
              title: "模板加载成功",
              icon: "success"
            });
          }
        }
      });
    },
    updatePrizes() {
      this.$forceUpdate();
    },
    spinWheel() {
      if (this.isSpinning || this.options.length < 2)
        return;
      this.isSpinning = true;
      this.result = "";
      const randomIndex = Math.floor(Math.random() * this.prizeList.length);
      this.$refs.luckyWheel.run(randomIndex);
    },
    onWheelClick() {
      this.spinWheel();
    },
    onWheelDone(index) {
      this.isSpinning = false;
      const selectedPrize = this.prizeList[index];
      const selectedOption = this.options[selectedPrize.originalIndex];
      this.result = selectedOption.text;
      this.resultWeight = selectedOption.weight;
      this.resultProbability = (selectedOption.weight / this.totalWeight * 100).toFixed(1);
      if (this.vibrationEnabled) {
        common_vendor.index.vibrateShort();
      }
      common_vendor.index.showToast({
        title: `🎉 ${selectedOption.text}`,
        icon: "none",
        duration: 2e3
      });
    },
    toggleSound(e) {
      this.soundEnabled = e.detail.value;
      common_vendor.index.setStorageSync("wheelSoundEnabled", this.soundEnabled);
    },
    toggleVibration(e) {
      this.vibrationEnabled = e.detail.value;
      common_vendor.index.setStorageSync("wheelVibrationEnabled", this.vibrationEnabled);
    },
    loadSettings() {
      try {
        const savedSound = common_vendor.index.getStorageSync("wheelSoundEnabled");
        if (savedSound !== null && savedSound !== void 0) {
          this.soundEnabled = savedSound;
        }
        const savedVibration = common_vendor.index.getStorageSync("wheelVibrationEnabled");
        if (savedVibration !== null && savedVibration !== void 0) {
          this.vibrationEnabled = savedVibration;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/biased-wheel.vue:454", "加载设置失败:", error);
      }
    }
  }
};
if (!Array) {
  const _easycom_lottery_wheel2 = common_vendor.resolveComponent("lottery-wheel");
  _easycom_lottery_wheel2();
}
const _easycom_lottery_wheel = () => "../../components/lottery-wheel/lottery-wheel.js";
if (!Math) {
  _easycom_lottery_wheel();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: common_assets._imports_1,
    c: common_vendor.o((...args) => $options.spinWheel && $options.spinWheel(...args)),
    d: common_vendor.sr("luckyWheel", "8fc283da-0"),
    e: common_vendor.o($options.onWheelClick),
    f: common_vendor.o($options.onWheelDone),
    g: common_vendor.p({
      prizes: $options.prizeList,
      size: 626,
      duration: 3e3,
      turns: 5,
      lightImgs: ["/static/light1.png", "/static/light2.png"],
      customStyle: "borderRadius: 50%;"
    }),
    h: $data.isSpinning
  }, $data.isSpinning ? {} : {}, {
    i: common_vendor.t($data.isSpinning ? "转动中..." : "开始转动"),
    j: common_vendor.o((...args) => $options.spinWheel && $options.spinWheel(...args)),
    k: $data.isSpinning || $data.options.length < 2,
    l: common_vendor.n($data.isSpinning ? "spinning" : ""),
    m: common_vendor.n($data.isSpinning || $data.options.length < 2 ? "disabled" : ""),
    n: $data.result && !$data.isSpinning
  }, $data.result && !$data.isSpinning ? {
    o: common_vendor.t($data.result),
    p: common_vendor.t($data.resultWeight),
    q: common_vendor.t($data.resultProbability)
  } : {}, {
    r: common_vendor.f($data.templates, (template, k0, i0) => {
      return {
        a: common_vendor.t(template.name),
        b: common_vendor.t(template.description),
        c: template.name,
        d: common_vendor.o(($event) => $options.loadTemplate(template), template.name)
      };
    }),
    s: common_vendor.o((...args) => $options.randomizeColors && $options.randomizeColors(...args)),
    t: common_vendor.o((...args) => $options.addOption && $options.addOption(...args)),
    v: common_vendor.f($data.options, (option, index, i0) => {
      return {
        a: option.color,
        b: common_vendor.o(($event) => $options.changeColor(option), option.id),
        c: `选项${index + 1}`,
        d: common_vendor.o([($event) => option.text = $event.detail.value, option.id, (...args) => $options.updatePrizes && $options.updatePrizes(...args), option.id], option.id),
        e: option.text,
        f: common_vendor.o([common_vendor.m(($event) => option.weight = $event.detail.value, {
          number: true
        }), option.id, (...args) => $options.updatePrizes && $options.updatePrizes(...args), option.id], option.id),
        g: option.weight,
        h: common_vendor.t($options.getOptionProbability(option)),
        i: common_vendor.o(($event) => $options.removeOption(option.id), option.id),
        j: option.id
      };
    }),
    w: $data.options.length <= 2,
    x: common_vendor.n($data.options.length <= 2 ? "disabled" : ""),
    y: $data.soundEnabled,
    z: common_vendor.o((...args) => $options.toggleSound && $options.toggleSound(...args)),
    A: $data.vibrationEnabled,
    B: common_vendor.o((...args) => $options.toggleVibration && $options.toggleVibration(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-8fc283da"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/biased-wheel.js.map
