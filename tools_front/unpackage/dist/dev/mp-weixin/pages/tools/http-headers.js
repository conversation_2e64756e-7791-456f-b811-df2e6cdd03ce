"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      searchQuery: "",
      headerType: "all",
      headers: [
        // 请求头
        { name: "Accept", type: "request", description: "指定客户端能够接收的内容类型", example: "Accept: text/html,application/json" },
        { name: "Accept-Encoding", type: "request", description: "指定浏览器可以支持的web服务器返回内容压缩编码类型", example: "Accept-Encoding: gzip, deflate" },
        { name: "Accept-Language", type: "request", description: "浏览器可接受的语言", example: "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8" },
        { name: "Authorization", type: "request", description: "HTTP授权的授权证书", example: "Authorization: Bearer token123" },
        { name: "Content-Type", type: "request", description: "请求的与实体对应的MIME信息", example: "Content-Type: application/json" },
        { name: "Cookie", type: "request", description: "HTTP请求发送时，会把保存在该请求域名下的所有cookie值一起发送给web服务器", example: "Cookie: name=value; name2=value2" },
        { name: "Host", type: "request", description: "指定请求的服务器的域名和端口号", example: "Host: www.example.com" },
        { name: "User-Agent", type: "request", description: "用户代理信息，包含发出请求的用户信息", example: "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)" },
        { name: "Referer", type: "request", description: "先前网页的地址，当前请求网页紧随其后", example: "Referer: https://www.google.com/" },
        { name: "X-Requested-With", type: "request", description: "用于识别Ajax请求", example: "X-Requested-With: XMLHttpRequest" },
        { name: "Origin", type: "request", description: "发起请求的原始域", example: "Origin: https://example.com" },
        { name: "If-Modified-Since", type: "request", description: "如果请求的资源在指定时间后被修改，则返回资源", example: "If-Modified-Since: Wed, 21 Oct 2015 07:28:00 GMT" },
        // 响应头
        { name: "Cache-Control", type: "response", description: "告诉所有的缓存机制是否可以缓存及哪种类型", example: "Cache-Control: max-age=3600" },
        { name: "Content-Length", type: "response", description: "请求体的长度", example: "Content-Length: 1024" },
        { name: "Content-Type", type: "response", description: "返回内容的MIME类型", example: "Content-Type: text/html; charset=utf-8" },
        { name: "Date", type: "response", description: "原始服务器消息发出的时间", example: "Date: Tue, 15 Nov 2023 08:12:31 GMT" },
        { name: "Location", type: "response", description: "用来重定向接收方到非请求URL的位置来完成请求或标识新的资源", example: "Location: https://www.example.com/new-page" },
        { name: "Server", type: "response", description: "web服务器软件名称", example: "Server: nginx/1.18.0" },
        { name: "Set-Cookie", type: "response", description: "设置Http Cookie", example: "Set-Cookie: name=value; Path=/; HttpOnly" },
        { name: "Access-Control-Allow-Origin", type: "response", description: "指示请求的资源能共享给哪些域", example: "Access-Control-Allow-Origin: *" },
        { name: "ETag", type: "response", description: "资源的特定版本的标识符", example: 'ETag: "33a64df551425fcc55e4d42a148795d9f25f89d4"' },
        { name: "Last-Modified", type: "response", description: "资源的最后修改时间", example: "Last-Modified: Wed, 21 Oct 2015 07:28:00 GMT" },
        { name: "WWW-Authenticate", type: "response", description: "定义了使用何种验证方式去获取对资源的连接", example: 'WWW-Authenticate: Basic realm="Access to staging site"' },
        { name: "Expires", type: "response", description: "响应过期的日期和时间", example: "Expires: Thu, 01 Dec 2023 16:00:00 GMT" }
      ]
    };
  },
  computed: {
    filteredHeaders() {
      return this.headers.filter((header) => {
        const matchesSearch = this.searchQuery === "" || header.name.toLowerCase().includes(this.searchQuery.toLowerCase()) || header.description.toLowerCase().includes(this.searchQuery.toLowerCase());
        const matchesType = this.headerType === "all" || header.type === this.headerType;
        return matchesSearch && matchesType;
      });
    }
  },
  methods: {
    onSearch() {
    },
    setHeaderType(type) {
      this.headerType = type;
    },
    getTypeClass(type) {
      return type === "request" ? "type-request" : "type-response";
    },
    copyToClipboard(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success",
            duration: 1500
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchQuery = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    b: $data.searchQuery,
    c: $data.headerType === "all" ? 1 : "",
    d: common_vendor.o(($event) => $options.setHeaderType("all")),
    e: $data.headerType === "request" ? 1 : "",
    f: common_vendor.o(($event) => $options.setHeaderType("request")),
    g: $data.headerType === "response" ? 1 : "",
    h: common_vendor.o(($event) => $options.setHeaderType("response")),
    i: common_vendor.f($options.filteredHeaders, (header, index, i0) => {
      return {
        a: common_vendor.t(header.name),
        b: common_vendor.t(header.type === "request" ? "请求头" : "响应头"),
        c: common_vendor.n($options.getTypeClass(header.type)),
        d: common_vendor.t(header.description),
        e: common_vendor.t(header.example),
        f: common_vendor.o(($event) => $options.copyToClipboard(header.example), index),
        g: index
      };
    }),
    j: $options.filteredHeaders.length === 0
  }, $options.filteredHeaders.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-b0e94c0c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/http-headers.js.map
