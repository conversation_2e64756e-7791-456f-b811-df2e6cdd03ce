/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-da2d62e1 {
  display: flex;
}
.flex-1.data-v-da2d62e1 {
  flex: 1;
}
.items-center.data-v-da2d62e1 {
  align-items: center;
}
.justify-center.data-v-da2d62e1 {
  justify-content: center;
}
.justify-between.data-v-da2d62e1 {
  justify-content: space-between;
}
.text-center.data-v-da2d62e1 {
  text-align: center;
}
.rounded.data-v-da2d62e1 {
  border-radius: 3px;
}
.rounded-lg.data-v-da2d62e1 {
  border-radius: 6px;
}
.shadow.data-v-da2d62e1 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-da2d62e1 {
  padding: 16rpx;
}
.m-4.data-v-da2d62e1 {
  margin: 16rpx;
}
.mb-4.data-v-da2d62e1 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-da2d62e1 {
  margin-top: 16rpx;
}
.small-letter-nickname.data-v-da2d62e1 {
  min-height: 100vh;
  background: #f9fafb;
}
.container.data-v-da2d62e1 {
  padding: 40rpx 30rpx;
  max-width: 750rpx;
  margin: 0 auto;
}
.header-section.data-v-da2d62e1 {
  text-align: center;
  margin-bottom: 40rpx;
}
.title-container.data-v-da2d62e1 {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}
.title-icon.data-v-da2d62e1 {
  font-size: 48rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.title-text.data-v-da2d62e1 {
  font-size: 40rpx;
  font-weight: 700;
  color: #1f2937;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.subtitle.data-v-da2d62e1 {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}
.input-section.data-v-da2d62e1, .style-section.data-v-da2d62e1, .result-section.data-v-da2d62e1, .example-section.data-v-da2d62e1, .help-section.data-v-da2d62e1 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid #f3f4f6;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: transform 0.2s cubic-bezier(0.2, 0, 0.1, 1), box-shadow 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.input-section.data-v-da2d62e1:hover, .style-section.data-v-da2d62e1:hover, .result-section.data-v-da2d62e1:hover, .example-section.data-v-da2d62e1:hover, .help-section.data-v-da2d62e1:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.06);
}
.section-header.data-v-da2d62e1 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.section-icon.data-v-da2d62e1 {
  font-size: 36rpx;
  margin-right: 16rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}
.section-title.data-v-da2d62e1 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  flex: 1;
}
.input-wrapper.data-v-da2d62e1 {
  position: relative;
}
.text-input.data-v-da2d62e1 {
  width: 100%;
  height: 96rpx;
  padding: 0 24rpx;
  border: 1.5rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 32rpx;
  color: #374151;
  background: rgba(249, 250, 251, 0.8);
  text-align: center;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
  box-sizing: border-box;
}
.text-input.data-v-da2d62e1:focus {
  border-color: #6366f1;
  background: #ffffff;
  box-shadow: 0 0 0 3rpx rgba(99, 102, 241, 0.15);
  outline: none;
}
.style-tabs.data-v-da2d62e1 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12rpx;
}
.style-tab.data-v-da2d62e1 {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 16rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.style-tab.active.data-v-da2d62e1 {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border-color: #6366f1;
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.3);
  transform: translateY(-2rpx);
}
.style-tab.data-v-da2d62e1:hover:not(.active) {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
}
.tab-icon.data-v-da2d62e1 {
  font-size: 28rpx;
  margin-bottom: 8rpx;
}
.tab-name.data-v-da2d62e1 {
  font-size: 24rpx;
  font-weight: 500;
}
.result-grid.data-v-da2d62e1 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.result-item.data-v-da2d62e1 {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #f3f4fd;
  border-radius: 16rpx;
  border-left: 6rpx solid #6366f1;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
}
.result-item.data-v-da2d62e1:hover {
  background: #ede9fe;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(99, 102, 241, 0.1);
}
.result-item.data-v-da2d62e1:active {
  transform: translateY(0) scale(0.98);
}
.result-text.data-v-da2d62e1 {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
  flex: 1;
  text-align: center;
  word-break: break-all;
}
.copy-btn.data-v-da2d62e1 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56rpx;
  height: 56rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.25s cubic-bezier(0.2, 0, 0.1, 1);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-left: 16rpx;
}
.copy-btn.data-v-da2d62e1:hover {
  background: #ffffff;
  transform: scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.copy-icon.data-v-da2d62e1 {
  font-size: 24rpx;
}
.example-grid.data-v-da2d62e1 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}
.example-item.data-v-da2d62e1 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 12rpx;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 12rpx;
  border: 1rpx solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.2, 0, 0.1, 1);
}
.example-item.data-v-da2d62e1:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}
.example-item.data-v-da2d62e1:active {
  transform: translateY(0) scale(0.98);
  background: #e5e7eb;
}
.example-text.data-v-da2d62e1 {
  font-size: 28rpx;
  color: #374151;
  font-weight: 500;
}
.help-content.data-v-da2d62e1 {
  background: rgba(249, 250, 251, 0.8);
  border-radius: 16rpx;
  padding: 28rpx;
  border: 1rpx solid #e5e7eb;
}
.help-item.data-v-da2d62e1 {
  display: block;
  font-size: 26rpx;
  color: #4b5563;
  line-height: 1.6;
  margin-bottom: 16rpx;
  position: relative;
  padding-left: 28rpx;
}
.help-item.data-v-da2d62e1:before {
  content: "•";
  position: absolute;
  left: 8rpx;
  color: #6366f1;
  font-weight: bold;
}
.help-item.data-v-da2d62e1:last-child {
  margin-bottom: 0;
}
@media (max-width: 400px) {
.container.data-v-da2d62e1 {
    padding: 30rpx 20rpx;
}
.style-tabs.data-v-da2d62e1 {
    grid-template-columns: repeat(2, 1fr);
}
.example-grid.data-v-da2d62e1 {
    grid-template-columns: repeat(2, 1fr);
}
}