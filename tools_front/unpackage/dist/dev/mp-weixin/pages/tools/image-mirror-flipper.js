"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedImage: "",
      processedImage: "",
      processing: false,
      processProgress: 0,
      keepOriginalQuality: true,
      addWatermark: false,
      processingTime: 0,
      imageSize: "",
      canvasWidth: 300,
      canvasHeight: 300,
      selectedMode: {
        id: "horizontal",
        name: "水平翻转",
        desc: "左右镜像",
        icon: "↔️"
      },
      flipModes: [
        { id: "horizontal", name: "水平翻转", desc: "左右镜像", icon: "↔️" },
        { id: "vertical", name: "垂直翻转", desc: "上下翻转", icon: "↕️" },
        { id: "both", name: "双向翻转", desc: "180度旋转", icon: "🔄" },
        { id: "diagonal", name: "对角翻转", desc: "对角镜像", icon: "↗️" }
      ]
    };
  },
  methods: {
    // 选择图片
    selectImage() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        sourceType: ["album"],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0];
          this.getImageInfo(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "error"
          });
        }
      });
    },
    // 拍照
    takePhoto() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["original"],
        sourceType: ["camera"],
        success: (res) => {
          this.selectedImage = res.tempFilePaths[0];
          this.getImageInfo(res.tempFilePaths[0]);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "拍照失败",
            icon: "error"
          });
        }
      });
    },
    // 获取图片信息
    getImageInfo(imagePath) {
      common_vendor.index.getImageInfo({
        src: imagePath,
        success: (res) => {
          this.imageSize = `${res.width} × ${res.height}`;
          this.canvasWidth = res.width;
          this.canvasHeight = res.height;
        }
      });
    },
    // 选择翻转模式
    selectMode(mode) {
      this.selectedMode = mode;
    },
    // 质量选项变化
    onQualityChange(e) {
      this.keepOriginalQuality = e.detail.value;
    },
    // 水印选项变化
    onWatermarkChange(e) {
      this.addWatermark = e.detail.value;
    },
    // 处理图片
    async processImage() {
      this.processing = true;
      this.processProgress = 0;
      const startTime = Date.now();
      try {
        const progressInterval = setInterval(() => {
          if (this.processProgress < 90) {
            this.processProgress += Math.random() * 15;
          }
        }, 200);
        const processedImagePath = await this.flipImageWithCanvas();
        clearInterval(progressInterval);
        this.processProgress = 100;
        const endTime = Date.now();
        this.processingTime = Math.round((endTime - startTime) / 1e3 * 10) / 10;
        this.processedImage = processedImagePath;
        common_vendor.index.showToast({
          title: "翻转完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "处理失败",
          icon: "error"
        });
      } finally {
        this.processing = false;
      }
    },
    // 使用Canvas翻转图片
    flipImageWithCanvas() {
      return new Promise((resolve, reject) => {
        const ctx = common_vendor.index.createCanvasContext("imageCanvas", this);
        common_vendor.index.getImageInfo({
          src: this.selectedImage,
          success: (imageInfo) => {
            const { width, height } = imageInfo;
            this.canvasWidth = width;
            this.canvasHeight = height;
            ctx.clearRect(0, 0, width, height);
            ctx.save();
            switch (this.selectedMode.id) {
              case "horizontal":
                ctx.scale(-1, 1);
                ctx.translate(-width, 0);
                break;
              case "vertical":
                ctx.scale(1, -1);
                ctx.translate(0, -height);
                break;
              case "both":
                ctx.scale(-1, -1);
                ctx.translate(-width, -height);
                break;
              case "diagonal":
                ctx.translate(width / 2, height / 2);
                ctx.rotate(Math.PI);
                ctx.scale(-1, 1);
                ctx.translate(-width / 2, -height / 2);
                break;
            }
            ctx.drawImage(this.selectedImage, 0, 0, width, height);
            ctx.restore();
            if (this.addWatermark) {
              ctx.setFontSize(16);
              ctx.setFillStyle("rgba(255, 255, 255, 0.8)");
              ctx.fillText("已翻转", width - 60, height - 20);
            }
            ctx.draw(false, () => {
              setTimeout(() => {
                common_vendor.index.canvasToTempFilePath({
                  canvasId: "imageCanvas",
                  fileType: this.keepOriginalQuality ? "png" : "jpg",
                  quality: this.keepOriginalQuality ? 1 : 0.8,
                  success: (res) => {
                    resolve(res.tempFilePath);
                  },
                  fail: (err) => {
                    reject(err);
                  }
                }, this);
              }, 100);
            });
          },
          fail: (err) => {
            reject(err);
          }
        });
      });
    },
    // 保存图片
    saveImage() {
      common_vendor.index.saveImageToPhotosAlbum({
        filePath: this.processedImage,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      });
    },
    // 分享图片
    shareImage() {
      common_vendor.index.showActionSheet({
        itemList: ["分享到微信", "分享到QQ", "分享到微博"],
        success: (res) => {
          common_vendor.index.showToast({
            title: "分享功能开发中",
            icon: "none"
          });
        }
      });
    },
    // 重置流程
    resetProcess() {
      this.selectedImage = "";
      this.processedImage = "";
      this.processing = false;
      this.processProgress = 0;
      this.processingTime = 0;
      this.imageSize = "";
      this.selectedMode = this.flipModes[0];
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedImage
  }, !$data.selectedImage ? {
    b: common_vendor.o((...args) => $options.selectImage && $options.selectImage(...args)),
    c: common_vendor.o((...args) => $options.takePhoto && $options.takePhoto(...args))
  } : {}, {
    d: $data.selectedImage && !$data.processedImage
  }, $data.selectedImage && !$data.processedImage ? common_vendor.e({
    e: $data.selectedImage,
    f: common_vendor.f($data.flipModes, (mode, k0, i0) => {
      return {
        a: common_vendor.t(mode.icon),
        b: common_vendor.t(mode.name),
        c: common_vendor.t(mode.desc),
        d: mode.id,
        e: $data.selectedMode.id === mode.id ? 1 : "",
        f: common_vendor.o(($event) => $options.selectMode(mode), mode.id)
      };
    }),
    g: $data.keepOriginalQuality,
    h: common_vendor.o((...args) => $options.onQualityChange && $options.onQualityChange(...args)),
    i: $data.addWatermark,
    j: common_vendor.o((...args) => $options.onWatermarkChange && $options.onWatermarkChange(...args)),
    k: !$data.processing
  }, !$data.processing ? {} : {
    l: common_vendor.t($data.processProgress)
  }, {
    m: common_vendor.o((...args) => $options.processImage && $options.processImage(...args)),
    n: $data.processing
  }) : {}, {
    o: $data.processedImage
  }, $data.processedImage ? {
    p: $data.selectedImage,
    q: common_vendor.t($data.selectedMode.name),
    r: $data.processedImage,
    s: common_vendor.t($data.selectedMode.name),
    t: common_vendor.t($data.processingTime),
    v: common_vendor.t($data.imageSize),
    w: common_vendor.o((...args) => $options.saveImage && $options.saveImage(...args)),
    x: common_vendor.o((...args) => $options.shareImage && $options.shareImage(...args)),
    y: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args))
  } : {}, {
    z: $data.canvasWidth + "px",
    A: $data.canvasHeight + "px"
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-912165e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-mirror-flipper.js.map
