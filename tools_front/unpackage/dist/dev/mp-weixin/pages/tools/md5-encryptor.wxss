/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-c2cde938 {
  display: flex;
}
.flex-1.data-v-c2cde938 {
  flex: 1;
}
.items-center.data-v-c2cde938 {
  align-items: center;
}
.justify-center.data-v-c2cde938 {
  justify-content: center;
}
.justify-between.data-v-c2cde938 {
  justify-content: space-between;
}
.text-center.data-v-c2cde938 {
  text-align: center;
}
.rounded.data-v-c2cde938 {
  border-radius: 3px;
}
.rounded-lg.data-v-c2cde938 {
  border-radius: 6px;
}
.shadow.data-v-c2cde938 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-c2cde938 {
  padding: 16rpx;
}
.m-4.data-v-c2cde938 {
  margin: 16rpx;
}
.mb-4.data-v-c2cde938 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-c2cde938 {
  margin-top: 16rpx;
}
.md5-encryptor.data-v-c2cde938 {
  min-height: 100vh;
  background: #ffffff;
  padding: 30rpx;
}
.header-card.data-v-c2cde938,
.single-encrypt-card.data-v-c2cde938,
.batch-encrypt-card.data-v-c2cde938,
.quick-encrypt-card.data-v-c2cde938,
.quick-result-card.data-v-c2cde938,
.tools-card.data-v-c2cde938,
.tips-card.data-v-c2cde938 {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.card-header.data-v-c2cde938 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #f8f9ff;
  position: relative;
}
.card-title.data-v-c2cde938 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.card-content.data-v-c2cde938 {
  padding: 30rpx;
}
.header-info.data-v-c2cde938 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.header-icon.data-v-c2cde938 {
  font-size: 60rpx;
}
.header-text.data-v-c2cde938 {
  flex: 1;
}
.header-title.data-v-c2cde938 {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8rpx;
}
.header-desc.data-v-c2cde938 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}
.clear-btn.data-v-c2cde938,
.export-btn.data-v-c2cde938 {
  font-size: 24rpx;
  color: #ef4444;
  background: none;
  border: none;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}
.export-btn.data-v-c2cde938 {
  color: #10b981;
  margin-right: 20rpx;
}
.clear-btn.data-v-c2cde938 {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}
.input-section.data-v-c2cde938,
.batch-input-section.data-v-c2cde938 {
  margin-bottom: 30rpx;
}
.input-label.data-v-c2cde938 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12rpx;
}
.text-input.data-v-c2cde938,
.batch-input.data-v-c2cde938 {
  width: 100%;
  box-sizing: border-box;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  font-size: 30rpx;
  line-height: 1.5;
  color: #1a1a1a;
  background: #fafafa;
  min-height: 120rpx;
  margin: 0;
}
.batch-input.data-v-c2cde938 {
  min-height: 200rpx;
}
.input-footer.data-v-c2cde938,
.batch-footer.data-v-c2cde938 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12rpx;
}
.char-count.data-v-c2cde938,
.line-count.data-v-c2cde938 {
  font-size: 24rpx;
  color: #6b7280;
}
.encrypt-btn.data-v-c2cde938,
.batch-encrypt-btn.data-v-c2cde938 {
  width: 100%;
  padding: 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
  margin-bottom: 30rpx;
}
.encrypt-btn.disabled.data-v-c2cde938,
.batch-encrypt-btn.disabled.data-v-c2cde938 {
  opacity: 0.5;
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
}
.result-section.data-v-c2cde938 {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  border: 2rpx solid #f0f0f0;
}
.result-label.data-v-c2cde938 {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 16rpx;
}
.result-display.data-v-c2cde938 {
  background: white;
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #e5e7eb;
}
.hash-output.data-v-c2cde938 {
  padding: 20rpx;
  background: #f8f9fa;
}
.hash-text.data-v-c2cde938 {
  font-family: "Courier New", monospace;
  font-size: 24rpx;
  color: #1a1a1a;
  word-break: break-all;
  line-height: 1.4;
}
.result-actions.data-v-c2cde938 {
  display: flex;
  border-top: 1rpx solid #e5e7eb;
}
.copy-btn.data-v-c2cde938,
.share-btn.data-v-c2cde938 {
  flex: 1;
  padding: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  background: white;
  border: none;
  text-align: center;
}
.copy-btn.data-v-c2cde938 {
  border-right: 1rpx solid #e5e7eb;
  color: #10b981;
}
.share-btn.data-v-c2cde938 {
  color: #3b82f6;
}
.hash-info.data-v-c2cde938 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}
.info-item.data-v-c2cde938 {
  font-size: 22rpx;
  color: #6b7280;
  background: white;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}
.batch-results.data-v-c2cde938 {
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx solid #f0f0f0;
  overflow: hidden;
}
.results-header.data-v-c2cde938 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  background: white;
  border-bottom: 1rpx solid #e5e7eb;
}
.results-title.data-v-c2cde938 {
  font-size: 28rpx;
  font-weight: 600;
  color: #1a1a1a;
}
.results-actions.data-v-c2cde938 {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.export-btn.data-v-c2cde938, .copy-all-btn.data-v-c2cde938 {
  font-size: 24rpx;
  color: #10b981;
  background: none;
  border: none;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
}
.export-btn.data-v-c2cde938 {
  margin-right: 0;
}
.results-list.data-v-c2cde938 {
  max-height: 600rpx;
  overflow-y: auto;
}
.result-item.data-v-c2cde938 {
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: white;
}
.result-item.data-v-c2cde938:last-child {
  border-bottom: none;
}
.result-header.data-v-c2cde938 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}
.original-text.data-v-c2cde938 {
  font-size: 26rpx;
  color: #374151;
  flex: 1;
  margin-right: 16rpx;
}
.result-index.data-v-c2cde938 {
  font-size: 22rpx;
  color: #6b7280;
  background: #f3f4f6;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}
.result-hash.data-v-c2cde938 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  padding: 16rpx;
  border-radius: 8rpx;
}
.hash-value.data-v-c2cde938 {
  font-family: "Courier New", monospace;
  font-size: 22rpx;
  color: #1a1a1a;
  word-break: break-all;
  flex: 1;
  margin-right: 16rpx;
}
.copy-icon.data-v-c2cde938 {
  font-size: 20rpx;
  color: #6b7280;
}
.tips-list.data-v-c2cde938 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.tip-item.data-v-c2cde938 {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}
.tip-bullet.data-v-c2cde938 {
  font-size: 28rpx;
  color: #667eea;
  margin-top: 4rpx;
}
.tip-text.data-v-c2cde938 {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #4b5563;
}
.tip-bold.data-v-c2cde938 {
  font-weight: 600;
  color: #1a1a1a;
}
.batch-actions.data-v-c2cde938 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.md5-main-card.data-v-c2cde938 {
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
}
.main-actions.data-v-c2cde938 {
  display: flex;
  align-items: center;
  gap: 16rpx;
}