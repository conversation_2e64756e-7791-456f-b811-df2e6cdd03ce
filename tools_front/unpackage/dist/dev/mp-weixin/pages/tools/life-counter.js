"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedText: '点击"换一句"开始生成随机的生活感悟...',
      selectedCategory: "all",
      isGenerating: false,
      favorites: [],
      categories: [
        { key: "all", name: "全部", count: 0 },
        { key: "inspirational", name: "励志", count: 0 },
        { key: "philosophical", name: "哲理", count: 0 },
        { key: "humorous", name: "幽默", count: 0 },
        { key: "emotional", name: "情感", count: 0 },
        { key: "life", name: "生活", count: 0 }
      ],
      texts: {
        inspirational: [
          "成功不是终点，失败不是末日，继续前进的勇气才最可贵。",
          "每一次跌倒都是为了更好地站起来。",
          "不要等待机会，而要创造机会。",
          "你的潜力远比你想象的要大。",
          "困难是成长的阶梯，挫折是智慧的老师。",
          "相信自己，你已经比昨天的自己更强了。",
          "梦想不会逃跑，逃跑的永远是自己。",
          "努力不一定成功，但不努力一定不会成功。",
          "每一个不曾起舞的日子，都是对生命的辜负。",
          "世界会向那些有目标和远见的人让路。"
        ],
        philosophical: [
          "人生如茶，静心以对，对错无辜，缘由前生。",
          "时间是最好的老师，但遗憾的是它最后把所有学生都杀死了。",
          "生活不是等待暴风雨过去，而是学会在雨中跳舞。",
          "我们都在阴沟里，但仍有人仰望星空。",
          "人生最大的敌人是自己，最大的失败是自大。",
          "智者千虑，必有一失；愚者千虑，必有一得。",
          "人生就像一盒巧克力，你永远不知道下一颗是什么味道。",
          "昨天是历史，明天是谜团，只有今天是天赐的礼物。",
          "生命的意义不在于活得长，而在于活得精彩。",
          "路是脚踏出来的，历史是人写出来的。"
        ],
        humorous: [
          "钱不是万能的，但没钱是万万不能的。",
          "人生苦短，我用Python。",
          "早起的鸟儿有虫吃，早起的虫儿被鸟吃。",
          "生活就像海绵里的水，只要愿意挤，总还是有的。",
          "不要和我比懒，我懒得和你比。",
          "别人的钱财乃我的身外之物。",
          "我不是随便的人，我随便起来不是人。",
          "天才就是百分之九十九的汗水加百分之一的灵感。",
          "世界上最远的距离，是我在if里你在else里。",
          "问君能有几多愁，恰似一群太监上青楼。"
        ],
        emotional: [
          "爱情就像沙漏，心满了，脑子就空了。",
          "有些人，一旦错过就不再。",
          "最深的孤独不是长久的一个人，而是心里没有了任何期望。",
          "时间会淡化一个人的记忆，却永远没有办法消磨一个人的悲伤。",
          "有时候，我们需要的不是一碗鸡汤，而是一个巴掌。",
          "人生最大的幸福，是发现自己爱的人正好也爱着自己。",
          "眼泪的存在，是为了证明悲伤不是一场幻觉。",
          "有些话，适合烂在心里，有些痛苦，适合无声无息的忘记。",
          "世界上最遥远的距离，不是生与死，而是我站在你面前，你却不知道我爱你。",
          "如果有一天我们不在一起了，也要像在一起一样。"
        ],
        life: [
          "生活不止眼前的苟且，还有诗和远方。",
          "房子是租来的，但生活不是。",
          "人间烟火气，最抚凡人心。",
          "生活的理想，就是为了理想的生活。",
          "平凡的日子里，总有一些小确幸在等着我们。",
          "生活就是一边拥有，一边失去；一边选择，一边放弃。",
          "每个人都有自己的时区，不要羡慕任何人。",
          "生活不会因为你的抱怨而改变，但会因为你的行动而不同。",
          "人生没有彩排，每一天都是现场直播。",
          "生活就像骑自行车，要想保持平衡就得往前走。"
        ]
      }
    };
  },
  computed: {
    allTexts() {
      return Object.values(this.texts).flat();
    },
    currentTexts() {
      if (this.selectedCategory === "all") {
        return this.allTexts;
      }
      return this.texts[this.selectedCategory] || [];
    }
  },
  onLoad() {
    this.updateCategoryCounts();
    this.loadFavorites();
    this.generateRandomText();
  },
  methods: {
    updateCategoryCounts() {
      this.categories.forEach((category) => {
        var _a;
        if (category.key === "all") {
          category.count = this.allTexts.length;
        } else {
          category.count = ((_a = this.texts[category.key]) == null ? void 0 : _a.length) || 0;
        }
      });
    },
    generateRandomText() {
      this.isGenerating = true;
      setTimeout(() => {
        const texts = this.currentTexts;
        if (texts.length > 0) {
          const randomIndex = Math.floor(Math.random() * texts.length);
          this.selectedText = texts[randomIndex];
        }
        this.isGenerating = false;
      }, 800);
    },
    selectCategory(categoryKey) {
      this.selectedCategory = categoryKey;
      this.generateRandomText();
    },
    setCurrentQuote(quote) {
      this.selectedText = quote;
    },
    copyToClipboard(text = this.selectedText) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        }
      });
    },
    addToFavorites(text) {
      if (!this.favorites.includes(text)) {
        this.favorites.push(text);
        this.saveFavorites();
        common_vendor.index.showToast({
          title: "已添加到收藏",
          icon: "success",
          duration: 1500
        });
      } else {
        common_vendor.index.showToast({
          title: "已存在收藏中",
          icon: "none",
          duration: 1500
        });
      }
    },
    removeFromFavorites(index) {
      this.favorites.splice(index, 1);
      this.saveFavorites();
      common_vendor.index.showToast({
        title: "已从收藏中移除",
        icon: "success",
        duration: 1500
      });
    },
    saveFavorites() {
      common_vendor.index.setStorageSync("life-counter-favorites", this.favorites);
    },
    loadFavorites() {
      const favorites = common_vendor.index.getStorageSync("life-counter-favorites");
      if (favorites) {
        this.favorites = favorites;
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.selectedText),
    b: common_vendor.t($data.isGenerating ? "生成中..." : "换一句"),
    c: common_vendor.o((...args) => $options.generateRandomText && $options.generateRandomText(...args)),
    d: $data.isGenerating ? 1 : "",
    e: common_vendor.o((...args) => $options.copyToClipboard && $options.copyToClipboard(...args)),
    f: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.name),
        b: common_vendor.t(category.count),
        c: category.key,
        d: $data.selectedCategory === category.key ? 1 : "",
        e: common_vendor.o(($event) => $options.selectCategory(category.key), category.key)
      };
    }),
    g: $data.favorites.length > 0
  }, $data.favorites.length > 0 ? {
    h: common_vendor.t($data.favorites.length),
    i: common_vendor.f($data.favorites, (favorite, index, i0) => {
      return {
        a: common_vendor.t(favorite),
        b: common_vendor.o(($event) => $options.copyToClipboard(favorite), index),
        c: common_vendor.o(($event) => $options.removeFromFavorites(index), index),
        d: index,
        e: common_vendor.o(($event) => $options.setCurrentQuote(favorite), index)
      };
    })
  } : {}, {
    j: common_vendor.f($options.currentTexts.slice(0, 6), (quote, index, i0) => {
      return {
        a: common_vendor.t(quote),
        b: common_vendor.o(($event) => $options.copyToClipboard(quote), index),
        c: common_vendor.o(($event) => $options.addToFavorites(quote), index),
        d: index,
        e: common_vendor.o(($event) => $options.setCurrentQuote(quote), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-e7106439"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/life-counter.js.map
