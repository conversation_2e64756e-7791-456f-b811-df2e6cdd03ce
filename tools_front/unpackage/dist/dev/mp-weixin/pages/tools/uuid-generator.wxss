/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-0c5e3227 {
  display: flex;
}
.flex-1.data-v-0c5e3227 {
  flex: 1;
}
.items-center.data-v-0c5e3227 {
  align-items: center;
}
.justify-center.data-v-0c5e3227 {
  justify-content: center;
}
.justify-between.data-v-0c5e3227 {
  justify-content: space-between;
}
.text-center.data-v-0c5e3227 {
  text-align: center;
}
.rounded.data-v-0c5e3227 {
  border-radius: 3px;
}
.rounded-lg.data-v-0c5e3227 {
  border-radius: 6px;
}
.shadow.data-v-0c5e3227 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-0c5e3227 {
  padding: 16rpx;
}
.m-4.data-v-0c5e3227 {
  margin: 16rpx;
}
.mb-4.data-v-0c5e3227 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-0c5e3227 {
  margin-top: 16rpx;
}
.uuid-generator.data-v-0c5e3227 {
  min-height: 100vh;
  background: #f8f9fa;
}
.navbar.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.navbar .nav-back.data-v-0c5e3227 {
  margin-right: 20rpx;
}
.navbar .nav-back .back-icon.data-v-0c5e3227 {
  font-size: 40rpx;
  color: #666;
}
.navbar .nav-title.data-v-0c5e3227 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.content.data-v-0c5e3227 {
  padding: 30rpx;
}
.card.data-v-0c5e3227 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card .card-header.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}
.card .card-header .hash-icon.data-v-0c5e3227 {
  font-size: 32rpx;
  color: #3b82f6;
  margin-right: 16rpx;
}
.card .card-header .header-title.data-v-0c5e3227 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}
.card .card-header .copy-all-btn.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
}
.card .card-header .copy-all-btn .copy-icon.data-v-0c5e3227 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.card .card-header .copy-all-btn .copy-text.data-v-0c5e3227 {
  font-size: 24rpx;
  color: #374151;
}
.card .card-content .setting-item.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card .card-content .setting-item .setting-label.data-v-0c5e3227 {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-right: 24rpx;
  min-width: 120rpx;
}
.card .card-content .setting-item .version-picker.data-v-0c5e3227 {
  flex: 1;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  background: white;
}
.card .card-content .setting-item .version-picker .picker-text.data-v-0c5e3227 {
  font-size: 28rpx;
  color: #333;
}
.card .card-content .setting-item .count-input.data-v-0c5e3227 {
  width: 160rpx;
  padding: 24rpx 20rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}
.card .card-content .format-section .format-label.data-v-0c5e3227 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 20rpx;
}
.card .card-content .format-section .format-list .format-item.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}
.card .card-content .format-section .format-list .format-item .format-radio.data-v-0c5e3227 {
  margin-right: 20rpx;
}
.card .card-content .format-section .format-list .format-item .format-radio .radio-dot.data-v-0c5e3227 {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid #d1d5db;
  border-radius: 50%;
  position: relative;
}
.card .card-content .format-section .format-list .format-item .format-radio .radio-dot.selected.data-v-0c5e3227 {
  border-color: #3b82f6;
}
.card .card-content .format-section .format-list .format-item .format-radio .radio-dot.selected.data-v-0c5e3227::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16rpx;
  height: 16rpx;
  background: #3b82f6;
  border-radius: 50%;
}
.card .card-content .format-section .format-list .format-item .format-info.data-v-0c5e3227 {
  flex: 1;
}
.card .card-content .format-section .format-list .format-item .format-info .format-name.data-v-0c5e3227 {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}
.card .card-content .format-section .format-list .format-item .format-info .format-example.data-v-0c5e3227 {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
  font-family: monospace;
}
.card .card-content .uuid-list.data-v-0c5e3227 {
  max-height: 600rpx;
  overflow-y: auto;
}
.card .card-content .uuid-list .uuid-item.data-v-0c5e3227 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  margin-bottom: 16rpx;
}
.card .card-content .uuid-list .uuid-item .uuid-text.data-v-0c5e3227 {
  flex: 1;
  font-family: monospace;
  font-size: 24rpx;
  color: #333;
  word-break: break-all;
  margin-right: 20rpx;
}
.card .card-content .uuid-list .uuid-item .copy-btn.data-v-0c5e3227 {
  padding: 12rpx;
  background: white;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
}
.card .card-content .uuid-list .uuid-item .copy-btn .copy-icon.data-v-0c5e3227 {
  font-size: 28rpx;
  color: #6b7280;
}
.card .card-content .description-list .description-item.data-v-0c5e3227 {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.generate-btn.data-v-0c5e3227 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  padding: 48rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.3);
}
.generate-btn .btn-icon.data-v-0c5e3227 {
  font-size: 40rpx;
  margin-right: 16rpx;
}
.generate-btn .btn-text.data-v-0c5e3227 {
  font-size: 36rpx;
  font-weight: 600;
}