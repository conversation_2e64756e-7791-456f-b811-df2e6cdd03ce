/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-9fc4fa69 {
  display: flex;
}
.flex-1.data-v-9fc4fa69 {
  flex: 1;
}
.items-center.data-v-9fc4fa69 {
  align-items: center;
}
.justify-center.data-v-9fc4fa69 {
  justify-content: center;
}
.justify-between.data-v-9fc4fa69 {
  justify-content: space-between;
}
.text-center.data-v-9fc4fa69 {
  text-align: center;
}
.rounded.data-v-9fc4fa69 {
  border-radius: 3px;
}
.rounded-lg.data-v-9fc4fa69 {
  border-radius: 6px;
}
.shadow.data-v-9fc4fa69 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-9fc4fa69 {
  padding: 16rpx;
}
.m-4.data-v-9fc4fa69 {
  margin: 16rpx;
}
.mb-4.data-v-9fc4fa69 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-9fc4fa69 {
  margin-top: 16rpx;
}
.fullscreen-clock.data-v-9fc4fa69 {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}
.fullscreen-clock.landscape-mode.data-v-9fc4fa69 {
  transform: none !important;
}
.fullscreen-clock.landscape-mode .digital-clock.data-v-9fc4fa69 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: none;
}
.fullscreen-clock.landscape-mode .digital-clock .time-display.data-v-9fc4fa69 {
  text-align: center;
  margin-bottom: 20rpx;
}
.fullscreen-clock.landscape-mode .digital-clock .time-main.data-v-9fc4fa69 {
  font-size: min(42vh, 400rpx);
  font-weight: 200;
  line-height: 1;
  letter-spacing: -0.02em;
}
.fullscreen-clock.landscape-mode .digital-clock .date-display.data-v-9fc4fa69 {
  text-align: center;
  margin-top: 0;
}
.fullscreen-clock.landscape-mode .digital-clock .date-display .date-main.data-v-9fc4fa69 {
  font-size: min(5vh, 48rpx);
  font-weight: 300;
  opacity: 0.8;
  letter-spacing: 0.05em;
  margin: 0;
}
.fullscreen-clock.landscape-mode .digital-clock .date-display .date-week.data-v-9fc4fa69 {
  display: none;
}
.fullscreen-clock.landscape-mode .control-toggle.data-v-9fc4fa69 {
  top: 40rpx;
  right: 40rpx;
  width: min(8vh, 80rpx);
  height: min(8vh, 80rpx);
}
.fullscreen-clock.landscape-mode .control-toggle .toggle-icon.data-v-9fc4fa69 {
  font-size: min(4vh, 36rpx);
}
.fullscreen-clock.landscape-mode .control-panel.data-v-9fc4fa69 {
  position: fixed;
  bottom: 40rpx;
  left: 40rpx;
  right: 40rpx;
  padding: min(4vh, 30rpx);
}
.fullscreen-clock.landscape-mode .control-panel .mode-btn.data-v-9fc4fa69 {
  font-size: min(3vh, 28rpx);
  padding: min(2vh, 20rpx);
}
.fullscreen-clock.landscape-mode .control-panel .theme-btn.data-v-9fc4fa69 {
  width: min(6vh, 60rpx);
  height: min(6vh, 60rpx);
}
.fullscreen-clock.landscape-mode .control-panel .control-btn.data-v-9fc4fa69 {
  font-size: min(2.8vh, 26rpx);
  padding: min(2vh, 20rpx);
}
.fullscreen-clock.landscape-mode .analog-clock.data-v-9fc4fa69 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: min(8vh, 80rpx);
  padding: 0 min(6vh, 60rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face.data-v-9fc4fa69 {
  width: min(90vh, 800rpx);
  height: min(90vh, 800rpx);
  border-width: min(1vh, 8rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .hour-mark.data-v-9fc4fa69 {
  width: min(1.2vh, 10rpx);
  height: min(8vh, 60rpx);
  top: 0;
  margin-left: min(-0.6vh, -5rpx);
  transform-origin: 50% min(45vh, 400rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .minute-mark.data-v-9fc4fa69 {
  width: min(0.6vh, 5rpx);
  height: min(4vh, 30rpx);
  top: 0;
  margin-left: min(-0.3vh, -2.5rpx);
  transform-origin: 50% min(45vh, 400rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .hour-hand.data-v-9fc4fa69 {
  width: min(1.6vh, 14rpx);
  height: min(30vh, 240rpx);
  bottom: 50%;
  margin-left: min(-0.8vh, -7rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .minute-hand.data-v-9fc4fa69 {
  width: min(1.2vh, 10rpx);
  height: min(36vh, 300rpx);
  bottom: 50%;
  margin-left: min(-0.6vh, -5rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .second-hand.data-v-9fc4fa69 {
  width: min(0.6vh, 5rpx);
  height: min(40vh, 320rpx);
  bottom: 50%;
  margin-left: min(-0.3vh, -2.5rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .clock-face .center-dot.data-v-9fc4fa69 {
  width: min(2.4vh, 20rpx);
  height: min(2.4vh, 20rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .time-info.data-v-9fc4fa69 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: min(4vh, 40rpx);
}
.fullscreen-clock.landscape-mode .analog-clock .time-info .digital-time.data-v-9fc4fa69 {
  font-size: min(10vh, 100rpx);
  font-weight: 200;
  letter-spacing: -0.02em;
}
.fullscreen-clock.landscape-mode .analog-clock .time-info .date-display.data-v-9fc4fa69 {
  font-size: min(4vh, 40rpx);
  font-weight: 300;
  opacity: 0.8;
}
.fullscreen-clock.landscape-mode .flip-clock.data-v-9fc4fa69 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container.data-v-9fc4fa69 {
  display: flex;
  align-items: center;
  gap: min(6vh, 60rpx);
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container .flip-unit.data-v-9fc4fa69 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: min(3vh, 30rpx);
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container .flip-unit .flip-card.data-v-9fc4fa69 {
  width: min(24vh, 240rpx);
  height: min(32vh, 320rpx);
  border-radius: min(3vh, 30rpx);
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container .flip-unit .flip-card .flip-number.data-v-9fc4fa69 {
  font-size: min(16vh, 160rpx);
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container .flip-unit .flip-label.data-v-9fc4fa69 {
  font-size: min(4vh, 40rpx);
}
.fullscreen-clock.landscape-mode .flip-clock .flip-container .flip-separator.data-v-9fc4fa69 {
  font-size: min(16vh, 160rpx);
  margin: 0 min(2vh, 20rpx);
}
.digital-clock.data-v-9fc4fa69 {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40rpx;
}
.time-display.data-v-9fc4fa69 {
  text-align: center;
}
.time-main.data-v-9fc4fa69 {
  font-size: 240rpx;
  font-weight: 200;
  line-height: 1;
  font-family: "Helvetica Neue", Arial, sans-serif;
  letter-spacing: -4rpx;
}
.date-display.data-v-9fc4fa69 {
  text-align: center;
}
.date-main.data-v-9fc4fa69 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.date-week.data-v-9fc4fa69 {
  font-size: 36rpx;
  opacity: 0.7;
}
.analog-clock.data-v-9fc4fa69 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 60rpx;
}
.analog-clock .time-info.data-v-9fc4fa69 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.clock-face.data-v-9fc4fa69 {
  width: 500rpx;
  height: 500rpx;
  border: 6rpx solid;
  border-radius: 50%;
  position: relative;
}
.clock-face .hour-mark.data-v-9fc4fa69 {
  position: absolute;
  width: 8rpx;
  height: 40rpx;
  background-color: currentColor;
  top: 0;
  left: 50%;
  margin-left: -4rpx;
  transform-origin: 50% 250rpx;
}
.clock-face .minute-mark.data-v-9fc4fa69 {
  position: absolute;
  width: 4rpx;
  height: 20rpx;
  background-color: currentColor;
  top: 0;
  left: 50%;
  margin-left: -2rpx;
  transform-origin: 50% 250rpx;
  opacity: 0.5;
}
.clock-face .hour-hand.data-v-9fc4fa69 {
  position: absolute;
  width: 12rpx;
  height: 160rpx;
  background-color: currentColor;
  bottom: 50%;
  left: 50%;
  margin-left: -6rpx;
  transform-origin: 50% 100%;
  border-radius: 6rpx;
}
.clock-face .minute-hand.data-v-9fc4fa69 {
  position: absolute;
  width: 8rpx;
  height: 200rpx;
  background-color: currentColor;
  bottom: 50%;
  left: 50%;
  margin-left: -4rpx;
  transform-origin: 50% 100%;
  border-radius: 4rpx;
}
.clock-face .second-hand.data-v-9fc4fa69 {
  position: absolute;
  width: 4rpx;
  height: 220rpx;
  background-color: currentColor;
  bottom: 50%;
  left: 50%;
  margin-left: -2rpx;
  transform-origin: 50% 100%;
  border-radius: 2rpx;
}
.clock-face .center-dot.data-v-9fc4fa69 {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background-color: currentColor;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
}
.digital-time.data-v-9fc4fa69 {
  font-size: 48rpx;
  font-weight: 300;
  font-family: "Helvetica Neue", Arial, sans-serif;
}
.flip-clock.data-v-9fc4fa69 {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flip-container.data-v-9fc4fa69 {
  display: flex;
  align-items: center;
  gap: 40rpx;
}
.flip-unit.data-v-9fc4fa69 {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}
.flip-card.data-v-9fc4fa69 {
  width: 160rpx;
  height: 200rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}
.flip-number.data-v-9fc4fa69 {
  font-size: 100rpx;
  font-weight: 300;
  font-family: "Helvetica Neue", Arial, sans-serif;
}
.flip-label.data-v-9fc4fa69 {
  font-size: 28rpx;
  opacity: 0.7;
}
.flip-separator.data-v-9fc4fa69 {
  font-size: 120rpx;
  font-weight: 300;
  opacity: 0.6;
}
.control-panel.data-v-9fc4fa69 {
  position: fixed;
  bottom: 60rpx;
  left: 60rpx;
  right: 60rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 30rpx;
  padding: 40rpx;
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
  transition: transform 0.3s ease, opacity 0.3s ease;
}
.control-panel.hidden.data-v-9fc4fa69 {
  transform: translateY(100%);
  opacity: 0;
  pointer-events: none;
}
.mode-switcher.data-v-9fc4fa69 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.mode-btn.data-v-9fc4fa69 {
  flex: 1;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 16rpx;
  color: white;
  font-size: 28rpx;
}
.mode-btn.active.data-v-9fc4fa69 {
  background: rgba(255, 255, 255, 0.3);
}
.theme-switcher.data-v-9fc4fa69 {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
  justify-content: center;
}
.theme-btn.data-v-9fc4fa69 {
  width: 60rpx;
  height: 60rpx;
  border: none;
  border-radius: 50%;
  position: relative;
  box-shadow: inset 0 2rpx 8rpx rgba(255, 255, 255, 0.2);
}
.theme-btn.active.data-v-9fc4fa69:after {
  content: "";
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  border: 3rpx solid white;
  border-radius: 50%;
  box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.3);
}
.other-controls.data-v-9fc4fa69 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}
.control-btn.data-v-9fc4fa69 {
  flex: 1;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 16rpx;
  color: white;
  font-size: 26rpx;
}
.control-toggle.data-v-9fc4fa69 {
  position: fixed;
  top: 180rpx;
  right: 60rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  z-index: 100;
}
.toggle-icon.data-v-9fc4fa69 {
  font-size: 36rpx;
  color: white;
}
.time-picker-modal.data-v-9fc4fa69 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.picker-content.data-v-9fc4fa69 {
  background: white;
  border-radius: 30rpx;
  width: 600rpx;
  overflow: hidden;
}
.picker-header.data-v-9fc4fa69 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 1rpx solid #eee;
}
.picker-title.data-v-9fc4fa69 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.close-btn.data-v-9fc4fa69 {
  font-size: 48rpx;
  color: #999;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.picker-view.data-v-9fc4fa69 {
  height: 400rpx;
}
.picker-item.data-v-9fc4fa69 {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 32rpx;
}
.picker-actions.data-v-9fc4fa69 {
  display: flex;
  border-top: 1rpx solid #eee;
}
.picker-btn.data-v-9fc4fa69 {
  flex: 1;
  padding: 40rpx;
  border: none;
  background: none;
  font-size: 32rpx;
}
.picker-btn.cancel.data-v-9fc4fa69 {
  color: #666;
}
.picker-btn.confirm.data-v-9fc4fa69 {
  color: #007aff;
  font-weight: bold;
}