"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "CommonNumbers",
  data() {
    return {
      searchTerm: "",
      selectedCategory: "all",
      categories: [
        { id: "all", name: "全部", icon: "📋" },
        { id: "emergency", name: "紧急求助", icon: "🚨" },
        { id: "government", name: "政府服务", icon: "🏛️" },
        { id: "transport", name: "交通出行", icon: "🚗" },
        { id: "utility", name: "公共事业", icon: "🔧" },
        { id: "finance", name: "金融服务", icon: "💳" },
        { id: "telecom", name: "通信服务", icon: "📡" },
        { id: "other", name: "其他服务", icon: "🔗" }
      ],
      phoneNumbers: [
        // 紧急求助
        { category: "emergency", name: "火警", number: "119", description: "火灾报警救援服务" },
        { category: "emergency", name: "匪警", number: "110", description: "治安报警求助服务" },
        { category: "emergency", name: "急救", number: "120", description: "医疗急救服务热线" },
        { category: "emergency", name: "交通事故", number: "122", description: "交通事故报警电话" },
        // 政府服务
        { category: "government", name: "市长热线", number: "12345", description: "政府服务便民热线" },
        { category: "government", name: "纪委举报", number: "12388", description: "纪检监察举报热线" },
        { category: "government", name: "税务服务", number: "12366", description: "税务服务咨询热线" },
        { category: "government", name: "工商投诉", number: "12315", description: "消费者投诉举报热线" },
        { category: "government", name: "质量监督", number: "12365", description: "产品质量投诉举报" },
        { category: "government", name: "价格举报", number: "12358", description: "价格监督举报热线" },
        // 交通出行
        { category: "transport", name: "民航服务", number: "95539", description: "民航服务质量监督" },
        { category: "transport", name: "铁路客服", number: "12306", description: "铁路客户服务中心" },
        { category: "transport", name: "公路客运", number: "96096", description: "公路客运服务热线" },
        { category: "transport", name: "出租车服务", number: "96103", description: "出租车服务热线" },
        { category: "transport", name: "违章查询", number: "12123", description: "交管12123服务" },
        // 公共事业
        { category: "utility", name: "供电服务", number: "95598", description: "国家电网客户服务" },
        { category: "utility", name: "自来水", number: "96116", description: "供水服务热线" },
        { category: "utility", name: "燃气服务", number: "96777", description: "燃气客户服务热线" },
        { category: "utility", name: "供暖服务", number: "96069", description: "供热客户服务热线" },
        { category: "utility", name: "有线电视", number: "96766", description: "广电网络客服" },
        // 金融服务
        { category: "finance", name: "中国银行", number: "95566", description: "中国银行客户服务" },
        { category: "finance", name: "工商银行", number: "95588", description: "工商银行客户服务" },
        { category: "finance", name: "建设银行", number: "95533", description: "建设银行客户服务" },
        { category: "finance", name: "农业银行", number: "95599", description: "农业银行客户服务" },
        { category: "finance", name: "招商银行", number: "95555", description: "招商银行客户服务" },
        { category: "finance", name: "交通银行", number: "95559", description: "交通银行客户服务" },
        { category: "finance", name: "支付宝", number: "95188", description: "支付宝客户服务" },
        // 通信服务
        { category: "telecom", name: "中国移动", number: "10086", description: "移动客户服务热线" },
        { category: "telecom", name: "中国联通", number: "10010", description: "联通客户服务热线" },
        { category: "telecom", name: "中国电信", number: "10000", description: "电信客户服务热线" },
        { category: "telecom", name: "中国广电", number: "10099", description: "广电客户服务热线" },
        // 其他服务
        { category: "other", name: "天气预报", number: "12121", description: "气象服务咨询电话" },
        { category: "other", name: "时间服务", number: "12117", description: "标准时间报时服务" },
        { category: "other", name: "邮政服务", number: "11183", description: "邮政客户服务热线" },
        { category: "other", name: "EMS快递", number: "11185", description: "EMS客户服务热线" },
        { category: "other", name: "中通快递", number: "95311", description: "中通快递客服热线" },
        { category: "other", name: "圆通快递", number: "95554", description: "圆通快递客服热线" }
      ]
    };
  },
  computed: {
    filteredNumbers() {
      return this.phoneNumbers.filter((item) => {
        const matchesSearch = item.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.number.includes(this.searchTerm) || item.description.toLowerCase().includes(this.searchTerm.toLowerCase());
        const matchesCategory = this.selectedCategory === "all" || item.category === this.selectedCategory;
        return matchesSearch && matchesCategory;
      });
    }
  },
  methods: {
    onSearch() {
    },
    clearSearch() {
      this.searchTerm = "";
    },
    selectCategory(categoryId) {
      this.selectedCategory = categoryId;
      const category = this.categories.find((cat) => cat.id === categoryId);
      if (category && categoryId !== "all") {
        common_vendor.index.showToast({
          title: `已切换到${category.name}`,
          icon: "none"
        });
      }
    },
    copyNumber(number) {
      common_vendor.index.setClipboardData({
        data: number,
        success: () => {
          common_vendor.index.showToast({
            title: "号码已复制",
            icon: "success",
            duration: 1500
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 1500
          });
        }
      });
    },
    callNumber(number) {
      if (["110", "119", "120", "122"].includes(number)) {
        common_vendor.index.showModal({
          title: "紧急号码提醒",
          content: "您即将拨打紧急服务号码，请确认是否为紧急情况？",
          confirmText: "确认拨打",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              this.makeCall(number);
            }
          }
        });
      } else {
        this.makeCall(number);
      }
    },
    makeCall(number) {
      common_vendor.index.makePhoneCall({
        phoneNumber: number,
        success: () => {
          common_vendor.index.__f__("log", "at pages/tools/common-numbers.vue:277", "拨打电话成功");
        },
        fail: (err) => {
          common_vendor.index.__f__("log", "at pages/tools/common-numbers.vue:280", "拨打电话失败", err);
          common_vendor.index.showToast({
            title: "拨打失败",
            icon: "none",
            duration: 2e3
          });
        }
      });
    },
    getServiceIcon(category) {
      const icons = {
        emergency: "🚨",
        government: "🏛️",
        transport: "🚗",
        utility: "🔧",
        finance: "💳",
        telecom: "📡",
        other: "🔗"
      };
      return icons[category] || "📞";
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.searchTerm = $event.detail.value, (...args) => $options.onSearch && $options.onSearch(...args)]),
    b: $data.searchTerm,
    c: $data.searchTerm
  }, $data.searchTerm ? {
    d: common_vendor.o((...args) => $options.clearSearch && $options.clearSearch(...args))
  } : {}, {
    e: common_vendor.f($data.categories, (category, k0, i0) => {
      return {
        a: common_vendor.t(category.icon),
        b: common_vendor.t(category.name),
        c: category.id,
        d: $data.selectedCategory === category.id ? 1 : "",
        e: common_vendor.o(($event) => $options.selectCategory(category.id), category.id)
      };
    }),
    f: common_vendor.f($options.filteredNumbers, (item, index, i0) => {
      return {
        a: common_vendor.t($options.getServiceIcon(item.category)),
        b: common_vendor.t(item.name),
        c: common_vendor.t(item.description),
        d: common_vendor.t(item.number),
        e: common_vendor.o(($event) => $options.callNumber(item.number), index),
        f: common_vendor.o(($event) => $options.copyNumber(item.number), index),
        g: index
      };
    }),
    g: $options.filteredNumbers.length === 0
  }, $options.filteredNumbers.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-a22f57c0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/common-numbers.js.map
