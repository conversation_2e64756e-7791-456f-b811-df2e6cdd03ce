"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      inputName: "",
      decoratedNames: [],
      specialChars: {
        prefix: ["๑", "♡", "◉", "◎", "●", "○", "◊", "◆", "◇", "♦"],
        suffix: ["๑", "♡", "◉", "◎", "●", "○", "◊", "◆", "◇", "♦"],
        decorative: ["·", "˙", "∴", "∵", "∷", "‥", "…", "⋯", "⋰", "⋱"],
        brackets: ["【】", "『』", "「」", "〖〗", "〔〕", "（）", "［］", "｛｝"]
      },
      inputTimeout: null
    };
  },
  methods: {
    onNameInput() {
      clearTimeout(this.inputTimeout);
      this.inputTimeout = setTimeout(() => {
        this.generateDecoratedNames();
      }, 300);
    },
    generateDecoratedNames() {
      const name = this.inputName.trim();
      if (!name) {
        this.decoratedNames = [];
        return;
      }
      const results = [];
      this.specialChars.prefix.slice(0, 5).forEach((prefix, i) => {
        const suffix = this.specialChars.suffix[i];
        results.push(`${prefix}${name}${suffix}`);
      });
      this.specialChars.brackets.forEach((bracket) => {
        const open = bracket.charAt(0);
        const close = bracket.charAt(1);
        results.push(`${open}${name}${close}`);
      });
      this.specialChars.decorative.slice(0, 4).forEach((dot) => {
        results.push(`${dot}${name}${dot}`);
        results.push(`${dot} ${name} ${dot}`);
      });
      results.push(`◉●○◎${name}◎○●◉`);
      results.push(`♡${name}♡`);
      results.push(`◆◇${name}◇◆`);
      results.push(`๑${name}๑`);
      this.decoratedNames = results;
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.vibrateShort) {
        common_vendor.index.vibrateShort();
      }
    },
    refreshNames() {
      this.generateDecoratedNames();
    },
    copyText(text) {
      common_vendor.index.setClipboardData({
        data: text,
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success",
            duration: 1500
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "none",
            duration: 1500
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.o([($event) => $data.inputName = $event.detail.value, (...args) => $options.onNameInput && $options.onNameInput(...args)]),
    b: $data.inputName,
    c: common_vendor.t($data.inputName.length),
    d: $data.decoratedNames.length > 0
  }, $data.decoratedNames.length > 0 ? {
    e: common_vendor.o((...args) => $options.refreshNames && $options.refreshNames(...args)),
    f: common_vendor.f($data.decoratedNames, (name, index, i0) => {
      return {
        a: common_vendor.t(name),
        b: common_vendor.o(($event) => $options.copyText(name), index),
        c: index,
        d: common_vendor.o(($event) => $options.copyText(name), index)
      };
    })
  } : {}, {
    g: common_vendor.f($data.specialChars.decorative, (char, index, i0) => {
      return {
        a: common_vendor.t(char),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(char), index)
      };
    }),
    h: common_vendor.f($data.specialChars.prefix, (char, index, i0) => {
      return {
        a: common_vendor.t(char),
        b: index,
        c: common_vendor.o(($event) => $options.copyText(char), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c7bbac36"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/braid-nickname.js.map
