"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      selectedVideo: "",
      videoPoster: "",
      videoError: false,
      compressedVideo: "",
      compressing: false,
      compressProgress: 0,
      quality: 70,
      frameRateOptimization: true,
      audioCompression: true,
      videoName: "",
      originalSize: 0,
      compressedSize: 0,
      compressionRatio: 0,
      processingTime: 0,
      savedSpace: 0,
      selectedResolution: {
        id: 2,
        name: "1080P",
        desc: "1920×1080"
      },
      resolutions: [
        { id: 1, name: "720P", desc: "1280×720" },
        { id: 2, name: "1080P", desc: "1920×1080" },
        { id: 3, name: "1440P", desc: "2560×1440" },
        { id: 4, name: "4K", desc: "3840×2160" }
      ]
    };
  },
  computed: {
    estimatedSize() {
      if (!this.originalSize)
        return 0;
      return (this.originalSize * (this.quality / 100)).toFixed(1);
    }
  },
  methods: {
    // 选择视频
    selectVideo() {
      this.videoError = false;
      common_vendor.index.chooseVideo({
        sourceType: ["album"],
        maxDuration: 60,
        camera: "back",
        compressed: false,
        success: (res) => {
          if (res.size > 100 * 1024 * 1024) {
            common_vendor.index.showToast({
              title: "视频大小不能超过100MB",
              icon: "none"
            });
            return;
          }
          this.selectedVideo = res.tempFilePath;
          this.videoName = this.extractFileName(res.tempFilePath);
          this.originalSize = (res.size / 1024 / 1024).toFixed(1);
          this.getVideoPoster(res.tempFilePath);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "选择视频失败",
            icon: "error"
          });
        }
      });
    },
    // 录制视频
    recordVideo() {
      common_vendor.index.chooseVideo({
        sourceType: ["camera"],
        maxDuration: 60,
        camera: "back",
        success: (res) => {
          this.selectedVideo = res.tempFilePath;
          this.videoName = this.extractFileName(res.tempFilePath);
          this.originalSize = (res.size / 1024 / 1024).toFixed(1);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            title: "录制视频失败",
            icon: "error"
          });
        }
      });
    },
    // 提取文件名
    extractFileName(path) {
      const fileName = path.split("/").pop();
      return fileName.length > 20 ? fileName.substring(0, 20) + "..." : fileName;
    },
    // 获取视频封面
    getVideoPoster(videoPath) {
      const videoContext = common_vendor.index.createVideoContext("previewVideo");
      videoContext.seek(0);
      videoContext.pause();
    },
    // 处理视频加载错误
    handleVideoError(e) {
      common_vendor.index.__f__("error", "at pages/tools/video-compressor.vue:373", "视频加载错误:", e);
      this.videoError = true;
      common_vendor.index.showToast({
        title: "视频加载失败",
        icon: "error"
      });
    },
    // 处理视频加载成功
    handleVideoLoad() {
      this.videoError = false;
    },
    // 质量变化
    onQualityChange(e) {
      this.quality = e.detail.value;
    },
    // 设置质量预设
    setQuality(value) {
      this.quality = value;
    },
    // 选择分辨率
    selectResolution(resolution) {
      this.selectedResolution = resolution;
    },
    // 帧率优化开关
    onFrameRateChange(e) {
      this.frameRateOptimization = e.detail.value;
    },
    // 音频压缩开关
    onAudioCompressionChange(e) {
      this.audioCompression = e.detail.value;
    },
    // 压缩视频
    async compressVideo() {
      this.compressing = true;
      this.compressProgress = 0;
      const startTime = Date.now();
      try {
        const progressInterval = setInterval(() => {
          if (this.compressProgress < 90) {
            this.compressProgress += Math.random() * 10;
          }
        }, 300);
        await new Promise((resolve) => setTimeout(resolve, 4e3));
        clearInterval(progressInterval);
        this.compressProgress = 100;
        const endTime = Date.now();
        this.processingTime = Math.round((endTime - startTime) / 1e3);
        this.compressedSize = (this.originalSize * (this.quality / 100)).toFixed(1);
        this.compressionRatio = ((this.originalSize - this.compressedSize) / this.originalSize * 100).toFixed(1);
        this.savedSpace = (this.originalSize - this.compressedSize).toFixed(1);
        this.compressedVideo = this.selectedVideo;
        common_vendor.index.showToast({
          title: "压缩完成",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.showToast({
          title: "压缩失败",
          icon: "error"
        });
      } finally {
        this.compressing = false;
      }
    },
    // 保存视频
    saveVideo() {
      common_vendor.index.saveVideoToPhotosAlbum({
        filePath: this.compressedVideo,
        success: () => {
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      });
    },
    // 重置流程
    resetProcess() {
      this.selectedVideo = "";
      this.compressedVideo = "";
      this.compressing = false;
      this.compressProgress = 0;
      this.quality = 70;
      this.frameRateOptimization = true;
      this.audioCompression = true;
      this.videoName = "";
      this.originalSize = 0;
      this.compressedSize = 0;
      this.compressionRatio = 0;
      this.processingTime = 0;
      this.savedSpace = 0;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$data.selectedVideo
  }, !$data.selectedVideo ? {
    b: common_vendor.o((...args) => $options.selectVideo && $options.selectVideo(...args)),
    c: common_vendor.o((...args) => $options.recordVideo && $options.recordVideo(...args))
  } : {}, {
    d: $data.selectedVideo && !$data.compressedVideo
  }, $data.selectedVideo && !$data.compressedVideo ? common_vendor.e({
    e: $data.selectedVideo && !$data.videoError
  }, $data.selectedVideo && !$data.videoError ? {
    f: $data.selectedVideo,
    g: $data.videoPoster,
    h: common_vendor.o((...args) => $options.handleVideoError && $options.handleVideoError(...args)),
    i: common_vendor.o((...args) => $options.handleVideoLoad && $options.handleVideoLoad(...args))
  } : {}, {
    j: $data.videoError
  }, $data.videoError ? {} : {}, {
    k: common_vendor.t($data.videoName),
    l: common_vendor.t($data.originalSize),
    m: common_vendor.t($options.estimatedSize),
    n: common_vendor.t($data.quality),
    o: $data.quality,
    p: common_vendor.o((...args) => $options.onQualityChange && $options.onQualityChange(...args)),
    q: $data.quality === 40 ? 1 : "",
    r: common_vendor.o(($event) => $options.setQuality(40)),
    s: $data.quality === 70 ? 1 : "",
    t: common_vendor.o(($event) => $options.setQuality(70)),
    v: $data.quality === 90 ? 1 : "",
    w: common_vendor.o(($event) => $options.setQuality(90)),
    x: common_vendor.f($data.resolutions, (res, k0, i0) => {
      return {
        a: common_vendor.t(res.name),
        b: common_vendor.t(res.desc),
        c: res.id,
        d: $data.selectedResolution.id === res.id ? 1 : "",
        e: common_vendor.o(($event) => $options.selectResolution(res), res.id)
      };
    }),
    y: $data.frameRateOptimization,
    z: common_vendor.o((...args) => $options.onFrameRateChange && $options.onFrameRateChange(...args)),
    A: $data.audioCompression,
    B: common_vendor.o((...args) => $options.onAudioCompressionChange && $options.onAudioCompressionChange(...args)),
    C: !$data.compressing
  }, !$data.compressing ? {} : {
    D: common_vendor.t($data.compressProgress)
  }, {
    E: common_vendor.o((...args) => $options.compressVideo && $options.compressVideo(...args)),
    F: $data.compressing
  }) : {}, {
    G: $data.compressedVideo
  }, $data.compressedVideo ? {
    H: common_vendor.t($data.compressionRatio),
    I: common_vendor.t($data.processingTime),
    J: common_vendor.t($data.savedSpace),
    K: common_vendor.t($data.originalSize),
    L: common_vendor.t($data.compressedSize),
    M: common_vendor.o((...args) => $options.saveVideo && $options.saveVideo(...args)),
    N: common_vendor.o((...args) => $options.resetProcess && $options.resetProcess(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-c015a0ff"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/video-compressor.js.map
