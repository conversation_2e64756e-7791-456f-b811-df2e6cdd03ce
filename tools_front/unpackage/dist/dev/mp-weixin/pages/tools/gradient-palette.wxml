<view class="gradient-palette data-v-e91c53a2"><view class="content data-v-e91c53a2"><view class="header-card data-v-e91c53a2"><view class="icon-container data-v-e91c53a2"><text class="icon data-v-e91c53a2">🎨</text></view><text class="title data-v-e91c53a2">精美渐变色卡</text><text class="desc data-v-e91c53a2">精选的渐变色彩方案，为您的设计提供灵感</text></view><view class="gradient-grid data-v-e91c53a2"><view wx:for="{{a}}" wx:for-item="preset" wx:key="e" class="gradient-card data-v-e91c53a2" bindtap="{{preset.f}}"><view class="gradient-preview data-v-e91c53a2" style="{{'background:' + preset.a}}"></view><view class="gradient-info data-v-e91c53a2"><view class="gradient-header data-v-e91c53a2"><text class="gradient-name data-v-e91c53a2">{{preset.b}}</text><view class="copy-btn data-v-e91c53a2" catchtap="{{preset.c}}"><text class="copy-icon data-v-e91c53a2">📋</text></view></view><text class="gradient-code data-v-e91c53a2">{{preset.d}}...</text></view></view></view><view wx:if="{{b}}" class="selected-card data-v-e91c53a2"><view class="card-header data-v-e91c53a2"><text class="header-title data-v-e91c53a2">选中的渐变</text></view><view class="card-content data-v-e91c53a2"><view class="selected-preview data-v-e91c53a2" style="{{'background:' + c}}"></view><view class="code-container data-v-e91c53a2"><text class="code-text data-v-e91c53a2">background: {{d}};</text><view class="copy-code-btn data-v-e91c53a2" bindtap="{{e}}"><text class="copy-icon data-v-e91c53a2">📋</text><text class="copy-text data-v-e91c53a2">复制</text></view></view></view></view><view class="info-card data-v-e91c53a2"><view class="card-header data-v-e91c53a2"><text class="header-title data-v-e91c53a2">使用说明</text></view><view class="card-content data-v-e91c53a2"><view class="info-list data-v-e91c53a2"><text class="info-item data-v-e91c53a2">• 点击渐变色卡预览效果</text><text class="info-item data-v-e91c53a2">• 点击复制按钮获取CSS代码</text><text class="info-item data-v-e91c53a2">• 直接将代码应用到您的项目中</text><text class="info-item data-v-e91c53a2">• 适用于网页背景、按钮等设计</text></view></view></view></view></view>