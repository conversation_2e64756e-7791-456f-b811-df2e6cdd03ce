"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      minValue: "1",
      maxValue: "100",
      count: "1",
      allowDuplicates: true,
      results: [],
      history: [],
      presets: [
        { name: "1-10", min: 1, max: 10, count: 1 },
        { name: "1-100", min: 1, max: 100, count: 1 },
        { name: "双色球红球", min: 1, max: 33, count: 6 },
        { name: "双色球蓝球", min: 1, max: 16, count: 1 },
        { name: "大乐透前区", min: 1, max: 35, count: 5 },
        { name: "大乐透后区", min: 1, max: 12, count: 2 },
        { name: "骰子", min: 1, max: 6, count: 1 },
        { name: "硬币", min: 0, max: 1, count: 1 }
      ]
    };
  },
  methods: {
    toggleAllowDuplicates(e) {
      this.allowDuplicates = e.detail.value;
    },
    generateRandomNumbers() {
      const min = parseInt(this.minValue);
      const max = parseInt(this.maxValue);
      const num = parseInt(this.count);
      if (isNaN(min) || isNaN(max) || isNaN(num) || min > max || num <= 0) {
        common_vendor.index.showToast({
          title: "请输入有效的参数",
          icon: "none"
        });
        return;
      }
      const range = max - min + 1;
      if (!this.allowDuplicates && num > range) {
        common_vendor.index.showToast({
          title: "不允许重复时，生成数量不能超过范围大小",
          icon: "none"
        });
        return;
      }
      const newResults = [];
      const used = /* @__PURE__ */ new Set();
      for (let i = 0; i < num; i++) {
        let randomNum;
        if (this.allowDuplicates) {
          randomNum = Math.floor(Math.random() * range) + min;
        } else {
          do {
            randomNum = Math.floor(Math.random() * range) + min;
          } while (used.has(randomNum));
          used.add(randomNum);
        }
        newResults.push(randomNum);
      }
      if (this.presets.some((p) => (p.name.includes("球") || p.name.includes("乐透")) && p.min == min && p.max == max && p.count == num)) {
        newResults.sort((a, b) => a - b);
      }
      this.results = newResults;
      this.history = [newResults, ...this.history.slice(0, 9)];
      common_vendor.index.showToast({
        title: "生成成功",
        icon: "success"
      });
    },
    usePreset(preset) {
      this.minValue = preset.min.toString();
      this.maxValue = preset.max.toString();
      this.count = preset.count.toString();
      this.allowDuplicates = !preset.name.includes("球") && !preset.name.includes("乐透");
      common_vendor.index.showToast({
        title: `已应用${preset.name}预设`,
        icon: "success"
      });
    },
    copyResults() {
      if (this.results.length > 0) {
        common_vendor.index.setClipboardData({
          data: this.results.join(", "),
          success: () => {
            common_vendor.index.showToast({
              title: "已复制到剪贴板",
              icon: "success"
            });
          }
        });
      }
    },
    copyRecord(record) {
      common_vendor.index.setClipboardData({
        data: record.join(", "),
        success: () => {
          common_vendor.index.showToast({
            title: "已复制到剪贴板",
            icon: "success"
          });
        }
      });
    },
    clearResults() {
      this.results = [];
      common_vendor.index.showToast({
        title: "结果已清空",
        icon: "success"
      });
    },
    clearHistory() {
      this.history = [];
      common_vendor.index.showToast({
        title: "历史记录已清空",
        icon: "success"
      });
    },
    formatResult(numbers) {
      if (this.presets.find((p) => p.name === "硬币" && this.minValue === "0" && this.maxValue === "1")) {
        return numbers.map((n) => n === 0 ? "反面" : "正面").join(", ");
      }
      return numbers.join(", ");
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.minValue,
    b: common_vendor.o(common_vendor.m(($event) => $data.minValue = $event.detail.value, {
      number: true
    })),
    c: $data.maxValue,
    d: common_vendor.o(common_vendor.m(($event) => $data.maxValue = $event.detail.value, {
      number: true
    })),
    e: $data.count,
    f: common_vendor.o(($event) => $data.count = $event.detail.value),
    g: $data.allowDuplicates,
    h: common_vendor.o((...args) => $options.toggleAllowDuplicates && $options.toggleAllowDuplicates(...args)),
    i: common_vendor.f($data.presets, (preset, index, i0) => {
      return {
        a: common_vendor.t(preset.name),
        b: common_vendor.t(preset.min),
        c: common_vendor.t(preset.max),
        d: common_vendor.t(preset.count),
        e: index,
        f: common_vendor.o(($event) => $options.usePreset(preset), index)
      };
    }),
    j: common_vendor.o((...args) => $options.generateRandomNumbers && $options.generateRandomNumbers(...args)),
    k: $data.results.length > 0
  }, $data.results.length > 0 ? {
    l: common_vendor.o((...args) => $options.copyResults && $options.copyResults(...args)),
    m: common_vendor.o((...args) => $options.clearResults && $options.clearResults(...args)),
    n: common_vendor.t($options.formatResult($data.results)),
    o: common_vendor.t($data.minValue),
    p: common_vendor.t($data.maxValue),
    q: common_vendor.t($data.results.length)
  } : {}, {
    r: $data.history.length > 0
  }, $data.history.length > 0 ? {
    s: common_vendor.o((...args) => $options.clearHistory && $options.clearHistory(...args)),
    t: common_vendor.f($data.history, (record, index, i0) => {
      return {
        a: common_vendor.t($options.formatResult(record)),
        b: index,
        c: common_vendor.o(($event) => $options.copyRecord(record), index)
      };
    })
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1b354db9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/random-number-generator.js.map
