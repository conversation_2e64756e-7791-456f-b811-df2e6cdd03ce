"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  name: "TimestampConverter",
  data() {
    return {
      currentTime: /* @__PURE__ */ new Date(),
      inputTimestamp: "",
      timestampUnit: 0,
      // 0: 秒, 1: 毫秒
      selectedDate: "",
      selectedTime: "",
      results: null,
      timer: null,
      commonTimestamps: [
        { name: "Unix元年", desc: "1970年1月1日", timestamp: 0 },
        { name: "千年虫", desc: "2000年1月1日", timestamp: 946684800 },
        { name: "北京奥运", desc: "2008年8月8日", timestamp: 1218124800 },
        { name: "世界末日", desc: "2012年12月21日", timestamp: 1356048e3 },
        { name: "新纪元", desc: "2020年1月1日", timestamp: 1577836800 }
      ]
    };
  },
  computed: {
    currentDateTime() {
      return this.currentTime.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
      });
    },
    currentTimestamp() {
      return Math.floor(this.currentTime.getTime() / 1e3);
    },
    resultsList() {
      if (!this.results)
        return [];
      return [
        { label: "时间戳(秒)", value: this.results.timestamp },
        { label: "时间戳(毫秒)", value: this.results.milliseconds },
        { label: "本地时间", value: this.results.localTime },
        { label: "ISO 8601", value: this.results.iso },
        { label: "UTC时间", value: this.results.utc },
        { label: "日期", value: this.results.date },
        { label: "时间", value: this.results.time }
      ].filter((item) => item.value !== void 0);
    }
  },
  mounted() {
    this.startTimer();
    this.initializeDateTime();
  },
  beforeDestroy() {
    this.stopTimer();
  },
  methods: {
    startTimer() {
      this.timer = setInterval(() => {
        this.currentTime = /* @__PURE__ */ new Date();
      }, 1e3);
    },
    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    refreshTime() {
      this.currentTime = /* @__PURE__ */ new Date();
      common_vendor.index.showToast({
        title: "时间已刷新",
        icon: "success"
      });
    },
    initializeDateTime() {
      const now = /* @__PURE__ */ new Date();
      this.selectedDate = now.toISOString().split("T")[0];
      this.selectedTime = now.toTimeString().slice(0, 5);
    },
    onTimestampUnitChange(e) {
      this.timestampUnit = e.detail.value;
    },
    onDateChange(e) {
      this.selectedDate = e.detail.value;
    },
    onTimeChange(e) {
      this.selectedTime = e.detail.value;
    },
    convertFromTimestamp() {
      const input = parseFloat(this.inputTimestamp);
      if (isNaN(input)) {
        common_vendor.index.showToast({
          title: "请输入有效的时间戳",
          icon: "error"
        });
        return;
      }
      const ms = this.timestampUnit === 0 ? input * 1e3 : input;
      const date = new Date(ms);
      if (isNaN(date.getTime())) {
        common_vendor.index.showToast({
          title: "时间戳格式错误",
          icon: "error"
        });
        return;
      }
      this.results = {
        timestamp: Math.floor(ms / 1e3),
        milliseconds: ms,
        localTime: date.toLocaleString("zh-CN"),
        iso: date.toISOString(),
        utc: date.toUTCString(),
        date: date.toLocaleDateString("zh-CN"),
        time: date.toLocaleTimeString("zh-CN")
      };
    },
    convertToTimestamp() {
      if (!this.selectedDate || !this.selectedTime) {
        common_vendor.index.showToast({
          title: "请选择完整的日期和时间",
          icon: "error"
        });
        return;
      }
      const dateTimeString = `${this.selectedDate}T${this.selectedTime}:00`;
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) {
        common_vendor.index.showToast({
          title: "日期时间格式错误",
          icon: "error"
        });
        return;
      }
      const ms = date.getTime();
      this.results = {
        timestamp: Math.floor(ms / 1e3),
        milliseconds: ms,
        localTime: date.toLocaleString("zh-CN"),
        iso: date.toISOString(),
        utc: date.toUTCString(),
        date: date.toLocaleDateString("zh-CN"),
        time: date.toLocaleTimeString("zh-CN")
      };
    },
    useCurrentTime() {
      const now = /* @__PURE__ */ new Date();
      this.inputTimestamp = Math.floor(now.getTime() / 1e3).toString();
      this.timestampUnit = 0;
      this.selectedDate = now.toISOString().split("T")[0];
      this.selectedTime = now.toTimeString().slice(0, 5);
      this.results = {
        timestamp: Math.floor(now.getTime() / 1e3),
        milliseconds: now.getTime(),
        localTime: now.toLocaleString("zh-CN"),
        iso: now.toISOString(),
        utc: now.toUTCString(),
        date: now.toLocaleDateString("zh-CN"),
        time: now.toLocaleTimeString("zh-CN")
      };
      common_vendor.index.showToast({
        title: "已使用当前时间",
        icon: "success"
      });
    },
    useCommonTimestamp(timestamp) {
      this.inputTimestamp = timestamp.toString();
      this.timestampUnit = 0;
      this.convertFromTimestamp();
    },
    formatTimestamp(timestamp) {
      return new Date(timestamp * 1e3).toLocaleDateString("zh-CN");
    },
    copyTimestamp(timestamp) {
      this.copyResult(timestamp.toString());
    },
    copyResult(value) {
      if (!value)
        return;
      common_vendor.index.setClipboardData({
        data: value.toString(),
        success: () => {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "success"
          });
        },
        fail: () => {
          common_vendor.index.showToast({
            title: "复制失败",
            icon: "error"
          });
        }
      });
    },
    copyAllResults() {
      if (!this.results)
        return;
      const allResults = this.resultsList.map((item) => `${item.label}: ${item.value}`).join("\n");
      common_vendor.index.setClipboardData({
        data: allResults,
        success: () => {
          common_vendor.index.showToast({
            title: "全部结果已复制",
            icon: "success"
          });
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.currentDateTime),
    b: common_vendor.t($options.currentTimestamp),
    c: common_vendor.o((...args) => $options.useCurrentTime && $options.useCurrentTime(...args)),
    d: common_vendor.o(($event) => $options.copyTimestamp($options.currentTimestamp)),
    e: $data.inputTimestamp,
    f: common_vendor.o(($event) => $data.inputTimestamp = $event.detail.value),
    g: common_vendor.t($data.timestampUnit === 0 ? "秒" : "毫秒"),
    h: $data.timestampUnit,
    i: ["秒", "毫秒"],
    j: common_vendor.o((...args) => $options.onTimestampUnitChange && $options.onTimestampUnitChange(...args)),
    k: common_vendor.o((...args) => $options.convertFromTimestamp && $options.convertFromTimestamp(...args)),
    l: !$data.inputTimestamp,
    m: common_vendor.n(!$data.inputTimestamp ? "disabled" : ""),
    n: common_vendor.t($data.selectedDate || "选择日期"),
    o: $data.selectedDate,
    p: common_vendor.o((...args) => $options.onDateChange && $options.onDateChange(...args)),
    q: common_vendor.t($data.selectedTime || "选择时间"),
    r: $data.selectedTime,
    s: common_vendor.o((...args) => $options.onTimeChange && $options.onTimeChange(...args)),
    t: common_vendor.o((...args) => $options.convertToTimestamp && $options.convertToTimestamp(...args)),
    v: !$data.selectedDate || !$data.selectedTime,
    w: common_vendor.n(!$data.selectedDate || !$data.selectedTime ? "disabled" : ""),
    x: $data.results
  }, $data.results ? {
    y: common_vendor.o((...args) => $options.copyAllResults && $options.copyAllResults(...args)),
    z: common_vendor.f($options.resultsList, (item, index, i0) => {
      return {
        a: common_vendor.t(item.label),
        b: common_vendor.t(item.value),
        c: index,
        d: common_vendor.o(($event) => $options.copyResult(item.value), index)
      };
    })
  } : {}, {
    A: common_vendor.f($data.commonTimestamps, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.desc),
        c: common_vendor.t(item.timestamp),
        d: common_vendor.t($options.formatTimestamp(item.timestamp)),
        e: index,
        f: common_vendor.o(($event) => $options.copyTimestamp(item.timestamp), index)
      };
    })
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-432de3b9"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/timestamp-converter.js.map
