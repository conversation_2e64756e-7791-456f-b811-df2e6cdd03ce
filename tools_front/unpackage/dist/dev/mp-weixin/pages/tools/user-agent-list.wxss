/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-18c7a761 {
  display: flex;
}
.flex-1.data-v-18c7a761 {
  flex: 1;
}
.items-center.data-v-18c7a761 {
  align-items: center;
}
.justify-center.data-v-18c7a761 {
  justify-content: center;
}
.justify-between.data-v-18c7a761 {
  justify-content: space-between;
}
.text-center.data-v-18c7a761 {
  text-align: center;
}
.rounded.data-v-18c7a761 {
  border-radius: 3px;
}
.rounded-lg.data-v-18c7a761 {
  border-radius: 6px;
}
.shadow.data-v-18c7a761 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-18c7a761 {
  padding: 16rpx;
}
.m-4.data-v-18c7a761 {
  margin: 16rpx;
}
.mb-4.data-v-18c7a761 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-18c7a761 {
  margin-top: 16rpx;
}
.user-agent-list.data-v-18c7a761 {
  min-height: 100vh;
  background: #f8f9fa;
}
.content.data-v-18c7a761 {
  padding: 30rpx;
}
.card.data-v-18c7a761 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.card .card-header.data-v-18c7a761 {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.card .card-header .globe-icon.data-v-18c7a761 {
  font-size: 32rpx;
  margin-right: 16rpx;
}
.card .card-header .header-title.data-v-18c7a761 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.card .card-content .search-input.data-v-18c7a761 {
  width: 100%;
  height: 88rpx;
  padding: 0 32rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 30rpx;
  box-sizing: border-box;
  background: #f8f9fa;
}
.card .card-content .search-input.data-v-18c7a761::-webkit-input-placeholder {
  color: #9ca3af;
  font-size: 28rpx;
}
.card .card-content .search-input.data-v-18c7a761::placeholder {
  color: #9ca3af;
  font-size: 28rpx;
}
.card .card-content .search-input.data-v-18c7a761:focus {
  border-color: #3b82f6;
  background: #ffffff;
  outline: none;
}
.card .card-content .category-buttons.data-v-18c7a761 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}
.card .card-content .category-buttons .category-btn.data-v-18c7a761 {
  padding: 20rpx 32rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background: white;
}
.card .card-content .category-buttons .category-btn.active.data-v-18c7a761 {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-color: transparent;
}
.card .card-content .category-buttons .category-btn.active .btn-text.data-v-18c7a761 {
  color: white;
}
.card .card-content .category-buttons .category-btn .btn-text.data-v-18c7a761 {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}
.card .card-content .instructions .instruction-item.data-v-18c7a761 {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 16rpx;
  line-height: 1.6;
}
.ua-list.data-v-18c7a761 {
  margin-bottom: 30rpx;
}
.ua-list .ua-item.data-v-18c7a761 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.ua-list .ua-item .ua-header.data-v-18c7a761 {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 24rpx;
}
.ua-list .ua-item .ua-header .ua-info.data-v-18c7a761 {
  display: flex;
  align-items: center;
  gap: 24rpx;
  flex: 1;
}
.ua-list .ua-item .ua-header .ua-info .ua-name.data-v-18c7a761 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.data-v-18c7a761 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-chrome.data-v-18c7a761 {
  background: #d1fae5;
  color: #065f46;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-firefox.data-v-18c7a761 {
  background: #fed7aa;
  color: #9a3412;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-safari.data-v-18c7a761 {
  background: #dbeafe;
  color: #1e40af;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-edge.data-v-18c7a761 {
  background: #cffafe;
  color: #155e75;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-mobile.data-v-18c7a761 {
  background: #e9d5ff;
  color: #7c2d12;
}
.ua-list .ua-item .ua-header .ua-info .category-tag.tag-bots.data-v-18c7a761 {
  background: #f3f4f6;
  color: #374151;
}
.ua-list .ua-item .ua-header .ua-info .category-tag .tag-text.data-v-18c7a761 {
  font-size: 20rpx;
}
.ua-list .ua-item .ua-header .copy-btn.data-v-18c7a761 {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  background: white;
}
.ua-list .ua-item .ua-header .copy-btn .copy-icon.data-v-18c7a761 {
  font-size: 24rpx;
  margin-right: 8rpx;
}
.ua-list .ua-item .ua-header .copy-btn .copy-text.data-v-18c7a761 {
  font-size: 24rpx;
  color: #6c757d;
}
.ua-list .ua-item .ua-content.data-v-18c7a761 {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}
.ua-list .ua-item .ua-content .ua-string.data-v-18c7a761 {
  font-family: monospace;
  font-size: 24rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;
}
.empty-state.data-v-18c7a761 {
  margin-bottom: 30rpx;
}
.empty-state .empty-card.data-v-18c7a761 {
  background: white;
  border-radius: 16rpx;
  padding: 120rpx 40rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.empty-state .empty-card .empty-text.data-v-18c7a761 {
  font-size: 28rpx;
  color: #6c757d;
}