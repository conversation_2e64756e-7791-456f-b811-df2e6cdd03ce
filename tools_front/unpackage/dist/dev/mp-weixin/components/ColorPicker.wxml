<view wx:if="{{a}}" class="color-picker-modal data-v-9964845b" bindtap="{{p}}"><view class="picker-container data-v-9964845b" catchtap="{{o}}"><view class="picker-header data-v-9964845b"><text class="picker-title data-v-9964845b">选择颜色</text><view class="close-btn data-v-9964845b" bindtap="{{b}}"><text class="close-icon data-v-9964845b">✕</text></view></view><view class="picker-content data-v-9964845b"><view class="current-color-section data-v-9964845b"><view class="color-preview-large data-v-9964845b" style="{{'background-color:' + c}}"></view><view class="color-info data-v-9964845b"><text class="color-hex data-v-9964845b">{{d}}</text><text class="color-desc data-v-9964845b">当前选择的颜色</text></view></view><view class="rgb-inputs data-v-9964845b"><view class="input-group data-v-9964845b"><text class="input-label data-v-9964845b">R</text><input type="number" bindinput="{{e}}" class="rgb-input data-v-9964845b" min="0" max="255" value="{{f}}"/></view><view class="input-group data-v-9964845b"><text class="input-label data-v-9964845b">G</text><input type="number" bindinput="{{g}}" class="rgb-input data-v-9964845b" min="0" max="255" value="{{h}}"/></view><view class="input-group data-v-9964845b"><text class="input-label data-v-9964845b">B</text><input type="number" bindinput="{{i}}" class="rgb-input data-v-9964845b" min="0" max="255" value="{{j}}"/></view></view><view class="preset-colors data-v-9964845b"><text class="preset-title data-v-9964845b">常用颜色</text><view class="preset-grid data-v-9964845b"><view wx:for="{{k}}" wx:for-item="color" wx:key="a" class="preset-color data-v-9964845b" style="{{'background-color:' + color.b}}" bindtap="{{color.c}}"></view></view></view><view class="basic-colors data-v-9964845b"><text class="basic-title data-v-9964845b">基础颜色</text><view class="basic-grid data-v-9964845b"><view wx:for="{{l}}" wx:for-item="color" wx:key="a" class="basic-color data-v-9964845b" style="{{'background-color:' + color.b}}" bindtap="{{color.c}}"></view></view></view></view><view class="picker-footer data-v-9964845b"><button class="btn-cancel data-v-9964845b" bindtap="{{m}}">取消</button><button class="btn-confirm data-v-9964845b" bindtap="{{n}}">确定</button></view></view></view>