"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "ColorPicker",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: "#3b82f6"
    }
  },
  data() {
    return {
      currentColor: "",
      rgbValues: { r: 0, g: 0, b: 0 },
      presetColors: [
        "#FF6B6B",
        "#4ECDC4",
        "#45B7D1",
        "#96CEB4",
        "#FFEAA7",
        "#DDA0DD",
        "#98D8C8",
        "#F39C12",
        "#E74C3C",
        "#9B59B6",
        "#3498DB",
        "#2ECC71",
        "#F1C40F",
        "#E67E22",
        "#1ABC9C",
        "#34495E",
        "#95A5A6",
        "#D35400",
        "#C0392B",
        "#8E44AD"
      ],
      basicColors: [
        "#000000",
        "#333333",
        "#666666",
        "#999999",
        "#CCCCCC",
        "#FFFFFF",
        "#FF0000",
        "#00FF00",
        "#0000FF",
        "#FFFF00",
        "#FF00FF",
        "#00FFFF",
        "#800000",
        "#008000",
        "#000080",
        "#808000",
        "#800080",
        "#008080",
        "#FFA500",
        "#FFC0CB",
        "#A0522D",
        "#808080",
        "#000000",
        "#FFFFFF"
      ]
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && newVal !== this.currentColor) {
          this.updateFromHex(newVal);
        }
      },
      immediate: true
    },
    visible: {
      handler(newVal) {
        if (newVal && this.value) {
          this.updateFromHex(this.value);
        }
      },
      immediate: true
    }
  },
  methods: {
    updateFromHex(hex) {
      if (!hex || hex === this.currentColor)
        return;
      this.currentColor = hex;
      const rgb = this.hexToRgb(hex);
      this.rgbValues = { ...rgb };
    },
    onRgbChange() {
      if (this.rgbUpdateTimer) {
        clearTimeout(this.rgbUpdateTimer);
      }
      this.rgbUpdateTimer = setTimeout(() => {
        const { r, g, b } = this.rgbValues;
        const validR = Math.max(0, Math.min(255, parseInt(r) || 0));
        const validG = Math.max(0, Math.min(255, parseInt(g) || 0));
        const validB = Math.max(0, Math.min(255, parseInt(b) || 0));
        this.rgbValues = { r: validR, g: validG, b: validB };
        this.currentColor = this.rgbToHex(validR, validG, validB);
      }, 300);
    },
    selectPresetColor(color) {
      if (color && color !== this.currentColor) {
        this.updateFromHex(color);
      }
    },
    // 颜色转换工具函数
    hexToRgb(hex) {
      if (!hex)
        return { r: 0, g: 0, b: 0 };
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
      return result ? {
        r: parseInt(result[1], 16),
        g: parseInt(result[2], 16),
        b: parseInt(result[3], 16)
      } : { r: 0, g: 0, b: 0 };
    },
    rgbToHex(r, g, b) {
      const toHex = (n) => {
        const hex = Math.max(0, Math.min(255, parseInt(n) || 0)).toString(16);
        return hex.length === 1 ? "0" + hex : hex;
      };
      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    },
    closeOnOverlay(e) {
      if (e.target === e.currentTarget) {
        this.close();
      }
    },
    close() {
      this.$emit("close");
    },
    confirm() {
      this.$emit("confirm", this.currentColor);
    }
  },
  beforeDestroy() {
    if (this.rgbUpdateTimer) {
      clearTimeout(this.rgbUpdateTimer);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.visible
  }, $props.visible ? {
    b: common_vendor.o((...args) => $options.close && $options.close(...args)),
    c: $data.currentColor,
    d: common_vendor.t($data.currentColor.toUpperCase()),
    e: common_vendor.o([common_vendor.m(($event) => $data.rgbValues.r = $event.detail.value, {
      number: true
    }), (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    f: $data.rgbValues.r,
    g: common_vendor.o([common_vendor.m(($event) => $data.rgbValues.g = $event.detail.value, {
      number: true
    }), (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    h: $data.rgbValues.g,
    i: common_vendor.o([common_vendor.m(($event) => $data.rgbValues.b = $event.detail.value, {
      number: true
    }), (...args) => $options.onRgbChange && $options.onRgbChange(...args)]),
    j: $data.rgbValues.b,
    k: common_vendor.f($data.presetColors, (color, k0, i0) => {
      return {
        a: color,
        b: color,
        c: common_vendor.o(($event) => $options.selectPresetColor(color), color)
      };
    }),
    l: common_vendor.f($data.basicColors, (color, k0, i0) => {
      return {
        a: color,
        b: color,
        c: common_vendor.o(($event) => $options.selectPresetColor(color), color)
      };
    }),
    m: common_vendor.o((...args) => $options.close && $options.close(...args)),
    n: common_vendor.o((...args) => $options.confirm && $options.confirm(...args)),
    o: common_vendor.o(() => {
    }),
    p: common_vendor.o((...args) => $options.closeOnOverlay && $options.closeOnOverlay(...args))
  } : {});
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-9964845b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/ColorPicker.js.map
