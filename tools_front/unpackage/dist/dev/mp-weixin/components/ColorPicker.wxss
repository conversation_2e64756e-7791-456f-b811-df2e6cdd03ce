/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-9964845b {
  display: flex;
}
.flex-1.data-v-9964845b {
  flex: 1;
}
.items-center.data-v-9964845b {
  align-items: center;
}
.justify-center.data-v-9964845b {
  justify-content: center;
}
.justify-between.data-v-9964845b {
  justify-content: space-between;
}
.text-center.data-v-9964845b {
  text-align: center;
}
.rounded.data-v-9964845b {
  border-radius: 3px;
}
.rounded-lg.data-v-9964845b {
  border-radius: 6px;
}
.shadow.data-v-9964845b {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-9964845b {
  padding: 16rpx;
}
.m-4.data-v-9964845b {
  margin: 16rpx;
}
.mb-4.data-v-9964845b {
  margin-bottom: 16rpx;
}
.mt-4.data-v-9964845b {
  margin-top: 16rpx;
}
.color-picker-modal.data-v-9964845b {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.picker-container.data-v-9964845b {
  background: #ffffff;
  border-radius: 24rpx;
  width: 90%;
  max-width: 640rpx;
  max-height: 90vh;
  overflow: hidden;
}
.picker-header.data-v-9964845b {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #f8f9fa;
}
.picker-title.data-v-9964845b {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.close-btn.data-v-9964845b {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.close-btn.data-v-9964845b:active {
  background: #d1d5db;
}
.close-icon.data-v-9964845b {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: bold;
}
.picker-content.data-v-9964845b {
  padding: 32rpx;
  max-height: 60vh;
  overflow-y: auto;
}
.current-color-section.data-v-9964845b {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
}
.color-preview-large.data-v-9964845b {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e5e7eb;
  flex-shrink: 0;
}
.color-info.data-v-9964845b {
  flex: 1;
}
.color-hex.data-v-9964845b {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  display: block;
  margin-bottom: 8rpx;
}
.color-desc.data-v-9964845b {
  font-size: 24rpx;
  color: #6b7280;
}
.rgb-inputs.data-v-9964845b {
  display: flex;
  gap: 16rpx;
  margin-bottom: 32rpx;
}
.input-group.data-v-9964845b {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}
.input-label.data-v-9964845b {
  font-size: 24rpx;
  font-weight: 500;
  color: #6b7280;
}
.rgb-input.data-v-9964845b {
  width: 100%;
  height: 64rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 8rpx;
  text-align: center;
  font-size: 24rpx;
  color: #1f2937;
  background: #ffffff;
}
.rgb-input.data-v-9964845b:focus {
  border-color: #3b82f6;
  outline: none;
}
.preset-colors.data-v-9964845b,
.basic-colors.data-v-9964845b {
  margin-bottom: 32rpx;
}
.preset-colors.data-v-9964845b:last-child,
.basic-colors.data-v-9964845b:last-child {
  margin-bottom: 0;
}
.preset-title.data-v-9964845b,
.basic-title.data-v-9964845b {
  font-size: 26rpx;
  font-weight: 500;
  color: #374151;
  display: block;
  margin-bottom: 16rpx;
}
.preset-grid.data-v-9964845b {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 12rpx;
}
.basic-grid.data-v-9964845b {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12rpx;
}
.preset-color.data-v-9964845b,
.basic-color.data-v-9964845b {
  width: 48rpx;
  height: 48rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
}
.preset-color.data-v-9964845b:active,
.basic-color.data-v-9964845b:active {
  transform: scale(0.95);
  border-color: #3b82f6;
}
.picker-footer.data-v-9964845b {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #f8f9fa;
}
.btn-cancel.data-v-9964845b,
.btn-confirm.data-v-9964845b {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}
.btn-cancel.data-v-9964845b:active,
.btn-confirm.data-v-9964845b:active {
  transform: scale(0.98);
}
.btn-cancel.data-v-9964845b {
  background: #f3f4f6;
  color: #6b7280;
}
.btn-cancel.data-v-9964845b:active {
  background: #e5e7eb;
}
.btn-confirm.data-v-9964845b {
  background: #3b82f6;
  color: #ffffff;
}
.btn-confirm.data-v-9964845b:active {
  background: #2563eb;
}