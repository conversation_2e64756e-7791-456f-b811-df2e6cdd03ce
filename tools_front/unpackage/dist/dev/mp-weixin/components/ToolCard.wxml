<view class="{{['tool-card', 'data-v-59f88834', A]}}" bindtap="{{B}}"><view wx:if="{{a}}" class="list-content data-v-59f88834"><view wx:if="{{b}}" class="{{['status-indicator', 'data-v-59f88834', c && 'new', d && 'in-development', e && 'disabled']}}"></view><view class="{{['icon-container', 'data-v-59f88834', g]}}"><text class="icon data-v-59f88834">{{f}}</text></view><view class="content data-v-59f88834"><text class="title data-v-59f88834">{{h}}</text><text class="description data-v-59f88834">{{i}}</text></view><view class="actions data-v-59f88834"><view class="category-tag data-v-59f88834">{{j}}</view><view class="{{['star-btn', 'data-v-59f88834', l && 'active']}}" catchtap="{{m}}"><text class="star-icon data-v-59f88834">{{k}}</text></view></view></view><view wx:else class="grid-content data-v-59f88834"><view wx:if="{{n}}" class="{{['status-indicator', 'data-v-59f88834', o && 'new', p && 'in-development', q && 'disabled']}}"></view><view class="{{['icon-container', 'data-v-59f88834', s]}}"><text class="icon data-v-59f88834">{{r}}</text></view><text class="title data-v-59f88834">{{t}}</text><text class="description data-v-59f88834">{{v}}</text><view class="bottom-actions data-v-59f88834"><view class="category-tag data-v-59f88834">{{w}}</view><view class="{{['star-btn', 'data-v-59f88834', y && 'active']}}" catchtap="{{z}}"><text class="star-icon data-v-59f88834">{{x}}</text></view></view></view></view>