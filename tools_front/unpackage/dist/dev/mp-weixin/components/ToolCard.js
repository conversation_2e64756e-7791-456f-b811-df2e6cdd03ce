"use strict";
const common_vendor = require("../common/vendor.js");
const _sfc_main = {
  name: "ToolCard",
  props: {
    tool: {
      type: Object,
      required: true
    },
    viewMode: {
      type: String,
      default: "grid",
      validator: (value) => ["grid", "list"].includes(value)
    },
    isStarred: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick() {
      const toolPath = `/pages/tools/${this.tool.name.toLowerCase().replace(/\s+/g, "-")}`;
      common_vendor.index.navigateTo({
        url: toolPath,
        fail: () => {
          common_vendor.index.navigateTo({
            url: `/pages/tool-detail/tool-detail?id=${this.tool.id}`
          });
        }
      });
    },
    toggleStar() {
      this.$emit("toggle-star", this.tool.id);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $props.viewMode === "list"
  }, $props.viewMode === "list" ? common_vendor.e({
    b: $props.tool.isNew || $props.tool.isInDevelopment || $props.tool.isDisabled
  }, $props.tool.isNew || $props.tool.isInDevelopment || $props.tool.isDisabled ? {
    c: $props.tool.isNew ? 1 : "",
    d: $props.tool.isInDevelopment ? 1 : "",
    e: $props.tool.isDisabled ? 1 : ""
  } : {}, {
    f: common_vendor.t($props.tool.icon),
    g: common_vendor.n($props.tool.color),
    h: common_vendor.t($props.tool.name),
    i: common_vendor.t($props.tool.description),
    j: common_vendor.t($props.tool.category),
    k: common_vendor.t($props.isStarred ? "★" : "☆"),
    l: $props.isStarred ? 1 : "",
    m: common_vendor.o((...args) => $options.toggleStar && $options.toggleStar(...args))
  }) : common_vendor.e({
    n: $props.tool.isNew || $props.tool.isInDevelopment || $props.tool.isDisabled
  }, $props.tool.isNew || $props.tool.isInDevelopment || $props.tool.isDisabled ? {
    o: $props.tool.isNew ? 1 : "",
    p: $props.tool.isInDevelopment ? 1 : "",
    q: $props.tool.isDisabled ? 1 : ""
  } : {}, {
    r: common_vendor.t($props.tool.icon),
    s: common_vendor.n($props.tool.color),
    t: common_vendor.t($props.tool.name),
    v: common_vendor.t($props.tool.description),
    w: common_vendor.t($props.tool.category),
    x: common_vendor.t($props.isStarred ? "★" : "☆"),
    y: $props.isStarred ? 1 : "",
    z: common_vendor.o((...args) => $options.toggleStar && $options.toggleStar(...args))
  }), {
    A: common_vendor.n($props.viewMode === "list" ? "list-view" : "grid-view"),
    B: common_vendor.o((...args) => $options.handleClick && $options.handleClick(...args))
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-59f88834"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../.sourcemap/mp-weixin/components/ToolCard.js.map
