/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 自定义颜色 */
/* 工具卡片颜色 */
/* 通用样式类 */
.flex.data-v-59f88834 {
  display: flex;
}
.flex-1.data-v-59f88834 {
  flex: 1;
}
.items-center.data-v-59f88834 {
  align-items: center;
}
.justify-center.data-v-59f88834 {
  justify-content: center;
}
.justify-between.data-v-59f88834 {
  justify-content: space-between;
}
.text-center.data-v-59f88834 {
  text-align: center;
}
.rounded.data-v-59f88834 {
  border-radius: 3px;
}
.rounded-lg.data-v-59f88834 {
  border-radius: 6px;
}
.shadow.data-v-59f88834 {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
.p-4.data-v-59f88834 {
  padding: 16rpx;
}
.m-4.data-v-59f88834 {
  margin: 16rpx;
}
.mb-4.data-v-59f88834 {
  margin-bottom: 16rpx;
}
.mt-4.data-v-59f88834 {
  margin-top: 16rpx;
}
.tool-card.data-v-59f88834 {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.08);
  border: 2rpx solid #f0f0f0;
  transition: all 0.3s ease;
  position: relative;
}
.tool-card.data-v-59f88834:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.tool-card .status-indicator.data-v-59f88834 {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  z-index: 1;
}
.tool-card .status-indicator.new.data-v-59f88834 {
  background-color: #10B981;
  box-shadow: 0 0 8rpx rgba(16, 185, 129, 0.4);
  animation: pulse-59f88834 2s infinite;
}
.tool-card .status-indicator.in-development.data-v-59f88834 {
  background-color: #F59E0B;
  box-shadow: 0 0 8rpx rgba(245, 158, 11, 0.4);
  animation: pulse-59f88834 2s infinite;
}
.tool-card .status-indicator.disabled.data-v-59f88834 {
  background-color: #EF4444;
  box-shadow: 0 0 8rpx rgba(239, 68, 68, 0.4);
}
@keyframes pulse-59f88834 {
0% {
    transform: scale(1);
    opacity: 1;
}
50% {
    transform: scale(1.2);
    opacity: 0.8;
}
100% {
    transform: scale(1);
    opacity: 1;
}
}
.list-view .list-content.data-v-59f88834 {
  display: flex;
  align-items: center;
  gap: 32rpx;
}
.list-view .list-content .icon-container.data-v-59f88834 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.list-view .list-content .icon-container .icon.data-v-59f88834 {
  font-size: 48rpx;
}
.list-view .list-content .content.data-v-59f88834 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.list-view .list-content .content .title.data-v-59f88834 {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}
.list-view .list-content .content .description.data-v-59f88834 {
  font-size: 26rpx;
  color: #6b7280;
}
.list-view .list-content .actions.data-v-59f88834 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 16rpx;
}
.list-view .list-content .actions .category-tag.data-v-59f88834 {
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 20rpx;
}
.list-view .list-content .actions .star-btn.data-v-59f88834 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #f9fafb;
  transition: all 0.3s ease;
}
.list-view .list-content .actions .star-btn.data-v-59f88834:active {
  background: #f3f4f6;
}
.list-view .list-content .actions .star-btn.active.data-v-59f88834 {
  background: rgba(255, 215, 0, 0.1);
}
.list-view .list-content .actions .star-btn.active .star-icon.data-v-59f88834 {
  color: #ffd700 !important;
  transform: scale(1.1);
  opacity: 1 !important;
}
.list-view .list-content .actions .star-btn .star-icon.data-v-59f88834 {
  font-size: 32rpx;
  color: #ccc;
  opacity: 0.6;
  transition: all 0.3s ease;
}
.grid-view .grid-content.data-v-59f88834 {
  text-align: center;
}
.grid-view .grid-content .icon-container.data-v-59f88834 {
  width: 96rpx;
  height: 96rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  transition: transform 0.3s ease;
}
.grid-view .grid-content .icon-container .icon.data-v-59f88834 {
  font-size: 48rpx;
}
.grid-view .grid-content .title.data-v-59f88834 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}
.grid-view .grid-content .description.data-v-59f88834 {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 24rpx;
  line-height: 1.4;
}
.grid-view .grid-content .bottom-actions.data-v-59f88834 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.grid-view .grid-content .bottom-actions .category-tag.data-v-59f88834 {
  font-size: 20rpx;
  padding: 8rpx 16rpx;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 20rpx;
}
.grid-view .grid-content .bottom-actions .star-btn.data-v-59f88834 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  background: #f9fafb;
  transition: all 0.3s ease;
}
.grid-view .grid-content .bottom-actions .star-btn.data-v-59f88834:active {
  background: #f3f4f6;
  transform: scale(0.9);
}
.grid-view .grid-content .bottom-actions .star-btn.active.data-v-59f88834 {
  background: rgba(255, 215, 0, 0.1);
}
.grid-view .grid-content .bottom-actions .star-btn.active .star-icon.data-v-59f88834 {
  color: #ffd700 !important;
  transform: scale(1.1);
  opacity: 1 !important;
}
.grid-view .grid-content .bottom-actions .star-btn .star-icon.data-v-59f88834 {
  font-size: 32rpx;
  color: #ccc;
  opacity: 0.6;
  transition: all 0.3s ease;
}
.grid-view:hover .grid-content .icon-container.data-v-59f88834 {
  transform: scale(1.1);
}
.grid-view:hover .grid-content .star-btn.data-v-59f88834 {
  opacity: 1;
}
.bg-pink-100.data-v-59f88834 {
  background-color: #fce7f3;
}
.bg-yellow-100.data-v-59f88834 {
  background-color: #fef3c7;
}
.bg-red-100.data-v-59f88834 {
  background-color: #fee2e2;
}
.bg-blue-100.data-v-59f88834 {
  background-color: #dbeafe;
}
.bg-green-100.data-v-59f88834 {
  background-color: #dcfce7;
}
.bg-purple-100.data-v-59f88834 {
  background-color: #f3e8ff;
}
.bg-gray-100.data-v-59f88834 {
  background-color: #f3f4f6;
}
.bg-orange-100.data-v-59f88834 {
  background-color: #fed7aa;
}
.bg-teal-100.data-v-59f88834 {
  background-color: #ccfbf1;
}
.bg-slate-100.data-v-59f88834 {
  background-color: #f1f5f9;
}
.bg-indigo-100.data-v-59f88834 {
  background-color: #e0e7ff;
}
.bg-cyan-100.data-v-59f88834 {
  background-color: #cffafe;
}
.bg-lime-100.data-v-59f88834 {
  background-color: #ecfccb;
}
.bg-rose-100.data-v-59f88834 {
  background-color: #ffe4e6;
}
.bg-violet-100.data-v-59f88834 {
  background-color: #ede9fe;
}
.bg-amber-100.data-v-59f88834 {
  background-color: #fef3c7;
}
.bg-emerald-100.data-v-59f88834 {
  background-color: #d1fae5;
}
.bg-sky-100.data-v-59f88834 {
  background-color: #e0f2fe;
}