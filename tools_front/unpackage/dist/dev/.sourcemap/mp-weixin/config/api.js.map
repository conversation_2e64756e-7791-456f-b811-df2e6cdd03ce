{"version": 3, "file": "api.js", "sources": ["config/api.js"], "sourcesContent": ["// API配置文件\nconst config = {\n  // 开发环境API地址\n  development: {\n    baseURL: 'http://localhost:8080',\n    timeout: 30000\n  },\n  \n  // 生产环境API地址\n  production: {\n    baseURL: 'https://your-production-domain.com',\n    timeout: 30000\n  }\n}\n\n// 获取当前环境\nconst env = process.env.NODE_ENV || 'development'\n\n// 导出当前环境配置\nexport const API_CONFIG = config[env]\n\n// API接口地址\nexport const API_ENDPOINTS = {\n  // 图片去水印相关接口\n  IMAGE_WATERMARK_REMOVAL: {\n    BATCH: '/api/tools/media/image-watermark-removal/batch',\n    SINGLE: '/api/tools/media/image-watermark-removal/single',\n    PROGRESS: '/api/tools/media/image-watermark-removal/progress',\n    RESULT: '/api/tools/media/image-watermark-removal/result',\n    CLEANUP: '/api/tools/media/image-watermark-removal/cleanup',\n    ALGORITHMS: '/api/tools/media/image-watermark-removal/algorithms'\n  },\n  \n  // 视频去水印相关接口\n  VIDEO_WATERMARK_REMOVAL: {\n    UPLOAD: '/api/tools/media/video-subtitle-remover/upload',\n    PROGRESS: '/api/tools/media/video-subtitle-remover/progress',\n    RESULT: '/api/tools/media/video-subtitle-remover/result',\n    DOWNLOAD: '/api/tools/media/video-subtitle-remover/download',\n    CLEANUP: '/api/tools/media/video-subtitle-remover/cleanup',\n    ALGORITHMS: '/api/tools/media/video-subtitle-remover/algorithms'\n  },\n  \n  // 文件服务接口\n  FILE_SERVICE: {\n    PROCESSED_IMAGE: '/file/processed/image',\n    PROCESSED_VIDEO: '/file/processed/video'\n  }\n}\n\n// 构建完整的API URL\nexport function buildApiUrl(endpoint) {\n  return API_CONFIG.baseURL + endpoint\n}\n\n// HTTP请求工具函数\nexport const httpRequest = {\n  // GET请求\n  async get(url, options = {}) {\n    const fullUrl = buildApiUrl(url)\n    \n    // #ifdef H5\n    const response = await fetch(fullUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    })\n    return await response.json()\n    // #endif\n    \n    // #ifdef MP-WEIXIN\n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: fullUrl,\n        method: 'GET',\n        header: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        success: (res) => resolve(res.data),\n        fail: reject,\n        ...options\n      })\n    })\n    // #endif\n  },\n\n  // POST请求（JSON）\n  async post(url, data = {}, options = {}) {\n    const fullUrl = buildApiUrl(url)\n    \n    // #ifdef H5\n    const response = await fetch(fullUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      body: JSON.stringify(data),\n      ...options\n    })\n    return await response.json()\n    // #endif\n    \n    // #ifdef MP-WEIXIN\n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: fullUrl,\n        method: 'POST',\n        data: data,\n        header: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        success: (res) => resolve(res.data),\n        fail: reject,\n        ...options\n      })\n    })\n    // #endif\n  },\n\n  // POST请求（FormData）\n  async postFormData(url, formData, options = {}) {\n    const fullUrl = buildApiUrl(url)\n    \n    // #ifdef H5\n    const response = await fetch(fullUrl, {\n      method: 'POST',\n      body: formData,\n      ...options\n    })\n    return await response.json()\n    // #endif\n    \n    // #ifdef MP-WEIXIN\n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: fullUrl,\n        method: 'POST',\n        data: formData,\n        header: {\n          'Content-Type': 'multipart/form-data',\n          ...options.headers\n        },\n        success: (res) => resolve(res.data),\n        fail: reject,\n        ...options\n      })\n    })\n    // #endif\n  },\n\n  // DELETE请求\n  async delete(url, options = {}) {\n    const fullUrl = buildApiUrl(url)\n    \n    // #ifdef H5\n    const response = await fetch(fullUrl, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers\n      },\n      ...options\n    })\n    return await response.json()\n    // #endif\n    \n    // #ifdef MP-WEIXIN\n    return new Promise((resolve, reject) => {\n      uni.request({\n        url: fullUrl,\n        method: 'DELETE',\n        header: {\n          'Content-Type': 'application/json',\n          ...options.headers\n        },\n        success: (res) => resolve(res.data),\n        fail: reject,\n        ...options\n      })\n    })\n    // #endif\n  }\n}\n\nexport default {\n  API_CONFIG,\n  API_ENDPOINTS,\n  buildApiUrl,\n  httpRequest\n}\n"], "names": ["uni"], "mappings": ";;AACA,MAAM,SAAS;AAAA;AAAA,EAEb,aAAa;AAAA,IACX,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AAAA;AAAA,EAGA,YAAY;AAAA,IACV,SAAS;AAAA,IACT,SAAS;AAAA,EACX;AACF;AAGA,MAAM,MAAM;AAGC,MAAA,aAAa,OAAO,GAAG;AAG7B,MAAM,gBAAgB;AAAA;AAAA,EAE3B,yBAAyB;AAAA,IACvB,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA;AAAA,EAGA,yBAAyB;AAAA,IACvB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,SAAS;AAAA,IACT,YAAY;AAAA,EACd;AAAA;AAAA,EAGA,cAAc;AAAA,IACZ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,EACnB;AACF;AAGO,SAAS,YAAY,UAAU;AACpC,SAAO,WAAW,UAAU;AAC9B;AAGO,MAAM,cAAc;AAAA;AAAA,EAEzB,MAAM,IAAI,KAAK,UAAU,IAAI;AACrB,UAAA,UAAU,YAAY,GAAG;AAe/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,GAAG,QAAQ;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ,QAAQ,IAAI,IAAI;AAAA,QAClC,MAAM;AAAA,QACN,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,CACF;AAAA,EAEH;AAAA;AAAA,EAGA,MAAM,KAAK,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AACjC,UAAA,UAAU,YAAY,GAAG;AAgB/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR;AAAA,QACA,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,GAAG,QAAQ;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ,QAAQ,IAAI,IAAI;AAAA,QAClC,MAAM;AAAA,QACN,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,CACF;AAAA,EAEH;AAAA;AAAA,EAGA,MAAM,aAAa,KAAK,UAAU,UAAU,CAAA,GAAI;AACxC,UAAA,UAAU,YAAY,GAAG;AAY/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,GAAG,QAAQ;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ,QAAQ,IAAI,IAAI;AAAA,QAClC,MAAM;AAAA,QACN,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,CACF;AAAA,EAEH;AAAA;AAAA,EAGA,MAAM,OAAO,KAAK,UAAU,IAAI;AACxB,UAAA,UAAU,YAAY,GAAG;AAe/B,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtCA,oBAAAA,MAAI,QAAQ;AAAA,QACV,KAAK;AAAA,QACL,QAAQ;AAAA,QACR,QAAQ;AAAA,UACN,gBAAgB;AAAA,UAChB,GAAG,QAAQ;AAAA,QACb;AAAA,QACA,SAAS,CAAC,QAAQ,QAAQ,IAAI,IAAI;AAAA,QAClC,MAAM;AAAA,QACN,GAAG;AAAA,MAAA,CACJ;AAAA,IAAA,CACF;AAAA,EAEH;AACF;;;"}