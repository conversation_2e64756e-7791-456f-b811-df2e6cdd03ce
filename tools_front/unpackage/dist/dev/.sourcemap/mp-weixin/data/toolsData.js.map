{"version": 3, "file": "toolsData.js", "sources": ["data/toolsData.js"], "sourcesContent": ["// 工具数据 - 完全对应React版本的117个工具\nexport const toolsData = [\n  // 好玩推荐 (3个)\n  { \n    id: 1, \n    name: '趣味图片生成器', \n    icon: '🎨', \n    category: '好玩推荐', \n    description: '生成有趣的图片', \n    color: 'bg-pink-100',\n    isNew: true,\n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'fun-image-generator'\n  },\n  { \n    id: 2, \n    name: '获取永久会员', \n    icon: '👑', \n    category: '好玩推荐', \n    description: '会员权益获取', \n    color: 'bg-yellow-100',\n    isInDevelopment: true,\n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'get-vip-membership'\n  },\n  { \n    id: 3, \n    name: '表白代码制作', \n    icon: '💝', \n    category: '好玩推荐', \n    description: '制作浪漫表白代码', \n    color: 'bg-red-100',\n    isDisabled: true,\n    isUnlocked: false,\n    needsBackend: false\n  },\n\n  // 媒体工具 (25个)\n  { \n    id: 4, \n    name: '视频解析去水印', \n    icon: '🎬', \n    category: '媒体工具', \n    description: '去除视频水印', \n    color: 'bg-blue-100',\n    isNew: true,\n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'video-watermark-remover'\n  },\n  { \n    id: 5, \n    name: '图集解析去水印', \n    icon: '🖼️', \n    category: '媒体工具', \n    description: '批量去图片水印', \n    color: 'bg-blue-100',\n    isInDevelopment: true,\n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'image-watermark-remover'\n  },\n  { \n    id: 6, \n    name: '视频H下载', \n    icon: '📥', \n    category: '媒体工具', \n    description: '高清视频下载', \n    color: 'bg-green-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'video-downloader'\n  },\n  { id: 7, name: '音频剪辑', icon: '🎵', category: '媒体工具', description: '在线音频编辑', color: 'bg-green-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 8, name: '视频转音频', icon: '🔄', category: '媒体工具', description: '提取视频音轨', color: 'bg-purple-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 9, \n    name: '音乐下载器', \n    icon: '🎼', \n    category: '媒体工具', \n    description: '音乐下载工具', \n    color: 'bg-pink-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'music-downloader'\n  },\n  { \n    id: 10, \n    name: '文案提取器', \n    icon: '📝', \n    category: '媒体工具', \n    description: '提取图片文案', \n    color: 'bg-gray-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'text-extractor'\n  },\n  { id: 11, name: '九宫格切图', icon: '⚏', category: '媒体工具', description: '图片九宫格分割', color: 'bg-orange-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 12, name: '图片压缩', icon: '📷', category: '媒体工具', description: '压缩图片大小', color: 'bg-teal-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 13, name: '修改MD5', icon: '🔧', category: '媒体工具', description: '修改文件MD5值', color: 'bg-slate-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 14, name: '截图加壳', icon: '📱', category: '媒体工具', description: '手机截图美化', color: 'bg-indigo-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 15, \n    name: '游戏语音合成', \n    icon: '🎮', \n    category: '媒体工具', \n    description: '游戏角色语音', \n    color: 'bg-purple-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'game-voice-synthesizer'\n  },\n  { \n    id: 16, \n    name: '音效大全', \n    icon: '🔊', \n    category: '媒体工具', \n    description: '各种音效资源', \n    color: 'bg-blue-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'sound-effects-library'\n  },\n  { id: 17, name: '图片拼接', icon: '🖼️', category: '媒体工具', description: '多图拼接合成', color: 'bg-green-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 18, name: '黑白图转换', icon: '⚫', category: '媒体工具', description: '彩色转黑白', color: 'bg-gray-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 19, name: '图片加水印', icon: '💧', category: '媒体工具', description: '添加图片水印', color: 'bg-cyan-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 20, name: '图片打码', icon: '🔒', category: '媒体工具', description: '隐私信息打码', color: 'bg-red-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 21, \n    name: '魔法抹除水印', \n    icon: '✨', \n    category: '媒体工具', \n    description: 'AI智能去水印', \n    color: 'bg-purple-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'magic-watermark-remover'\n  },\n  { \n    id: 22, \n    name: '证件照制作', \n    icon: '📸', \n    category: '媒体工具', \n    description: '制作标准证件照', \n    color: 'bg-indigo-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'id-photo-maker'\n  },\n  { id: 23, name: '视频压缩', icon: '🗜️', category: '媒体工具', description: '压缩视频文件', color: 'bg-teal-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 24, name: '图片镜像翻转', icon: '🪞', category: '媒体工具', description: '图片镜像处理', color: 'bg-blue-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 25, name: '图片像素化', icon: '🔲', category: '媒体工具', description: '像素风格转换', color: 'bg-lime-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 26, name: '隐藏图制作', icon: '🫥', category: '媒体工具', description: '制作隐藏图片', color: 'bg-gray-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n\n  // 颜色工具 (8个)\n  { id: 28, name: '色卡配色', icon: '🎨', category: '颜色工具', description: '专业配色方案', color: 'bg-rose-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 29, name: 'RGB与HEX', icon: '🌈', category: '颜色工具', description: '颜色格式转换', color: 'bg-violet-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 30, name: '图片取色', icon: '🎯', category: '颜色工具', description: '从图片提取颜色', color: 'bg-cyan-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 31, name: '渐变色卡', icon: '🌅', category: '颜色工具', description: '渐变色彩搭配', color: 'bg-amber-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 32, name: '渐变代码生成', icon: '📋', category: '颜色工具', description: 'CSS渐变代码', color: 'bg-purple-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 33, name: '对比度检测', icon: '👁️', category: '颜色工具', description: '检查颜色对比度', color: 'bg-lime-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 35, name: '色调生成器', icon: '🎪', category: '颜色工具', description: '色调变化生成', color: 'bg-indigo-100', isNew: true, isUnlocked: false, needsBackend: false },\n\n  // 实用工具 (9个)\n  { \n    id: 36, \n    name: '车辆价格查询', \n    icon: '🚗', \n    category: '实用工具', \n    description: '汽车价格查询', \n    color: 'bg-blue-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'vehicle-price-query'\n  },\n  { \n    id: 37, \n    name: '全国油价', \n    icon: '⛽', \n    category: '实用工具', \n    description: '实时油价信息', \n    color: 'bg-yellow-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'gas-price-query'\n  },\n  { id: 38, name: '常用号码', icon: '📞', category: '实用工具', description: '常用电话号码', color: 'bg-green-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 39, name: '手机清灰', icon: '🔊', category: '实用工具', description: '声波清理灰尘', color: 'bg-gray-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 40, name: '手持弹幕', icon: '💬', category: '实用工具', description: '手持弹幕显示', color: 'bg-pink-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 41, name: 'BMI计算器', icon: '⚖️', category: '实用工具', description: '身体质量指数', color: 'bg-emerald-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 42, \n    name: '全国天气查询', \n    icon: '🌤️', \n    category: '实用工具', \n    description: '实时天气信息', \n    color: 'bg-sky-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'weather-query'\n  },\n  { id: 43, name: '生成随机数', icon: '🎲', category: '实用工具', description: '随机数生成器', color: 'bg-purple-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 44, name: '安全期计算', icon: '📅', category: '实用工具', description: '生理周期计算', color: 'bg-pink-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n\n  // 趣味工具 (15个)\n  { \n    id: 45, \n    name: '兽语加密解密', \n    icon: '🐾', \n    category: '趣味工具', \n    description: '兽语转换工具', \n    color: 'bg-orange-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'beast-language'\n  },\n  { id: 46, name: '偏心大转盘', icon: '🎡', category: '趣味工具', description: '随机选择转盘', color: 'bg-rainbow', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 47, name: '起始时间计算', icon: '⏰', category: '趣味工具', description: '计算时间差', color: 'bg-blue-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 48, \n    name: '历史上的今天', \n    icon: '📅', \n    category: '趣味工具', \n    description: '今日历史事件', \n    color: 'bg-purple-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'history-today'\n  },\n  { id: 49, name: '吃掉GIF头像', icon: '😋', category: '趣味工具', description: '趣味头像生成', color: 'bg-yellow-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 50, name: 'IP签名档', icon: '📝', category: '趣味工具', description: 'IP地址签名', color: 'bg-gray-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 51, name: '全屏时钟', icon: '⏰', category: '趣味工具', description: '大屏数字时钟', color: 'bg-indigo-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 52, name: '模拟来电', icon: '📞', category: '趣味工具', description: '假装来电话', color: 'bg-green-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 53, name: '手机检测', icon: '📱', category: '趣味工具', description: '手机性能检测', color: 'bg-blue-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 54, name: '一生时间', icon: '🕐', category: '趣味工具', description: '人生时间计算', color: 'bg-rose-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 55, \n    name: '显卡天梯图', \n    icon: '🎮', \n    category: '趣味工具', \n    description: '显卡性能排行', \n    color: 'bg-purple-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'gpu-ladder'\n  },\n  { \n    id: 56, \n    name: 'CPU天梯图', \n    icon: '💻', \n    category: '趣味工具', \n    description: 'CPU性能排行', \n    color: 'bg-cyan-100', \n    isDisabled: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'cpu-ladder'\n  },\n  { id: 57, name: '功德木鱼', icon: '🪕', category: '趣味工具', description: '电子木鱼敲击', color: 'bg-amber-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 58, name: '便携风扇', icon: '🌀', category: '趣味工具', description: '手机变身小风扇', color: 'bg-cyan-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 59, name: '摇骰子', icon: '🎲', category: '趣味工具', description: '虚拟骰子游戏', color: 'bg-pink-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n\n  // 程序员工具 (20个)\n  { id: 60, name: 'PX与EM', icon: '📐', category: '程序员工具', description: '单位转换工具', color: 'bg-blue-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 61, name: '时间戳工具', icon: '⏱️', category: '程序员工具', description: '时间戳转换', color: 'bg-slate-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 62, name: 'MD5加密', icon: '🔐', category: '程序员工具', description: 'MD5哈希加密', color: 'bg-gray-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 63, name: 'ASCII转换', icon: '🔤', category: '程序员工具', description: 'ASCII码转换', color: 'bg-green-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 64, name: '动画代码生成', icon: '🎬', category: '程序员工具', description: 'CSS动画生成', color: 'bg-purple-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 65, name: 'Grid布局生成', icon: '📏', category: '程序员工具', description: 'CSS Grid生成', color: 'bg-indigo-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 66, name: 'Flex布局生成', icon: '📋', category: '程序员工具', description: 'CSS Flex生成', color: 'bg-teal-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 67, name: '磨砂玻璃生成', icon: '🪟', category: '程序员工具', description: '毛玻璃效果CSS', color: 'bg-cyan-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 68, name: 'CSS文字效果', icon: '✨', category: '程序员工具', description: 'CSS特效生成', color: 'bg-violet-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 69, \n    name: '生成网站快照', \n    icon: '📸', \n    category: '程序员工具', \n    description: '网站截图工具', \n    color: 'bg-orange-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'website-snapshot'\n  },\n  { id: 70, name: '端口检测', icon: '🔌', category: '程序员工具', description: '网络端口检测', color: 'bg-red-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 71, name: 'Unicode', icon: '🌐', category: '程序员工具', description: 'Unicode编码', color: 'bg-lime-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 72, name: 'HTTP状态码', icon: '🌐', category: '程序员工具', description: 'HTTP状态码查询', color: 'bg-green-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 73, name: 'HTTP请求头', icon: '📡', category: '程序员工具', description: 'HTTP请求头查看', color: 'bg-blue-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 74, name: 'keyCode按键码', icon: '⌨️', category: '程序员工具', description: '键盘按键码', color: 'bg-gray-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 75, name: 'Linux常用命令', icon: '🐧', category: '程序员工具', description: 'Linux命令查询', color: 'bg-slate-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 76, name: '密码生成器', icon: '🔑', category: '程序员工具', description: '生成安全密码', color: 'bg-red-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 77, name: '常用端口大全', icon: '', category: '程序员工具', description: '常用端口查询', color: 'bg-yellow-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 78, name: 'UA标识大全', icon: '🏷️', category: '程序员工具', description: '用户代理标识', color: 'bg-pink-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 79, name: 'UUID生成器', icon: '🆔', category: '程序员工具', description: '生成唯一标识符', color: 'bg-blue-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n\n  // 文字工具 (30个)\n  { id: 80, name: '伤感文案库', icon: '😢', category: '文字工具', description: '伤感文案集合', color: 'bg-blue-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 81, name: '疯狂星期四文案', icon: '🍗', category: '文字工具', description: 'KFC文案生成器', color: 'bg-orange-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 82, name: '答案之书', icon: '📚', category: '文字工具', description: '随机答案生成', color: 'bg-purple-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 83, name: '名言警句', icon: '💭', category: '文字工具', description: '经典名言集合', color: 'bg-indigo-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 84, name: '文案大全', icon: '📝', category: '文字工具', description: '各类文案素材', color: 'bg-green-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 85, name: 'WiFi昵称', icon: '📶', category: '文字工具', description: '创意WiFi名称', color: 'bg-cyan-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 86, name: '特殊小尾巴', icon: '🎀', category: '文字工具', description: '文字装饰符号', color: 'bg-pink-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 87, name: '小辫子昵称', icon: '🎭', category: '文字工具', description: '特殊字符昵称', color: 'bg-violet-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 88, name: '文本逆序', icon: '🔄', category: '文字工具', description: '文字顺序颠倒', color: 'bg-gray-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 89, name: '上标电话', icon: '☎️', category: '文字工具', description: '上标电话号码', color: 'bg-blue-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 90, name: '下标电话', icon: '📞', category: '文字工具', description: '下标电话号码', color: 'bg-teal-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 91, name: '尖叫文字', icon: '😱', category: '文字工具', description: '夸张文字效果', color: 'bg-red-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 92, name: '小字母昵称', icon: 'ᵃᵇᶜ', category: '文字工具', description: '小字母装饰', color: 'bg-lime-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 93, name: '大小写转换', icon: 'Aa', category: '文字工具', description: '英文大小写转换', color: 'bg-blue-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 94, name: '爱心文字', icon: '💖', category: '文字工具', description: '爱心形状文字', color: 'bg-pink-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 95, name: '文字九宫格', icon: '#️⃣', category: '文字工具', description: '九宫格文字排列', color: 'bg-purple-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 96, name: '带框文字', icon: '🔲', category: '文字工具', description: '文字加边框', color: 'bg-gray-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 97, name: '颜文字合集', icon: '(ﾟ∀ﾟ)', category: '文字工具', description: '各种表情符号', color: 'bg-yellow-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 98, name: '删除线文字', icon: '̶T̶e̶x̶t̶', category: '文字工具', description: '删除线效果', color: 'bg-slate-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 99, name: '爱心颜文字', icon: '♥', category: '文字工具', description: '爱心表情符号', color: 'bg-rose-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 100, name: '520字', icon: '5️⃣2️⃣0️⃣', category: '文字工具', description: '520数字艺术', color: 'bg-red-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 101, name: '箭头文字', icon: '➡️', category: '文字工具', description: '各种箭头符号', color: 'bg-blue-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 102, name: '人间凑数的日子', icon: '📆', category: '文字工具', description: '佛系文案生成', color: 'bg-amber-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 103, name: '坦克文字', icon: '🚗', category: '文字工具', description: '坦克ASCII艺术', color: 'bg-green-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 104, name: '直升机文字', icon: '🚁', category: '文字工具', description: '飞机ASCII艺术', color: 'bg-sky-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 105, name: '音乐文字', icon: '🎵', category: '文字工具', description: '音符符号文字', color: 'bg-purple-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n  { id: 106, name: '发疯语录', icon: '🤪', category: '文字工具', description: '搞怪文案集合', color: 'bg-lime-100', isDisabled: true, isUnlocked: false, needsBackend: false },\n  { id: 107, name: '爱情公寓语录', icon: '🏠', category: '文字工具', description: '经典台词集合', color: 'bg-pink-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { id: 108, name: '随机情话', icon: '💕', category: '文字工具', description: '浪漫情话生成', color: 'bg-rose-100', isInDevelopment: true, isUnlocked: false, needsBackend: false },\n\n  // 图片壁纸 (9个)\n  { \n    id: 109, \n    name: '随机头像', \n    icon: '👤', \n    category: '图片壁纸', \n    description: '随机生成头像', \n    color: 'bg-indigo-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'random-avatar'\n  },\n  { \n    id: 110, \n    name: '随机加载图', \n    icon: '🔄', \n    category: '图片壁纸', \n    description: '随机占位图片', \n    color: 'bg-gray-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'random-loading-image'\n  },\n  {\n    id: 111,\n    name: '随机手机壁纸',\n    icon: '📱',\n    category: '图片壁纸',\n    description: '精美手机壁纸',\n    color: 'bg-teal-100',\n    isNew: true,\n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'bing-daily-wallpaper'\n  },\n  { \n    id: 112, \n    name: '随机PC壁纸', \n    icon: '🖥️', \n    category: '图片壁纸', \n    description: '电脑桌面壁纸', \n    color: 'bg-blue-100', \n    isNew: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'random-pc-wallpaper'\n  },\n  { id: 115, name: '文字壁纸大全', icon: '📝', category: '图片壁纸', description: '文字背景壁纸', color: 'bg-gray-100', isNew: true, isUnlocked: false, needsBackend: false },\n  { \n    id: 116, \n    name: '动漫壁纸', \n    icon: '🎭', \n    category: '图片壁纸', \n    description: '高清动漫壁纸', \n    color: 'bg-purple-100', \n    isInDevelopment: true, \n    isUnlocked: false,\n    needsBackend: true,\n    toolIdentifier: 'anime-wallpapers'\n  },\n];\n\n// 工具分类\nexport const categories = ['全部', '好玩推荐', '媒体工具', '颜色工具', '实用工具', '趣味工具', '程序员工具', '文字工具', '图片壁纸'];\n\n// 根据分类获取工具\nexport function getToolsByCategory(category) {\n  if (category === '全部') {\n    return toolsData;\n  }\n  return toolsData.filter(tool => tool.category === category);\n}\n\n// 根据ID获取工具\nexport function getToolById(id) {\n  return toolsData.find(tool => tool.id === parseInt(id));\n}\n\n// 根据toolIdentifier获取工具\nexport function getToolByIdentifier(toolIdentifier) {\n  return toolsData.find(tool => tool.toolIdentifier === toolIdentifier);\n}\n\n// 搜索工具\nexport function searchTools(query) {\n  if (!query) return toolsData;\n  return toolsData.filter(tool => \n    tool.name.toLowerCase().includes(query.toLowerCase()) || \n    tool.description.toLowerCase().includes(query.toLowerCase())\n  );\n}\n\n// 获取需要后端支持的工具\nexport function getBackendTools() {\n  return toolsData.filter(tool => tool.needsBackend === true);\n}\n\n// 获取可以离线使用的工具\nexport function getOfflineTools() {\n  return toolsData.filter(tool => tool.needsBackend !== true);\n} "], "names": [], "mappings": ";AACY,MAAC,YAAY;AAAA;AAAA,EAEvB;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,EACf;AAAA;AAAA,EAGD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,GAAG,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,GAAG,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC/J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,KAAK,UAAU,QAAQ,aAAa,WAAW,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxJ,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,YAAY,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACtK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC/J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,OAAO,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC/J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,KAAK,UAAU,QAAQ,aAAa,SAAS,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvJ,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,cAAc,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC5J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,OAAO,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC9J,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC1J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAG9J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxJ,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC9J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,WAAW,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,OAAO,UAAU,QAAQ,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAG3J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC9J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxJ,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,kBAAkB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACrK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAG9J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,cAAc,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,KAAK,UAAU,QAAQ,aAAa,SAAS,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC7J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC9J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,KAAK,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,SAAS,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC7J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxJ;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,WAAW,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,OAAO,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAG5J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,SAAS,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC1J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,SAAS,aAAa,SAAS,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACpK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,SAAS,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,SAAS,aAAa,YAAY,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC/J,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,SAAS,aAAa,WAAW,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxK,EAAE,IAAI,IAAI,MAAM,YAAY,MAAM,MAAM,UAAU,SAAS,aAAa,cAAc,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxK,EAAE,IAAI,IAAI,MAAM,YAAY,MAAM,MAAM,UAAU,SAAS,aAAa,cAAc,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACjK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,SAAS,aAAa,YAAY,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvK,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,KAAK,UAAU,SAAS,aAAa,WAAW,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,SAAS,aAAa,UAAU,OAAO,cAAc,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,SAAS,aAAa,aAAa,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACpK,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,SAAS,aAAa,aAAa,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,SAAS,aAAa,aAAa,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzK,EAAE,IAAI,IAAI,MAAM,cAAc,MAAM,MAAM,UAAU,SAAS,aAAa,SAAS,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,aAAa,MAAM,MAAM,UAAU,SAAS,aAAa,aAAa,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,SAAS,aAAa,UAAU,OAAO,cAAc,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,IAAI,UAAU,SAAS,aAAa,UAAU,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,OAAO,UAAU,SAAS,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC5J,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,SAAS,aAAa,WAAW,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAGvK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,WAAW,MAAM,MAAM,UAAU,QAAQ,aAAa,YAAY,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC/J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC1J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,YAAY,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACjK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACrK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC7J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACxJ,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,cAAc,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC5J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,OAAO,UAAU,QAAQ,aAAa,SAAS,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,WAAW,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACpK,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC7J,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,OAAO,UAAU,QAAQ,aAAa,WAAW,OAAO,iBAAiB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC7J,EAAE,IAAI,IAAI,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,SAAS,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACjK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,SAAS,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACnK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,aAAa,UAAU,QAAQ,aAAa,SAAS,OAAO,gBAAgB,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAChK,EAAE,IAAI,IAAI,MAAM,SAAS,MAAM,KAAK,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,aAAa,UAAU,QAAQ,aAAa,WAAW,OAAO,cAAc,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACrK,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACzJ,EAAE,IAAI,KAAK,MAAM,WAAW,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,gBAAgB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACvK,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,aAAa,OAAO,gBAAgB,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAClK,EAAE,IAAI,KAAK,MAAM,SAAS,MAAM,MAAM,UAAU,QAAQ,aAAa,aAAa,OAAO,cAAc,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC5J,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,iBAAiB,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EACrK,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,YAAY,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC9J,EAAE,IAAI,KAAK,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC3J,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,iBAAiB,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA;AAAA,EAGnK;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AAAA,EACD,EAAE,IAAI,KAAK,MAAM,UAAU,MAAM,MAAM,UAAU,QAAQ,aAAa,UAAU,OAAO,eAAe,OAAO,MAAM,YAAY,OAAO,cAAc,MAAO;AAAA,EAC3J;AAAA,IACE,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,gBAAgB;AAAA,EACjB;AACH;AAGY,MAAC,aAAa,CAAC,MAAM,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,MAAM;AAGzF,SAAS,mBAAmB,UAAU;AAC3C,MAAI,aAAa,MAAM;AACrB,WAAO;AAAA,EACR;AACD,SAAO,UAAU,OAAO,UAAQ,KAAK,aAAa,QAAQ;AAC5D;AAGO,SAAS,YAAY,IAAI;AAC9B,SAAO,UAAU,KAAK,UAAQ,KAAK,OAAO,SAAS,EAAE,CAAC;AACxD;AAQO,SAAS,YAAY,OAAO;AACjC,MAAI,CAAC;AAAO,WAAO;AACnB,SAAO,UAAU;AAAA,IAAO,UACtB,KAAK,KAAK,YAAW,EAAG,SAAS,MAAM,aAAa,KACpD,KAAK,YAAY,YAAW,EAAG,SAAS,MAAM,aAAa;AAAA,EAC/D;AACA;;;;;;"}